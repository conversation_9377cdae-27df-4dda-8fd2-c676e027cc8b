<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
  <context id="context1">
  	<commentGenerator>
        <property name="suppressAllComments" value="true" />
    </commentGenerator>
    <jdbcConnection connectionURL="*******************************************" 
    driverClass="oracle.jdbc.driver.OracleDriver" password="efps#123" userId="efps" />
    <javaModelGenerator targetPackage="com.epaylinks.efps.rc.domain" targetProject="rc" />
    <sqlMapGenerator targetPackage="com.epaylinks.efps.rc.dao" targetProject="rc" />
    <javaClientGenerator targetPackage="com.epaylinks.efps.rc.dao" targetProject="rc" type="XMLMAPPER" />
    <table tableName="RC_LIMIT_DATA" domainObjectName="RcLimitData" enableCountByExample="false"
    enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" 
    selectByExampleQueryId="false">
    </table>
  </context>
</generatorConfiguration>
package monitor;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import com.epaylinks.efps.common.tool.spring.SpringUtils;
import com.epaylinks.efps.rc.dao.EarlyWarningMapper;
import com.epaylinks.efps.common.myredis.MyRedisTemplate
import com.epaylinks.efps.common.myredis.MyRedisTemplateService
import java.util.concurrent.TimeUnit
import com.epaylinks.efps.common.tool.time.Timex
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import com.epaylinks.efps.common.business.cum.CustomerInfo
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl
import com.epaylinks.efps.common.business.pay.request.UserType
import java.util.Map
import com.epaylinks.efps.rc.service.RiskEventRuleService
import java.util.Optional
import java.util.HashMap
import com.epaylinks.efps.rc.dao.OtherMapper;

function MyRedisTemplate getRedisTemplate() {
    return SpringUtils.getBean(MyRedisTemplateService.class).getMyRedisTemplate();
}

function long redisIncr(String key, long amount, Integer expiredDays) {
    Long sumAmount = getRedisTemplate().opsForValue().increment(key, amount);
    if (amount == sumAmount) {
        getRedisTemplate().expire(key, expiredDays, TimeUnit.DAYS);
    }
    return sumAmount;
}

function long dayAmount(String customerCode, String bizType, long amount) {
    if (bizType == null) {
        bizType = "ALL";
    }
    return redisIncr(customerCode + ":" + "dayAmount" + ":" + bizType + ":" + Timex.now().to(Timex.Format.yyyyMMdd), amount, 4);
}

function long dayCount(String customerCode, String bizType) {
    if (bizType == null) {
        bizType = "ALL";
    }
    return redisIncr(customerCode + ":" + "dayCount" + ":" + bizType + ":" + Timex.now().to(Timex.Format.yyyyMMdd), 1, 4);
}

function int getLoginTimes(String customerCode, Integer i){
     return SpringUtils.getBean(EarlyWarningMapper.class).countLoginTimes(customerCode, i);
}

function CustomerInfo getCustomerInfo(String customerCode) {
    return SpringUtils.getBean(CumCacheServiceImpl.class).getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
}

function long toLong(String str) {
    if (str == null) {
        return 0;
    }
    return Long.parseLong(str);
}

function Object setTriggerValue(TxsPayResultMsg msg, String ruleCode, Object value) {
    if (msg.getIndexs() == null) {
        msg.setIndexs(new HashMap<>());
    }
    msg.getIndexs().put("_TriggerValue" + ruleCode, String.valueOf(value));
    return value;
}

function String getTriggerValue(TxsPayResultMsg msg, String ruleCode) {
    return Optional.ofNullable((String)(msg.getIndexs().get("_TriggerValue"+ ruleCode))).orElse("");
}

function boolean setBizType(TxsPayResultMsg msg, String ruleCode, Object value) {
    if (msg.getIndexs() == null) {
        msg.setIndexs(new HashMap<>());
    }
    msg.getIndexs().put("_BizType"+ ruleCode, String.valueOf(value));
    return true;
}

function String getBizType(TxsPayResultMsg msg, String ruleCode) {
    return Optional.ofNullable((String)(msg.getIndexs().get("_BizType"+ ruleCode))).orElse("");
}

function  boolean isCustomerExclude(String customerCode, String ruleCode) {
    return SpringUtils.getBean(RiskEventRuleService.class).isCustomerExclude(customerCode, ruleCode);
}

function int countBlackListLimit(String customerCode) {
    return SpringUtils.getBean(EarlyWarningMapper.class).countBlackListLimit(customerCode);
}

function int countAmountLimit(String customerCode) {
    return SpringUtils.getBean(EarlyWarningMapper.class).countAmountLimit(customerCode);
}

function String getInnerBizType(String customerCode) {
    return SpringUtils.getBean(OtherMapper.class).selectInnerBizType(customerCode);
}

function boolean isInTimePeriod(String[] timePeriod) {
    if (timePeriod == null || timePeriod.length % 2 != 0) {
        return false;
    }
    Timex now = Timex.now();
    long ts = now.toMillis();
    String yyyyMMdd = now.to(Timex.Format.yyyyMMdd);
    for (int i = 0; i < timePeriod.length; i++) {
        Timex t1 = Timex.of(yyyyMMdd + timePeriod[i] + "00", Timex.Format.yyyyMMddHHmmss);
        Timex t2 = Timex.of(yyyyMMdd + timePeriod[++i] + "00", Timex.Format.yyyyMMddHHmmss);
        if (ts >= t1.toMillis() && ts <= t2.toMillis()) {
            return true;
        }
    }
    return false;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcArchiveMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcArchive" >
    <id column="ARCHIVE_ID" property="archiveId" jdbcType="DECIMAL" />
    <result column="ARCHIVE_TYPE" property="archiveType" jdbcType="VARCHAR" />
    <result column="CUS_STATUS" property="cusStatus" jdbcType="VARCHAR" />
    <result column="RC_STATUS" property="rcStatus" jdbcType="VARCHAR" />
    <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="VARCHAR" />
    <result column="RC_LEVEL" property="rcLevel" jdbcType="VARCHAR" />
    <result column="REG_TIME" property="regTime" jdbcType="TIMESTAMP" />
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
    <result column="ARCHIVE_CODE" property="archiveCode" jdbcType="VARCHAR" />
    <result column="ARCHIVE_NAME" property="archiveName" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="FROZEN_BALANCE" property="frozenBalance" jdbcType="DECIMAL" />
    <result column="RC_STATUS_REASON" property="rcStatusReason" jdbcType="VARCHAR" />
    <result column="ACCOUNT_STATUS_REASON" property="accountStatusReason" jdbcType="VARCHAR" />
    <result column="RC_LEVEL_REASON" property="rcLevelReason" jdbcType="VARCHAR" />
    <result column="RC_BALANCE_REASON" property="rcBalanceReason" jdbcType="VARCHAR" />
    <result column="BIND_CARD" property="bindCard" jdbcType="VARCHAR" />
    <result column="BIND_CARD_REASON" property="bindCardReason" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR" />
    <result column="PARENT_CODE" property="parentCode" jdbcType="VARCHAR" />
    <result column="AUD_STATUS" property="audStatus" jdbcType="VARCHAR" />
    <result column="AUTH_FINACIAL" property="authFinacial" jdbcType="VARCHAR" />
    <result column="CHECK_RECHARGE_CARD" property="checkRechargeCard" jdbcType="VARCHAR" />
    <result column="CHECK_CARD_REASON" property="checkCardReason" jdbcType="VARCHAR" />
    <result column="E_BANK_SETTING" property="eBankSetting" jdbcType="VARCHAR" />
    <result column="TRADE_START_TIME" property="tradeStartTime" jdbcType="VARCHAR" />
    <result column="TRADE_END_TIME" property="tradeEndTime" jdbcType="VARCHAR" />
    <result column="BIND_CARD_RATIO" property="bindCardRatio" jdbcType="DECIMAL" />
    <result column="temporary_status" property="temporaryStatus" jdbcType="VARCHAR" />
    <result column="temporary_status_reason" property="temporaryStatusReason" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="DECIMAL" />
    <result column="mcc" property="mcc" jdbcType="VARCHAR" />
    <result column="industry" property="industry" jdbcType="VARCHAR" />
    <result column="REGISTERED_CAPITAL" property="registeredCapital" jdbcType="DECIMAL" />
    <result column="PROPORTION" property="proportion" jdbcType="DECIMAL" />
    <result column="WITHDRAW_START_TIME" property="withdrawStartTime" jdbcType="VARCHAR" />
    <result column="WITHDRAW_END_TIME" property="withdrawEndTime" jdbcType="VARCHAR" />
    <result column="WITHDRAW_REASON" property="withdrawReason" jdbcType="VARCHAR" />
    <result column="WITHDRAW_UNIQUE_ID" property="withdrawUniqueId" jdbcType="VARCHAR" />
    <result column="PAY_SETTING" property="paySetting" jdbcType="VARCHAR" />
    <result column="PAY_SETTING_REASON" property="paySettingReason" jdbcType="VARCHAR" />
    <result column="PAY_SETTING_UNIQUE_ID" property="paySettingUniqueId" jdbcType="VARCHAR" />
    <result column="MIN_WITHDRAW_RATIO" property="minWithdrawRatio" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    ARCHIVE_ID, ARCHIVE_TYPE, CUS_STATUS, RC_STATUS, ACCOUNT_STATUS, RC_LEVEL, REG_TIME, 
    SOURCE, ARCHIVE_CODE, ARCHIVE_NAME, USER_ID, USER_NAME, FROZEN_BALANCE, RC_STATUS_REASON, 
    ACCOUNT_STATUS_REASON, RC_LEVEL_REASON, RC_BALANCE_REASON, BIND_CARD, BIND_CARD_REASON, 
    UPDATE_TIME, CERTIFICATE_NO, PARENT_CODE, AUD_STATUS, AUTH_FINACIAL, CHECK_RECHARGE_CARD, 
    CHECK_CARD_REASON, E_BANK_SETTING, TRADE_START_TIME, TRADE_END_TIME, BIND_CARD_RATIO,temporary_status,temporary_status_reason
    ,type,mcc,industry,REGISTERED_CAPITAL,PROPORTION,WITHDRAW_START_TIME,WITHDRAW_END_TIME,WITHDRAW_REASON,WITHDRAW_UNIQUE_ID
    ,PAY_SETTING,PAY_SETTING_REASON,PAY_SETTING_UNIQUE_ID,MIN_WITHDRAW_RATIO
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_ARCHIVE
    where ARCHIVE_ID = #{archiveId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_ARCHIVE
    where ARCHIVE_ID = #{archiveId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcArchive" >
    insert into RC_ARCHIVE (ARCHIVE_ID, ARCHIVE_TYPE, CUS_STATUS, 
      RC_STATUS, ACCOUNT_STATUS, RC_LEVEL, 
      REG_TIME, SOURCE, ARCHIVE_CODE, 
      ARCHIVE_NAME, USER_ID, USER_NAME, 
      FROZEN_BALANCE, RC_STATUS_REASON, ACCOUNT_STATUS_REASON, 
      RC_LEVEL_REASON, RC_BALANCE_REASON, BIND_CARD, 
      BIND_CARD_REASON, UPDATE_TIME, CERTIFICATE_NO, 
      PARENT_CODE, AUD_STATUS, AUTH_FINACIAL, 
      CHECK_RECHARGE_CARD, CHECK_CARD_REASON, E_BANK_SETTING, 
      TRADE_START_TIME, TRADE_END_TIME, BIND_CARD_RATIO,temporary_status,temporary_status_reason,type,mcc,industry,REGISTERED_CAPITAL,
      PROPORTION,WITHDRAW_START_TIME,WITHDRAW_END_TIME,WITHDRAW_REASON,WITHDRAW_UNIQUE_ID)
    values (#{archiveId,jdbcType=DECIMAL}, #{archiveType,jdbcType=VARCHAR}, #{cusStatus,jdbcType=VARCHAR}, 
      #{rcStatus,jdbcType=VARCHAR}, #{accountStatus,jdbcType=VARCHAR}, #{rcLevel,jdbcType=VARCHAR}, 
      #{regTime,jdbcType=TIMESTAMP}, #{source,jdbcType=VARCHAR}, #{archiveCode,jdbcType=VARCHAR}, 
      #{archiveName,jdbcType=VARCHAR}, #{userId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, 
      #{frozenBalance,jdbcType=DECIMAL}, #{rcStatusReason,jdbcType=VARCHAR}, #{accountStatusReason,jdbcType=VARCHAR}, 
      #{rcLevelReason,jdbcType=VARCHAR}, #{rcBalanceReason,jdbcType=VARCHAR}, #{bindCard,jdbcType=VARCHAR}, 
      #{bindCardReason,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{certificateNo,jdbcType=VARCHAR}, 
      #{parentCode,jdbcType=VARCHAR}, #{audStatus,jdbcType=VARCHAR}, #{authFinacial,jdbcType=VARCHAR}, 
      #{checkRechargeCard,jdbcType=VARCHAR}, #{checkCardReason,jdbcType=VARCHAR}, #{eBankSetting,jdbcType=VARCHAR}, 
      #{tradeStartTime,jdbcType=VARCHAR}, #{tradeEndTime,jdbcType=VARCHAR}, #{bindCardRatio,jdbcType=DECIMAL},
      #{temporaryStatus,jdbcType=VARCHAR}, #{temporaryStatusReason,jdbcType=VARCHAR},#{type,jdbcType=DECIMAL},
      #{mcc,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},#{registeredCapital,jdbcType=DECIMAL},
      #{proportion,jdbcType=DECIMAL},#{withdrawStartTime,jdbcType=VARCHAR},#{withdrawEndTime,jdbcType=VARCHAR},
      #{withdrawReason,jdbcType=VARCHAR},#{withdrawUniqueId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcArchive" >
    insert into RC_ARCHIVE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="archiveId != null" >
        ARCHIVE_ID,
      </if>
      <if test="archiveType != null" >
        ARCHIVE_TYPE,
      </if>
      <if test="cusStatus != null" >
        CUS_STATUS,
      </if>
      <if test="rcStatus != null" >
        RC_STATUS,
      </if>
      <if test="accountStatus != null" >
        ACCOUNT_STATUS,
      </if>
      <if test="rcLevel != null" >
        RC_LEVEL,
      </if>
      <if test="regTime != null" >
        REG_TIME,
      </if>
      <if test="source != null" >
        SOURCE,
      </if>
      <if test="archiveCode != null" >
        ARCHIVE_CODE,
      </if>
      <if test="archiveName != null" >
        ARCHIVE_NAME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="frozenBalance != null" >
        FROZEN_BALANCE,
      </if>
      <if test="rcStatusReason != null" >
        RC_STATUS_REASON,
      </if>
      <if test="accountStatusReason != null" >
        ACCOUNT_STATUS_REASON,
      </if>
      <if test="rcLevelReason != null" >
        RC_LEVEL_REASON,
      </if>
      <if test="rcBalanceReason != null" >
        RC_BALANCE_REASON,
      </if>
      <if test="bindCard != null" >
        BIND_CARD,
      </if>
      <if test="bindCardReason != null" >
        BIND_CARD_REASON,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="certificateNo != null" >
        CERTIFICATE_NO,
      </if>
      <if test="parentCode != null" >
        PARENT_CODE,
      </if>
      <if test="audStatus != null" >
        AUD_STATUS,
      </if>
      <if test="authFinacial != null" >
        AUTH_FINACIAL,
      </if>
      <if test="checkRechargeCard != null" >
        CHECK_RECHARGE_CARD,
      </if>
      <if test="checkCardReason != null" >
        CHECK_CARD_REASON,
      </if>
      <if test="eBankSetting != null" >
        E_BANK_SETTING,
      </if>
      <if test="tradeStartTime != null" >
        TRADE_START_TIME,
      </if>
      <if test="tradeEndTime != null" >
        TRADE_END_TIME,
      </if>
      <if test="bindCardRatio != null">
        BIND_CARD_RATIO,
      </if>
      <if test="temporaryStatus != null">
        temporary_status,
      </if>
      <if test="temporaryStatusReason != null">
        temporary_status_reason,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="mcc != null">
        mcc,
      </if>
      <if test="industry != null">
        industry,
      </if>
      <if test="registeredCapital != null">
        registered_capital,
      </if>
      <if test="proportion != null">
        PROPORTION,
      </if>
      <if test="withdrawStartTime != null">
        WITHDRAW_START_TIME,
      </if>
      <if test="withdrawEndTime != null">
        WITHDRAW_END_TIME,
      </if>
      <if test="withdrawReason != null">
        WITHDRAW_REASON,
      </if>
      <if test="withdrawUniqueId != null">
        WITHDRAW_UNIQUE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="archiveId != null" >
        #{archiveId,jdbcType=DECIMAL},
      </if>
      <if test="archiveType != null" >
        #{archiveType,jdbcType=VARCHAR},
      </if>
      <if test="cusStatus != null" >
        #{cusStatus,jdbcType=VARCHAR},
      </if>
      <if test="rcStatus != null" >
        #{rcStatus,jdbcType=VARCHAR},
      </if>
      <if test="accountStatus != null" >
        #{accountStatus,jdbcType=VARCHAR},
      </if>
      <if test="rcLevel != null" >
        #{rcLevel,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null" >
        #{regTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="archiveCode != null" >
        #{archiveCode,jdbcType=VARCHAR},
      </if>
      <if test="archiveName != null" >
        #{archiveName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="frozenBalance != null" >
        #{frozenBalance,jdbcType=DECIMAL},
      </if>
      <if test="rcStatusReason != null" >
        #{rcStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="accountStatusReason != null" >
        #{accountStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="rcLevelReason != null" >
        #{rcLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="rcBalanceReason != null" >
        #{rcBalanceReason,jdbcType=VARCHAR},
      </if>
      <if test="bindCard != null" >
        #{bindCard,jdbcType=VARCHAR},
      </if>
      <if test="bindCardReason != null" >
        #{bindCardReason,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certificateNo != null" >
        #{certificateNo,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null" >
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="audStatus != null" >
        #{audStatus,jdbcType=VARCHAR},
      </if>
      <if test="authFinacial != null" >
        #{authFinacial,jdbcType=VARCHAR},
      </if>
      <if test="checkRechargeCard != null" >
        #{checkRechargeCard,jdbcType=VARCHAR},
      </if>
      <if test="checkCardReason != null" >
        #{checkCardReason,jdbcType=VARCHAR},
      </if>
      <if test="eBankSetting != null" >
        #{eBankSetting,jdbcType=VARCHAR},
      </if>
      <if test="tradeStartTime != null" >
        #{tradeStartTime,jdbcType=VARCHAR},
      </if>
      <if test="tradeEndTime != null" >
        #{tradeEndTime,jdbcType=VARCHAR},
      </if>
      <if test="bindCardRatio != null">
        #{bindCardRatio,jdbcType=DECIMAL},
      </if>
      <if test="temporaryStatus != null">
        #{temporaryStatus,jdbcType=VARCHAR},
      </if>
      <if test="temporaryStatusReason != null">
        #{temporaryStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="mcc != null">
        #{mcc,jdbcType=VARCHAR},
      </if>
      <if test="industry != null">
        #{industry,jdbcType=VARCHAR},
      </if>
      <if test="registeredCapital != null">
        #{registeredCapital,jdbcType=DECIMAL},
      </if>
      <if test="proportion != null">
        #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="withdrawStartTime != null">
        #{withdrawStartTime,jdbcType=VARCHAR},
      </if>
      <if test="withdrawEndTime != null">
        #{withdrawEndTime,jdbcType=VARCHAR},
      </if>
      <if test="withdrawReason != null">
        #{withdrawReason,jdbcType=VARCHAR},
      </if>
      <if test="withdrawUniqueId != null">
        #{withdrawUniqueId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcArchive" >
    update RC_ARCHIVE
    <set >
      <if test="archiveType != null" >
        ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR},
      </if>
      <if test="cusStatus != null" >
        CUS_STATUS = #{cusStatus,jdbcType=VARCHAR},
      </if>
      <if test="rcStatus != null" >
        RC_STATUS = #{rcStatus,jdbcType=VARCHAR},
      </if>
      <if test="accountStatus != null" >
        ACCOUNT_STATUS = #{accountStatus,jdbcType=VARCHAR},
      </if>
      <if test="rcLevel != null" >
        RC_LEVEL = #{rcLevel,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null" >
        REG_TIME = #{regTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=VARCHAR},
      </if>
      <if test="archiveCode != null" >
        ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR},
      </if>
      <if test="archiveName != null" >
        ARCHIVE_NAME = #{archiveName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="frozenBalance != null" >
        FROZEN_BALANCE = #{frozenBalance,jdbcType=DECIMAL},
      </if>
      <if test="rcStatusReason != null" >
        RC_STATUS_REASON = #{rcStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="accountStatusReason != null" >
        ACCOUNT_STATUS_REASON = #{accountStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="rcLevelReason != null" >
        RC_LEVEL_REASON = #{rcLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="rcBalanceReason != null" >
        RC_BALANCE_REASON = #{rcBalanceReason,jdbcType=VARCHAR},
      </if>
      <if test="bindCard != null" >
        BIND_CARD = #{bindCard,jdbcType=VARCHAR},
      </if>
      <if test="bindCardReason != null" >
        BIND_CARD_REASON = #{bindCardReason,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certificateNo != null" >
        CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null" >
        PARENT_CODE = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="audStatus != null" >
        AUD_STATUS = #{audStatus,jdbcType=VARCHAR},
      </if>
      <if test="authFinacial != null" >
        AUTH_FINACIAL = #{authFinacial,jdbcType=VARCHAR},
      </if>
      <if test="checkRechargeCard != null" >
        CHECK_RECHARGE_CARD = #{checkRechargeCard,jdbcType=VARCHAR},
      </if>
      <if test="checkCardReason != null" >
        CHECK_CARD_REASON = #{checkCardReason,jdbcType=VARCHAR},
      </if>
      <if test="eBankSetting != null" >
        E_BANK_SETTING = #{eBankSetting,jdbcType=VARCHAR},
      </if>
      <if test="tradeStartTime != null" >
        TRADE_START_TIME = #{tradeStartTime,jdbcType=VARCHAR},
      </if>
      <if test="tradeEndTime != null" >
        TRADE_END_TIME = #{tradeEndTime,jdbcType=VARCHAR},
      </if>
      <if test="bindCardRatio != null">
        BIND_CARD_RATIO = #{bindCardRatio,jdbcType=DECIMAL},
      </if>
      <if test="temporaryStatus != null">
        temporary_status = #{temporaryStatus,jdbcType=VARCHAR},
      </if>
      <if test="temporaryStatusReason != null">
        temporary_status_reason = #{temporaryStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=DECIMAL},
      </if>
      <if test="mcc != null">
        mcc = #{mcc,jdbcType=VARCHAR},
      </if>
      <if test="industry != null">
        industry = #{industry,jdbcType=VARCHAR},
      </if>
      <if test="registeredCapital != null">
        registered_capital = #{registeredCapital,jdbcType=VARCHAR},
      </if>
      <if test="proportion != null">
        PROPORTION = #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="withdrawStartTime != null">
        WITHDRAW_START_TIME = #{withdrawStartTime,jdbcType=VARCHAR},
      </if>
      <if test="withdrawEndTime != null">
        WITHDRAW_END_TIME = #{withdrawEndTime,jdbcType=VARCHAR},
      </if>
      <if test="withdrawReason != null">
        WITHDRAW_REASON = #{withdrawReason,jdbcType=VARCHAR},
      </if>
      <if test="withdrawUniqueId != null">
        WITHDRAW_UNIQUE_ID = #{withdrawUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="minWithdrawRatio != null">
        MIN_WITHDRAW_RATIO = #{minWithdrawRatio,jdbcType=DECIMAL},
      </if>
    </set>
    where ARCHIVE_ID = #{archiveId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcArchive" >
    update RC_ARCHIVE
    set ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR},
      CUS_STATUS = #{cusStatus,jdbcType=VARCHAR},
      RC_STATUS = #{rcStatus,jdbcType=VARCHAR},
      ACCOUNT_STATUS = #{accountStatus,jdbcType=VARCHAR},
      RC_LEVEL = #{rcLevel,jdbcType=VARCHAR},
      REG_TIME = #{regTime,jdbcType=TIMESTAMP},
      SOURCE = #{source,jdbcType=VARCHAR},
      ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR},
      ARCHIVE_NAME = #{archiveName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      FROZEN_BALANCE = #{frozenBalance,jdbcType=DECIMAL},
      RC_STATUS_REASON = #{rcStatusReason,jdbcType=VARCHAR},
      ACCOUNT_STATUS_REASON = #{accountStatusReason,jdbcType=VARCHAR},
      RC_LEVEL_REASON = #{rcLevelReason,jdbcType=VARCHAR},
      RC_BALANCE_REASON = #{rcBalanceReason,jdbcType=VARCHAR},
      BIND_CARD = #{bindCard,jdbcType=VARCHAR},
      BIND_CARD_REASON = #{bindCardReason,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
      PARENT_CODE = #{parentCode,jdbcType=VARCHAR},
      AUD_STATUS = #{audStatus,jdbcType=VARCHAR},
      AUTH_FINACIAL = #{authFinacial,jdbcType=VARCHAR},
      CHECK_RECHARGE_CARD = #{checkRechargeCard,jdbcType=VARCHAR},
      CHECK_CARD_REASON = #{checkCardReason,jdbcType=VARCHAR},
      E_BANK_SETTING = #{eBankSetting,jdbcType=VARCHAR},
      TRADE_START_TIME = #{tradeStartTime,jdbcType=VARCHAR},
      TRADE_END_TIME = #{tradeEndTime,jdbcType=VARCHAR},
      BIND_CARD_RATIO = #{bindCardRatio,jdbcType=DECIMAL},
      temporary_status = #{temporaryStatus,jdbcType=VARCHAR},
      temporary_status_reason = #{temporaryStatusReason,jdbcType=VARCHAR},
      type = #{type,jdbcType=DECIMAL},
      mcc = #{mcc,jdbcType=VARCHAR},
      industry = #{industry,jdbcType=VARCHAR},
      registered_capital = #{registeredCapital,jdbcType=DECIMAL},
      PROPORTION = #{proportion,jdbcType=DECIMAL},
      WITHDRAW_START_TIME = #{withdrawStartTime,jdbcType=VARCHAR},
      WITHDRAW_END_TIME = #{withdrawEndTime,jdbcType=VARCHAR},
      WITHDRAW_REASON = #{withdrawReason,jdbcType=VARCHAR},
      WITHDRAW_UNIQUE_ID = #{withdrawUniqueId,jdbcType=VARCHAR},
      MIN_WITHDRAW_RATIO = #{minWithdrawRatio,jdbcType=DECIMAL}
    where ARCHIVE_ID = #{archiveId,jdbcType=DECIMAL}
  </update>
  

  <select id="selectByCodeOrName" resultMap="BaseResultMap"> <!-- 兼容旧通过商户号或商户名称查询 --> 
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_ARCHIVE 
    WHERE ARCHIVE_TYPE in ('005','009')
    <if test="levelCode != null and levelCode != ''">
      AND ARCHIVE_CODE = #{levelCode}
    </if>
    <if test="name != null and name !=''">
      AND ARCHIVE_NAME = #{name}
    </if>
  </select>

  <select id="selectByMap" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_ARCHIVE t
    WHERE ARCHIVE_TYPE in ('005','009')
    <if test="levelCode != null and levelCode != ''">
      AND ARCHIVE_CODE = #{levelCode}
    </if>
    <if test="name != null and name !=''">
      AND ARCHIVE_NAME = #{name}
    </if>
    <if test="userCompanyId != null">
      AND t.ARCHIVE_CODE in (
        select cc.customer_no from cust_customer cc where cc.company_id in (
        select company_id from pas_company
        start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
        connect by parent_id = prior company_id
        )
      )
    </if>
    <if test="businessManId != null">
      AND t.ARCHIVE_CODE in (
        select cc.customer_no from cust_customer cc
        where cc.business_man_id = #{businessManId,jdbcType=DECIMAL}
      )
    </if>
    <if test="companyIds != null">
      and exists (
        select 1 from cust_customer_draft cd
        where t.ARCHIVE_CODE = cd.CUSTOMER_NO
        and cd.company_id in
        <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      )
    </if>
  </select>
  
  <select id="selectByTypeAndCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from RC_ARCHIVE 
    where 1=1
      <if test="archiveType != null and archiveType != ''">
        and ARCHIVE_TYPE = #{archiveType , jdbcType = VARCHAR}
      </if>
    and ARCHIVE_CODE = #{archiveCode , jdbcType = VARCHAR}
  </select>

  <select id="selectInfoId" parameterType="String" resultType="String">
      SELECT NEWEST_CUSTOMER_INFO_ID FROM PAS_CUSTOMER WHERE customer_code =#{customerCode}
  </select>


  <select id="totalLimitQuery" parameterType="String" resultType="int">
    SELECT count(LIMIT_ID) FROM RC_LIMIT WHERE BUSINESS_TAGER_ID = #{archiveCode}
  </select>

  <select id="listLimitQuery" resultType="java.util.HashMap">
    SELECT * FROM (
       SELECT c.* ,rownum rn FROM (
          SELECT b.remark as "remark",b.define_identifier as "identifier",b.alias as "alias" , a.limit_value as "limitValue" , a.limit_type as "type"
          FROM rc_limit a
          INNER JOIN  rc_define b
          ON a.DEFINE_CODE = b.CODE
          WHERE  a.BUSINESS_TAGER_ID = #{archiveCode}
          ORDER BY a.CREATE_TIME DESC
      ) c WHERE rownum &lt;= #{endNum}
    ) WHERE rn &gt;= #{startNum}
  </select>

  <select id="selectByArchiveCode" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from RC_ARCHIVE 
    where ARCHIVE_CODE = #{archiveCode , jdbcType = VARCHAR}
  </select>

  <update id="updateCusStatus">
    UPDATE RC_ARCHIVE SET CUS_STATUS = #{newStatus} WHERE ARCHIVE_CODE = #{customerCode}
  </update>

  <sql id="rcArchinveCondition">
    <if test="archiveType != null and archiveType != ''" >
      ARCHIVE_TYPE = #{archiveType,jdbcType = VARCHAR}
    </if>
    <if test="archiveCode != null and archiveCode != ''" >
      AND ARCHIVE_CODE = #{archiveCode,jdbcType = VARCHAR}
    </if>
    <if test="archiveName != null and archiveName != ''" ><!-- 模糊查询 -->
      AND ARCHIVE_NAME LIKE '%'||#{archiveName,jdbcType = VARCHAR}||'%'
    </if>
    <if test="archiveNameExact != null and archiveNameExact != ''" ><!-- 精确查询 -->
      AND ARCHIVE_NAME = #{archiveNameExact,jdbcType = VARCHAR}
    </if>
    <if test="audStatus != null and audStatus != ''" >
      AND AUD_STATUS = #{audStatus,jdbcType = VARCHAR}
    </if>
    <if test="certificateNo != null and certificateNo != ''" >
      AND CERTIFICATE_NO = #{certificateNo,jdbcType = VARCHAR}
    </if>
    <if test="rcLevel != null and rcLevel != ''" >
      AND RC_LEVEL = #{rcLevel,jdbcType = VARCHAR}
    </if>
    <if test="type != null and type != ''">
      and type = #{type,jdbcType=DECIMAL}
    </if>
    <if test="innerBusinessType != null and innerBusinessType != ''">
      <choose>
        <when test="source == 'industry'">
          and (
            industry = #{innerBusinessType,jdbcType = VARCHAR}
          or
          exists (
            select 1 from cust_mcc_business_type bt
            where bt.business = #{innerBusinessType,jdbcType = VARCHAR}
            and bt.mcc = a.mcc
          )
          )
        </when>
        <otherwise>
          and industry = #{innerBusinessType,jdbcType = VARCHAR}
        </otherwise>
      </choose>
    </if>
    <if test="mcc != null and mcc != ''">
      and mcc = #{mcc,jdbcType = VARCHAR}
    </if>
    <if test="clientNo != null and clientNo != ''" >
      AND ARCHIVE_CODE in (
        select c.customer_no from cust_customer c where c.client_no = #{clientNo,jdbcType = VARCHAR}
      )
    </if>
    <if test="userCompanyId != null">
      <choose>
        <when test='archiveType != null and archiveType == "012"'>
          AND a.ARCHIVE_CODE in (
            select cc.CLIENT_NO from cust_customer cc where cc.company_id in (
              select company_id from pas_company
              start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
              connect by parent_id = prior company_id
            )
          )
        </when>
        <when test='archiveType != null and archiveType == "008"'>
          AND t.customer_code in (
            select cc.customer_no from cust_customer cc where cc.company_id in (
              select company_id from pas_company
              start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
              connect by parent_id = prior company_id
            )
          )
        </when>
        <when test='archiveType != "008" and archiveType != "012" and archiveType != "013"'>
          AND a.ARCHIVE_CODE in (
            select cc.customer_no from cust_customer cc where cc.company_id in (
              select company_id from pas_company
              start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
              connect by parent_id = prior company_id
            )
          )
        </when>
      </choose>
    </if>
    <if test="businessManId != null">
      <choose>
        <when test='archiveType != null and archiveType == "012"'>
          AND a.ARCHIVE_CODE in (
            select cc.CLIENT_NO from cust_customer cc
            where cc.business_man_id = #{businessManId,jdbcType=DECIMAL}
          )
        </when>
        <when test='archiveType != null and archiveType == "008"'>
          AND t.customer_code in (
            select cc.customer_no from cust_customer cc
            where cc.business_man_id = #{businessManId,jdbcType=DECIMAL}
          )
        </when>
        <when test='archiveType != "008" and archiveType != "012" and archiveType != "013"'>
          AND a.ARCHIVE_CODE in (
            select cc.customer_no from cust_customer cc
            where cc.business_man_id = #{businessManId,jdbcType=DECIMAL}
          )
        </when>
      </choose>
    </if>
    <if test="companyIds != null">
      <choose>
        <when test='archiveType != null and archiveType == "012"'>
          and exists (
            select 1 from cust_customer cd
            where a.ARCHIVE_CODE = cd.CLIENT_NO
            and cd.company_id in
            <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
            </foreach>
          )
        </when>
        <when test='archiveType != null and archiveType == "008"'>
          and exists (
            select 1 from cust_customer_draft cd
            where t.customer_code = cd.CUSTOMER_NO
            and cd.company_id in
            <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
            </foreach>
          )
        </when>
        <when test='archiveType != "008" and archiveType != "012" and archiveType != "013"'>
          and exists (
            select 1 from cust_customer_draft cd
            where a.ARCHIVE_CODE = cd.CUSTOMER_NO
            and cd.company_id in
            <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
            </foreach>
          )
        </when>
      </choose>
    </if>
    <choose>
      <when test='archiveType != null and archiveType == "008"'><!-- 只查终端 -->
        AND ARCHIVE_TYPE = '008'
        <if test="machineCode != null and machineCode != ''" >
          AND ARCHIVE_CODE in (
            select t.terminal_code from cust_terminal t where t.machine_code = #{machineCode,jdbcType = VARCHAR}
          )
        </if>
      </when>
      <when test='archiveType != null and archiveType == "009"'><!-- 只查个人账户 -->
        AND ARCHIVE_TYPE = '009'
      </when>
      <when test='archiveType != null and archiveType == "012"'>
        AND ARCHIVE_TYPE = '012'
      </when>
      <when test='archiveType != null and archiveType == "013"'>
        AND ARCHIVE_TYPE = '013'
      </when>
      <otherwise>
        <![CDATA[ AND ARCHIVE_TYPE <> '008' AND ARCHIVE_TYPE <> '009' AND ARCHIVE_TYPE <> '012' AND ARCHIVE_TYPE <> '013']]>
      </otherwise>
    </choose>
  </sql>

  <select id="totalPageQuery" resultType="int"  parameterType="java.util.Map">
  SELECT count(*) FROM RC_ARCHIVE a
  <if test='archiveType != null and archiveType == "008"'>
    left join cust_terminal t on a.archive_code = t.terminal_code
  </if>
    <where>
      <include refid="rcArchinveCondition" />
    </where>
  </select>

  <select id="pageQuery" resultType="com.epaylinks.efps.rc.domain.RcArchivePageResponse"  parameterType="java.util.Map">
    SELECT * FROM (
      SELECT c.* ,rownum rn FROM (
        SELECT ARCHIVE_ID as "archiveId", ARCHIVE_CODE as "archiveCode",ARCHIVE_NAME as "archiveName",RC_LEVEL as "rcLevel",
               ARCHIVE_TYPE as "archiveType", a.UPDATE_TIME as "updateTime", AUD_STATUS as "audStatus", certificate_no as "certificateNo"
                ,a.TYPE as "type",MCC as "mcc",INDUSTRY as "industry"
        FROM RC_ARCHIVE a
        <if test='archiveType != null and archiveType == "008"'>
          left join cust_terminal t on a.archive_code = t.terminal_code
        </if>
        <where>
          <include refid="rcArchinveCondition" />
        </where>
        ORDER BY a.REG_TIME DESC
      ) c WHERE rownum &lt;= #{endNum,jdbcType=DECIMAL}
      
    ) WHERE rn &gt;= #{startNum,jdbcType=DECIMAL}
  </select>


  <select id="selectByCertificateNo" resultMap="BaseResultMap"> <!-- 通过证件号查询 --> 
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_ARCHIVE 
    WHERE ARCHIVE_TYPE = '005'
      AND CERTIFICATE_NO = #{certificateNo,jdbcType = VARCHAR}
  </select>


  <select id="queryTermMachineCode" parameterType="String" resultType="String">
      select t.machine_code from cust_terminal t where t.terminal_code = #{terminalCode,jdbcType = VARCHAR}
  </select>
  
  <select id="queryClientNo" parameterType="String" resultType="String">
      select t.client_no from cust_customer t where t.customer_no = #{customerCode,jdbcType = VARCHAR}
  </select>


  <select id="queryRiskInfo" parameterType="java.util.Map" resultType="com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse">
    select *
      from (
      select rownum rn,r.* from (
        select
               t.customer_no customerNo,
               t.name customerName,
               t.type type,
              case when t.plat_customer_no is null or t.plat_customer_no = '' then t.plat_customer_no
              else t.plat_customer_no || '/' || plat.name
              end as platCustomerNo,
              case when t.service_customer_no is null or t.service_customer_no = '' then t.service_customer_no
              else t.service_customer_no || '/' || service.name
              end as serviceCustomerNo,
               a.RC_LEVEL riskLevel,
               a.BIND_CARD bindCardStatus,
               u.REAL_NAME salesName,
               company.name companyName,
               to_char(a.reg_time,'yyyy-MM-dd hh24:mi:ss') regTime,
               a.RC_STATUS riskControlStatus,
               to_char(e.AUDIT_TIME,'yyyy-MM-dd') lastFrozenTime,
               a.RC_STATUS_REASON lastFrozenReason,
               riskCtrl.REAL_NAME riskCtrlChangeOperatorName,
               case when a.CUS_STATUS = '3' then '注销'
                   else a.ACCOUNT_STATUS
               end as accountStatus,
               to_char(c.AUDIT_TIME,'yyyy-MM-dd') lastAbnormalTime,
               a.ACCOUNT_STATUS_REASON lastAbnormalReason,
               accountChange.REAL_NAME accountChangeOperatorName,
               a1.rcbalance frozenAmount,
               ( nvl(cr.TOTAL_BALANCE, case
      when a.RC_STATUS = 1 or t.STATUS in (2, 4)
      then a1.AVAILABLEBALANCE + a1.FLOATBALANCE + nvl(b1.AVAILABLEBALANCE, 0)
      else (case
      when a1.AVAILABLEBALANCE + a1.FLOATBALANCE &gt;= a1.RCBALANCE then a1.RCBALANCE
      else a1.AVAILABLEBALANCE + a1.FLOATBALANCE end) end)) actualFrozenAmount,
               nvl(a.TEMPORARY_STATUS,'1') temporaryStatus,
               case when a.TEMPORARY_STATUS = '2' then to_char(tl.CREATE_TIME,'yyyy-MM-dd')
                   else ''
               end as temporaryLimitTime,
               case when a.TEMPORARY_STATUS = '2' then a.TEMPORARY_STATUS_REASON
                   else ''
               end as temporaryLimitReason,
               case when a.TEMPORARY_STATUS = '2' then limitChange.REAL_NAME
                   else ''
               end as temporaryLimitOperatorName,
               c.NEW_VALUE accountNewValue,
               e.NEW_VALUE riskNewValue
        from rc_archive a
        inner join cust_customer t on a.ARCHIVE_CODE = t.customer_no
        left join cust_customer_draft plat on t.plat_customer_no = plat.customer_no
        left join cust_customer_draft service on t.service_customer_no = service.customer_no
        left join ACC_ACCOUNT a1 on t.customer_no = a1.CUSTOMERCODE and a1.ACCOUNTTYPEID = 2
          left join ACC_ACCOUNT b1 on t.customer_no = b1.CUSTOMERCODE and b1.ACCOUNTTYPEID = 3
          left join V_ACC_CANCEL_RECORD cr on t.CUSTOMER_NO = cr.CUSTOMER_CODE
        <if test="timeType == 3">
          inner join (
            select r.* from rc_audit_record r
            inner join (
              select TARGET_TYPE,TARGET_ID,max(id) mid from rc_audit_record
              where TARGET_TYPE = 'ACCOUNT_STATUS'
              and NEW_VALUE not like '%"status":"0"%'
              and NEW_VALUE is not null
              and AUDIT_RESULT = 1
              <if test="timeType != null and timeType == 3">
                <![CDATA[ and AUDIT_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
                <![CDATA[ and AUDIT_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
              </if>
              group by TARGET_TYPE,TARGET_ID
            )b on b.mid = id
          )c on a.archive_id = c.TARGET_ID
        </if>
        <if test="timeType != 3">
          left join (
            select r.* from rc_audit_record r
            inner join (
              select TARGET_TYPE,TARGET_ID,max(id) mid from rc_audit_record
              where TARGET_TYPE = 'ACCOUNT_STATUS'
              and NEW_VALUE not like '%"status":"0"%'
              and NEW_VALUE is not null
              and AUDIT_TIME is not null
              and AUDIT_RESULT = 1
              group by TARGET_TYPE,TARGET_ID
            )b on b.mid = id
          )c on a.archive_id = c.TARGET_ID
        </if>
        <if test="timeType == 2">
          inner join (
            select r.* from rc_audit_record r
            inner join (
              select TARGET_TYPE,TARGET_ID,max(id) mid from rc_audit_record
              where TARGET_TYPE = 'RISK_CONTROL_STATUS'
              and NEW_VALUE not like '%"status":"0"%'
              and NEW_VALUE is not null
              and AUDIT_RESULT = 1
              <if test="timeType != null and timeType == 2">
                <![CDATA[ and AUDIT_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
                <![CDATA[ and AUDIT_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
              </if>
              group by TARGET_TYPE,TARGET_ID
            )d on d.mid = id
          )e on a.archive_id = e.TARGET_ID
        </if>
        <if test="timeType != 2">
          left join (
            select r.* from rc_audit_record r
            inner join (
              select TARGET_TYPE,TARGET_ID,max(id) mid from rc_audit_record
              where TARGET_TYPE = 'RISK_CONTROL_STATUS'
              and NEW_VALUE not like '%"status":"0"%'
              and NEW_VALUE is not null
              and AUDIT_TIME is not null
              and AUDIT_RESULT = 1
              group by TARGET_TYPE,TARGET_ID
            )d on d.mid = id
          )e on a.archive_id = e.TARGET_ID
        </if>
        left join pas_company company on company.COMPANY_ID = t.COMPANY_ID
        left join pas_user u on t.BUSINESS_MAN_ID = u.user_id
        left join pas_user riskCtrl on e.OPER_ID = riskCtrl.user_id
        left join pas_user accountChange on c.OPER_ID = accountChange.user_id
        left join (
          select r.* from rc_audit_record r
          inner join (
            select TARGET_TYPE,TARGET_ID,max(id) mid from rc_audit_record
            where TARGET_TYPE = 'TEMPORARY_DEPOSIT_LIMIT'
            group by TARGET_TYPE,TARGET_ID
          )d on d.mid = id
        )tl on a.archive_id = tl.TARGET_ID
        left join pas_user limitChange on tl.OPER_ID = limitChange.user_id
        where 1=1
        <if test="customerNo != null and customerNo != ''">
          <if test="customerType == null or customerType == '' or customerType == 0">
            and t.CUSTOMER_NO = #{customerNo}
          </if>
          <if test="customerType == 2">
            and t.PLAT_CUSTOMER_NO = #{customerNo}
          </if>
          <if test="customerType == 3">
            and t.SERVICE_CUSTOMER_NO = #{customerNo}
          </if>
        </if>
        and a.archive_type = '005'
        <if test="timeType != null and timeType == 1">
          <![CDATA[ and a.REG_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
          <![CDATA[ and a.REG_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
        </if>
      order by ${sort} desc
      )r  where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>


  <!-- 待验证后打开 -->
  <!--
  <select id="queryRiskInfo" parameterType="java.util.Map" resultType="com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse">
    select *
    from (
      select rownum rn,r.* from (
        WITH AUDIT_RECORD_DATA AS (
          SELECT * FROM (
            SELECT r.*, ROW_NUMBER() OVER (PARTITION BY TARGET_TYPE, TARGET_ID ORDER BY id DESC) as rn
            FROM rc_audit_record r
            WHERE
            (
                TARGET_TYPE = 'ACCOUNT_STATUS'
                AND AUDIT_RESULT = 1
                <if test="timeType != null and timeType == 3">
                  <![CDATA[ AND AUDIT_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
                  <![CDATA[ AND AUDIT_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
                </if>
                AND NEW_VALUE NOT LIKE '%"status":"0"%'
            )
            OR
            (
                TARGET_TYPE = 'RISK_CONTROL_STATUS'
                AND AUDIT_RESULT = 1
                <if test="timeType != null and timeType == 2">
                <![CDATA[ AND AUDIT_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
                <![CDATA[ AND AUDIT_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
                </if>
                AND NEW_VALUE NOT LIKE '%"status":"0"%'
            )
            OR
            (TARGET_TYPE = 'TEMPORARY_DEPOSIT_LIMIT')
          )
          WHERE rn = 1
        )
        select
          t.customer_no customerNo,
          t.name customerName,
          t.type type,
          case
              when t.plat_customer_no is null or t.plat_customer_no = '' then t.plat_customer_no
          else t.plat_customer_no || '/' || plat.name
          end as platCustomerNo,
          case
              when t.service_customer_no is null or t.service_customer_no = '' then t.service_customer_no
          else t.service_customer_no || '/' || service.name
          end as serviceCustomerNo,
          a.RC_LEVEL riskLevel,
          a.BIND_CARD bindCardStatus,
          u.REAL_NAME salesName,
          company.name companyName,
          to_char(a.reg_time,'yyyy-MM-dd hh24:mi:ss') regTime,
          a.RC_STATUS riskControlStatus,
          to_char(e.AUDIT_TIME,'yyyy-MM-dd') lastFrozenTime,
          a.RC_STATUS_REASON lastFrozenReason,
          riskCtrl.REAL_NAME riskCtrlChangeOperatorName,
          case
              when a.CUS_STATUS = '3' then '注销'
          else a.ACCOUNT_STATUS
          end as accountStatus,
          to_char(c.AUDIT_TIME,'yyyy-MM-dd') lastAbnormalTime,
          a.ACCOUNT_STATUS_REASON lastAbnormalReason,
          accountChange.REAL_NAME accountChangeOperatorName,
          a1.rcbalance frozenAmount,
          (
            nvl(
                cr.TOTAL_BALANCE,
                case
                  when a.RC_STATUS = 1 or t.STATUS in (2, 4) then a1.AVAILABLEBALANCE + a1.FLOATBALANCE + nvl(b1.AVAILABLEBALANCE, 0)
                else (
                    case
                        when a1.AVAILABLEBALANCE + a1.FLOATBALANCE &gt;= a1.RCBALANCE then a1.RCBALANCE
                    else a1.AVAILABLEBALANCE + a1.FLOATBALANCE
                    end
                )
                end
            )
          ) actualFrozenAmount,
          nvl(a.TEMPORARY_STATUS,'1') temporaryStatus,
          case
              when a.TEMPORARY_STATUS = '2' then to_char(tl.CREATE_TIME,'yyyy-MM-dd')
          else ''
          end as temporaryLimitTime,
          case
              when a.TEMPORARY_STATUS = '2' then a.TEMPORARY_STATUS_REASON
          else ''
          end as temporaryLimitReason,
          case
              when a.TEMPORARY_STATUS = '2' then limitChange.REAL_NAME
          else ''
          end as temporaryLimitOperatorName,
          c.NEW_VALUE accountNewValue,
          e.NEW_VALUE riskNewValue
        from rc_archive a
        inner join cust_customer t on a.ARCHIVE_CODE = t.customer_no
        left join cust_customer_draft plat on t.plat_customer_no = plat.customer_no
        left join cust_customer_draft service on t.service_customer_no = service.customer_no
        left join ACC_ACCOUNT a1 on t.customer_no = a1.CUSTOMERCODE and a1.ACCOUNTTYPEID = 2
        left join ACC_ACCOUNT b1 on t.customer_no = b1.CUSTOMERCODE and b1.ACCOUNTTYPEID = 3
        left join V_ACC_CANCEL_RECORD cr on t.CUSTOMER_NO = cr.CUSTOMER_CODE
        <if test="timeType == 3">
          INNER JOIN AUDIT_RECORD_DATA c ON a.archive_id = c.TARGET_ID AND c.TARGET_TYPE = 'ACCOUNT_STATUS'
        </if>
        <if test="timeType != 3">
          LEFT JOIN AUDIT_RECORD_DATA c ON a.archive_id = c.TARGET_ID AND c.TARGET_TYPE = 'ACCOUNT_STATUS'
        </if>
        <if test="timeType == 2">
          INNER JOIN AUDIT_RECORD_DATA e ON a.archive_id = e.TARGET_ID AND e.TARGET_TYPE = 'RISK_CONTROL_STATUS'
        </if>
        <if test="timeType != 2">
          LEFT JOIN AUDIT_RECORD_DATA e ON a.archive_id = e.TARGET_ID AND e.TARGET_TYPE = 'RISK_CONTROL_STATUS'
        </if>
        left join pas_company company on company.COMPANY_ID = t.COMPANY_ID
        left join pas_user u on t.BUSINESS_MAN_ID = u.user_id
        left join pas_user riskCtrl on e.OPER_ID = riskCtrl.user_id
        left join pas_user accountChange on c.OPER_ID = accountChange.user_id
        LEFT JOIN AUDIT_RECORD_DATA tl ON a.archive_id = tl.TARGET_ID AND tl.TARGET_TYPE = 'TEMPORARY_DEPOSIT_LIMIT' AND tl.rn = 1
        left join pas_user limitChange on tl.OPER_ID = limitChange.user_id
        where 1=1
        <if test="customerNo != null and customerNo != ''">
          <if test="customerType == null or customerType == '' or customerType == 0">
            and t.CUSTOMER_NO = #{customerNo}
          </if>
          <if test="customerType == 2">
            and t.PLAT_CUSTOMER_NO = #{customerNo}
          </if>
          <if test="customerType == 3">
            and t.SERVICE_CUSTOMER_NO = #{customerNo}
          </if>
        </if>
        and a.archive_type = '005'
        <if test="timeType != null and timeType == 1">
          <![CDATA[ and a.REG_TIME >= TO_DATE(#{startTime},'yyyyMMddhh24miss')]]>
          <![CDATA[ and a.REG_TIME <= TO_DATE(#{endTime},'yyyyMMddhh24miss')]]>
        </if>
        order by ${sort} desc
      )r  where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>
  -->

  <select id="exportWebsiteEffectiveness" parameterType="java.util.List" resultType="com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo">
    select rownum, h.*
    from (
           select c.customer_no         customerNo,
                  c.name                customerName,
                  c.plat_customer_no    platCustomerNo,
                  p.name                platCustomerName,
                  c.service_customer_no serviceCustomerNo,
                  s.name                serviceCustomerName,
                  decode(c.category,'0','普通商户','2','平台商户','3','代理商','4','个人','5','结算商户') category,
                  u.real_name           businessMan,
                  company.name          companyName,
                  e.site_url            siteUrl,
                  a.temporary_status    temporaryStatus,
                  c.customer_id         customerId
           from rc_archive a
                  inner join cust_customer c on a.archive_code = c.customer_no
                  left join cust_customer p on c.plat_customer_no = p.customer_no
                  left join cust_customer s on c.service_customer_no = s.customer_no
                  left join pas_user u on c.business_man_id = u.user_id
                  left join pas_company company on c.company_id = company.company_id
                  inner join cust_customer_enterprise e
                             on c.customer_id = e.customer_id and e.site_url is not null
           where a.archive_type = '005'
             and a.cus_status = '1'
             and a.rc_status = '0'
             and a.account_status = '0'
             and not exists(
                   select 1
                   from rc_bw_list b
                   where b.business_tager_type = '005'
                     and b.bw_type = '0'
                     and b.business_tager_id = a.archive_code
             )
             <if test="list != null">
               and c.customer_no in
               <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                 <if test="(index % 999) == 998">
                   NULL) OR c.customer_no IN(
                 </if>
                 #{item}
               </foreach>
             </if>
            -- 业务角色为以下的商户:标准商户or平台型商户or分账型商户
            and c.business_role in ('100','300','400')
            -- 任一入金业务（含ACS充值、订单转账）审核通过and启用
            and exists (
              select 1
              from cust_business cb
              where cb.customer_id = c.customer_id
              and cb.business_code in (
                  select pb.code
                  from pas_business pb
                  where pb.business_group in (
                    '1000007','1000008','1000025','1000009','1000010','1000011','1000012','1000005','1000017','1000018','1000019','1000020','1000021','1000023','1000022'
                    )
                )
              and cb.state = 1
              and (cb.audit_status is null or cb.audit_status = '4')
            )
           order by a.archive_id desc
         )h
  </select>

  <select id="exportWebByImport" parameterType="java.util.List" resultType="com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo">
    select rownum, h.*
    from (
      select c.customer_no         customerNo,
      c.name                customerName,
      c.plat_customer_no    platCustomerNo,
      p.name                platCustomerName,
      c.service_customer_no serviceCustomerNo,
      s.name                serviceCustomerName,
      decode(c.category,'0','普通商户','2','平台商户','3','代理商','4','个人','5','结算商户') category,
      u.real_name           businessMan,
      company.name          companyName,
      e.site_url            siteUrl
      from cust_customer c
      left join cust_customer p on c.plat_customer_no = p.customer_no
      left join cust_customer s on c.service_customer_no = s.customer_no
      left join pas_user u on c.business_man_id = u.user_id
      left join pas_company company on c.company_id = company.company_id
      left join cust_customer_enterprise e on c.customer_id = e.customer_id
      where 1=1
      <if test="list != null">
        and c.customer_no in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
          <if test="(index % 999) == 998">
            NULL) OR c.customer_no IN(
          </if>
          #{item}
        </foreach>
      </if>
      <if test="list != null">
        order by decode(c.customer_no,
        <foreach collection="list" item="item" index="index" open="" close=")" separator=",">
          #{item},#{index}
        </foreach>
      </if>
    )h
  </select>
  
  <select id="queryCustomerStateInfo" parameterType="java.lang.String" resultType="com.epaylinks.efps.rc.vo.CustomerStateInfoVo">
    select
        c.CUSTOMER_NO customerNo,
        c.STATUS cusState,
        c.RC_STATUS rcState,
        e.site_url url,
        a.ARCHIVE_ID archiveId
    from cust_customer c
    left join rc_archive a on a.archive_code = c.customer_no
    left join cust_customer_enterprise e on c.customer_id = e.customer_id
    where c.customer_no = #{customerNo}
  </select>

  <update id="updatePaySetting" parameterType="java.util.Map">
    update rc_archive
    set PAY_SETTING = #{paySetting,jdbcType=VARCHAR},
        PAY_SETTING_REASON = #{paySettingReason,jdbcType=VARCHAR},
        PAY_SETTING_UNIQUE_ID = #{paySettingUniqueId,jdbcType=VARCHAR}
    where ARCHIVE_ID = #{archiveId,jdbcType=DECIMAL}
  </update>

</mapper>
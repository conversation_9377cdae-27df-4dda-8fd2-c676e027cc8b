<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcDroolsSceneThenMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDroolsSceneThen">
    <!--@mbg.generated-->
    <!--@Table RC_DROOLS_SCENE_THEN-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="SCENE_TEXT" jdbcType="VARCHAR" property="sceneText" />
    <result column="THEN_TEXT" jdbcType="VARCHAR" property="thenText" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCENE_TEXT, THEN_TEXT, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_DROOLS_SCENE_THEN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from RC_DROOLS_SCENE_THEN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneThen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_SCENE_THEN (ID, SCENE_TEXT, THEN_TEXT, 
      CREATE_TIME, UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{sceneText,jdbcType=VARCHAR}, #{thenText,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneThen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_SCENE_THEN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="sceneText != null">
        SCENE_TEXT,
      </if>
      <if test="thenText != null">
        THEN_TEXT,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="sceneText != null">
        #{sceneText,jdbcType=VARCHAR},
      </if>
      <if test="thenText != null">
        #{thenText,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneThen">
    <!--@mbg.generated-->
    update RC_DROOLS_SCENE_THEN
    <set>
      <if test="sceneText != null">
        SCENE_TEXT = #{sceneText,jdbcType=VARCHAR},
      </if>
      <if test="thenText != null">
        THEN_TEXT = #{thenText,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneThen">
    <!--@mbg.generated-->
    update RC_DROOLS_SCENE_THEN
    set SCENE_TEXT = #{sceneText,jdbcType=VARCHAR},
      THEN_TEXT = #{thenText,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <select id="selectAllOrderById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from RC_DROOLS_SCENE_THEN order by ID asc
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-22-->
  <select id="selectAllBySceneText" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RC_DROOLS_SCENE_THEN
        where SCENE_TEXT=#{sceneText,jdbcType=VARCHAR}
    </select>
</mapper>
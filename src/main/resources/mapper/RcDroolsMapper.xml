<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcDroolsMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDrools">
    <!--@mbg.generated-->
    <!--@Table RC_DROOLS-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="DRL_CONTENT" jdbcType="BLOB" property="drlContent" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DRL_CONTENT, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_DROOLS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from RC_DROOLS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDrools">
    <!--@mbg.generated-->
    insert into RC_DROOLS (ID, DRL_CONTENT, CREATE_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{drlContent,jdbcType=BLOB}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDrools">
    <!--@mbg.generated-->
    insert into RC_DROOLS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="drlContent != null">
        DRL_CONTENT,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="drlContent != null">
        #{drlContent,jdbcType=BLOB},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDrools">
    <!--@mbg.generated-->
    update RC_DROOLS
    <set>
      <if test="drlContent != null">
        DRL_CONTENT = #{drlContent,jdbcType=BLOB},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDrools">
    <!--@mbg.generated-->
    update RC_DROOLS
    set DRL_CONTENT = #{drlContent,jdbcType=BLOB},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
  <select id="selectAllOrderById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from RC_DROOLS order by ID asc
  </select>
</mapper>
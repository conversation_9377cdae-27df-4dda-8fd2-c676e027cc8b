<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.CommonSysParamMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.CommonSysParam">
    <!--@mbg.generated-->
    <!--@Table COMMON_SYS_PARAM-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OWNER" jdbcType="VARCHAR" property="owner" />
    <result column="KEY" jdbcType="VARCHAR" property="key" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="VALUE" jdbcType="VARCHAR" property="value" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "OWNER", "KEY", "TYPE", "VALUE"
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from COMMON_SYS_PARAM
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from COMMON_SYS_PARAM
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.CommonSysParam">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
      select SEQ_COMMON_SYS_PARAM_ID.nextval from dual
    </selectKey>
    insert into COMMON_SYS_PARAM (ID, "OWNER", "KEY", 
      "TYPE", "VALUE")
    values (#{id,jdbcType=DECIMAL}, #{owner,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.CommonSysParam">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
      select SEQ_COMMON_SYS_PARAM_ID.nextval from dual
    </selectKey>
    insert into COMMON_SYS_PARAM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="owner != null">
        "OWNER",
      </if>
      <if test="key != null">
        "KEY",
      </if>
      <if test="type != null">
        "TYPE",
      </if>
      <if test="value != null">
        "VALUE",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.CommonSysParam">
    <!--@mbg.generated-->
    update COMMON_SYS_PARAM
    <set>
      <if test="owner != null">
        "OWNER" = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        "KEY" = #{key,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        "TYPE" = #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        "VALUE" = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.CommonSysParam">
    <!--@mbg.generated-->
    update COMMON_SYS_PARAM
    set "OWNER" = #{owner,jdbcType=VARCHAR},
      "KEY" = #{key,jdbcType=VARCHAR},
      "TYPE" = #{type,jdbcType=VARCHAR},
      "VALUE" = #{value,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-10-11-->
  <select id="selectOneByOwnerAndKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from COMMON_SYS_PARAM
    where "OWNER"=#{owner,jdbcType=VARCHAR} and "KEY"=#{key,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-10-11-->
  <select id="selectByOwner" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from COMMON_SYS_PARAM
    where "OWNER"=#{owner,jdbcType=VARCHAR}
  </select>
</mapper>
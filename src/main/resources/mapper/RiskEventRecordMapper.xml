<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RiskEventRecordMapper" >
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    <id column="EVENT_ID" property="eventId" jdbcType="DECIMAL" />
    <result column="RULE_CODE" property="ruleCode" jdbcType="VARCHAR" />
    <result column="TRIGGER_TIME" property="triggerTime" jdbcType="TIMESTAMP" />
    <result column="RULE_TYPE" property="ruleType" jdbcType="VARCHAR" />
    <result column="RULE_DESC" property="ruleDesc" jdbcType="VARCHAR" />
    <result column="TRIGGER_CUST_CODE" property="triggerCustCode" jdbcType="VARCHAR" />
    <result column="TRIGGER_CUST_NAME" property="triggerCustName" jdbcType="VARCHAR" />
    <result column="TRIGGER_VALUE" property="triggerValue" jdbcType="VARCHAR" />
    <result column="TRIGGER_ACTION" property="triggerAction" jdbcType="VARCHAR" />
    <result column="PARAM_TYPE" property="paramType" jdbcType="VARCHAR" />
    <result column="DEAL_RESULT" property="dealResult" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    EVENT_ID, RULE_CODE, TRIGGER_TIME, RULE_TYPE, RULE_DESC, TRIGGER_CUST_CODE, TRIGGER_CUST_NAME, 
    TRIGGER_VALUE, TRIGGER_ACTION, PARAM_TYPE, DEAL_RESULT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_RISK_EVENT_RECORD
    where EVENT_ID = #{eventId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_RISK_EVENT_RECORD
    where EVENT_ID = #{eventId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    insert into RC_RISK_EVENT_RECORD (EVENT_ID, RULE_CODE, TRIGGER_TIME, 
      RULE_TYPE, RULE_DESC, TRIGGER_CUST_CODE, 
      TRIGGER_CUST_NAME, TRIGGER_VALUE, TRIGGER_ACTION, 
      PARAM_TYPE, DEAL_RESULT)
    values (#{eventId,jdbcType=DECIMAL}, #{ruleCode,jdbcType=VARCHAR}, #{triggerTime,jdbcType=TIMESTAMP}, 
      #{ruleType,jdbcType=VARCHAR}, #{ruleDesc,jdbcType=VARCHAR}, #{triggerCustCode,jdbcType=VARCHAR}, 
      #{triggerCustName,jdbcType=VARCHAR}, #{triggerValue,jdbcType=VARCHAR}, #{triggerAction,jdbcType=VARCHAR}, 
      #{paramType,jdbcType=VARCHAR}, #{dealResult,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    insert into RC_RISK_EVENT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eventId != null" >
        EVENT_ID,
      </if>
      <if test="ruleCode != null" >
        RULE_CODE,
      </if>
      <if test="triggerTime != null" >
        TRIGGER_TIME,
      </if>
      <if test="ruleType != null" >
        RULE_TYPE,
      </if>
      <if test="ruleDesc != null" >
        RULE_DESC,
      </if>
      <if test="triggerCustCode != null" >
        TRIGGER_CUST_CODE,
      </if>
      <if test="triggerCustName != null" >
        TRIGGER_CUST_NAME,
      </if>
      <if test="triggerValue != null" >
        TRIGGER_VALUE,
      </if>
      <if test="triggerAction != null" >
        TRIGGER_ACTION,
      </if>
      <if test="paramType != null" >
        PARAM_TYPE,
      </if>
      <if test="dealResult != null" >
        DEAL_RESULT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eventId != null" >
        #{eventId,jdbcType=DECIMAL},
      </if>
      <if test="ruleCode != null" >
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="triggerTime != null" >
        #{triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleType != null" >
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null" >
        #{ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="triggerCustCode != null" >
        #{triggerCustCode,jdbcType=VARCHAR},
      </if>
      <if test="triggerCustName != null" >
        #{triggerCustName,jdbcType=VARCHAR},
      </if>
      <if test="triggerValue != null" >
        #{triggerValue,jdbcType=VARCHAR},
      </if>
      <if test="triggerAction != null" >
        #{triggerAction,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null" >
        #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null" >
        #{dealResult,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    update RC_RISK_EVENT_RECORD
    <set >
      <if test="ruleCode != null" >
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="triggerTime != null" >
        TRIGGER_TIME = #{triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleType != null" >
        RULE_TYPE = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null" >
        RULE_DESC = #{ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="triggerCustCode != null" >
        TRIGGER_CUST_CODE = #{triggerCustCode,jdbcType=VARCHAR},
      </if>
      <if test="triggerCustName != null" >
        TRIGGER_CUST_NAME = #{triggerCustName,jdbcType=VARCHAR},
      </if>
      <if test="triggerValue != null" >
        TRIGGER_VALUE = #{triggerValue,jdbcType=VARCHAR},
      </if>
      <if test="triggerAction != null" >
        TRIGGER_ACTION = #{triggerAction,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null" >
        PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null" >
        DEAL_RESULT = #{dealResult,jdbcType=VARCHAR},
      </if>
    </set>
    where EVENT_ID = #{eventId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    update RC_RISK_EVENT_RECORD
    set RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      TRIGGER_TIME = #{triggerTime,jdbcType=TIMESTAMP},
      RULE_TYPE = #{ruleType,jdbcType=VARCHAR},
      RULE_DESC = #{ruleDesc,jdbcType=VARCHAR},
      TRIGGER_CUST_CODE = #{triggerCustCode,jdbcType=VARCHAR},
      TRIGGER_CUST_NAME = #{triggerCustName,jdbcType=VARCHAR},
      TRIGGER_VALUE = #{triggerValue,jdbcType=VARCHAR},
      TRIGGER_ACTION = #{triggerAction,jdbcType=VARCHAR},
      PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      DEAL_RESULT = #{dealResult,jdbcType=VARCHAR}
    where EVENT_ID = #{eventId,jdbcType=DECIMAL}
  </update>
  
  <!-- 获取主键ID -->
  <select id="selectIdFromSeq" resultType="java.lang.Long" >
    select SEQ_RC_RISK_EVENT_RECORD.nextval from dual
  </select>
  <!-- 新增可疑事件监控记录 -->
  <insert id="addEventRecord" parameterType="com.epaylinks.efps.rc.domain.RiskEventRecord" >
    insert into RC_RISK_EVENT_RECORD ( EVENT_ID, RULE_CODE, TRIGGER_TIME, RULE_TYPE, RULE_DESC, TRIGGER_CUST_CODE,
    TRIGGER_CUST_NAME, TRIGGER_VALUE, TRIGGER_ACTION, PARAM_TYPE )
    values (#{eventId,jdbcType=DECIMAL}, #{ruleCode,jdbcType=VARCHAR}, #{triggerTime,jdbcType=TIMESTAMP},
      #{ruleType,jdbcType=VARCHAR}, #{ruleDesc,jdbcType=VARCHAR}, #{triggerCustCode,jdbcType=VARCHAR},
      #{triggerCustName,jdbcType=VARCHAR}, #{triggerValue,jdbcType=VARCHAR}, #{triggerAction,jdbcType=VARCHAR},
      #{paramType,jdbcType=VARCHAR}
      )
  </insert>

  <sql id="pageEventRecordConditions">
    <if test="ruleType != null and ruleType != ''">
      and RULE_TYPE = #{ruleType,jdbcType = VARCHAR}
    </if>
    <if test="triggerAction != null and triggerAction != ''">
      and TRIGGER_ACTION = #{triggerAction,jdbcType = VARCHAR}
    </if>
    <if test="searchKey != null and searchKey != ''">
      and (
      TRIGGER_CUST_NAME LIKE '%'|| #{searchKey,jdbcType=VARCHAR} || '%'
      OR TRIGGER_CUST_CODE LIKE '%'|| #{searchKey,jdbcType=VARCHAR} || '%'
      )
    </if>
    <if test="startTime != null and startTime != ''">
      and TRIGGER_TIME <![CDATA[ >= ]]> to_date(#{startTime},'yyyyMMddhh24miss')
    </if>
    <if test="endTime != null and endTime != ''">
      and TRIGGER_TIME <![CDATA[ < ]]> to_date(#{endTime},'yyyyMMddhh24miss') + 1 / 86400
    </if>
    <if test="userCompanyId != null">
      AND t.TRIGGER_CUST_CODE in (
        select c.customer_no from cust_customer c where c.company_id in (
          select company_id from pas_company
          start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
          connect by parent_id = prior company_id
        )
      )
    </if>
    <if test="businessManId != null">
      AND t.TRIGGER_CUST_CODE in (
        select c.customer_no from cust_customer c where c.business_man_id = #{businessManId,jdbcType=DECIMAL}
      )
    </if>
    <if test="companyIds != null">
      and exists (
        select 1 from cust_customer_draft cd
        where t.TRIGGER_CUST_CODE = cd.CUSTOMER_NO
        and cd.company_id in
        <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      )
    </if>
  </sql>

  <!-- 分页统计可疑事件监控记录数 -->
  <select id="pageCountEventRecord" resultType="int">
   SELECT count (*) FROM RC_RISK_EVENT_RECORD t
    <where>
      <include refid="pageEventRecordConditions" />
    </where>
  </select>
  <!-- 分页查询可疑事件监控记录 -->
  <select id="pageQueryEventRecord" resultMap="BaseResultMap">
  SELECT * FROM (
  SELECT a.*,rownum rn FROM (
  SELECT
  <include refid="Base_Column_List" />
  FROM RC_RISK_EVENT_RECORD t
    <where>
      <include refid="pageEventRecordConditions" />
    </where>
    ORDER BY TRIGGER_TIME DESC
  )a WHERE rownum <![CDATA[ <= ]]> #{endNum}
  ) WHERE rn <![CDATA[ >= ]]> #{startNum}
  </select>


  <!-- 可疑事件汇总 -->
  <resultMap id="SummaryResultMap" type="com.epaylinks.efps.rc.domain.RiskEventSummary" >
    <result column="TRIGGER_DAY" property="triggerDay" jdbcType="VARCHAR" />
    <result column="RULE_CODE" property="ruleCode" jdbcType="VARCHAR" />
    <result column="RULE_TYPE" property="ruleType" jdbcType="VARCHAR" />
    <result column="RULE_DESC" property="ruleDesc" jdbcType="VARCHAR" />
    <result column="TRIGGER_CUST_CODE" property="triggerCustCode" jdbcType="VARCHAR" />
    <result column="TRIGGER_CUST_NAME" property="triggerCustName" jdbcType="VARCHAR" />
    <result column="DAY_COUNT" property="dayCount" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Summary_Column_List" >
    TRIGGER_DAY, RULE_CODE, RULE_TYPE, RULE_DESC, TRIGGER_CUST_CODE, TRIGGER_CUST_NAME, DAY_COUNT
  </sql>

  <sql id="pageConditions" >
    <if test="ruleCode != null and ruleCode != ''">
      and RULE_CODE = #{ruleCode,jdbcType = VARCHAR}
    </if>
    <if test="ruleType != null and ruleType != ''">
      and RULE_TYPE = #{ruleType,jdbcType = VARCHAR}
    </if>
    <if test="searchKey != null and searchKey != ''">
      and TRIGGER_CUST_CODE = #{searchKey,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null and startTime != ''">
      and TRIGGER_TIME <![CDATA[ >= ]]> to_date(#{startTime},'yyyyMMddhh24miss')
    </if>
    <if test="endTime != null and endTime != ''">
      and TRIGGER_TIME <![CDATA[ <= ]]> to_date(#{endTime},'yyyyMMddhh24miss')
    </if>
    <if test="userCompanyId != null">
      AND t.TRIGGER_CUST_CODE in (
        select c.customer_no from cust_customer c where c.company_id in (
          select company_id from pas_company
          start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
          connect by parent_id = prior company_id
        )
      )
    </if>
    <if test="businessManId != null">
      AND t.TRIGGER_CUST_CODE in (
        select c.customer_no from cust_customer c where c.business_man_id = #{businessManId,jdbcType=DECIMAL}
      )
    </if>
    <if test="companyIds != null">
      and exists (
        select 1 from cust_customer_draft cd
        where t.TRIGGER_CUST_CODE = cd.CUSTOMER_NO
        and cd.company_id in
        <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      )
    </if>
  </sql>

  <select id="countEventSummary" resultType="int"  parameterType="java.util.Map">
    select count(*) from(
      SELECT
      t.trigger_cust_code
      FROM RC_RISK_EVENT_RECORD t
      <where>
        <include refid="pageConditions" />
      </where>
        GROUP BY t.rule_code, to_char(t.trigger_time, 'yyyy-MM-dd'), t.rule_type ,t.trigger_cust_code, t.trigger_cust_name, t.rule_desc
    )
  </select>
  <select id="pageEventSummary" resultMap="SummaryResultMap" parameterType="java.util.Map">
    SELECT * FROM (
      SELECT a.*,rownum rn FROM (
      SELECT
        t.rule_code, to_char(t.trigger_time, 'yyyy-MM-dd') trigger_day, t.rule_type, 
        t.trigger_cust_code, t.trigger_cust_name, t.rule_desc, count(1) day_count
      FROM RC_RISK_EVENT_RECORD t 
        <where>
          <include refid="pageConditions" />
        </where>
        GROUP BY t.rule_code, to_char(t.trigger_time, 'yyyy-MM-dd'), t.rule_type ,t.trigger_cust_code, t.trigger_cust_name, t.rule_desc
        ORDER BY to_char(t.trigger_time, 'yyyy-MM-dd') DESC
      )a WHERE rownum <![CDATA[ <= ]]> #{endNum}
    ) WHERE rn <![CDATA[ >= ]]> #{startNum}
  </select>


</mapper>
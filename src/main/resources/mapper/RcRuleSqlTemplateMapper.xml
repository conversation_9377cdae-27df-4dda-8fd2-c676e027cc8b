<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcRuleSqlTemplateMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcRuleSqlTemplate">
    <!--@mbg.generated-->
    <!--@Table RC_RULE_SQL_TEMPLATE-->
    <id column="SCENE" jdbcType="VARCHAR" property="scene" />
    <result column="SQL_TEMPLATE" jdbcType="VARCHAR" property="sqlTemplate" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SCENE, SQL_TEMPLATE, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_RULE_SQL_TEMPLATE
    where SCENE = #{scene,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from RC_RULE_SQL_TEMPLATE
    where SCENE = #{scene,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcRuleSqlTemplate">
    <!--@mbg.generated-->
    insert into RC_RULE_SQL_TEMPLATE (SCENE, SQL_TEMPLATE, CREATE_TIME, 
      UPDATE_TIME)
    values (#{scene,jdbcType=VARCHAR}, #{sqlTemplate,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcRuleSqlTemplate">
    <!--@mbg.generated-->
    insert into RC_RULE_SQL_TEMPLATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        SCENE,
      </if>
      <if test="sqlTemplate != null">
        SQL_TEMPLATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="sqlTemplate != null">
        #{sqlTemplate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcRuleSqlTemplate">
    <!--@mbg.generated-->
    update RC_RULE_SQL_TEMPLATE
    <set>
      <if test="sqlTemplate != null">
        SQL_TEMPLATE = #{sqlTemplate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SCENE = #{scene,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcRuleSqlTemplate">
    <!--@mbg.generated-->
    update RC_RULE_SQL_TEMPLATE
    set SQL_TEMPLATE = #{sqlTemplate,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where SCENE = #{scene,jdbcType=VARCHAR}
  </update>
</mapper>
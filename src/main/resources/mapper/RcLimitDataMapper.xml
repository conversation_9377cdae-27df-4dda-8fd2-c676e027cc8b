<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcLimitDataMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcLimitData">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="BUSINESSTARGETID" jdbcType="VARCHAR" property="businesstargetid" />
    <result column="DEFINECODE" jdbcType="VARCHAR" property="definecode" />
    <result column="VALUE" jdbcType="VARCHAR" property="value" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="STATUS" jdbcType="VARCHAR" property="status"/>
    <result column="DATETIME" jdbcType="VARCHAR" property="datetime"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, BUSINESSTARGETID, DEFINECODE, VALUE, CREATETIME, UPDATETIME , STATUS, DATETIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from RC_LIMIT_DATA
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from RC_LIMIT_DATA
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcLimitData">
    insert into RC_LIMIT_DATA (ID, BUSINESSTARGETID, DEFINECODE, 
      VALUE, CREATETIME, UPDATETIME , STATUS, DATETIME
      )
    values (#{id,jdbcType=DECIMAL}, #{businesstargetid,jdbcType=VARCHAR}, #{definecode,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR}, #{createtime,jdbcType=TIMESTAMP}, #{updatetime,jdbcType=TIMESTAMP} , 
      #{status , jdbcType = VARCHAR},  #{datetime , jdbcType = VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitData">
    insert into RC_LIMIT_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="businesstargetid != null">
        BUSINESSTARGETID,
      </if>
      <if test="definecode != null">
        DEFINECODE,
      </if>
      <if test="value != null">
        VALUE,
      </if>
      <if test="createtime != null">
        CREATETIME,
      </if>
      <if test="updatetime != null">
        UPDATETIME,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="datetime != null">
        DATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="businesstargetid != null">
        #{businesstargetid,jdbcType=VARCHAR},
      </if>
      <if test="definecode != null">
        #{definecode,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="datetime != null">
        #{datetime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitData">
    update RC_LIMIT_DATA
    <set>
      <if test="businesstargetid != null">
        BUSINESSTARGETID = #{businesstargetid,jdbcType=VARCHAR},
      </if>
      <if test="definecode != null">
        DEFINECODE = #{definecode,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        VALUE = #{value,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="datetime != null">
        STATUS = #{datetime,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcLimitData">
    update RC_LIMIT_DATA
    set BUSINESSTARGETID = #{businesstargetid,jdbcType=VARCHAR},
      DEFINECODE = #{definecode,jdbcType=VARCHAR},
      VALUE = #{value,jdbcType=VARCHAR},
      CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
      STATUS = #{status , jdbcType = VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus" resultMap="BaseResultMap">
  	select *from (
	  	select 
	  	<include refid="Base_Column_List" />
	    from RC_LIMIT_DATA 
	    where DEFINECODE = #{defineCode , jdbcType = VARCHAR} 
	    and BUSINESSTARGETID = #{businessTargetId , jdbcType = VARCHAR} 
	    and DATETIME =  #{datetime , jdbcType = VARCHAR}
	    and STATUS = #{status , jdbcType = VARCHAR}
	    ORDER BY updatetime desc
    ) where rownum = 1
  </select>
  
  
   <select id="totalInitRedis" parameterType="String" resultType="int">
       select count(*) from (
          select t.id  from rc_limit_data t where t.datetime = to_char(sysdate, 'yyyymm') and t.status = 'processing'
          union all
          select t.id  from rc_limit_data t where t.datetime = to_char(sysdate, 'yyyy') and t.status = 'processing'
        )
   </select>
   
  
    <select id="pageInitRedisData"  resultMap="BaseResultMap"><!-- 初始化风控指标，分页顺序排序处理 -->
        SELECT * FROM (
            SELECT a.* ,rownum rn FROM (
                SELECT
                <include refid="Base_Column_List" />
                from (
                    select  <include refid="Base_Column_List" />  from rc_limit_data t
                     where t.datetime = to_char(sysdate, 'yyyymm') and t.status = 'processing'
                    union all
                    select  <include refid="Base_Column_List" /> from rc_limit_data t
                      where t.datetime = to_char(sysdate, 'yyyy') and t.status = 'processing'
                ) a
                ORDER BY a.id ASC
            )a WHERE rownum &lt;= #{endNum}
        )WHERE rn &gt;= #{startNum}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-11-16-->
  <select id="selectOneByBusinesstargetidAndDefinecodeAndDatetime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from RC_LIMIT_DATA
    <where>
      BUSINESSTARGETID=#{businesstargetid,jdbcType=VARCHAR} and DEFINECODE=#{definecode,jdbcType=VARCHAR}
      <if test="datetime != null">
        and "DATETIME"=#{datetime,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.TemporaryLimitMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.TemporaryLimit" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="LIMIT_TYPE" property="limitType" jdbcType="VARCHAR" />
    <result column="LIMIT_VALUE" property="limitValue" jdbcType="DECIMAL" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="ENABLE" property="enable" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TYPE, CODE, LIMIT_TYPE, LIMIT_VALUE, UNIT, CREATE_TIME, UPDATE_TIME,ENABLE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_TEMPORARY_LIMIT
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_TEMPORARY_LIMIT
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.TemporaryLimit" >
    insert into RC_TEMPORARY_LIMIT (ID, TYPE, CODE, 
      LIMIT_TYPE, LIMIT_VALUE, UNIT, 
      CREATE_TIME, UPDATE_TIME,ENABLE)
    values (#{id,jdbcType=DECIMAL}, #{type,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{limitType,jdbcType=VARCHAR}, #{limitValue,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{enable,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.TemporaryLimit" >
    insert into RC_TEMPORARY_LIMIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="code != null" >
        CODE,
      </if>
      <if test="limitType != null" >
        LIMIT_TYPE,
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="enable != null">
        ENABLE
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="limitType != null" >
        #{limitType,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        #{limitValue,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.TemporaryLimit" >
    update RC_TEMPORARY_LIMIT
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="limitType != null" >
        LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE = #{limitValue,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null" >
        ENABLE = #{enable,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.TemporaryLimit" >
    update RC_TEMPORARY_LIMIT
    set TYPE = #{type,jdbcType=VARCHAR},
      CODE = #{code,jdbcType=VARCHAR},
      LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
      LIMIT_VALUE = #{limitValue,jdbcType=DECIMAL},
      UNIT = #{unit,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ENABLE = #{enable,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="queryByCodeAndType" parameterType="java.util.Map" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from RC_TEMPORARY_LIMIT
    where TYPE = #{type,jdbcType=VARCHAR}
    and CODE = #{code,jdbcType=VARCHAR}
    and LIMIT_TYPE = #{limitType,jdbcType=VARCHAR}
    <if test="enable != null">
      and ENABLE = #{enable,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryListByCode" parameterType="java.util.Map" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from RC_TEMPORARY_LIMIT
    where TYPE = #{type,jdbcType=VARCHAR}
    and CODE = #{code,jdbcType=VARCHAR}
    <if test="enable != null">
      and ENABLE = #{enable,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="querySeq" resultType="java.lang.Long" >
    select seq_rc_temporary_limit.nextval from dual
  </select>

  <select id="queryLimitTypeByCode" resultType="java.lang.String">
    select
        rtl.limit_type
    from rc_temporary_limit rtl
    where rtl.type = '005'
      and rtl.enable = '2'
      and rtl.code = #{code,jdbcType=VARCHAR}
  </select>
</mapper>
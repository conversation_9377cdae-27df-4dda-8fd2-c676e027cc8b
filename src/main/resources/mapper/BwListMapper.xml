<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.BwListMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.BwList" >
    <id column="BW_ID" property="bwId" jdbcType="DECIMAL" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <result column="BUSINESS_TAGER_TYPE" property="businessTagerType" jdbcType="VARCHAR" />
    <result column="BUSINESS_TAGER_ID" property="businessTagerId" jdbcType="VARCHAR" />
    <result column="BW_TYPE" property="bwType" jdbcType="VARCHAR" />
    <result column="START_TIME" property="startTime" jdbcType="TIMESTAMP" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="TAGER_ID_HASH" property="tagerHash" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="USE_STATUS" property="useStatus" jdbcType="DECIMAL" />
    <result column="RISK_TAG" property="riskTag" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    BW_ID, BUSINESS_TYPE, BUSINESS_TAGER_TYPE, BUSINESS_TAGER_ID, BW_TYPE, START_TIME, 
    END_TIME, USER_ID, USER_NAME, to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') as CREATE_TIME, REMARK, STATUS, USE_STATUS, TAGER_ID_HASH,RISK_TAG
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_BW_LIST
    where BW_ID = #{bwId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_BW_LIST
    where BW_ID = #{bwId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.BwList" >
    insert into RC_BW_LIST (BW_ID, BUSINESS_TYPE, BUSINESS_TAGER_TYPE, 
      BUSINESS_TAGER_ID, BW_TYPE, START_TIME, 
      END_TIME, USER_ID, USER_NAME, 
      CREATE_TIME, REMARK, STATUS, 
      USE_STATUS, TAGER_ID_HASH,RISK_TAG)
    values (#{bwId,jdbcType=DECIMAL}, #{businessType,jdbcType=VARCHAR}, #{businessTagerType,jdbcType=VARCHAR}, 
      #{businessTagerId,jdbcType=VARCHAR}, #{bwType,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, 
      to_date(#{createTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'), #{remark,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, 
      #{useStatus,jdbcType=DECIMAL}, #{tagerHash,jdbcType=VARCHAR},#{riskTag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.BwList" >
    insert into RC_BW_LIST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bwId != null" >
        BW_ID,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="businessTagerType != null" >
        BUSINESS_TAGER_TYPE,
      </if>
      <if test="businessTagerId != null" >
        BUSINESS_TAGER_ID,
      </if>
      <if test="bwType != null" >
        BW_TYPE,
      </if>
      <if test="startTime != null" >
        START_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="useStatus != null" >
        USE_STATUS,
      </if>
      <if test="tagerHash != null" >
        TAGER_ID_HASH,
      </if>
      <if test="riskTag != null" >
        RISK_TAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bwId != null" >
        #{bwId,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerType != null" >
        #{businessTagerType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerId != null" >
        #{businessTagerId,jdbcType=VARCHAR},
      </if>
      <if test="bwType != null" >
        #{bwType,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        to_date(#{createTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="useStatus != null" >
        #{useStatus,jdbcType=DECIMAL},
      </if>
      <if test="tagerHash != null" >
        #{tagerHash,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        #{riskTag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.BwList" >
    update RC_BW_LIST
    <set >
      <if test="businessType != null" >
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerType != null" >
        BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerId != null" >
        BUSINESS_TAGER_ID = #{businessTagerId,jdbcType=VARCHAR},
      </if>
      <if test="bwType != null" >
        BW_TYPE = #{bwType,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = to_date(#{createTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="useStatus != null" >
        USE_STATUS = #{useStatus,jdbcType=DECIMAL},
      </if>
      <if test="tagerHash != null" >
        TAGER_ID_HASH = #{tagerHash,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      </if>
    </set>
    where BW_ID = #{bwId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.BwList" >
    update RC_BW_LIST
    set BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR},
      BUSINESS_TAGER_ID = #{businessTagerId,jdbcType=VARCHAR},
      BW_TYPE = #{bwType,jdbcType=VARCHAR},
      START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      USER_ID = #{userId,jdbcType=DECIMAL},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      CREATE_TIME = to_date(#{createTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
      REMARK = #{remark,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      USE_STATUS = #{useStatus,jdbcType=DECIMAL},
      TAGER_ID_HASH = #{tagerHash,jdbcType=VARCHAR},
        RISK_TAG = #{riskTag,jdbcType=VARCHAR}
    where BW_ID = #{bwId,jdbcType=DECIMAL}
  </update>


  <select id="queryExistence" parameterType="com.epaylinks.efps.rc.domain.BwList" resultType="int">
    SELECT COUNT(BW_ID) FROM RC_BW_LIST WHERE BUSINESS_TAGER_TYPE = #{businessTagerType} AND BUSINESS_TAGER_ID = #{businessTagerId}
    <if test="bwId != null">
      AND BW_ID != #{bwId}
    </if>
  </select>


  <select id="queryTotal" resultType="int" parameterType="java.util.Map">
    SELECT COUNT(BW_ID) FROM RC_BW_LIST WHERE 1 = 1
    <if test="startTime != null and startTime != ''">
      AND create_time &gt;  to_date(#{startTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
    </if>
    <if test="endTime != null and endTime != ''">
      AND create_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
    </if>
    <if test="businessTagerType != null and businessTagerType != ''">
      AND BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
    </if>
    <if test="businessTagerId != null and businessTagerId != ''">
      AND BUSINESS_TAGER_ID LIKE '%'||#{businessTagerId,jdbcType=VARCHAR}||'%'
    </if>
    <if test="bwType != null and bwType != ''">
      AND BW_TYPE = #{bwType,jdbcType=VARCHAR}
    </if>
    <if test="status != null">
      AND status = #{status,jdbcType=DECIMAL}
    </if>
    <if test="riskTag != null and riskTag != '' and riskTag != 'none'">
      and RISK_TAG like '%' || #{riskTag,jdbcType=VARCHAR} || '%'
    </if>
    <if test="riskTag != null and riskTag == 'none'">
      and RISK_TAG is null
    </if>
  </select>

  <select id="queryByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
   SELECT * FROM(
    SELECT a.*,rownum rn FROM (
      SELECT <include refid="Base_Column_List" /> FROM RC_BW_LIST WHERE 1 = 1
      <if test="startTime != null and startTime != ''">
        AND create_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
      </if>
      <if test="endTime != null and endTime != ''">
        AND create_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
      </if>
      <if test="businessTagerType != null and businessTagerType != ''">
        AND BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
      </if>
      <if test="businessTagerId != null and businessTagerId != ''">
        AND BUSINESS_TAGER_ID LIKE '%'||#{businessTagerId,jdbcType=VARCHAR}||'%'
      </if>
      <if test="bwType != null and bwType != ''">
        AND BW_TYPE = #{bwType,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        AND status = #{status,jdbcType=DECIMAL}
      </if>
      <if test="riskTag != null and riskTag != '' and riskTag != 'none'">
        and RISK_TAG like '%' || #{riskTag,jdbcType=VARCHAR} || '%'
      </if>
      <if test="riskTag != null and riskTag == 'none'">
        and RISK_TAG is null
      </if>
      ORDER BY CREATE_TIME DESC
    )a WHERE rownum &lt;= #{endNum,jdbcType=DECIMAL}
   ) WHERE rn &gt;= #{startNum,jdbcType=DECIMAL}
  </select>

  <select id="selectAll" resultType="String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_BW_LIST
    WHERE BW_TYPE = #{bwType}
  </select>

  <select id="getByTagerTypeAndTagerId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_BW_LIST
    WHERE BUSINESS_TAGER_TYPE = #{businessTagerType} and BUSINESS_TAGER_ID = #{businessTagerId}
  </select>

  <select id="getByTagerType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_BW_LIST
    WHERE BUSINESS_TAGER_TYPE = #{businessTagerType}
  </select>

  <select id="selectNoHashByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select *
    from (
    select A.*, rownum RN
    from (
    select
    <include refid="Base_Column_List" />
    from RC_BW_LIST
    where (BUSINESS_TAGER_TYPE = '001' or BUSINESS_TAGER_TYPE = '002' or BUSINESS_TAGER_TYPE = '003' or BUSINESS_TAGER_TYPE = '004')
    and (BUSINESS_TAGER_ID is not null and TAGER_ID_HASH is null)
    ) A
    where rownum <![CDATA[ <= ]]> #{endRowNo,jdbcType=DECIMAL}
    )
    where RN <![CDATA[ >= ]]>
    #{beginRowNo,jdbcType=DECIMAL}
  </select>

  <update id="updateHashInfoBySelective" parameterType="com.epaylinks.efps.rc.domain.BwList">
    update RC_BW_LIST
    <set>
      <if test="tagerHash != null">
        TAGER_ID_HASH = #{tagerHash,jdbcType=VARCHAR},
      </if>
    </set>
    where BW_ID = #{bwId,jdbcType=DECIMAL}
  </update>
</mapper>
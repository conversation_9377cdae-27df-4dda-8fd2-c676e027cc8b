<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcAudLimitMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcAudLimit" >
    <id column="LIMIT_ID" property="limitId" jdbcType="DECIMAL" />
    <result column="LIMIT_VALUE" property="limitValue" jdbcType="VARCHAR" />
    <result column="AUD_ID" property="audId" jdbcType="DECIMAL" />
    <result column="DEFINE_CODE" property="defineCode" jdbcType="VARCHAR" />
    <result column="DEFINE_ID" property="defineId" jdbcType="DECIMAL" />
    <result column="DEFAULT_VALUE" property="defaultValue" jdbcType="VARCHAR" />
    <result column="OLD_VALUE" property="oldValue" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LIMIT_ID, LIMIT_VALUE, AUD_ID, DEFINE_CODE, DEFINE_ID, DEFAULT_VALUE, OLD_VALUE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from RC_AUD_LIMIT
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_AUD_LIMIT
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcAudLimit" >
    insert into RC_AUD_LIMIT (LIMIT_ID, LIMIT_VALUE, AUD_ID,
    DEFINE_CODE, DEFINE_ID, DEFAULT_VALUE,
    OLD_VALUE)
    values (#{limitId,jdbcType=DECIMAL}, #{limitValue,jdbcType=VARCHAR}, #{audId,jdbcType=DECIMAL},
    #{defineCode,jdbcType=VARCHAR}, #{defineId,jdbcType=DECIMAL}, #{defaultValue,jdbcType=VARCHAR},
    #{oldValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcAudLimit" >
    insert into RC_AUD_LIMIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="limitId != null" >
        LIMIT_ID,
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE,
      </if>
      <if test="audId != null" >
        AUD_ID,
      </if>
      <if test="defineCode != null" >
        DEFINE_CODE,
      </if>
      <if test="defineId != null" >
        DEFINE_ID,
      </if>
      <if test="defaultValue != null" >
        DEFAULT_VALUE,
      </if>
      <if test="oldValue != null" >
        OLD_VALUE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="limitId != null" >
        #{limitId,jdbcType=DECIMAL},
      </if>
      <if test="limitValue != null" >
        #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="audId != null" >
        #{audId,jdbcType=DECIMAL},
      </if>
      <if test="defineCode != null" >
        #{defineCode,jdbcType=VARCHAR},
      </if>
      <if test="defineId != null" >
        #{defineId,jdbcType=DECIMAL},
      </if>
      <if test="defaultValue != null" >
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null" >
        #{oldValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcAudLimit" >
    update RC_AUD_LIMIT
    <set >
      <if test="limitValue != null" >
        LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="audId != null" >
        AUD_ID = #{audId,jdbcType=DECIMAL},
      </if>
      <if test="defineCode != null" >
        DEFINE_CODE = #{defineCode,jdbcType=VARCHAR},
      </if>
      <if test="defineId != null" >
        DEFINE_ID = #{defineId,jdbcType=DECIMAL},
      </if>
      <if test="defaultValue != null" >
        DEFAULT_VALUE = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null" >
        OLD_VALUE = #{oldValue,jdbcType=VARCHAR},
      </if>
    </set>
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcAudLimit" >
    update RC_AUD_LIMIT
    set LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
      AUD_ID = #{audId,jdbcType=DECIMAL},
      DEFINE_CODE = #{defineCode,jdbcType=VARCHAR},
      DEFINE_ID = #{defineId,jdbcType=DECIMAL},
      DEFAULT_VALUE = #{defaultValue,jdbcType=VARCHAR},
      OLD_VALUE = #{oldValue,jdbcType=VARCHAR}
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </update>

  <select id="selectList"  resultType="java.util.HashMap">
    SELECT DEFINE_CODE, ${valueName} as "LIMIT_VALUE" FROM RC_AUD_LIMIT WHERE AUD_ID = #{audId}
  </select>

  <select id="queryAudLimit" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_AUD_LIMIT
    WHERE DEFINE_CODE = #{defineCode,jdbcType=VARCHAR}
    AND AUD_ID = #{audId,jdbcType=DECIMAL}
  </select>

</mapper>
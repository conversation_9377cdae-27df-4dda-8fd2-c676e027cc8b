<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcLimitLogMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcLimitLog" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="BUSINESS_TARGET_ID" property="businessTargetId" jdbcType="VARCHAR" />
    <result column="DEFINECODE" property="definecode" jdbcType="VARCHAR" />
    <result column="CURRENT_VALUE" property="currentValue" jdbcType="VARCHAR" />
    <result column="TOTAL_VALUE" property="totalValue" jdbcType="VARCHAR" />
    <result column="LIMIT_VALUE" property="limitValue" jdbcType="VARCHAR" />
    <result column="BANK_CARD_NO" property="bankCardNo" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TRANSACTION_NO, BUSINESS_TARGET_ID, DEFINECODE, CURRENT_VALUE, TOTAL_VALUE, LIMIT_VALUE, 
    BANK_CARD_NO, UNIT, CREATETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_LIMIT_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_LIMIT_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcLimitLog" >
    insert into RC_LIMIT_LOG (ID, TRANSACTION_NO, BUSINESS_TARGET_ID, 
      DEFINECODE, CURRENT_VALUE, TOTAL_VALUE, 
      LIMIT_VALUE, BANK_CARD_NO, UNIT, 
      CREATETIME)
    values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, #{businessTargetId,jdbcType=VARCHAR}, 
      #{definecode,jdbcType=VARCHAR}, #{currentValue,jdbcType=VARCHAR}, #{totalValue,jdbcType=VARCHAR}, 
      #{limitValue,jdbcType=VARCHAR}, #{bankCardNo,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitLog" >
    insert into RC_LIMIT_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="businessTargetId != null" >
        BUSINESS_TARGET_ID,
      </if>
      <if test="definecode != null" >
        DEFINECODE,
      </if>
      <if test="currentValue != null" >
        CURRENT_VALUE,
      </if>
      <if test="totalValue != null" >
        TOTAL_VALUE,
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE,
      </if>
      <if test="bankCardNo != null" >
        BANK_CARD_NO,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="createtime != null" >
        CREATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetId != null" >
        #{businessTargetId,jdbcType=VARCHAR},
      </if>
      <if test="definecode != null" >
        #{definecode,jdbcType=VARCHAR},
      </if>
      <if test="currentValue != null" >
        #{currentValue,jdbcType=VARCHAR},
      </if>
      <if test="totalValue != null" >
        #{totalValue,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="bankCardNo != null" >
        #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null" >
        #{createtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitLog" >
    update RC_LIMIT_LOG
    <set >
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetId != null" >
        BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR},
      </if>
      <if test="definecode != null" >
        DEFINECODE = #{definecode,jdbcType=VARCHAR},
      </if>
      <if test="currentValue != null" >
        CURRENT_VALUE = #{currentValue,jdbcType=VARCHAR},
      </if>
      <if test="totalValue != null" >
        TOTAL_VALUE = #{totalValue,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="bankCardNo != null" >
        BANK_CARD_NO = #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null" >
        CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcLimitLog" >
    update RC_LIMIT_LOG
    set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR},
      DEFINECODE = #{definecode,jdbcType=VARCHAR},
      CURRENT_VALUE = #{currentValue,jdbcType=VARCHAR},
      TOTAL_VALUE = #{totalValue,jdbcType=VARCHAR},
      LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
      BANK_CARD_NO = #{bankCardNo,jdbcType=VARCHAR},
      UNIT = #{unit,jdbcType=VARCHAR},
      CREATETIME = #{createtime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>
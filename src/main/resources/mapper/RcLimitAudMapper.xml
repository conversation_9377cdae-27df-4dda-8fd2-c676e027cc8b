<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcLimitAudMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcLimitAud" >
    <id column="AUD_ID" property="audId" jdbcType="DECIMAL" />
    <result column="AUD_CODE" property="audCode" jdbcType="VARCHAR" />
    <result column="AUD_NAME" property="audName" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="AUD_STATUS" property="audStatus" jdbcType="VARCHAR" />
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <result column="FIRST_OPINION" property="firstOpinion" jdbcType="VARCHAR" />
    <result column="FIRST_TIME" property="firstTime" jdbcType="TIMESTAMP" />
    <result column="FIRST_USER" property="firstUser" jdbcType="DECIMAL" />
    <result column="FIRST_NAME" property="firstName" jdbcType="VARCHAR" />
    <result column="LAST_OPINION" property="lastOpinion" jdbcType="VARCHAR" />
    <result column="LAST_TIME" property="lastTime" jdbcType="TIMESTAMP" />
    <result column="LAST_USER" property="lastUser" jdbcType="DECIMAL" />
    <result column="LAST_NAME" property="lastName" jdbcType="VARCHAR" />
    <result column="REVOKE_USER" property="revokeUser" jdbcType="DECIMAL" />
    <result column="REVOKE_NAME" property="revokeName" jdbcType="VARCHAR" />
    <result column="REVOKE_TIME" property="revokeTime" jdbcType="TIMESTAMP" />
    <result column="TARGET_TYPE" property="targetType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    AUD_ID, AUD_CODE, AUD_NAME, USER_ID, USER_NAME, CREATE_TIME, AUD_STATUS, URL, FIRST_OPINION, 
    FIRST_TIME, FIRST_USER, FIRST_NAME, LAST_OPINION, LAST_TIME, LAST_USER, LAST_NAME, 
    REVOKE_USER, REVOKE_NAME, REVOKE_TIME, TARGET_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_LIMIT_AUD
    where AUD_ID = #{audId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_LIMIT_AUD
    where AUD_ID = #{audId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcLimitAud" >
    insert into RC_LIMIT_AUD (AUD_ID, AUD_CODE, AUD_NAME, 
      USER_ID, USER_NAME, CREATE_TIME, 
      AUD_STATUS, URL, FIRST_OPINION, 
      FIRST_TIME, FIRST_USER, FIRST_NAME, 
      LAST_OPINION, LAST_TIME, LAST_USER, 
      LAST_NAME, REVOKE_USER, REVOKE_NAME, 
      REVOKE_TIME, TARGET_TYPE)
    values (#{audId,jdbcType=DECIMAL}, #{audCode,jdbcType=VARCHAR}, #{audName,jdbcType=VARCHAR}, 
      #{userId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{audStatus,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{firstOpinion,jdbcType=VARCHAR}, 
      #{firstTime,jdbcType=TIMESTAMP}, #{firstUser,jdbcType=DECIMAL}, #{firstName,jdbcType=VARCHAR}, 
      #{lastOpinion,jdbcType=VARCHAR}, #{lastTime,jdbcType=TIMESTAMP}, #{lastUser,jdbcType=DECIMAL}, 
      #{lastName,jdbcType=VARCHAR}, #{revokeUser,jdbcType=DECIMAL}, #{revokeName,jdbcType=VARCHAR}, 
      #{revokeTime,jdbcType=TIMESTAMP}, #{targetType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitAud" >
    insert into RC_LIMIT_AUD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="audId != null" >
        AUD_ID,
      </if>
      <if test="audCode != null" >
        AUD_CODE,
      </if>
      <if test="audName != null" >
        AUD_NAME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="audStatus != null" >
        AUD_STATUS,
      </if>
      <if test="url != null" >
        URL,
      </if>
      <if test="firstOpinion != null" >
        FIRST_OPINION,
      </if>
      <if test="firstTime != null" >
        FIRST_TIME,
      </if>
      <if test="firstUser != null" >
        FIRST_USER,
      </if>
      <if test="firstName != null" >
        FIRST_NAME,
      </if>
      <if test="lastOpinion != null" >
        LAST_OPINION,
      </if>
      <if test="lastTime != null" >
        LAST_TIME,
      </if>
      <if test="lastUser != null" >
        LAST_USER,
      </if>
      <if test="lastName != null" >
        LAST_NAME,
      </if>
      <if test="revokeUser != null" >
        REVOKE_USER,
      </if>
      <if test="revokeName != null" >
        REVOKE_NAME,
      </if>
      <if test="revokeTime != null" >
        REVOKE_TIME,
      </if>
      <if test="targetType != null" >
        TARGET_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="audId != null" >
        #{audId,jdbcType=DECIMAL},
      </if>
      <if test="audCode != null" >
        #{audCode,jdbcType=VARCHAR},
      </if>
      <if test="audName != null" >
        #{audName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="audStatus != null" >
        #{audStatus,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="firstOpinion != null" >
        #{firstOpinion,jdbcType=VARCHAR},
      </if>
      <if test="firstTime != null" >
        #{firstTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstUser != null" >
        #{firstUser,jdbcType=DECIMAL},
      </if>
      <if test="firstName != null" >
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastOpinion != null" >
        #{lastOpinion,jdbcType=VARCHAR},
      </if>
      <if test="lastTime != null" >
        #{lastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUser != null" >
        #{lastUser,jdbcType=DECIMAL},
      </if>
      <if test="lastName != null" >
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="revokeUser != null" >
        #{revokeUser,jdbcType=DECIMAL},
      </if>
      <if test="revokeName != null" >
        #{revokeName,jdbcType=VARCHAR},
      </if>
      <if test="revokeTime != null" >
        #{revokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="targetType != null" >
        #{targetType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcLimitAud" >
    update RC_LIMIT_AUD
    <set >
      <if test="audCode != null" >
        AUD_CODE = #{audCode,jdbcType=VARCHAR},
      </if>
      <if test="audName != null" >
        AUD_NAME = #{audName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="audStatus != null" >
        AUD_STATUS = #{audStatus,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="firstOpinion != null" >
        FIRST_OPINION = #{firstOpinion,jdbcType=VARCHAR},
      </if>
      <if test="firstTime != null" >
        FIRST_TIME = #{firstTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstUser != null" >
        FIRST_USER = #{firstUser,jdbcType=DECIMAL},
      </if>
      <if test="firstName != null" >
        FIRST_NAME = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastOpinion != null" >
        LAST_OPINION = #{lastOpinion,jdbcType=VARCHAR},
      </if>
      <if test="lastTime != null" >
        LAST_TIME = #{lastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUser != null" >
        LAST_USER = #{lastUser,jdbcType=DECIMAL},
      </if>
      <if test="lastName != null" >
        LAST_NAME = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="revokeUser != null" >
        REVOKE_USER = #{revokeUser,jdbcType=DECIMAL},
      </if>
      <if test="revokeName != null" >
        REVOKE_NAME = #{revokeName,jdbcType=VARCHAR},
      </if>
      <if test="revokeTime != null" >
        REVOKE_TIME = #{revokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="targetType != null" >
        TARGET_TYPE = #{targetType,jdbcType=VARCHAR},
      </if>
    </set>
    where AUD_ID = #{audId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcLimitAud" >
    update RC_LIMIT_AUD
    set AUD_CODE = #{audCode,jdbcType=VARCHAR},
      AUD_NAME = #{audName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      AUD_STATUS = #{audStatus,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      FIRST_OPINION = #{firstOpinion,jdbcType=VARCHAR},
      FIRST_TIME = #{firstTime,jdbcType=TIMESTAMP},
      FIRST_USER = #{firstUser,jdbcType=DECIMAL},
      FIRST_NAME = #{firstName,jdbcType=VARCHAR},
      LAST_OPINION = #{lastOpinion,jdbcType=VARCHAR},
      LAST_TIME = #{lastTime,jdbcType=TIMESTAMP},
      LAST_USER = #{lastUser,jdbcType=DECIMAL},
      LAST_NAME = #{lastName,jdbcType=VARCHAR},
      REVOKE_USER = #{revokeUser,jdbcType=DECIMAL},
      REVOKE_NAME = #{revokeName,jdbcType=VARCHAR},
      REVOKE_TIME = #{revokeTime,jdbcType=TIMESTAMP},
      TARGET_TYPE = #{targetType,jdbcType=VARCHAR}
    where AUD_ID = #{audId,jdbcType=DECIMAL}
  </update>

  <select id="queryLastRecord" resultMap="BaseResultMap">
    SELECT * FROM (
      SELECT
      <include refid="Base_Column_List"/>
      FROM RC_LIMIT_AUD
      WHERE AUD_CODE = #{customerCode}
      AND TARGET_TYPE = #{targetType}
      ORDER BY CREATE_TIME DESC
    ) WHERE rownum = 1
  </select>

  <select id="pageTotal" resultType="int">
    SELECT COUNT (AUD_ID) FROM RC_LIMIT_AUD WHERE 1 = 1
    <if test="status != null and status != ''">
      AND AUD_STATUS = #{status}
    </if>
    <if test="customerCode != null and customerCode != ''">
      AND AUD_CODE LIKE '%'||#{customerCode,jdbcType=VARCHAR}||'%'
    </if>
    <if test="startTime != null and startTime != ''">
      AND CREATE_TIME &gt;= to_date( #{startTime},'yyyymmddhh24miss')
    </if>
    <if test="endTime != null and endTime != ''">
      AND CREATE_TIME &lt;= to_date(#{endTime},'yyyymmddhh24miss')
    </if>
  </select>

  <select id="queryByPage" resultMap="BaseResultMap">

    SELECT * FROM (
      SELECT a.*,rownum rn FROM (
        SELECT
        <include refid="Base_Column_List"/>
        FROM RC_LIMIT_AUD WHERE 1 = 1
        <if test="status != null and status != ''">
          AND AUD_STATUS = #{status}
        </if>
        <if test="customerCode != null and customerCode != ''">
          AND AUD_CODE LIKE '%'||#{customerCode,jdbcType=VARCHAR}||'%'
        </if>
        <if test="startTime != null and startTime != ''">
          AND CREATE_TIME &gt;= to_date( #{startTime},'yyyymmddhh24miss')
        </if>
        <if test="endTime != null and endTime != ''">
          AND CREATE_TIME &lt;= to_date(#{endTime},'yyyymmddhh24miss')
        </if>
        ORDER BY CREATE_TIME DESC
      )a WHERE rownum &lt;= #{endNum}
    )WHERE rn &gt;= #{startNum}

  </select>

  <select id="queryRecordLog" resultMap="BaseResultMap">
    SELECT * FROM (
    SELECT
    <include refid="Base_Column_List"/>
    FROM RC_LIMIT_AUD
    WHERE AUD_CODE = #{customerCode}
    AND TARGET_TYPE = #{targetType}
    AND to_char(LAST_TIME,'yyyy-MM-dd hh24:mi:ss') = #{auditTime}
    ORDER BY CREATE_TIME DESC
    ) WHERE rownum = 1
  </select>

</mapper>
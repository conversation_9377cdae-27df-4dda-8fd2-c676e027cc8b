<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcDroolsSceneWhenMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDroolsSceneWhen">
    <!--@mbg.generated-->
    <!--@Table RC_DROOLS_SCENE_WHEN-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="SCENE_TEXT" jdbcType="VARCHAR" property="sceneText" />
    <result column="LEFT_TEXT" jdbcType="VARCHAR" property="leftText" />
    <result column="PRIORITY" jdbcType="DECIMAL" property="priority" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REQUIRED" jdbcType="DECIMAL" property="required" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCENE_TEXT, LEFT_TEXT, PRIORITY, CREATE_TIME, UPDATE_TIME, REQUIRED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_DROOLS_SCENE_WHEN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from RC_DROOLS_SCENE_WHEN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneWhen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_SCENE_WHEN (ID, SCENE_TEXT, LEFT_TEXT, 
      PRIORITY, CREATE_TIME, UPDATE_TIME, 
      REQUIRED)
    values (#{id,jdbcType=DECIMAL}, #{sceneText,jdbcType=VARCHAR}, #{leftText,jdbcType=VARCHAR}, 
      #{priority,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{required,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneWhen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_SCENE_WHEN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="sceneText != null">
        SCENE_TEXT,
      </if>
      <if test="leftText != null">
        LEFT_TEXT,
      </if>
      <if test="priority != null">
        PRIORITY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="required != null">
        REQUIRED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="sceneText != null">
        #{sceneText,jdbcType=VARCHAR},
      </if>
      <if test="leftText != null">
        #{leftText,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="required != null">
        #{required,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneWhen">
    <!--@mbg.generated-->
    update RC_DROOLS_SCENE_WHEN
    <set>
      <if test="sceneText != null">
        SCENE_TEXT = #{sceneText,jdbcType=VARCHAR},
      </if>
      <if test="leftText != null">
        LEFT_TEXT = #{leftText,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="required != null">
        REQUIRED = #{required,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDroolsSceneWhen">
    <!--@mbg.generated-->
    update RC_DROOLS_SCENE_WHEN
    set SCENE_TEXT = #{sceneText,jdbcType=VARCHAR},
      LEFT_TEXT = #{leftText,jdbcType=VARCHAR},
      PRIORITY = #{priority,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REQUIRED = #{required,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <select id="selectAllOrderById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from RC_DROOLS_SCENE_WHEN order by ID asc
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
  <select id="selectAllBySceneTextOrderByPriority" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from RC_DROOLS_SCENE_WHEN
    where SCENE_TEXT=#{sceneText,jdbcType=VARCHAR} order by PRIORITY asc
  </select>
</mapper>
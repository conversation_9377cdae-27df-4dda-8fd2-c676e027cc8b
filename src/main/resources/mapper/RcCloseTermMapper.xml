<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcCloseTermMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcCloseTerm">
    <!--@mbg.generated-->
    <!--@Table RC_CLOSE_TERM-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="TERM_CODE" jdbcType="VARCHAR" property="termCode" />
    <result column="CLOSE_TIME" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="REOPEN_TIME" jdbcType="TIMESTAMP" property="reopenTime" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CLOSE_TYPE" jdbcType="DECIMAL" property="closeType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CUSTOMER_CODE, TERM_CODE, CLOSE_TIME, REOPEN_TIME, CREATE_TIME, UPDATE_TIME, 
    CLOSE_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_CLOSE_TERM
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from RC_CLOSE_TERM
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcCloseTerm">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
      select SEQ_RC_CLOSE_TERM.nextval from dual
    </selectKey>
    insert into RC_CLOSE_TERM (ID, CUSTOMER_CODE, TERM_CODE, 
      CLOSE_TIME, REOPEN_TIME, CREATE_TIME, 
      UPDATE_TIME, CLOSE_TYPE)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{termCode,jdbcType=VARCHAR}, 
      #{closeTime,jdbcType=TIMESTAMP}, #{reopenTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{closeType,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcCloseTerm">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
      select SEQ_RC_CLOSE_TERM.nextval from dual
    </selectKey>
    insert into RC_CLOSE_TERM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="termCode != null">
        TERM_CODE,
      </if>
      <if test="closeTime != null">
        CLOSE_TIME,
      </if>
      <if test="reopenTime != null">
        REOPEN_TIME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="closeType != null">
        CLOSE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="termCode != null">
        #{termCode,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reopenTime != null">
        #{reopenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeType != null">
        #{closeType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcCloseTerm">
    <!--@mbg.generated-->
    update RC_CLOSE_TERM
    <set>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="termCode != null">
        TERM_CODE = #{termCode,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reopenTime != null">
        REOPEN_TIME = #{reopenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeType != null">
        CLOSE_TYPE = #{closeType,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcCloseTerm">
    <!--@mbg.generated-->
    update RC_CLOSE_TERM
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      TERM_CODE = #{termCode,jdbcType=VARCHAR},
      CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
      REOPEN_TIME = #{reopenTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CLOSE_TYPE = #{closeType,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-05-26-->
  <select id="selectByReopenTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from RC_CLOSE_TERM
    where REOPEN_TIME=#{reopenTime,jdbcType=TIMESTAMP}
  </select>
</mapper>
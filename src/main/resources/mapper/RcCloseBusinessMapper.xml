<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcCloseBusinessMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcCloseBusiness">
    <!--@mbg.generated-->
    <!--@Table RC_CLOSE_BUSINESS-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="CLOSE_TIME" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="REOPEN_TIME" jdbcType="TIMESTAMP" property="reopenTime" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CUSTOMER_CODE, BUSINESS_CODE, CLOSE_TIME, REOPEN_TIME, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_CLOSE_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from RC_CLOSE_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcCloseBusiness">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_RC_CLOSE_BUSINESS.nextval from dual
    </selectKey>
    insert into RC_CLOSE_BUSINESS (ID, CUSTOMER_CODE, BUSINESS_CODE, 
      CLOSE_TIME, REOPEN_TIME, CREATE_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, 
      #{closeTime,jdbcType=TIMESTAMP}, #{reopenTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcCloseBusiness">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_RC_CLOSE_BUSINESS.nextval from dual
    </selectKey>
    insert into RC_CLOSE_BUSINESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="closeTime != null">
        CLOSE_TIME,
      </if>
      <if test="reopenTime != null">
        REOPEN_TIME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reopenTime != null">
        #{reopenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcCloseBusiness">
    <!--@mbg.generated-->
    update RC_CLOSE_BUSINESS
    <set>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reopenTime != null">
        REOPEN_TIME = #{reopenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcCloseBusiness">
    <!--@mbg.generated-->
    update RC_CLOSE_BUSINESS
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
      REOPEN_TIME = #{reopenTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-10-11-->
  <select id="selectByReopenTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RC_CLOSE_BUSINESS
        where REOPEN_TIME=#{reopenTime,jdbcType=TIMESTAMP}
    </select>
</mapper>
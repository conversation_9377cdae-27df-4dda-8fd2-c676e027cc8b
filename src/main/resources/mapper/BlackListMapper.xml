<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.BlackListMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.BlackList" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="VALUE" property="value" jdbcType="VARCHAR" />
    <result column="VALUE_ENC" property="valueEnc" jdbcType="VARCHAR" />
    <result column="VALUE_HASH" property="valueHash" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="RISK_TAG" property="riskTag" jdbcType="VARCHAR" />
    <result column="DISPLAY" property="display" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="REMARK_ENC" property="remarkEnc" jdbcType="VARCHAR" />
    <result column="REMARK_HASH" property="remarkHash" jdbcType="VARCHAR" />
    <result column="CHANNEL" property="channel" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TYPE, VALUE, VALUE_ENC, VALUE_HASH, NAME, RISK_TAG, DISPLAY, REMARK, REMARK_ENC, 
    REMARK_HASH, CHANNEL, OPERATOR, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_BLACK_LIST
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_BLACK_LIST
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.BlackList" >
    insert into RC_BLACK_LIST (ID, TYPE, VALUE, 
      VALUE_ENC, VALUE_HASH, NAME, 
      RISK_TAG, DISPLAY, REMARK, 
      REMARK_ENC, REMARK_HASH, CHANNEL, 
      OPERATOR, CREATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{type,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, 
      #{valueEnc,jdbcType=VARCHAR}, #{valueHash,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{riskTag,jdbcType=VARCHAR}, #{display,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{remarkEnc,jdbcType=VARCHAR}, #{remarkHash,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.BlackList" >
    insert into RC_BLACK_LIST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="value != null" >
        VALUE,
      </if>
      <if test="valueEnc != null" >
        VALUE_ENC,
      </if>
      <if test="valueHash != null" >
        VALUE_HASH,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="riskTag != null" >
        RISK_TAG,
      </if>
      <if test="display != null" >
        DISPLAY,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="remarkEnc != null" >
        REMARK_ENC,
      </if>
      <if test="remarkHash != null" >
        REMARK_HASH,
      </if>
      <if test="channel != null" >
        CHANNEL,
      </if>
      <if test="operator != null" >
        OPERATOR,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueEnc != null" >
        #{valueEnc,jdbcType=VARCHAR},
      </if>
      <if test="valueHash != null" >
        #{valueHash,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        #{riskTag,jdbcType=VARCHAR},
      </if>
      <if test="display != null" >
        #{display,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEnc != null" >
        #{remarkEnc,jdbcType=VARCHAR},
      </if>
      <if test="remarkHash != null" >
        #{remarkHash,jdbcType=VARCHAR},
      </if>
      <if test="channel != null" >
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.BlackList" >
    update RC_BLACK_LIST
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        VALUE = #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueEnc != null" >
        VALUE_ENC = #{valueEnc,jdbcType=VARCHAR},
      </if>
      <if test="valueHash != null" >
        VALUE_HASH = #{valueHash,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      </if>
      <if test="display != null" >
        DISPLAY = #{display,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEnc != null" >
        REMARK_ENC = #{remarkEnc,jdbcType=VARCHAR},
      </if>
      <if test="remarkHash != null" >
        REMARK_HASH = #{remarkHash,jdbcType=VARCHAR},
      </if>
      <if test="channel != null" >
        CHANNEL = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        OPERATOR = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.BlackList" >
    update RC_BLACK_LIST
    set TYPE = #{type,jdbcType=VARCHAR},
      VALUE = #{value,jdbcType=VARCHAR},
      VALUE_ENC = #{valueEnc,jdbcType=VARCHAR},
      VALUE_HASH = #{valueHash,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      DISPLAY = #{display,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      REMARK_ENC = #{remarkEnc,jdbcType=VARCHAR},
      REMARK_HASH = #{remarkHash,jdbcType=VARCHAR},
      CHANNEL = #{channel,jdbcType=VARCHAR},
      OPERATOR = #{operator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="querySeq" resultType="java.lang.Long">
    select SEQ_RC_BLACK_LIST.nextval from dual
  </select>

    <select id="queryOneByTypeAndValue" resultMap="BaseResultMap">
      select tt.*
      from (
        select
        <include refid="Base_Column_List" />
        from rc_black_list t
        where t.type = #{type,jdbcType=VARCHAR}
        and (
          t.value_hash = #{valueHash,jdbcType=VARCHAR}
          or
          t.value = #{value,jdbcType=VARCHAR}
        )
    )tt where rownum &lt;= 1
    </select>
</mapper>
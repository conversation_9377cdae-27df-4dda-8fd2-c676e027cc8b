<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.LimitSwitchMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.LimitSwitch" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR" />
    <result column="LIMIT_INDEX" property="limitIndex" jdbcType="VARCHAR" />
    <result column="LIMIT_VALUE" property="limitValue" jdbcType="DECIMAL" />
    <result column="LIMIT_STATUS" property="limitStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_NO, LIMIT_INDEX, LIMIT_VALUE, LIMIT_STATUS, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_LIMIT_SWITCH
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_LIMIT_SWITCH
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.LimitSwitch" >
    insert into RC_LIMIT_SWITCH (ID, CUSTOMER_NO, LIMIT_INDEX, 
      LIMIT_VALUE, LIMIT_STATUS, CREATE_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{customerNo,jdbcType=VARCHAR}, #{limitIndex,jdbcType=VARCHAR}, 
      #{limitValue,jdbcType=DECIMAL}, #{limitStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.LimitSwitch" >
    insert into RC_LIMIT_SWITCH
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO,
      </if>
      <if test="limitIndex != null" >
        LIMIT_INDEX,
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE,
      </if>
      <if test="limitStatus != null" >
        LIMIT_STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerNo != null" >
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="limitIndex != null" >
        #{limitIndex,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        #{limitValue,jdbcType=DECIMAL},
      </if>
      <if test="limitStatus != null" >
        #{limitStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.LimitSwitch" >
    update RC_LIMIT_SWITCH
    <set >
      <if test="customerNo != null" >
        CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="limitIndex != null" >
        LIMIT_INDEX = #{limitIndex,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE = #{limitValue,jdbcType=DECIMAL},
      </if>
      <if test="limitStatus != null" >
        LIMIT_STATUS = #{limitStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.LimitSwitch" >
    update RC_LIMIT_SWITCH
    set CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      LIMIT_INDEX = #{limitIndex,jdbcType=VARCHAR},
      LIMIT_VALUE = #{limitValue,jdbcType=DECIMAL},
      LIMIT_STATUS = #{limitStatus,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectByCustomerNoAndIndex" resultMap="BaseResultMap">
    select *
    from (
        select
        <include refid="Base_Column_List" />
        from RC_LIMIT_SWITCH
        where customer_no = #{customerNo,jdbcType=VARCHAR}
        and LIMIT_INDEX = #{limitIndex,jdbcType=VARCHAR}
        order by id desc
    ) where rownum = 1
  </select>

  <!-- 查询主键值 -->
  <select id="selectPriKey" resultType="java.lang.Long">
    select SEQ_LIMIT_SWITCH.nextval from dual
  </select>

<!--auto generated by MybatisCodeHelper on 2022-12-12-->
  <select id="selectOpenByCustomerNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from RC_LIMIT_SWITCH
    where CUSTOMER_NO=#{customerNo,jdbcType=VARCHAR} and LIMIT_STATUS = '1'
  </select>
</mapper>
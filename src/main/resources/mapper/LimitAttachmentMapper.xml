<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.LimitAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.LimitAttachment" >
    <id column="ATTACHMENT_ID" property="attachmentId" jdbcType="DECIMAL" />
    <result column="ARCHIVE_TYPE" property="archiveType" jdbcType="VARCHAR" />
    <result column="ARCHIVE_CODE" property="archiveCode" jdbcType="VARCHAR" />
    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="VARCHAR" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs" extends="BaseResultMap" >
    <result column="OLD_DATA" property="oldData" jdbcType="CLOB" />
    <result column="ATTACHMENT_DATA" property="attachmentData" jdbcType="CLOB" />
  </resultMap>
  <sql id="Base_Column_List" >
    ATTACHMENT_ID, ARCHIVE_TYPE, ARCHIVE_CODE, AUDIT_STATUS, REMARKS, CREATE_TIME, UPDATE_TIME
  </sql>
  <sql id="Blob_Column_List" >
    OLD_DATA, ATTACHMENT_DATA
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from RC_LIMIT_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_LIMIT_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs" >
    insert into RC_LIMIT_ATTACHMENT (ATTACHMENT_ID, ARCHIVE_TYPE, ARCHIVE_CODE, 
      AUDIT_STATUS, REMARKS, CREATE_TIME, 
      UPDATE_TIME, OLD_DATA, ATTACHMENT_DATA
      )
    values (#{attachmentId,jdbcType=DECIMAL}, #{archiveType,jdbcType=VARCHAR}, #{archiveCode,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{oldData,jdbcType=CLOB}, #{attachmentData,jdbcType=CLOB}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs" >
    insert into RC_LIMIT_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        ATTACHMENT_ID,
      </if>
      <if test="archiveType != null" >
        ARCHIVE_TYPE,
      </if>
      <if test="archiveCode != null" >
        ARCHIVE_CODE,
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="oldData != null" >
        OLD_DATA,
      </if>
      <if test="attachmentData != null" >
        ATTACHMENT_DATA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        #{attachmentId,jdbcType=DECIMAL},
      </if>
      <if test="archiveType != null" >
        #{archiveType,jdbcType=VARCHAR},
      </if>
      <if test="archiveCode != null" >
        #{archiveCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldData != null" >
        #{oldData,jdbcType=CLOB},
      </if>
      <if test="attachmentData != null" >
        #{attachmentData,jdbcType=CLOB},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs" >
    update RC_LIMIT_ATTACHMENT
    <set >
      <if test="archiveType != null" >
        ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR},
      </if>
      <if test="archiveCode != null" >
        ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldData != null" >
        OLD_DATA = #{oldData,jdbcType=CLOB},
      </if>
      <if test="attachmentData != null" >
        ATTACHMENT_DATA = #{attachmentData,jdbcType=CLOB},
      </if>
    </set>
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs" >
    update RC_LIMIT_ATTACHMENT
    set ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR},
      ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      OLD_DATA = #{oldData,jdbcType=CLOB},
      ATTACHMENT_DATA = #{attachmentData,jdbcType=CLOB}
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.LimitAttachment" >
    update RC_LIMIT_ATTACHMENT
    set ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR},
      ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeq" resultType="java.lang.Long">
    select SEQ_LIMIT_ATTACHMENT.nextval from dual
  </select>

  <select id="selectByArchiveTypeAndCode" resultMap="ResultMapWithBLOBs" parameterType="java.util.Map">
    select <include refid="Base_Column_List" />,
    <include refid="Blob_Column_List" />
    from RC_LIMIT_ATTACHMENT
    where ARCHIVE_TYPE = #{archiveType,jdbcType=VARCHAR}
    and ARCHIVE_CODE = #{archiveCode,jdbcType=VARCHAR}
    <if test="auditStatus != null">
      and AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
    </if>
    order by ATTACHMENT_ID desc
  </select>
</mapper>
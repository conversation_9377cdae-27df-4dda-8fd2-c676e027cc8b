<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.OtherMapper" >
    <resultMap id="UserResultMap" type="com.epaylinks.efps.rc.domain.User">
        <id column="USER_ID" jdbcType="DECIMAL" property="uid" />
        <result column="DEPT_ID" jdbcType="DECIMAL" property="deptId" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="REAL_NAME" jdbcType="VARCHAR" property="realName" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
        <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="EMAIL" jdbcType="VARCHAR" property="email" />
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
        <result column="STATUS" jdbcType="VARCHAR" property="status" />
        <result column="role_id" jdbcType="VARCHAR" property="roleId" />
        <result column="role_name" jdbcType="VARCHAR" property="roleName" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="USER_TYPE" jdbcType="DECIMAL" property="userType" />
        <result column="COMPANY_ID" jdbcType="DECIMAL" property="companyId" />
    </resultMap>

    <sql id="User_Column_List">
        USER_ID, DEPT_ID, NAME, REAL_NAME, REMARK,  PASSWORD, CREATOR,
        CREATE_TIME,
        UPDATOR, UPDATE_TIME, EMAIL, MOBILE, STATUS,USER_TYPE,COMPANY_ID
    </sql>

    <select id="selectUserById" parameterType="java.lang.Long" resultMap="UserResultMap">
	    select
        <include refid="User_Column_List" />
        from PAS_USER
        where USER_ID = #{uid,jdbcType=DECIMAL}
    </select>

    <!-- 查询风控类型审核权限 -->
    <select id="selectAuditAuthRC" resultType="java.lang.String">
        select * from (
            select distinct r.perm_id from pas_role_user t
            left join pas_role_perm r on t.role_id = r.role_id
            inner join pas_perm p on r.perm_id = p.perm_id
            where t.user_id = #{userId} and p.name = #{auditName}
        ) where rownum = 1
    </select>
    <!-- 查询用户风控类型查看权限 -->
    <select id="selectQueryAuthRC" parameterType="java.lang.Long" resultType="java.lang.String">
        select distinct r.perm_id from pas_role_user t
        left join pas_role_perm r on t.role_id = r.role_id
        inner join pas_perm p on r.perm_id = p.perm_id and p.parent_id = 80801
        where t.user_id = #{userId}
    </select>

    <select id="selectPayMethodByBusinessCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT pay_method_code
        FROM cum_business_paymethod
        WHERE business_code = #{businessCode,jdbcType=VARCHAR}
    </select>

    <!-- txs订单定义 -->
    <resultMap id="TxsOrderResultMap" type="com.epaylinks.efps.rc.domain.txs.TxsPreOrder" >
        <id column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
        <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
        <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
        <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode"/>
    </resultMap>
    <sql id="TxsOrder_Column_List" >
        ORDER_ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE
    </sql>
    <select id="queryTxsOrderByChannelOrder" parameterType="java.lang.String" resultMap="TxsOrderResultMap" >
        select 
        <include refid="TxsOrder_Column_List" />
        from txs_pre_order t where t.channel_order = #{channelOrder,jdbcType=VARCHAR}
        and t.create_time > sysdate - 1
    </select>

         
    <!-- cust商户定义 -->
    <resultMap id="CustomerResultMap" type="com.epaylinks.efps.rc.domain.cust.Customer" >
        <id column="CUSTOMER_ID" property="customerId" jdbcType="DECIMAL" />
        <result column="PARENT_CUSTOMER_ID" property="parentCustomerId" jdbcType="DECIMAL" />
        <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR" />
        <result column="NAME" property="name" jdbcType="VARCHAR" />
        <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR" />
        <result column="PHONE" property="phone" jdbcType="VARCHAR" />
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
        <result column="EMAIL" property="email" jdbcType="VARCHAR" />
        <result column="REGISTER_TIME" property="registerTime" jdbcType="TIMESTAMP" />
        <result column="SOURCE_CHANNEL" property="sourceChannel" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="DECIMAL" />
        <result column="TYPE" property="type" jdbcType="DECIMAL" />
        <result column="CUST_LEVEL" property="custLevel" jdbcType="DECIMAL" />
        <result column="IS_OWN" property="isOwn" jdbcType="DECIMAL" />
        <result column="IS_AUTHED" property="isAuthed" jdbcType="DECIMAL" />
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR" />
        <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR" />
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR" />
        <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
        <result column="VERSION" property="version" jdbcType="DECIMAL" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
        <result column="CUSTOMER_UID" property="customerUid" jdbcType="VARCHAR" />
        <result column="SERVICE_CUSTOMER_NO" property="serviceCustomerNo" jdbcType="VARCHAR" />
        <result column="CATEGORY" property="category" jdbcType="DECIMAL" />
        <result column="SIGN_MODE" property="signMode" jdbcType="VARCHAR" />
        <result column="BILL_MODE" property="billMode" jdbcType="VARCHAR" />
        <result column="CHANNEL_GROUP_ID" property="channelGroupId" jdbcType="VARCHAR" />
        <result column="PLAT_CUSTOMER_NO" property="platCustomerNo" jdbcType="VARCHAR" />
        <result column="OPEN_ACCOUNT" property="openAccount" jdbcType="DECIMAL" />
        <result column="ACCEPT_ORDER" property="acceptOrder" jdbcType="DECIMAL" />
        <result column="TERMINAL_CODE" property="terminalCode" jdbcType="VARCHAR" />
        <result column="MCC" property="mcc" jdbcType="VARCHAR" />
        <result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL" />
        <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
        <result column="RC_STATUS" property="rcStatus" jdbcType="VARCHAR" />
        <result column="BUDGET_GROUP" property="budgetGroup" jdbcType="VARCHAR" />
        <result column="TEMPORARY_STATUS" property="temporaryStatus" jdbcType="VARCHAR" />
        <result column="TEMPORARY_STATUS_REASON" property="temporaryStatusReason" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Customer_Column_List" >
        CUSTOMER_ID, PARENT_CUSTOMER_ID, CUSTOMER_NO, NAME, SHORT_NAME, PHONE, MOBILE, EMAIL, 
        REGISTER_TIME, SOURCE_CHANNEL, STATUS, TYPE, CUST_LEVEL, IS_OWN, IS_AUTHED, PROVINCE_CODE, 
        CITY_CODE, DISTRICT_CODE, ADDRESS, VERSION, CREATE_TIME, UPDATE_TIME, USER_ID, CUSTOMER_UID, 
        SERVICE_CUSTOMER_NO, CATEGORY, SIGN_MODE, BILL_MODE, CHANNEL_GROUP_ID, PLAT_CUSTOMER_NO, 
        OPEN_ACCOUNT, ACCEPT_ORDER, TERMINAL_CODE, MCC, BUSINESS_MAN_ID, COMPANY_ID, RC_STATUS, 
        BUDGET_GROUP
    </sql>
    <!--通过customerNo查找Customer-->
    <select id="queryCustomerByCustomerNo" resultMap="CustomerResultMap" parameterType="java.lang.String">
        select
        <include refid="Customer_Column_List" />
        from CUST_CUSTOMER where CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    </select>
    <select id="queryCustomerByCustomerId" resultMap="CustomerResultMap" parameterType="java.lang.Long">
        select
        <include refid="Customer_Column_List" />
        from CUST_CUSTOMER where CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
    </select>
    <select id="queryCustomerDraftByCustomerNo" resultMap="CustomerResultMap" parameterType="java.lang.String">
        select
        CUSTOMER_ID customerId,
        customer_no customerNo,
        SERVICE_CUSTOMER_NO serviceCustomerNo,
        PLAT_CUSTOMER_NO platCustomerNo,
        NAME name,
        CATEGORY category,
        TEMPORARY_STATUS temporaryStatus,
        TEMPORARY_STATUS_REASON temporaryStatusReason
        from cust_customer_draft where CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    </select>

    <select id="queryCustomerDraftByCustomerNoList" resultType="java.lang.Integer" parameterType="java.util.List">
        select
            count(*)
        from cust_customer_draft
        where CUSTOMER_NO in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            <if test="(index % 999) == 998">
                NULL) OR CUSTOMER_NO IN(
            </if>
            #{item}
        </foreach>
    </select>
    
    <select id="selectParamValueByTypeAndName" resultType="java.lang.String"  >
        select 
        PARAM_VALUE
        from CUST_STATIC_PARAM
        where PARAM_TYPE = #{paramType,jdbcType=VARCHAR}
        and PARAM_NAME = #{paramName,jdbcType=VARCHAR}
    </select>

    <!-- 根据bwId删除记录 -->
    <delete id="deleteByBwId" parameterType="java.lang.Long" >
        delete from CUST_PIECES_BW_LIST
        where id = #{id,jdbcType=DECIMAL}
    </delete>
    <!-- 更新手机白名单表审核状态 -->
    <update id="updateAuditStatusByBwId" >
        update CUST_PIECES_BW_LIST
        set audit_status = #{auditStatus,jdbcType=DECIMAL}
        where id = #{id,jdbcType=DECIMAL}
    </update>
    <!-- 更新手机白名单记录 -->
    <update id="updateAuditByBwId" parameterType="java.util.Map">
        update CUST_PIECES_BW_LIST
        <set>
            <if test="type != null" >
                TYPE = #{type,jdbcType=VARCHAR},
            </if>
            <if test="bwValue != null" >
                BW_VALUE = #{bwValue,jdbcType=VARCHAR},
            </if>
            <if test="bwType != null" >
                BW_TYPE = #{bwType,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                STATUS = #{status,jdbcType=DECIMAL},
            </if>
            <if test="remark != null" >
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="phoneStatus != null" >
                phone_status = #{phoneStatus,jdbcType=DECIMAL},
            </if>
            <if test="auditStatus != null" >
                audit_status = #{auditStatus,jdbcType=DECIMAL},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>

    <select id="selectServiceFee" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*)
        from CUST_BUSINESS
        where CUSTOMER_ID = #{customerId}
        and BUSINESS_CODE = #{businessCode}
        and PAY_SERVICE_FEE is not null
    </select>

    <select id="selectCustomerByBusinessLicenseNo" parameterType="java.lang.String" resultType="java.lang.String">
        select t.customer_code from cum_customer_info t 
        where t.business_license_no = #{businessLicenseCode,jdbcType=VARCHAR}
    </select>

    <select id="selectCustomerByCertificateNo" parameterType="java.lang.String" resultType="java.lang.String">
        select t.customer_code from cum_customer_info t 
        where t.lea_personi_dentification_no = #{certificateNo,jdbcType=VARCHAR}
    </select>

    <select id="queryVerifyBusiness" parameterType="java.util.ArrayList" resultType="java.lang.String">
        select wm_concat(NAME) from cum_business
        where code in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectInnerBizType" resultType="java.lang.String">
        select INNER_BUSINESS_TYPE
        from CUST_CUSTOMER_DRAFT
        where CUSTOMER_NO = #{customerCode,jdbcType=VARCHAR}
    </select>

    <update id="updateDraftTemporary">
        update cust_customer_draft d
        set d.TEMPORARY_STATUS = #{temporaryStatus,jdbcType=VARCHAR},
            d.TEMPORARY_STATUS_REASON = #{temporaryStatusReason,jdbcType=VARCHAR}
        where d.CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    </update>

    <select id="selectBalance" resultType="long">
        select sum(AVAILABLEBALANCE + FLOATBALANCE) from ACC_ACCOUNT where CODE = 'JY-A' || #{customerCode,jdbcType=VARCHAR}
    </select>

    <select id="isChannelTransfer" resultType="long">
        select count(1)
        from TXS_WITHDRAW_TRADE_ORDER
        where bus_pdt_flag = '24'
        and TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>

    <select id="queryMccInfo" parameterType="java.lang.String" resultType="com.epaylinks.efps.rc.domain.cust.MccBusinessType">
        select
            t.MCC mcc,
            t.MCC_NAME mccName,
            t.BUSINESS business,
            t.BUSINESS_NAME businessName,
            t.CATEGORY category
        from cust_mcc_business_type t
        where t.mcc = #{mcc,jdbcType=VARCHAR}
    </select>

    <select id="queryDictBusinessType" parameterType="java.lang.String" resultType="java.lang.String">
        select t.NAME
        from CUST_DICT_BUSINESS_TYPE t
        where t.CODE = #{industry,jdbcType=VARCHAR}
    </select>

    <select id="queryRegistCapital" parameterType="java.lang.Long" resultType="java.lang.Long">
        select registered_capital
        from cust_customer_enterprise
        where customer_id = #{customerId,jdbcType=DECIMAL}
    </select>

    <select id="whetherOpenWithdraw" parameterType="java.lang.String" resultType="java.lang.Integer" >
        select count(*) from cust_customer c
        inner join cust_business b on c.customer_id = b.customer_id
            and b.business_code in ('Withdraw-AliPay','Withdraw-WeChat','Withdraw-CreditCard','Withdraw-SavingCard-Batch','Withdraw-CreditCard-Batch','DZ-Withdraw-SavingCard','DZ-Withdraw-CreditCard','DZ-Withdraw-SavingCard-Batch','DZ-Withdraw-CreditCard-Batch','Withdraw')
        where c.customer_no = #{customerNo,jdbcType=VARCHAR}
    </select>
    
    <select id="queryWithdrawParam" parameterType="java.lang.String" resultType="java.lang.String">
        select PARAM_VALUE
        from CUST_STATIC_PARAM
        where PARAM_TYPE = 'RC_PARAM'
        and PARAM_NAME = #{paramName,jdbcType=VARCHAR}
    </select>

    <select id="queryCertNoEnc" parameterType="java.lang.String" resultType="java.lang.String">
        select tt.certificatesno
        from (
                 select t.certificatesno from cum_quickpaycustomerinfo t
                 where t.BANKCARDNO = #{cardNoEnc,jdbcType=VARCHAR}
                 order by t.id desc
             )tt where rownum &lt;= 1
    </select>
    
    <select id="queryCardNoEnc" parameterType="java.lang.String" resultType="java.lang.String">
        select t.card_no_enc from txs_pay_trade_order t
        where t.transaction_no = #{orderNo,jdbcType=VARCHAR}
    </select>
    
    <select id="queryBusinessByIdAndCode" parameterType="java.util.Map" resultType="java.lang.Boolean">
        select
               count(1)
        from cust_business b
        where  b.customer_id = #{customerId,jdbcType=DECIMAL}
          and b.business_code in
            <foreach collection="businessCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
          and b.state = 1
          and (b.audit_status is null or b.audit_status = '4')
    </select>
</mapper>
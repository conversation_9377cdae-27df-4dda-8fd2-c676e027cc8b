<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcAuditRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcAuditRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TARGET_TYPE" property="targetType" jdbcType="VARCHAR" />
    <result column="TARGET_ID" property="targetId" jdbcType="DECIMAL" />
    <result column="OLD_VALUE" property="oldValue" jdbcType="VARCHAR" />
    <result column="NEW_VALUE" property="newValue" jdbcType="VARCHAR" />
    <result column="ACTION_TYPE" property="actionType" jdbcType="VARCHAR" />
    <result column="REASON" property="reason" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="OPER_ID" property="operId" jdbcType="DECIMAL" />
    <result column="AUDIT_RESULT" property="auditResult" jdbcType="DECIMAL" />
    <result column="AUDIT_OPER_ID" property="auditOperId" jdbcType="DECIMAL" />
    <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
      <result column="UNIQUED" property="uniqued" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TARGET_TYPE, TARGET_ID, OLD_VALUE, NEW_VALUE, ACTION_TYPE, REASON, CREATE_TIME, 
    OPER_ID, AUDIT_RESULT, AUDIT_OPER_ID, AUDIT_TIME, REMARKS,UNIQUED
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_AUDIT_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_AUDIT_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcAuditRecord" >
    insert into RC_AUDIT_RECORD (ID, TARGET_TYPE, TARGET_ID, 
      OLD_VALUE, NEW_VALUE, ACTION_TYPE, 
      REASON, CREATE_TIME, OPER_ID, 
      AUDIT_RESULT, AUDIT_OPER_ID, AUDIT_TIME, 
      REMARKS,UNIQUED)
    values (#{id,jdbcType=DECIMAL}, #{targetType,jdbcType=VARCHAR}, #{targetId,jdbcType=DECIMAL}, 
      #{oldValue,jdbcType=VARCHAR}, #{newValue,jdbcType=VARCHAR}, #{actionType,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{operId,jdbcType=DECIMAL}, 
      #{auditResult,jdbcType=DECIMAL}, #{auditOperId,jdbcType=DECIMAL}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{remarks,jdbcType=VARCHAR},#{uniqued,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcAuditRecord" >
    insert into RC_AUDIT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="targetType != null" >
        TARGET_TYPE,
      </if>
      <if test="targetId != null" >
        TARGET_ID,
      </if>
      <if test="oldValue != null" >
        OLD_VALUE,
      </if>
      <if test="newValue != null" >
        NEW_VALUE,
      </if>
      <if test="actionType != null" >
        ACTION_TYPE,
      </if>
      <if test="reason != null" >
        REASON,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="operId != null" >
        OPER_ID,
      </if>
      <if test="auditResult != null" >
        AUDIT_RESULT,
      </if>
      <if test="auditOperId != null" >
        AUDIT_OPER_ID,
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
      <if test="uniqued != null" >
        UNIQUED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="targetType != null" >
        #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null" >
        #{targetId,jdbcType=DECIMAL},
      </if>
      <if test="oldValue != null" >
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null" >
        #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null" >
        #{actionType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operId != null" >
        #{operId,jdbcType=DECIMAL},
      </if>
      <if test="auditResult != null" >
        #{auditResult,jdbcType=DECIMAL},
      </if>
      <if test="auditOperId != null" >
        #{auditOperId,jdbcType=DECIMAL},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="uniqued != null" >
        #{uniqued,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcAuditRecord" >
    update RC_AUDIT_RECORD
    <set >
      <if test="targetType != null" >
        TARGET_TYPE = #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null" >
        TARGET_ID = #{targetId,jdbcType=DECIMAL},
      </if>
      <if test="oldValue != null" >
        OLD_VALUE = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null" >
        NEW_VALUE = #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null" >
        ACTION_TYPE = #{actionType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null" >
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operId != null" >
        OPER_ID = #{operId,jdbcType=DECIMAL},
      </if>
      <if test="auditResult != null" >
        AUDIT_RESULT = #{auditResult,jdbcType=DECIMAL},
      </if>
      <if test="auditOperId != null" >
        AUDIT_OPER_ID = #{auditOperId,jdbcType=DECIMAL},
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="uniqued != null" >
        UNIQUED = #{uniqued,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcAuditRecord" >
    update RC_AUDIT_RECORD
    set TARGET_TYPE = #{targetType,jdbcType=VARCHAR},
      TARGET_ID = #{targetId,jdbcType=DECIMAL},
      OLD_VALUE = #{oldValue,jdbcType=VARCHAR},
      NEW_VALUE = #{newValue,jdbcType=VARCHAR},
      ACTION_TYPE = #{actionType,jdbcType=VARCHAR},
      REASON = #{reason,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      OPER_ID = #{operId,jdbcType=DECIMAL},
      AUDIT_RESULT = #{auditResult,jdbcType=DECIMAL},
      AUDIT_OPER_ID = #{auditOperId,jdbcType=DECIMAL},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      UNIQUED = #{uniqued,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  
  <select id="queryByTargetTypeAndId" resultMap="BaseResultMap">
      select * from(
            select 
            <include refid="Base_Column_List" />
            from RC_AUDIT_RECORD
            where TARGET_TYPE = #{targetType,jdbcType=VARCHAR}
            and TARGET_ID = #{targetId,jdbcType=DECIMAL}
            order by CREATE_TIME desc
      ) where rownum = 1
  </select>

    <select id="queryListByTargetTypeAndId" resultType="java.lang.Long">
        select
            ID
        from RC_AUDIT_RECORD
        where TARGET_TYPE = #{targetType,jdbcType=VARCHAR}
        and TARGET_ID = #{targetId,jdbcType=DECIMAL}
        and AUDIT_RESULT = 0
        and ID != #{id,jdbcType=DECIMAL}
        order by CREATE_TIME desc
    </select>

    <select id="queryHistoryAuditRecord" resultMap="BaseResultMap">
        select * from(
        select
        <include refid="Base_Column_List" />
        from RC_AUDIT_RECORD
        where TARGET_TYPE = #{targetType,jdbcType=VARCHAR}
        and TARGET_ID = #{targetId,jdbcType=DECIMAL}
        and to_char(${field},'yyyy-MM-dd hh24:mi:ss') = #{auditTime}
        order by id desc
        ) where rownum = 1
    </select>

    <sql id="pageConditions">
        <if test="targetTypeList != null and targetTypeList.size() != 0">
            and target_type in
            <foreach collection="targetTypeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="targetTypeList == null or targetTypeList.size() == 0">
            and 1 = 2
        </if>
        <if test="startTime != null and startTime != ''">
            AND create_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="targetType != null and targetType != ''">
            AND target_type = #{targetType,jdbcType=VARCHAR}
        </if>
        <if test="targetId != null and targetId !=''">
            AND target_id = #{targetId,jdbcType=DECIMAL}
        </if>
        <if test="audStatus != null">
            AND audit_result = #{audStatus,jdbcType=DECIMAL}
        </if>
        <if test="customerCode != null and customerCode != ''">
            AND  (
                EXISTS (
                    SELECT 1 FROM rc_archive r WHERE
                    ( r.archive_code like '%'|| #{customerCode,jdbcType=VARCHAR} ||'%' OR
                    r.archive_name like '%'|| #{customerCode,jdbcType=VARCHAR} ||'%')
                    AND r.archive_id = t.target_id
                )
                or
                EXISTS (
                    select 1 from cust_customer c
                    where c.customer_id = t.target_id
                    and c.customer_no = #{customerCode,jdbcType=VARCHAR}
                    and t.target_type = 'TRANSACTION'
                )
            )
        </if>
        <if test="userCompanyId != null">
            AND ra.ARCHIVE_CODE in (
                select c.customer_no from cust_customer c where c.company_id in (
                    select company_id from pas_company
                    start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
                    connect by parent_id = prior company_id
                )
            )
        </if>
        <if test="businessManId != null">
            AND ra.ARCHIVE_CODE in (
                select c.customer_no from cust_customer c where c.business_man_id = #{businessManId,jdbcType=DECIMAL}
            )
        </if>
        <if test="companyIds != null">
            and exists (
                select 1 from cust_customer_draft cd
                where ra.ARCHIVE_CODE = cd.CUSTOMER_NO
                and cd.company_id in
                <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
        </if>
    </sql>

    <select id="countByParam" resultType="int" parameterType="java.util.Map">
        SELECT COUNT(1) FROM RC_AUDIT_RECORD t
        LEFT JOIN rc_archive ra on ra.ARCHIVE_ID = t.TARGET_ID
        <where>
            <include refid="pageConditions" />
        </where>

    </select>

    <select id="listByParam" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT * FROM(
            SELECT a.*,rownum rn FROM (
                SELECT
                <include refid="Base_Column_List" />
                FROM RC_AUDIT_RECORD t
                LEFT JOIN rc_archive ra on ra.ARCHIVE_ID = t.TARGET_ID
                <where>
                    <include refid="pageConditions" />
                </where>
                ORDER BY  t.AUDIT_TIME DESC nulls first,t.ID DESC
            )a WHERE rownum &lt;= #{endNum,jdbcType=DECIMAL}
        ) WHERE rn &gt;= #{startNum,jdbcType=DECIMAL}
    </select>
  
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcLimitMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcLimit" >
    <id column="LIMIT_ID" property="limitId" jdbcType="DECIMAL" />
    <result column="DEFINE_CODE" property="defineCode" jdbcType="VARCHAR" />
    <result column="BUSINNES_TYPE" property="businnesType" jdbcType="VARCHAR" />
    <result column="BUSINESS_TAGER_TYPE" property="businessTagerType" jdbcType="VARCHAR" />
    <result column="BUSINESS_TAGER_ID" property="businessTagerId" jdbcType="VARCHAR" />
    <result column="LIMIT_VALUE" property="limitValue" jdbcType="VARCHAR" />
    <result column="LIMIT_TYPE" property="limitType" jdbcType="VARCHAR" />
    <result column="LIMIT_LEVEL" property="limitLevel" jdbcType="CHAR" />
    <result column="LIMIT_VERSION" property="limitVersion" jdbcType="VARCHAR" />
    <result column="START_TIME" property="startTime" jdbcType="TIMESTAMP" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="EFFECT_VALUE" property="effectValue" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="DEFINE_ID" property="defineId" jdbcType="DECIMAL" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LIMIT_ID, DEFINE_CODE, BUSINNES_TYPE, BUSINESS_TAGER_TYPE, BUSINESS_TAGER_ID, LIMIT_VALUE,
    LIMIT_TYPE, LIMIT_LEVEL, LIMIT_VERSION, START_TIME, END_TIME, EFFECT_VALUE, USER_ID,
    USER_NAME, CREATE_TIME, DEFINE_ID, UNIT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from RC_LIMIT
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_LIMIT
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcLimit" >
    insert into RC_LIMIT (LIMIT_ID, DEFINE_CODE, BUSINNES_TYPE,
    BUSINESS_TAGER_TYPE, BUSINESS_TAGER_ID, LIMIT_VALUE,
    LIMIT_TYPE, LIMIT_LEVEL, LIMIT_VERSION,
    START_TIME, END_TIME, EFFECT_VALUE,
    USER_ID, USER_NAME, CREATE_TIME,
    DEFINE_ID, UNIT)
    values (#{limitId,jdbcType=DECIMAL}, #{defineCode,jdbcType=VARCHAR}, #{businnesType,jdbcType=VARCHAR},
    #{businessTagerType,jdbcType=VARCHAR}, #{businessTagerId,jdbcType=VARCHAR}, #{limitValue,jdbcType=VARCHAR},
    #{limitType,jdbcType=VARCHAR}, #{limitLevel,jdbcType=CHAR}, #{limitVersion,jdbcType=VARCHAR},
    #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{effectValue,jdbcType=VARCHAR},
    #{userId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{defineId,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcLimit" >
    insert into RC_LIMIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="limitId != null" >
        LIMIT_ID,
      </if>
      <if test="defineCode != null" >
        DEFINE_CODE,
      </if>
      <if test="businnesType != null" >
        BUSINNES_TYPE,
      </if>
      <if test="businessTagerType != null" >
        BUSINESS_TAGER_TYPE,
      </if>
      <if test="businessTagerId != null" >
        BUSINESS_TAGER_ID,
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE,
      </if>
      <if test="limitType != null" >
        LIMIT_TYPE,
      </if>
      <if test="limitLevel != null" >
        LIMIT_LEVEL,
      </if>
      <if test="limitVersion != null" >
        LIMIT_VERSION,
      </if>
      <if test="startTime != null" >
        START_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="effectValue != null" >
        EFFECT_VALUE,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="defineId != null" >
        DEFINE_ID,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="limitId != null" >
        #{limitId,jdbcType=DECIMAL},
      </if>
      <if test="defineCode != null" >
        #{defineCode,jdbcType=VARCHAR},
      </if>
      <if test="businnesType != null" >
        #{businnesType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerType != null" >
        #{businessTagerType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerId != null" >
        #{businessTagerId,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="limitType != null" >
        #{limitType,jdbcType=VARCHAR},
      </if>
      <if test="limitLevel != null" >
        #{limitLevel,jdbcType=CHAR},
      </if>
      <if test="limitVersion != null" >
        #{limitVersion,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectValue != null" >
        #{effectValue,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="defineId != null" >
        #{defineId,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcLimit" >
    update RC_LIMIT
    <set >
      <if test="defineCode != null" >
        DEFINE_CODE = #{defineCode,jdbcType=VARCHAR},
      </if>
      <if test="businnesType != null" >
        BUSINNES_TYPE = #{businnesType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerType != null" >
        BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR},
      </if>
      <if test="businessTagerId != null" >
        BUSINESS_TAGER_ID = #{businessTagerId,jdbcType=VARCHAR},
      </if>
      <if test="limitValue != null" >
        LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
      </if>
      <if test="limitType != null" >
        LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
      </if>
      <if test="limitLevel != null" >
        LIMIT_LEVEL = #{limitLevel,jdbcType=CHAR},
      </if>
      <if test="limitVersion != null" >
        LIMIT_VERSION = #{limitVersion,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectValue != null" >
        EFFECT_VALUE = #{effectValue,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="defineId != null" >
        DEFINE_ID = #{defineId,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
    </set>
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcLimit" >
    update RC_LIMIT
    set DEFINE_CODE = #{defineCode,jdbcType=VARCHAR},
    BUSINNES_TYPE = #{businnesType,jdbcType=VARCHAR},
    BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR},
    BUSINESS_TAGER_ID = #{businessTagerId,jdbcType=VARCHAR},
    LIMIT_VALUE = #{limitValue,jdbcType=VARCHAR},
    LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
    LIMIT_LEVEL = #{limitLevel,jdbcType=CHAR},
    LIMIT_VERSION = #{limitVersion,jdbcType=VARCHAR},
    START_TIME = #{startTime,jdbcType=TIMESTAMP},
    END_TIME = #{endTime,jdbcType=TIMESTAMP},
    EFFECT_VALUE = #{effectValue,jdbcType=VARCHAR},
    USER_ID = #{userId,jdbcType=DECIMAL},
    USER_NAME = #{userName,jdbcType=VARCHAR},
    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
    DEFINE_ID = #{defineId,jdbcType=DECIMAL},
    UNIT = #{unit,jdbcType=VARCHAR}
    where LIMIT_ID = #{limitId,jdbcType=DECIMAL}
  </update>
  <select id="queryAmountLimitMap" resultType="java.util.HashMap">
    SELECT
    DEFINE_CODE,LIMIT_VALUE
    FROM RC_LIMIT
    WHERE
    BUSINESS_TAGER_ID = #{code,jdbcType=VARCHAR}
    AND BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
    AND DEFINE_ID in(
      SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
      SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1'))

  </select>

  <select id="queryAddAmountLimitMap" resultType="java.util.HashMap">
    SELECT T.DEFINE_CODE,T.LIMIT_VALUE FROM RC_LIMIT T
    WHERE T.BUSINESS_TAGER_ID = #{level,jdbcType=VARCHAR}
      AND T.BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
      AND T.DEFINE_ID IN(
        SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
            SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1'
        )
      )
      AND T.DEFINE_CODE NOT IN (
        SELECT C.DEFINE_CODE FROM RC_LIMIT C
        WHERE C.BUSINESS_TAGER_ID = #{code,jdbcType=VARCHAR}
        AND C.BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
        AND C.DEFINE_ID in(
          SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
            SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1')
        )
      )
  </select>

  <select id="queryAddIndustryAmountLimitMap" resultType="java.util.HashMap">
    SELECT T.DEFINE_CODE,T.LIMIT_VALUE FROM RC_LIMIT T
    WHERE T.BUSINESS_TAGER_ID = #{level,jdbcType=VARCHAR}
      AND T.BUSINESS_TAGER_TYPE = '013'
      AND T.DEFINE_ID IN(
      SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
        SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1'
      )
    )
      AND T.DEFINE_CODE NOT IN (
      SELECT C.DEFINE_CODE FROM RC_LIMIT C
      WHERE C.BUSINESS_TAGER_ID = #{code,jdbcType=VARCHAR}
        AND C.BUSINESS_TAGER_TYPE = #{businessTagerType,jdbcType=VARCHAR}
        AND C.DEFINE_ID in(
        SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
          SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1')
      )
    )
  </select>

  <select id="queryLimit" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM RC_LIMIT
      WHERE DEFINE_ID = #{defineId} AND BUSINESS_TAGER_ID = #{businessTagerId}
      AND BUSINESS_TAGER_TYPE = #{businessTagerType}
  </select>

   <select id="totalLimitByCustomer" parameterType="String" resultType="int">
       SELECT COUNT(LIMIT_ID) FROM RC_LIMIT WHERE BUSINESS_TAGER_ID = #{customerCode}
   </select>
   
   <select id="totalLimit" parameterType="String" resultType="int">
       SELECT COUNT(LIMIT_ID) FROM RC_LIMIT
   </select>
   
    <select id="pageLimitByCustomer" resultType="java.util.HashMap">
        SELECT * FROM (
            SELECT a.* ,rownum rn FROM (
                SELECT
                c.DEFINE_IDENTIFIER AS "identifier",c.ALIAS AS "name",c.REMARK as "remark" ,b.LIMIT_VALUE as "limitValue",b.LIMIT_TYPE AS "type"
                FROM RC_LIMIT b
                INNER JOIN RC_DEFINE c
                ON b.DEFINE_ID = c.DEFINE_ID
                WHERE BUSINESS_TAGER_ID = #{customerCode}
                ORDER BY b.LIMIT_ID DESC
            )a WHERE rownum &lt;= #{endNum}
        )WHERE rn &gt;= #{startNum}
    </select>
    
    <select id="pageLimit"  resultMap="BaseResultMap"><!-- 初始化风控指标，分页顺序排序处理 -->
        SELECT * FROM (
            SELECT a.* ,rownum rn FROM (
                SELECT
                <include refid="Base_Column_List" />
                FROM RC_LIMIT b
                ORDER BY b.LIMIT_ID ASC
            )a WHERE rownum &lt;= #{endNum}
        )WHERE rn &gt;= #{startNum}
    </select>

  <select id="queryAllLimit" parameterType="String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM RC_LIMIT
    WHERE BUSINESS_TAGER_ID = #{customerCode}
  </select>

  <select id="queryAmountLimits" parameterType="String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM RC_LIMIT
    WHERE
    BUSINESS_TAGER_ID = #{levelCode}
    AND DEFINE_ID in(
      SELECT DEFINE_ID FROM RC_DEFINE WHERE GROUP_ID IN (
      SELECT GROUP_ID FROM RC_DEFINE_GROUP WHERE PARENT_ID = '1'))
  </select>

<select id="selectALL" resultMap="BaseResultMap">
  SELECT
  <include refid="Base_Column_List"/>
  FROM RC_LIMIT
</select>

  <delete id="deleteLevelLimit" parameterType="String">
    delete rc_limit t
    where t.define_id not in ('8','9','10','16')
    and t.business_tager_id = #{businessTagerId}
  </delete>
  
  
  <select id="listByBusinessTypeAndTagerId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM RC_LIMIT
    WHERE
        BUSINNES_TYPE = #{businessType,jdbcType=VARCHAR}
        AND BUSINESS_TAGER_ID = #{businessTagerId,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-01-->
  <select id="selectAllByBusinnesTypeAndBusinessTagerTypeAndBusinessTagerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RC_LIMIT
        where BUSINNES_TYPE=#{businnesType,jdbcType=VARCHAR} and
        BUSINESS_TAGER_TYPE=#{businessTagerType,jdbcType=VARCHAR} and
        BUSINESS_TAGER_ID=#{businessTagerId,jdbcType=VARCHAR}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.VerifyConfigMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.VerifyConfig" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR" />
    <result column="CHECK_BUSINESS" property="checkBusiness" jdbcType="VARCHAR" />
    <result column="FIRST_VERIFY_CHANNEL" property="firstVerifyChannel" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="VARCHAR" />
    <result column="USE_STATUS" property="useStatus" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_NO, CUSTOMER_NAME, CHECK_BUSINESS, FIRST_VERIFY_CHANNEL, REMARK, AUDIT_STATUS, 
    USE_STATUS, OPERATOR, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_VERIFY_CONFIG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_VERIFY_CONFIG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.VerifyConfig" >
    insert into RC_VERIFY_CONFIG (ID, CUSTOMER_NO, CUSTOMER_NAME, 
      CHECK_BUSINESS, FIRST_VERIFY_CHANNEL, REMARK, 
      AUDIT_STATUS, USE_STATUS, OPERATOR, 
      CREATE_TIME, UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{customerNo,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{checkBusiness,jdbcType=VARCHAR}, #{firstVerifyChannel,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=VARCHAR}, #{useStatus,jdbcType=VARCHAR}, #{operator,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.VerifyConfig" >
    insert into RC_VERIFY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO,
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME,
      </if>
      <if test="checkBusiness != null" >
        CHECK_BUSINESS,
      </if>
      <if test="firstVerifyChannel != null" >
        FIRST_VERIFY_CHANNEL,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS,
      </if>
      <if test="useStatus != null" >
        USE_STATUS,
      </if>
      <if test="operator != null" >
        OPERATOR,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerNo != null" >
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="checkBusiness != null" >
        #{checkBusiness,jdbcType=VARCHAR},
      </if>
      <if test="firstVerifyChannel != null" >
        #{firstVerifyChannel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="useStatus != null" >
        #{useStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.VerifyConfig" >
    update RC_VERIFY_CONFIG
    <set >
      <if test="customerNo != null" >
        CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="checkBusiness != null" >
        CHECK_BUSINESS = #{checkBusiness,jdbcType=VARCHAR},
      </if>
      <if test="firstVerifyChannel != null" >
        FIRST_VERIFY_CHANNEL = #{firstVerifyChannel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="useStatus != null" >
        USE_STATUS = #{useStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        OPERATOR = #{operator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.VerifyConfig" >
    update RC_VERIFY_CONFIG
    set CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      CHECK_BUSINESS = #{checkBusiness,jdbcType=VARCHAR},
      FIRST_VERIFY_CHANNEL = #{firstVerifyChannel,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      USE_STATUS = #{useStatus,jdbcType=VARCHAR},
      OPERATOR = #{operator,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="queryByCustomerNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from RC_VERIFY_CONFIG
    where CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
  </select>

  <select id="queryByCustomerNoValid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from RC_VERIFY_CONFIG
    where CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    and USE_STATUS = '1'
  </select>

  <select id="querySEQ" resultType="java.lang.Long">
    select SEQ_VERIFY_CONFIG.nextval from dual
  </select>

  <select id="pageQuery" parameterType="java.util.Map"
          resultType="com.epaylinks.efps.rc.controller.response.VerifyConfigResponse">
    select *
    from (
      select A.*,rownum rn
      from (
        select
        c.id,
        c.CUSTOMER_NO customerNo,
        c.CUSTOMER_NAME customerName,
        c.CHECK_BUSINESS checkBusiness,
        c.FIRST_VERIFY_CHANNEL firstVerifyChannel,
        c.REMARK,
        c.AUDIT_STATUS auditStatus,
    c.USE_STATUS useStatus,
        c.OPERATOR userId,
        u.REAL_NAME userName,
        to_char(c.UPDATE_TIME,'yyyy-MM-dd hh24:mi:ss') opeTime
        from RC_VERIFY_CONFIG c
        left join pas_user u on c.OPERATOR = u.user_id
        where 1=1
        <if test="customerNo != null and customerNo != ''">
          and CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
          and AUDIT_STATUS = #{auditStatus}
        </if>
        <if test="useStatus != null and useStatus != ''">
          and USE_STATUS = #{useStatus}
        </if>
        <if test="firstVerifyChannel != null and firstVerifyChannel != ''">
          and FIRST_VERIFY_CHANNEL = #{firstVerifyChannel}
        </if>
        <if test="checkBusiness != null and checkBusiness != ''">
          and ',' || CHECK_BUSINESS ||',' like '%,' || #{checkBusiness} || ',%'
        </if>
        <if test="startTime != null and  startTime != ''">
          <![CDATA[ and c.UPDATE_TIME >= TO_DATE(#{startTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="endTime != null and  endTime != ''">
          <![CDATA[ and c.UPDATE_TIME <= TO_DATE(#{endTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        order by id desc
      ) A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="count" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(*)
    from (
      select
      c.id,
      c.CUSTOMER_NO customerNo,
      c.CUSTOMER_NAME customerName,
      c.CHECK_BUSINESS checkBusiness,
      c.FIRST_VERIFY_CHANNEL firstVerifyChannel,
      c.REMARK,
      c.AUDIT_STATUS auditStatus,
      c.USE_STATUS useStatus,
      c.OPERATOR userId,
      u.REAL_NAME userName,
      to_char(c.UPDATE_TIME,'yyyy-MM-dd hh24:mi:ss') opeTime
      from RC_VERIFY_CONFIG c
      left join pas_user u on c.OPERATOR = u.user_id
      where 1=1
      <if test="customerNo != null and customerNo != ''">
        and CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
      </if>
      <if test="auditStatus != null and auditStatus != ''">
        and AUDIT_STATUS = #{auditStatus}
      </if>
      <if test="useStatus != null and useStatus != ''">
        and USE_STATUS = #{useStatus}
      </if>
      <if test="firstVerifyChannel != null and firstVerifyChannel != ''">
        and FIRST_VERIFY_CHANNEL = #{firstVerifyChannel}
      </if>
      <if test="checkBusiness != null and checkBusiness != ''">
        and ',' || CHECK_BUSINESS ||',' like '%,' || #{checkBusiness} || ',%'
      </if>
      <if test="startTime != null and  startTime != ''">
        <![CDATA[ and c.UPDATE_TIME >= TO_DATE(#{startTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
      </if>
      <if test="endTime != null and  endTime != ''">
        <![CDATA[ and c.UPDATE_TIME <= TO_DATE(#{endTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
      </if>
      order by id desc
    )
  </select>
</mapper>
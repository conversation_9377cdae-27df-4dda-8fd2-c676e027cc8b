<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcTxsOrderMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcTxsOrder" >
    <id column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <id column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <id column="BUSINESS_TARGET_IDS" property="businessTargetIds" jdbcType="VARCHAR" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="INDEXS" property="indexs" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="PAY_STATE" property="payState" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRANSACTION_NO, BUSINESS_TYPE, BUSINESS_TARGET_IDS, OUT_TRADE_NO, INDEXS, CREATE_TIME, 
    CUSTOMER_CODE, BUSINESS_CODE, AMOUNT, PAY_STATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrderKey" >
    select 
    <include refid="Base_Column_List" />
    from RC_TXS_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
      and BUSINESS_TARGET_IDS = #{businessTargetIds,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrderKey" >
    delete from RC_TXS_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
      and BUSINESS_TARGET_IDS = #{businessTargetIds,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrder" >
    insert into RC_TXS_ORDER (TRANSACTION_NO, BUSINESS_TYPE, BUSINESS_TARGET_IDS, 
      OUT_TRADE_NO, INDEXS, CREATE_TIME, 
      CUSTOMER_CODE, BUSINESS_CODE, AMOUNT, 
      PAY_STATE)
    values (#{transactionNo,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{businessTargetIds,jdbcType=VARCHAR}, 
      #{outTradeNo,jdbcType=VARCHAR}, #{indexs,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{customerCode,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{payState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrder" >
    insert into RC_TXS_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="businessTargetIds != null" >
        BUSINESS_TARGET_IDS,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="indexs != null" >
        INDEXS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="payState != null" >
        PAY_STATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetIds != null" >
        #{businessTargetIds,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="indexs != null" >
        #{indexs,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="payState != null" >
        #{payState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrder" >
    update RC_TXS_ORDER
    <set >
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="indexs != null" >
        INDEXS = #{indexs,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="payState != null" >
        PAY_STATE = #{payState,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
      and BUSINESS_TARGET_IDS = #{businessTargetIds,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcTxsOrder" >
    update RC_TXS_ORDER
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      INDEXS = #{indexs,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PAY_STATE = #{payState,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
      and BUSINESS_TARGET_IDS = #{businessTargetIds,jdbcType=VARCHAR}
  </update>
  
  
  <select id="queryTxsCustomerCodeList" resultType="java.lang.String"  >
    select distinct(customer_code) from rc_txs_order t
    where t.create_time >= to_date(#{dateStr,jdbcType=VARCHAR}, 'yyyymmdd')

  </select>

  <select id="queryTxsPlatCustomerCodeList" resultType="java.lang.String"  >
    select distinct(c.PLAT_CUSTOMER_NO) from rc_txs_order t,cust_customer c
    where t.create_time >= to_date(#{dateStr,jdbcType=VARCHAR}, 'yyyymmdd')
    and  t.CUSTOMER_CODE = c.CUSTOMER_NO and c.PLAT_CUSTOMER_NO is not null
  </select>

<!--auto generated by MybatisCodeHelper on 2022-10-11-->
  <select id="findOneByTransactionNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from RC_TXS_ORDER
    where TRANSACTION_NO=#{transactionNo,jdbcType=VARCHAR}
  </select>
</mapper>
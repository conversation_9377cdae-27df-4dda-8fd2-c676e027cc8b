<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcCalculateLogMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcCalculateLog" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="LOG_IDENTIFIER" property="logIdentifier" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="BUSINESS_TARGET_TYPE" property="businessTargetType" jdbcType="VARCHAR" />
    <result column="BUSINESS_TARGET_ID" property="businessTargetId" jdbcType="VARCHAR" />
    <result column="BUSINESS_TARGET_NAME" property="businessTargetName" jdbcType="VARCHAR" />
    <result column="RC_LIMIT_TYPE" property="rcLimitType" jdbcType="VARCHAR" />
    <result column="RC_LIMIT_DEFINE_CODE" property="rcLimitDefineCode" jdbcType="VARCHAR" />
    <result column="RC_LIMIT_VALUE" property="rcLimitValue" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="TRANSACTION_VALUT" property="transactionValut" jdbcType="VARCHAR" />
    <result column="CUMULATIVE_VALUE" property="cumulativeValue" jdbcType="VARCHAR" />
    <result column="RC_RESULT" property="rcResult" jdbcType="VARCHAR" />
    <result column="RC_MESSAGE" property="rcMessage" jdbcType="VARCHAR" />
    <result column="LIMIT_UNIT" property="limitUnit" jdbcType="VARCHAR" />
    <result column="MESSAGE_ENCRYPT" property="messageEncrypt" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, LOG_IDENTIFIER, CREATE_TIME, BUSINESS_TARGET_TYPE, BUSINESS_TARGET_ID, BUSINESS_TARGET_NAME, 
    RC_LIMIT_TYPE, RC_LIMIT_DEFINE_CODE, RC_LIMIT_VALUE, TRANSACTION_NO, TRANSACTION_VALUT, 
    CUMULATIVE_VALUE, RC_RESULT, RC_MESSAGE, LIMIT_UNIT,MESSAGE_ENCRYPT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from RC_CALCULATE_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from RC_CALCULATE_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcCalculateLog" >
    insert into RC_CALCULATE_LOG (ID, LOG_IDENTIFIER, CREATE_TIME, 
      BUSINESS_TARGET_TYPE, BUSINESS_TARGET_ID, BUSINESS_TARGET_NAME, 
      RC_LIMIT_TYPE, RC_LIMIT_DEFINE_CODE, RC_LIMIT_VALUE, 
      TRANSACTION_NO, TRANSACTION_VALUT, CUMULATIVE_VALUE, 
      RC_RESULT, RC_MESSAGE, LIMIT_UNIT,MESSAGE_ENCRYPT
      )
    values (#{id,jdbcType=DECIMAL}, #{logIdentifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{businessTargetType,jdbcType=VARCHAR}, #{businessTargetId,jdbcType=VARCHAR}, #{businessTargetName,jdbcType=VARCHAR}, 
      #{rcLimitType,jdbcType=VARCHAR}, #{rcLimitDefineCode,jdbcType=VARCHAR}, #{rcLimitValue,jdbcType=VARCHAR},
      #{transactionNo,jdbcType=VARCHAR}, #{transactionValut,jdbcType=VARCHAR}, #{cumulativeValue,jdbcType=VARCHAR}, 
      #{rcResult,jdbcType=VARCHAR}, #{rcMessage,jdbcType=VARCHAR}, #{limitUnit,jdbcType=VARCHAR}, #{messageEncrypt,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcCalculateLog" >
    insert into RC_CALCULATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="logIdentifier != null" >
        LOG_IDENTIFIER,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="businessTargetType != null" >
        BUSINESS_TARGET_TYPE,
      </if>
      <if test="businessTargetId != null" >
        BUSINESS_TARGET_ID,
      </if>
      <if test="businessTargetName != null" >
        BUSINESS_TARGET_NAME,
      </if>
      <if test="rcLimitType != null" >
        RC_LIMIT_TYPE,
      </if>
      <if test="rcLimitDefineCode != null" >
        RC_LIMIT_DEFINE_CODE,
      </if>
      <if test="rcLimitValue != null" >
        RC_LIMIT_VALUE,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="transactionValut != null" >
        TRANSACTION_VALUT,
      </if>
      <if test="cumulativeValue != null" >
        CUMULATIVE_VALUE,
      </if>
      <if test="rcResult != null" >
        RC_RESULT,
      </if>
      <if test="rcMessage != null" >
        RC_MESSAGE,
      </if>
      <if test="limitUnit != null" >
        LIMIT_UNIT,
      </if>
      <if test="messageEncrypt != null">
        MESSAGE_ENCRYPT
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="logIdentifier != null" >
        #{logIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessTargetType != null" >
        #{businessTargetType,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetId != null" >
        #{businessTargetId,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetName != null" >
        #{businessTargetName,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitType != null" >
        #{rcLimitType,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitDefineCode != null" >
        #{rcLimitDefineCode,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitValue != null" >
        #{rcLimitValue,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionValut != null" >
        #{transactionValut,jdbcType=VARCHAR},
      </if>
      <if test="cumulativeValue != null" >
        #{cumulativeValue,jdbcType=VARCHAR},
      </if>
      <if test="rcResult != null" >
        #{rcResult,jdbcType=VARCHAR},
      </if>
      <if test="rcMessage != null" >
        #{rcMessage,jdbcType=VARCHAR},
      </if>
      <if test="limitUnit != null" >
        #{limitUnit,jdbcType=VARCHAR},
      </if>
      <if test="messageEncrypt != null">
        #{messageEncrypt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcCalculateLog" >
    update RC_CALCULATE_LOG
    <set >
      <if test="logIdentifier != null" >
        LOG_IDENTIFIER = #{logIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessTargetType != null" >
        BUSINESS_TARGET_TYPE = #{businessTargetType,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetId != null" >
        BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR},
      </if>
      <if test="businessTargetName != null" >
        BUSINESS_TARGET_NAME = #{businessTargetName,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitType != null" >
        RC_LIMIT_TYPE = #{rcLimitType,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitDefineCode != null" >
        RC_LIMIT_DEFINE_CODE = #{rcLimitDefineCode,jdbcType=VARCHAR},
      </if>
      <if test="rcLimitValue != null" >
        RC_LIMIT_VALUE = #{rcLimitValue,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionValut != null" >
        TRANSACTION_VALUT = #{transactionValut,jdbcType=VARCHAR},
      </if>
      <if test="cumulativeValue != null" >
        CUMULATIVE_VALUE = #{cumulativeValue,jdbcType=VARCHAR},
      </if>
      <if test="rcResult != null" >
        RC_RESULT = #{rcResult,jdbcType=VARCHAR},
      </if>
      <if test="rcMessage != null" >
        RC_MESSAGE = #{rcMessage,jdbcType=VARCHAR},
      </if>
      <if test="limitUnit != null" >
        LIMIT_UNIT = #{limitUnit,jdbcType=VARCHAR},
      </if>
      <if test="messageEncrypt != null">
        MESSAGE_ENCRYPT = #{messageEncrypt,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcCalculateLog" >
    update RC_CALCULATE_LOG
    set LOG_IDENTIFIER = #{logIdentifier,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      BUSINESS_TARGET_TYPE = #{businessTargetType,jdbcType=VARCHAR},
      BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR},
      BUSINESS_TARGET_NAME = #{businessTargetName,jdbcType=VARCHAR},
      RC_LIMIT_TYPE = #{rcLimitType,jdbcType=VARCHAR},
      RC_LIMIT_DEFINE_CODE = #{rcLimitDefineCode,jdbcType=VARCHAR},
      RC_LIMIT_VALUE = #{rcLimitValue,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      TRANSACTION_VALUT = #{transactionValut,jdbcType=VARCHAR},
      CUMULATIVE_VALUE = #{cumulativeValue,jdbcType=VARCHAR},
      RC_RESULT = #{rcResult,jdbcType=VARCHAR},
      RC_MESSAGE = #{rcMessage,jdbcType=VARCHAR},
      LIMIT_UNIT = #{limitUnit,jdbcType=VARCHAR},
    MESSAGE_ENCRYPT = #{messageEncrypt,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

 <select id="pageQueryCount" resultType="int">
   SELECT count (*) FROM RC_CALCULATE_LOG
   <where>
    <trim>
      <if test="targetTypeCode != null and targetTypeCode != ''">
         and BUSINESS_TARGET_TYPE = #{targetTypeCode,jdbcType = VARCHAR}
      </if>
      <if test="businessTargetId != null and businessTargetId != ''">
        and BUSINESS_TARGET_ID = #{businessTargetId,jdbcType = VARCHAR}
      </if>
      <if test="transactionNo != null and transactionNo != ''">
        and TRANSACTION_NO = #{transactionNo,jdbcType = VARCHAR}
      </if>
      <if test="limitTypeCode != null and limitTypeCode != ''">
        and RC_LIMIT_TYPE = #{limitTypeCode,jdbcType = VARCHAR}
      </if>
      <if test="startTime != null and startTime != ''">
        and CREATE_TIME &gt;= to_date(#{startTime},'yyyyMMddhh24miss')
      </if>
      <if test="endTime != null and endTime != ''">
        and CREATE_TIME &lt; to_date(#{endTime},'yyyyMMddhh24miss') + 1 / 86400
      </if>
    </trim>
   </where>
 </select>

<select id="pageQuery" resultMap="BaseResultMap">
  SELECT * FROM (
  SELECT a.*,rownum rn FROM (
  SELECT
  <include refid="Base_Column_List" />
  FROM RC_CALCULATE_LOG
  <where>
    <trim>
      <if test="targetTypeCode != null and targetTypeCode != ''">
        and BUSINESS_TARGET_TYPE = #{targetTypeCode,jdbcType = VARCHAR}
      </if>
      <if test="businessTargetId != null and businessTargetId != ''">
        and BUSINESS_TARGET_ID = #{businessTargetId,jdbcType = VARCHAR}
      </if>
      <if test="transactionNo != null and transactionNo != ''">
        and TRANSACTION_NO = #{transactionNo,jdbcType = VARCHAR}
      </if>
      <if test="limitTypeCode != null and limitTypeCode != ''">
        and RC_LIMIT_TYPE = #{limitTypeCode,jdbcType = VARCHAR}
      </if>
      <if test="startTime != null and startTime != ''">
        and CREATE_TIME &gt;= to_date(#{startTime},'yyyyMMddhh24miss')
      </if>
      <if test="endTime != null and endTime != ''">
        and CREATE_TIME &lt; to_date(#{endTime},'yyyyMMddhh24miss') + 1 / 86400
      </if>
    </trim>
  </where>
  ORDER BY CREATE_TIME DESC
  )a WHERE rownum &lt;= #{endNum}
  ) WHERE rn &gt;= #{startNum}
</select>


</mapper>
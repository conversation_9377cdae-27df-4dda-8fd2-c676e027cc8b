<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcDefineMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDefine" >
    <id column="DEFINE_ID" property="defineId" jdbcType="DECIMAL" />
    <result column="GROUP_ID" property="groupId" jdbcType="DECIMAL" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="RULE" property="rule" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="CHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="EFFECT_VALUE" property="effectValue" jdbcType="VARCHAR" />
    <result column="SERVICE_ID" property="serviceId" jdbcType="VARCHAR" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <result column="ALIAS" property="alias" jdbcType="VARCHAR" />
    <result column="DEFINE_IDENTIFIER" property="defineIdentifier" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DEFINE_ID, GROUP_ID, CODE, REMARK, RULE, TYPE, USER_ID, USER_NAME, to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') as "CREATE_TIME", EFFECT_VALUE,
    SERVICE_ID, BUSINESS_TYPE, ALIAS, DEFINE_IDENTIFIER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from RC_DEFINE
    where DEFINE_ID = #{defineId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_DEFINE
    where DEFINE_ID = #{defineId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDefine" >
    insert into RC_DEFINE (DEFINE_ID, GROUP_ID, CODE,
    REMARK, RULE, TYPE, USER_ID,
    USER_NAME, CREATE_TIME, EFFECT_VALUE,
    SERVICE_ID, BUSINESS_TYPE, ALIAS,
    DEFINE_IDENTIFIER)
    values (#{defineId,jdbcType=DECIMAL}, #{groupId,jdbcType=DECIMAL}, #{code,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{rule,jdbcType=VARCHAR}, #{type,jdbcType=CHAR}, #{userId,jdbcType=DECIMAL},
    #{userName,jdbcType=VARCHAR}, to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'), #{effectValue,jdbcType=VARCHAR},
    #{serviceId,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{alias,jdbcType=VARCHAR},
    #{defineIdentifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDefine" >
    insert into RC_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="defineId != null" >
        DEFINE_ID,
      </if>
      <if test="groupId != null" >
        GROUP_ID,
      </if>
      <if test="code != null" >
        CODE,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="rule != null" >
        RULE,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="effectValue != null" >
        EFFECT_VALUE,
      </if>
      <if test="serviceId != null" >
        SERVICE_ID,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="alias != null" >
        ALIAS,
      </if>
      <if test="defineIdentifier != null" >
        DEFINE_IDENTIFIER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="defineId != null" >
        #{defineId,jdbcType=DECIMAL},
      </if>
      <if test="groupId != null" >
        #{groupId,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="rule != null" >
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=CHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="effectValue != null" >
        #{effectValue,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null" >
        #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="defineIdentifier != null" >
        #{defineIdentifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDefine" >
    update RC_DEFINE
    <set >
      <if test="groupId != null" >
        GROUP_ID = #{groupId,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="rule != null" >
        RULE = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=CHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="effectValue != null" >
        EFFECT_VALUE = #{effectValue,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null" >
        SERVICE_ID = #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        ALIAS = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="defineIdentifier != null" >
        DEFINE_IDENTIFIER = #{defineIdentifier,jdbcType=VARCHAR},
      </if>
    </set>
    where DEFINE_ID = #{defineId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDefine" >
    update RC_DEFINE
    set GROUP_ID = #{groupId,jdbcType=DECIMAL},
      CODE = #{code,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      RULE = #{rule,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=CHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      CREATE_TIME = to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
      EFFECT_VALUE = #{effectValue,jdbcType=VARCHAR},
      SERVICE_ID = #{serviceId,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      ALIAS = #{alias,jdbcType=VARCHAR},
      DEFINE_IDENTIFIER = #{defineIdentifier,jdbcType=VARCHAR}
    where DEFINE_ID = #{defineId,jdbcType=DECIMAL}
  </update>

  <select id="selectByCode"  resultMap="BaseResultMap" parameterType="String">
    select
    <include refid="Base_Column_List" />
    from RC_DEFINE
    where CODE = #{code}
  </select>


  <select id="totalDefineQuery" resultType="int">
    SELECT COUNT (DEFINE_ID) FROM RC_DEFINE a INNER JOIN RC_DEFINE_GROUP b ON  a.GROUP_ID = b.GROUP_ID
    WHERE 1 = 1
    <if test="defineIdentifier != null and defineIdentifier != ''">
     AND a.DEFINE_IDENTIFIER = #{defineIdentifier}
    </if>
    <if test="bigId != null ">
      AND b.PARENT_ID = #{bigId}
    </if>
    <if test="smallId != null ">
      AND b.GROUP_ID = #{smallId}
    </if>
    <if test="startTime != null and startTime != ''">
      AND a.CREATE_TIME &gt;= to_date(#{startTime},'yyyymmddhh24miss')
    </if>
    <if test="endTime != null and endTime != ''">
      AND a.CREATE_TIME &lt;= to_date(#{endTime},'yyyymmddhh24miss')
    </if>
  </select>

  <select id="pageDefineQuery" resultType="java.util.HashMap">
    SELECT * FROM (
      SELECT c.* ,rownum rn FROM (
        SELECT
        a.DEFINE_IDENTIFIER AS "defineIdentifier",a.alias AS "alias",a.remark as "remark",b.alias as "smallName",to_char(a.create_time,'yyyy-mm-dd hh24:mi:ss') AS  "createTime"
        FROM
        RC_DEFINE a INNER JOIN RC_DEFINE_GROUP b ON  a.GROUP_ID = b.GROUP_ID
        WHERE 1 = 1
        <if test="defineIdentifier != null and defineIdentifier!=''">
          AND a.DEFINE_IDENTIFIER = #{defineIdentifier}
        </if>
        <if test="bigId != null">
          AND b.PARENT_ID = #{bigId}
        </if>
        <if test="smallId != null">
          AND b.GROUP_ID = #{smallId}
        </if>
        <if test="startTime != null and startTime != ''">
          AND a.CREATE_TIME &gt;= to_date(#{startTime},'yyyymmddhh24miss')
        </if>
        <if test="endTime != null and endTime != ''">
          AND a.CREATE_TIME &lt;= to_date(#{endTime},'yyyymmddhh24miss')
        </if>
        ORDER BY a.CREATE_TIME DESC
      )c WHERE rownum &lt;= #{endNum}
    )WHERE rn &gt;= #{startNum}

  </select>

</mapper>
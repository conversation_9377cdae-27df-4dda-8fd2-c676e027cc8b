<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.ChannelRiskRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.ChannelRiskRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="TRADE_NOS" property="tradeNos" jdbcType="VARCHAR" />
    <result column="CHANNEL_TRADE_NO" property="channelTradeNo" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="CHANNEL_MCHT_NO" property="channelMchtNo" jdbcType="VARCHAR" />
    <result column="RISK_TYPE" property="riskType" jdbcType="VARCHAR" />
    <result column="RISK_LEVEL" property="riskLevel" jdbcType="VARCHAR" />
    <result column="RISK_DESC" property="riskDesc" jdbcType="VARCHAR" />
    <result column="BANK_CARD_NO" property="bankCardNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, TRANSACTION_NO, TRADE_NOS, CHANNEL_TRADE_NO, CHANNEL_ID, CHANNEL_MCHT_NO, 
    RISK_TYPE, RISK_LEVEL, RISK_DESC, BANK_CARD_NO, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_CHANNEL_RISK_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_CHANNEL_RISK_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.ChannelRiskRecord" >
    insert into RC_CHANNEL_RISK_RECORD (ID, CUSTOMER_CODE, TRANSACTION_NO, 
      TRADE_NOS, CHANNEL_TRADE_NO, CHANNEL_ID, 
      CHANNEL_MCHT_NO, RISK_TYPE, RISK_LEVEL, 
      RISK_DESC, BANK_CARD_NO, CREATE_TIME
      )
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{transactionNo,jdbcType=VARCHAR}, 
      #{tradeNos,jdbcType=VARCHAR}, #{channelTradeNo,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR}, 
      #{channelMchtNo,jdbcType=VARCHAR}, #{riskType,jdbcType=VARCHAR}, #{riskLevel,jdbcType=VARCHAR}, 
      #{riskDesc,jdbcType=VARCHAR}, #{bankCardNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.ChannelRiskRecord" >
    insert into RC_CHANNEL_RISK_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="tradeNos != null" >
        TRADE_NOS,
      </if>
      <if test="channelTradeNo != null" >
        CHANNEL_TRADE_NO,
      </if>
      <if test="channelId != null" >
        CHANNEL_ID,
      </if>
      <if test="channelMchtNo != null" >
        CHANNEL_MCHT_NO,
      </if>
      <if test="riskType != null" >
        RISK_TYPE,
      </if>
      <if test="riskLevel != null" >
        RISK_LEVEL,
      </if>
      <if test="riskDesc != null" >
        RISK_DESC,
      </if>
      <if test="bankCardNo != null" >
        BANK_CARD_NO,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeNos != null" >
        #{tradeNos,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null" >
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="channelMchtNo != null" >
        #{channelMchtNo,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null" >
        #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null" >
        #{riskLevel,jdbcType=VARCHAR},
      </if>
      <if test="riskDesc != null" >
        #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankCardNo != null" >
        #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.ChannelRiskRecord" >
    update RC_CHANNEL_RISK_RECORD
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeNos != null" >
        TRADE_NOS = #{tradeNos,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null" >
        CHANNEL_TRADE_NO = #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="channelMchtNo != null" >
        CHANNEL_MCHT_NO = #{channelMchtNo,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null" >
        RISK_TYPE = #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null" >
        RISK_LEVEL = #{riskLevel,jdbcType=VARCHAR},
      </if>
      <if test="riskDesc != null" >
        RISK_DESC = #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankCardNo != null" >
        BANK_CARD_NO = #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.ChannelRiskRecord" >
    update RC_CHANNEL_RISK_RECORD
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      TRADE_NOS = #{tradeNos,jdbcType=VARCHAR},
      CHANNEL_TRADE_NO = #{channelTradeNo,jdbcType=VARCHAR},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      CHANNEL_MCHT_NO = #{channelMchtNo,jdbcType=VARCHAR},
      RISK_TYPE = #{riskType,jdbcType=VARCHAR},
      RISK_LEVEL = #{riskLevel,jdbcType=VARCHAR},
      RISK_DESC = #{riskDesc,jdbcType=VARCHAR},
      BANK_CARD_NO = #{bankCardNo,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  
  <select id="queryCount" resultType="java.lang.Integer" parameterType="java.util.Map">
    <if test="serviceCustomerCode != null and serviceCustomerCode != ''"  >
        with tmp as
        (select customer_no
            from cust_customer_draft
           where SERVICE_CUSTOMER_NO = #{serviceCustomerCode,jdbcType=VARCHAR}
          union all
          select CUSTOMER_NO
            from CUST_CUSTOMER_DRAFT
           where PARENT_CUSTOMER_ID in
                 (select CUSTOMER_ID
                    from CUST_CUSTOMER_DRAFT
                   where CUSTOMER_NO != #{serviceCustomerCode, jdbcType=VARCHAR}
                   start with CUSTOMER_NO = #{serviceCustomerCode, jdbcType=VARCHAR}
                   connect by prior CUSTOMER_ID = PARENT_CUSTOMER_ID)
        )
    </if>
    select count(*)
    from RC_CHANNEL_RISK_RECORD t
      <where>
          <include refid="pageConditions" />
      </where>
  </select>
  
  <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from (
            select A.*, rownum RN
            from (
                <if test="serviceCustomerCode != null and serviceCustomerCode != ''"  >
                    with tmp as
                    (select customer_no
                        from cust_customer_draft
                       where SERVICE_CUSTOMER_NO = #{serviceCustomerCode,jdbcType=VARCHAR}
                      union all
                      select CUSTOMER_NO
                        from CUST_CUSTOMER_DRAFT
                       where PARENT_CUSTOMER_ID in
                             (select CUSTOMER_ID
                                from CUST_CUSTOMER_DRAFT
                               where CUSTOMER_NO != #{serviceCustomerCode, jdbcType=VARCHAR}
                               start with CUSTOMER_NO = #{serviceCustomerCode, jdbcType=VARCHAR}
                               connect by prior CUSTOMER_ID = PARENT_CUSTOMER_ID)
                    )
                </if>
                select *
                from RC_CHANNEL_RISK_RECORD t
                <where>
                    <include refid="pageConditions" />
                </where>
                order by ID desc
            ) A
        where ROWNUM <![CDATA[ <= ]]> #{endRowNo,jdbcType=DECIMAL}
        )
        where RN <![CDATA[ >= ]]> #{beginRowNo,jdbcType=DECIMAL}
  </select>

    <sql id="pageConditions" >
        <if test="customerCode != null and customerCode != ''">
            AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
        </if>
        <if test="platCustomerCode != null and platCustomerCode != ''">
            AND CUSTOMER_CODE in (
            select customer_no from cust_customer t where t.plat_customer_no = #{platCustomerCode,jdbcType=VARCHAR}
            )
        </if>
        <if test="serviceCustomerCode != null and serviceCustomerCode != ''">
            AND CUSTOMER_CODE in (
            select customer_no from tmp
            )
        </if>
        <if test="channelMchtNo != null and channelMchtNo != ''">
            AND CHANNEL_MCHT_NO = #{channelMchtNo,jdbcType=VARCHAR}
        </if>
        <if test="channelId != null and channelId != ''">
            AND CHANNEL_ID = #{channelId,jdbcType=VARCHAR}
        </if>
        <if test="riskType != null and  riskType != ''">
            AND RISK_TYPE = #{riskType,jdbcType=VARCHAR}
        </if>
        <if test="beginCreateTime != null and  beginCreateTime != ''">
            <![CDATA[ AND CREATE_TIME >= TO_DATE(#{beginCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="endCreateTime != null and  endCreateTime != ''">
            <![CDATA[ AND CREATE_TIME <= TO_DATE(#{endCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="userCompanyId != null">
            AND t.customer_code in (
                select c.customer_no from cust_customer c where c.company_id in (
                    select company_id from pas_company
                    start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
                    connect by parent_id = prior company_id
                )
            )
        </if>
        <if test="businessManId != null">
            AND t.customer_code in (
                select c.customer_no from cust_customer c where c.business_man_id = #{businessManId,jdbcType=DECIMAL}
            )
        </if>
        <if test="companyIds != null">
            and exists (
                select 1 from cust_customer_draft cd
                where t.CUSTOMER_CODE = cd.CUSTOMER_NO
                and cd.company_id in
                <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
        </if>
    </sql>
  
</mapper>
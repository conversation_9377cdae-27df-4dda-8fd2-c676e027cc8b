<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcDefineGroupMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDefineGroup" >
    <id column="GROUP_ID" property="groupId" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="PARENT_ID" property="parentId" jdbcType="DECIMAL" />
    <result column="ALIAS" property="alias" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="GROUP_IDENTIFIER" property="groupIdentifier" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    GROUP_ID, NAME, PARENT_ID, ALIAS, REMARK, USER_ID, USER_NAME, to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') as "CREATE_TIME", GROUP_IDENTIFIER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from RC_DEFINE_GROUP
    where GROUP_ID = #{groupId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_DEFINE_GROUP
    where GROUP_ID = #{groupId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDefineGroup" >
    insert into RC_DEFINE_GROUP (GROUP_ID, NAME, PARENT_ID,
    ALIAS, REMARK, USER_ID,
    USER_NAME, CREATE_TIME, GROUP_IDENTIFIER
    )
    values (#{groupId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=DECIMAL},
    #{alias,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{userId,jdbcType=DECIMAL},
    #{userName,jdbcType=VARCHAR}, to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'), #{groupIdentifier,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDefineGroup" >
    insert into RC_DEFINE_GROUP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="groupId != null" >
        GROUP_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="alias != null" >
        ALIAS,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="groupIdentifier != null" >
        GROUP_IDENTIFIER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="groupId != null" >
        #{groupId,jdbcType=DECIMAL},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="alias != null" >
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="groupIdentifier != null" >
        #{groupIdentifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDefineGroup" >
    update RC_DEFINE_GROUP
    <set >
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="alias != null" >
        ALIAS = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
      </if>
      <if test="groupIdentifier != null" >
        GROUP_IDENTIFIER = #{groupIdentifier,jdbcType=VARCHAR},
      </if>
    </set>
    where GROUP_ID = #{groupId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDefineGroup" >
    update RC_DEFINE_GROUP
    set NAME = #{name,jdbcType=VARCHAR},
    PARENT_ID = #{parentId,jdbcType=DECIMAL},
    ALIAS = #{alias,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    USER_ID = #{userId,jdbcType=DECIMAL},
    USER_NAME = #{userName,jdbcType=VARCHAR},
    CREATE_TIME = to_date(#{createTime},'yyyy-mm-dd hh24:mi:ss'),
    GROUP_IDENTIFIER = #{groupIdentifier,jdbcType=VARCHAR}
    where GROUP_ID = #{groupId,jdbcType=DECIMAL}
  </update>


  <select id="totalPageQuery" resultType="int">
    SELECT count(group_id) FROM RC_DEFINE_GROUP WHERE
    PARENT_ID &lt;&gt; '0'
    AND PARENT_ID &lt;&gt; '-1'
    <if test="bigId != null">
     AND PARENT_ID = #{bigId}
    </if>
    <if test="identifier != null and identifier != ''">
      AND GROUP_IDENTIFIER = #{identifier}
    </if>

  </select>

  <select id="pageQuery" resultType="java.util.HashMap">
    select * from (
      SELECT a.* ,rownum rn FROM (
      SELECT
      GROUP_IDENTIFIER as "identifier", ALIAS as "smallName" ,REMARK as "remark",to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') as "createTime"
      FROM RC_DEFINE_GROUP
      WHERE PARENT_ID &lt;&gt; '0'
      AND PARENT_ID &lt;&gt;'-1'
      <if test="bigId != null and bigId != ''">
        AND PARENT_ID = #{bigId}
      </if>
      <if test="identifier != null and identifier != ''">
        AND GROUP_IDENTIFIER = #{identifier}
      </if>
      ORDER BY CREATE_TIME DESC
      )a WHERE rownum &lt;= #{endNum}
    )WHERE rn &gt;= #{startNum}
  </select>

  <select id="selectGroupByParentId" parameterType="Long" resultType="java.util.HashMap">
    SELECT GROUP_ID AS "id", ALIAS as "name" ,GROUP_IDENTIFIER AS "identifiner"
    FROM RC_DEFINE_GROUP
    WHERE PARENT_ID = #{parentId}
  </select>

  <select id="queryName" parameterType="String" resultType="String">
    SELECT alias FROM RC_DEFINE_GROUP WHERE GROUP_ID = (SELECT PARENT_ID FROM RC_DEFINE_GROUP WHERE alias = #{smallName})
  </select>

  <select id="totalBigPageQuery" parameterType="String" resultType="int">
    SELECT COUNT(GROUP_ID) FROM RC_DEFINE_GROUP WHERE PARENT_ID = '0'
    <if test="identifier != null and identifier != ''">
      AND GROUP_IDENTIFIER = #{identifier}
    </if>
  </select>

  <select id="pageBigQuery" resultMap="BaseResultMap">
    select * from (
    SELECT a.* ,rownum rn FROM (
    SELECT
    <include refid="Base_Column_List" />
    FROM RC_DEFINE_GROUP
    WHERE PARENT_ID = '0'
    <if test="identifier != null and identifier != ''">
      AND GROUP_IDENTIFIER = #{identifier}
    </if>
    ORDER BY CREATE_TIME DESC
    )a WHERE rownum &lt;= #{endNum}
    )WHERE rn &gt;= #{startNum}
  </select>

</mapper>
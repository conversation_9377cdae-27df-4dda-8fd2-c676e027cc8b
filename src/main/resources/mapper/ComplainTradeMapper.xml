<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.ComplainTradeMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.ComplainTrade" >
    <id column="CT_ID" property="ctId" jdbcType="DECIMAL" />
    <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR" />
    <result column="RISK_TIME" property="riskTime" jdbcType="TIMESTAMP" />
    <result column="TRADE_TIME" property="tradeTime" jdbcType="TIMESTAMP" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="VARCHAR" />
    <result column="RISK_DESC" property="riskDesc" jdbcType="VARCHAR" />
    <result column="COMPLAIN_CONTENT" property="complainContent" jdbcType="VARCHAR" />
    <result column="USER_MOBILE" property="userMobile" jdbcType="VARCHAR" />
    <result column="CHANNEL_MCH_ID" property="channelMchId" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="CUST_NAME" property="custName" jdbcType="VARCHAR" />
    <result column="AGENT_CUSTOMER_CODE" property="agentCustomerCode" jdbcType="VARCHAR" />
    <result column="AGENT_CUST_NAME" property="agentCustName" jdbcType="VARCHAR" />
    <result column="PLAT_CUSTOMER_CODE" property="platCustomerCode" jdbcType="VARCHAR" />
    <result column="PLAT_CUST_NAME" property="platCustName" jdbcType="VARCHAR" />
    <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
    <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UNI_REMARK" property="uniRemark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CT_ID, CHANNEL_TYPE, RISK_TIME, TRADE_TIME, ORDER_NO, AMOUNT, RISK_DESC, COMPLAIN_CONTENT, USER_MOBILE,
    CHANNEL_MCH_ID, CUSTOMER_CODE, CUST_NAME, AGENT_CUSTOMER_CODE, AGENT_CUST_NAME, PLAT_CUSTOMER_CODE, 
    PLAT_CUST_NAME, BUSINESS_MAN, BATCH_NO, CREATE_TIME, UNI_REMARK
  </sql>

  <!-- 获取主键ID -->
  <select id="selectIdFromSeq" resultType="java.lang.Long" >
    select SEQ_PAS_COMPLAIN_TRADE.nextval from dual
  </select>

  <!-- 获取导入明细主键ID -->
  <select id="selectBatchDetailId" resultType="java.lang.Long" >
    select SEQ_CUST_BATCH_DETAIL.nextval from dual
  </select>

  <select id="hasComplainRemark" resultType="boolean">
    select count(*)
    from PAS_COMPLAIN_TRADE
    where UNI_REMARK = #{uniRemark,jdbcType=VARCHAR}
  </select>

  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.ComplainTrade" >
    insert into PAS_COMPLAIN_TRADE (CT_ID, CHANNEL_TYPE, RISK_TIME, 
      TRADE_TIME, ORDER_NO, AMOUNT, RISK_DESC,
      COMPLAIN_CONTENT, USER_MOBILE, CHANNEL_MCH_ID, 
      CUSTOMER_CODE, CUST_NAME, AGENT_CUSTOMER_CODE, 
      AGENT_CUST_NAME, PLAT_CUSTOMER_CODE, PLAT_CUST_NAME, 
      BUSINESS_MAN, BATCH_NO, CREATE_TIME, UNI_REMARK)
    values (#{ctId,jdbcType=DECIMAL}, #{channelType,jdbcType=VARCHAR}, #{riskTime,jdbcType=TIMESTAMP}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR}, #{riskDesc,jdbcType=VARCHAR},
      #{complainContent,jdbcType=VARCHAR}, #{userMobile,jdbcType=VARCHAR}, #{channelMchId,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, #{agentCustomerCode,jdbcType=VARCHAR}, 
      #{agentCustName,jdbcType=VARCHAR}, #{platCustomerCode,jdbcType=VARCHAR}, #{platCustName,jdbcType=VARCHAR}, 
      #{businessMan,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{uniRemark,jdbcType=VARCHAR})
  </insert>


  <insert id="insertBatchDetail" parameterType="com.epaylinks.efps.common.dataimport.model.BatchDetail" >
    insert into CUST_BATCH_DETAIL (DETAIL_ID, TASK_ID, ROW_NO,
    ROW_NAME, STATUS, CREATE_TIME,
    RELATE_ID, REMARKS, SOURCE_DATA
    )
    values (#{detailId,jdbcType=DECIMAL}, #{taskId,jdbcType=DECIMAL}, #{rowNo,jdbcType=DECIMAL},
    #{rowName,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
    #{relateId,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{sourceData,jdbcType=CLOB}
    )
  </insert>

  <select id="selectCustByChannel" resultType="com.epaylinks.efps.rc.domain.ComplainTrade" >
    select CUSTOMER_CODE as customerCode,INLET_TYPE as inletType from CUST_INLET_RECORD
    where CHANNEL_MCHT_NO = #{channelMchId,jdbcType=VARCHAR}
  </select>

  <select id="selectByCust" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select c.customer_no as CUSTOMER_CODE, c.name as CUST_NAME, c.plat_customer_no as PLAT_CUSTOMER_CODE, c3.name as PLAT_CUST_NAME,
    c.service_customer_no as AGENT_CUSTOMER_CODE, c4.name as AGENT_CUST_NAME, u.real_name as BUSINESS_MAN
    from cust_customer c
    left join cust_customer_draft c2 on c2.customer_id = c.customer_id
    left join cust_customer_draft c3 on c3.customer_no = c.plat_customer_no
    left join cust_customer_draft c4 on c4.customer_no = c.service_customer_no
    left join pas_user u on u.user_id = c.business_man_id
    where c.customer_no = #{customerCode,jdbcType=VARCHAR}
  </select>

</mapper>
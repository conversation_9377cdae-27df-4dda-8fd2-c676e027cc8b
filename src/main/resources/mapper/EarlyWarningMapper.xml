<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.EarlyWarningMapper" >

    <!-- 以下两规则在风控计算时监控
        R001, 交易监控, 商户网络支付单笔入金交易≥[param1]元, 预警 
        R002, 交易监控, 商户银行卡收单笔入金交易≥[param1]元, 预警
    -->
    
    <!-- R003, 交易监控, 商户网络支付（除收单外）日交易量≥[param1]元, 预警  -->
    <!-- <select id="sumOnlineTradingAmount" resultType="long">
        SELECT NVL(SUM(t.AMOUNT), 0) FROM RC_TXS_ORDER t 
        WHERE t.CUSTOMER_CODE = #{businessTargetId,jdbcType=VARCHAR}
        and t.business_type = 'gateWayPay'
        and t.pay_state = '00'
        and t.BUSINESS_CODE not in ('POSUnionCreditCardPay_ABROAD', 'POSUnionDebitCardPay_ABROAD', 'POSUnionNfcCreditCardPay', 'POSUnionQrcodeDebitCard', 'POSUnionQrcodeCreditCard', 'POSUnionDebitCardPay', 'POSUnionCreditCardPay', 'POSUnionNfcDebitCardPay')
        <![CDATA[ and t.CREATE_TIME >= TRUNC(SYSDATE) ]]>
    </select> -->
    
    
    <!-- R004, 交易监控, 商户银行卡收单日交易量≥[param1]元, 预警  -->
   <!--  <select id="sumPOSUnionAmount" resultType="long">
        SELECT NVL(SUM(t.AMOUNT), 0) FROM RC_TXS_ORDER t 
        WHERE t.CUSTOMER_CODE = #{businessTargetId,jdbcType=VARCHAR}
        and t.business_type = 'gateWayPay'
        and t.pay_state = '00'
        and t.BUSINESS_CODE in ('POSUnionCreditCardPay_ABROAD', 'POSUnionDebitCardPay_ABROAD', 'POSUnionNfcCreditCardPay', 'POSUnionQrcodeDebitCard', 'POSUnionQrcodeCreditCard', 'POSUnionDebitCardPay', 'POSUnionCreditCardPay', 'POSUnionNfcDebitCardPay')
        <![CDATA[ and t.CREATE_TIME >= TRUNC(SYSDATE) ]]>
    </select> -->
    
    
    <!-- R014, 商户收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元, 预警  -->
    <!-- 查询前3天（除收单）交易金额（不包含当天，即 -1 , -2 , -3 三天） -->
    <!-- <select id="sumLastThreeDayOnlineTradingAmount" resultType="long">
        SELECT NVL(SUM(t.AMOUNT), 0) FROM RC_TXS_ORDER t 
        WHERE t.CUSTOMER_CODE = #{businessTargetId,jdbcType=VARCHAR}
        and t.business_type = 'gateWayPay'
        and t.pay_state = '00'
        and t.BUSINESS_CODE not in ('POSUnionCreditCardPay_ABROAD', 'POSUnionDebitCardPay_ABROAD', 'POSUnionNfcCreditCardPay', 'POSUnionQrcodeDebitCard', 'POSUnionQrcodeCreditCard', 'POSUnionDebitCardPay', 'POSUnionCreditCardPay', 'POSUnionNfcDebitCardPay')
        <![CDATA[ and t.CREATE_TIME >= TRUNC(SYSDATE - 3)  and t.CREATE_TIME < TRUNC(SYSDATE) ]]>
    </select> -->
    
    
    <!-- R015, POS终端收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元, 预警  -->
    <!-- 查询前3天（收单）交易金额（不包含当天，即 -1 , -2 , -3 三天） -->
    <!-- <select id="sumLastThreeDayPOSUnionAmount" resultType="long">
        SELECT NVL(SUM(t.AMOUNT), 0) FROM RC_TXS_ORDER t 
        WHERE t.CUSTOMER_CODE = #{businessTargetId,jdbcType=VARCHAR}
        and t.business_type = 'gateWayPay'
        and t.pay_state = '00'
        and t.BUSINESS_CODE in ('POSUnionCreditCardPay_ABROAD', 'POSUnionDebitCardPay_ABROAD', 'POSUnionNfcCreditCardPay', 'POSUnionQrcodeDebitCard', 'POSUnionQrcodeCreditCard', 'POSUnionDebitCardPay', 'POSUnionCreditCardPay', 'POSUnionNfcDebitCardPay')
        <![CDATA[  and t.CREATE_TIME >= TRUNC(SYSDATE - 3)  and t.CREATE_TIME < TRUNC(SYSDATE) ]]>
    </select> -->
    
    <!-- R006, 高频登陆, 商户在[param1]小时内登陆次数≥[param2]次, 关注 -->
    <select id="countLoginTimes" resultType="int">
        SELECT count(*) FROM PAS_LOGIN_LOG t WHERE
        t.USERNAME = #{businessTargetId,jdbcType=VARCHAR}
        and t.USERTYPE = '2' and t.log_type = 1
        <![CDATA[  and t.LOGIN_TIME  >= sysdate - #{hours,jdbcType=DECIMAL} /  24  ]]>
    </select>
    
    <!-- 用于计算 R007, 高退款率, 商户当日退款交易与总交易占比≥[param1], 关注  --> 
    <select id="countTxsByBusinessType" resultType="int">
       SELECT count(*) FROM RC_TXS_ORDER t WHERE  
            t.CUSTOMER_CODE = #{businessTargetId,jdbcType=VARCHAR}
            and t.business_type = #{businessType,jdbcType=VARCHAR}
            and t.pay_state = '00'
        <![CDATA[ and t.create_time >= TRUNC(SYSDATE) ]]>
    </select>
    
    <!-- R005, 交易监控, 商户代付交易日交易笔数≥[param1]次, 预警 -->
    <select id="countWithDraw" resultType="int">
        SELECT COUNT(*) FROM rc_txs_order t WHERE 
            t.customer_code = #{businessTargetId,jdbcType=VARCHAR}
            and t.BUSINESS_CODE in ('Withdraw','Withdraw-CreditCard')
            and t.pay_state = '00'
        <![CDATA[ and t.create_time >= TRUNC(SYSDATE) ]]>
    </select>
    
    <!-- R009, 交易限额, 商户当日触发限额类风控≥[param1]次, 关注 -->
    <select id="countAmountLimit" resultType="int">
        SELECT COUNT(*) FROM RC_CALCULATE_LOG t WHERE 
            t.BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR}
            and t.rc_limit_type = '001'
        <![CDATA[ and t.create_time >= TRUNC(SYSDATE) ]]>
    </select>
    
    <!-- R008, 黑名单, 商户当日命中黑名单≥[param1]次, 关注 -->
    <select id="countBlackListLimit" resultType="int">
        SELECT COUNT(*) FROM RC_CALCULATE_LOG t WHERE 
            t.BUSINESS_TARGET_ID = #{businessTargetId,jdbcType=VARCHAR}
            and t.rc_limit_type = '003'
        <![CDATA[ and t.create_time >= TRUNC(SYSDATE) ]]>
    </select>
    
  
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.GamblingPyramidRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR" />
    <result column="CARD_ACCOUNT" property="cardAccount" jdbcType="VARCHAR" />
    <result column="CARD_ACCOUNT_HASH" property="cardAccountHash" jdbcType="VARCHAR" />
    <result column="CARD_ACCOUNT_ENCRYPT" property="cardAccountEncrypt" jdbcType="VARCHAR" />
    <result column="JOIN_BLACKLIST" property="joinBlacklist" jdbcType="VARCHAR" />
    <result column="VERIFICATION_RESULTS" property="verificationResults" jdbcType="VARCHAR" />
    <result column="GAMBLING_OR_NOT" property="gamblingOrNot" jdbcType="VARCHAR" />
    <result column="PYRAMID_OR_NOT" property="pyramidOrNot" jdbcType="VARCHAR" />
    <result column="REQ_SERIAL_NO" property="reqSerialNo" jdbcType="VARCHAR" />
    <result column="PAY_SERIAL_NO" property="paySerialNo" jdbcType="VARCHAR" />
    <result column="SUBMIT_UPSTREAM_OR_NOT" property="submitUpstreamOrNot" jdbcType="VARCHAR" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR" />
    <result column="RESPONSE_CODE" property="responseCode" jdbcType="VARCHAR" />
    <result column="RESPONSE_MSG" property="responseMsg" jdbcType="VARCHAR" />
    <result column="CHECK_TIME" property="checkTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR" />
    <result column="TRANS_ORDER_NO" property="transOrderNo" jdbcType="VARCHAR" />
    <result column="CHECK_COST" property="checkCost" jdbcType="VARCHAR" />
    <result column="FRAUD_OR_NOT" property="fraudOrNot" jdbcType="VARCHAR" />
    <result column="CARD_ACCOUNT_TYPE" property="cardAccountType" jdbcType="VARCHAR" />
    <result column="RISK_TAG" property="riskTag" jdbcType="VARCHAR" />
    <result column="CHECK_TYPE" property="checkType" jdbcType="VARCHAR" />
    <result column="CASHOUT_OR_NOT" property="cashoutOrNot" jdbcType="VARCHAR" />
    <result column="VIOLATION_OR_NOT" property="violationOrNot" jdbcType="VARCHAR" />
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" extends="BaseResultMap" >
    <result column="SOURCE_DATA" property="sourceData" jdbcType="CLOB" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, BATCH_NO, CARD_ACCOUNT, CARD_ACCOUNT_HASH, CARD_ACCOUNT_ENCRYPT, JOIN_BLACKLIST, 
    VERIFICATION_RESULTS, GAMBLING_OR_NOT, PYRAMID_OR_NOT, REQ_SERIAL_NO, PAY_SERIAL_NO, 
    SUBMIT_UPSTREAM_OR_NOT, REMARKS, USER_ID, CHANNEL_NAME, RESPONSE_CODE, RESPONSE_MSG, 
    CHECK_TIME, CREATE_TIME, UPDATE_TIME,CUSTOMER_NO,TRANS_ORDER_NO,CHECK_COST,FRAUD_OR_NOT,
    CARD_ACCOUNT_TYPE, RISK_TAG, CHECK_TYPE,CASHOUT_OR_NOT,VIOLATION_OR_NOT,SOURCE
  </sql>
  <sql id="Blob_Column_List" >
    SOURCE_DATA
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from RC_GAMBLING_PYRAMID_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from RC_GAMBLING_PYRAMID_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    insert into RC_GAMBLING_PYRAMID_RECORD (ID, BATCH_NO, CARD_ACCOUNT, 
      CARD_ACCOUNT_HASH, CARD_ACCOUNT_ENCRYPT, JOIN_BLACKLIST, 
      VERIFICATION_RESULTS, GAMBLING_OR_NOT, PYRAMID_OR_NOT, 
      REQ_SERIAL_NO, PAY_SERIAL_NO, SUBMIT_UPSTREAM_OR_NOT, 
      REMARKS, USER_ID, CHANNEL_NAME, 
      RESPONSE_CODE, RESPONSE_MSG, CHECK_TIME, 
      CREATE_TIME, UPDATE_TIME, SOURCE_DATA,CUSTOMER_NO,TRANS_ORDER_NO,CHECK_COST,FRAUD_OR_NOT,
      CARD_ACCOUNT_TYPE, RISK_TAG, CHECK_TYPE,CASHOUT_OR_NOT,VIOLATION_OR_NOT,SOURCE
      )
    values (#{id,jdbcType=DECIMAL}, #{batchNo,jdbcType=VARCHAR}, #{cardAccount,jdbcType=VARCHAR}, 
      #{cardAccountHash,jdbcType=VARCHAR}, #{cardAccountEncrypt,jdbcType=VARCHAR}, #{joinBlacklist,jdbcType=VARCHAR}, 
      #{verificationResults,jdbcType=VARCHAR}, #{gamblingOrNot,jdbcType=VARCHAR}, #{pyramidOrNot,jdbcType=VARCHAR}, 
      #{reqSerialNo,jdbcType=VARCHAR}, #{paySerialNo,jdbcType=VARCHAR}, #{submitUpstreamOrNot,jdbcType=VARCHAR}, 
      #{remarks,jdbcType=VARCHAR}, #{userId,jdbcType=DECIMAL}, #{channelName,jdbcType=VARCHAR}, 
      #{responseCode,jdbcType=VARCHAR}, #{responseMsg,jdbcType=VARCHAR}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{sourceData,jdbcType=CLOB},
      #{customerNo,jdbcType=VARCHAR},#{transOrderNo,jdbcType=VARCHAR},#{checkCost,jdbcType=VARCHAR},
      #{fraudOrNot,jdbcType=VARCHAR},#{cardAccountType,jdbcType=VARCHAR},#{riskTag,jdbcType=VARCHAR},
      #{checkType,jdbcType=VARCHAR},#{cashoutOrNot,jdbcType=VARCHAR},#{violationOrNot,jdbcType=VARCHAR},#{source,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    insert into RC_GAMBLING_PYRAMID_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="batchNo != null" >
        BATCH_NO,
      </if>
      <if test="cardAccount != null" >
        CARD_ACCOUNT,
      </if>
      <if test="cardAccountHash != null" >
        CARD_ACCOUNT_HASH,
      </if>
      <if test="cardAccountEncrypt != null" >
        CARD_ACCOUNT_ENCRYPT,
      </if>
      <if test="joinBlacklist != null" >
        JOIN_BLACKLIST,
      </if>
      <if test="verificationResults != null" >
        VERIFICATION_RESULTS,
      </if>
      <if test="gamblingOrNot != null" >
        GAMBLING_OR_NOT,
      </if>
      <if test="pyramidOrNot != null" >
        PYRAMID_OR_NOT,
      </if>
      <if test="reqSerialNo != null" >
        REQ_SERIAL_NO,
      </if>
      <if test="paySerialNo != null" >
        PAY_SERIAL_NO,
      </if>
      <if test="submitUpstreamOrNot != null" >
        SUBMIT_UPSTREAM_OR_NOT,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="channelName != null" >
        CHANNEL_NAME,
      </if>
      <if test="responseCode != null" >
        RESPONSE_CODE,
      </if>
      <if test="responseMsg != null" >
        RESPONSE_MSG,
      </if>
      <if test="checkTime != null" >
        CHECK_TIME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="sourceData != null" >
        SOURCE_DATA,
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO,
      </if>
      <if test="transOrderNo != null" >
        TRANS_ORDER_NO,
      </if>
      <if test="checkCost != null" >
        CHECK_COST,
      </if>
      <if test="fraudOrNot != null" >
        FRAUD_OR_NOT,
      </if>
      <if test="cardAccountType != null" >
        CARD_ACCOUNT_TYPE,
      </if>
      <if test="riskTag != null" >
        RISK_TAG,
      </if>
      <if test="checkType != null" >
        CHECK_TYPE,
      </if>
      <if test="cashoutOrNot != null" >
        CASHOUT_OR_NOT,
      </if>
      <if test="violationOrNot != null" >
        VIOLATION_OR_NOT,
      </if>
      <if test="source != null">
        SOURCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null" >
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="cardAccount != null" >
        #{cardAccount,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountHash != null" >
        #{cardAccountHash,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountEncrypt != null" >
        #{cardAccountEncrypt,jdbcType=VARCHAR},
      </if>
      <if test="joinBlacklist != null" >
        #{joinBlacklist,jdbcType=VARCHAR},
      </if>
      <if test="verificationResults != null" >
        #{verificationResults,jdbcType=VARCHAR},
      </if>
      <if test="gamblingOrNot != null" >
        #{gamblingOrNot,jdbcType=VARCHAR},
      </if>
      <if test="pyramidOrNot != null" >
        #{pyramidOrNot,jdbcType=VARCHAR},
      </if>
      <if test="reqSerialNo != null" >
        #{reqSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="paySerialNo != null" >
        #{paySerialNo,jdbcType=VARCHAR},
      </if>
      <if test="submitUpstreamOrNot != null" >
        #{submitUpstreamOrNot,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="channelName != null" >
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null" >
        #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMsg != null" >
        #{responseMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkTime != null" >
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceData != null" >
        #{sourceData,jdbcType=CLOB},
      </if>
      <if test="customerNo != null" >
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="transOrderNo != null" >
        #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="checkCost != null" >
        #{checkCost,jdbcType=VARCHAR},
      </if>
      <if test="fraudOrNot != null" >
        #{fraudOrNot,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountType != null" >
        #{cardAccountType,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        #{riskTag,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null" >
        #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="cashoutOrNot != null" >
        #{cashoutOrNot,jdbcType=VARCHAR},
      </if>
      <if test="violationOrNot != null" >
        #{violationOrNot,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    update RC_GAMBLING_PYRAMID_RECORD
    <set >
      <if test="batchNo != null" >
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="cardAccount != null" >
        CARD_ACCOUNT = #{cardAccount,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountHash != null" >
        CARD_ACCOUNT_HASH = #{cardAccountHash,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountEncrypt != null" >
        CARD_ACCOUNT_ENCRYPT = #{cardAccountEncrypt,jdbcType=VARCHAR},
      </if>
      <if test="joinBlacklist != null" >
        JOIN_BLACKLIST = #{joinBlacklist,jdbcType=VARCHAR},
      </if>
      <if test="verificationResults != null" >
        VERIFICATION_RESULTS = #{verificationResults,jdbcType=VARCHAR},
      </if>
      <if test="gamblingOrNot != null" >
        GAMBLING_OR_NOT = #{gamblingOrNot,jdbcType=VARCHAR},
      </if>
      <if test="pyramidOrNot != null" >
        PYRAMID_OR_NOT = #{pyramidOrNot,jdbcType=VARCHAR},
      </if>
      <if test="reqSerialNo != null" >
        REQ_SERIAL_NO = #{reqSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="paySerialNo != null" >
        PAY_SERIAL_NO = #{paySerialNo,jdbcType=VARCHAR},
      </if>
      <if test="submitUpstreamOrNot != null" >
        SUBMIT_UPSTREAM_OR_NOT = #{submitUpstreamOrNot,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="channelName != null" >
        CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null" >
        RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMsg != null" >
        RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkTime != null" >
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceData != null" >
        SOURCE_DATA = #{sourceData,jdbcType=CLOB},
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="transOrderNo != null" >
        TRANS_ORDER_NO = #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="checkCost != null" >
        CHECK_COST = #{checkCost,jdbcType=VARCHAR},
      </if>
      <if test="fraudOrNot != null" >
        FRAUD_OR_NOT = #{fraudOrNot,jdbcType=VARCHAR},
      </if>
      <if test="cardAccountType != null" >
        CARD_ACCOUNT_TYPE = #{cardAccountType,jdbcType=VARCHAR},
      </if>
      <if test="riskTag != null" >
        RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null" >
        CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="cashoutOrNot != null" >
        CASHOUT_OR_NOT = #{cashoutOrNot,jdbcType=VARCHAR},
      </if>
      <if test="violationOrNot != null" >
        VIOLATION_OR_NOT = #{violationOrNot,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=VARCHAR}
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    update RC_GAMBLING_PYRAMID_RECORD
    set BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      CARD_ACCOUNT = #{cardAccount,jdbcType=VARCHAR},
      CARD_ACCOUNT_HASH = #{cardAccountHash,jdbcType=VARCHAR},
      CARD_ACCOUNT_ENCRYPT = #{cardAccountEncrypt,jdbcType=VARCHAR},
      JOIN_BLACKLIST = #{joinBlacklist,jdbcType=VARCHAR},
      VERIFICATION_RESULTS = #{verificationResults,jdbcType=VARCHAR},
      GAMBLING_OR_NOT = #{gamblingOrNot,jdbcType=VARCHAR},
      PYRAMID_OR_NOT = #{pyramidOrNot,jdbcType=VARCHAR},
      REQ_SERIAL_NO = #{reqSerialNo,jdbcType=VARCHAR},
      PAY_SERIAL_NO = #{paySerialNo,jdbcType=VARCHAR},
      SUBMIT_UPSTREAM_OR_NOT = #{submitUpstreamOrNot,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      SOURCE_DATA = #{sourceData,jdbcType=CLOB},
      CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      TRANS_ORDER_NO = #{transOrderNo,jdbcType=VARCHAR},
      CHECK_COST = #{checkCost,jdbcType=VARCHAR},
      FRAUD_OR_NOT = #{fraudOrNot,jdbcType=VARCHAR},
      CARD_ACCOUNT_TYPE = #{cardAccountType,jdbcType=VARCHAR},
      RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      CASHOUT_OR_NOT = #{cashoutOrNot,jdbcType=VARCHAR},
      VIOLATION_OR_NOT = #{violationOrNot,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.GamblingPyramidRecord" >
    update RC_GAMBLING_PYRAMID_RECORD
    set BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      CARD_ACCOUNT = #{cardAccount,jdbcType=VARCHAR},
      CARD_ACCOUNT_HASH = #{cardAccountHash,jdbcType=VARCHAR},
      CARD_ACCOUNT_ENCRYPT = #{cardAccountEncrypt,jdbcType=VARCHAR},
      JOIN_BLACKLIST = #{joinBlacklist,jdbcType=VARCHAR},
      VERIFICATION_RESULTS = #{verificationResults,jdbcType=VARCHAR},
      GAMBLING_OR_NOT = #{gamblingOrNot,jdbcType=VARCHAR},
      PYRAMID_OR_NOT = #{pyramidOrNot,jdbcType=VARCHAR},
      REQ_SERIAL_NO = #{reqSerialNo,jdbcType=VARCHAR},
      PAY_SERIAL_NO = #{paySerialNo,jdbcType=VARCHAR},
      SUBMIT_UPSTREAM_OR_NOT = #{submitUpstreamOrNot,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      TRANS_ORDER_NO = #{transOrderNo,jdbcType=VARCHAR},
      CHECK_COST = #{checkCost,jdbcType=VARCHAR},
      FRAUD_OR_NOT = #{fraudOrNot,jdbcType=VARCHAR},
      CARD_ACCOUNT_TYPE = #{cardAccountType,jdbcType=VARCHAR},
      RISK_TAG = #{riskTag,jdbcType=VARCHAR},
      CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      CASHOUT_OR_NOT = #{cashoutOrNot,jdbcType=VARCHAR},
      VIOLATION_OR_NOT = #{violationOrNot,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="judgeCheckRecordTime" resultType="java.lang.Integer">
    select count(*)
    from RC_GAMBLING_PYRAMID_RECORD
    where VERIFICATION_RESULTS = '1'
    and CREATE_TIME > sysdate - 90
  </select>
  
  <select id="querySeqNext" resultType="java.lang.Long">
    select SEQ_GAMBLING_PYRAMID.nextval from dual
  </select>

  <select id="selectGamblingPyramidList" parameterType="java.util.Map" resultType="com.epaylinks.efps.rc.controller.response.GamblingPyramidResponse">
    select *
    from (
      select A.*,rownum rn
      from (
        select
        t.ID,
        t.CARD_ACCOUNT_TYPE cardAccountType,
        t.CARD_ACCOUNT cardAccount,
        t.CARD_ACCOUNT_ENCRYPT cardEncrypt,
        t.JOIN_BLACKLIST joinBlacklist,
        t.VERIFICATION_RESULTS verificationResults,
        t.RISK_TAG riskTag,
        t.CHECK_TYPE checkType,
        NVL(t.REMARKS,t.RESPONSE_CODE || ' ' || t.RESPONSE_MSG) remarks,
        t.REQ_SERIAL_NO reqSerialNo,
        t.CUSTOMER_NO customerNo,
        p.NAME customerName,
        t.TRANS_ORDER_NO transOrderNo,
        t.CHANNEL_NAME channelName,
        t.USER_ID operId,
        t.BATCH_NO batchNo,
        TO_CHAR(t.CREATE_TIME,'yyyy-MM-dd HH24:MI:SS') createTime,
        TO_CHAR(t.CHECK_TIME,'yyyy-MM-dd HH24:MI:SS') checkTime,
        t.CHECK_COST checkCost,
        t.SOURCE source
        from RC_GAMBLING_PYRAMID_RECORD t
        left join cust_customer_draft p on t.CUSTOMER_NO = p.CUSTOMER_NO
        <if test="realName != null and realName !=''">
          inner join pas_user c on c.user_id = t.user_id and c.REAL_NAME = #{realName,jdbcType=VARCHAR}
        </if>
        <where>
          <include refid="pageCondition"></include>
        </where>
        order by t.CREATE_TIME desc
      ) A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="selectPageCount" resultType="java.lang.Integer">
    select count(*)
    from (
      select 1
      from RC_GAMBLING_PYRAMID_RECORD t
      <if test="realName != null and realName !=''">
        inner join pas_user c on c.user_id = t.user_id and c.REAL_NAME = #{realName,jdbcType=VARCHAR}
      </if>
      <where>
        <include refid="pageCondition"></include>
      </where>
      order by t.CREATE_TIME desc
    )
  </select>

  <sql id="pageCondition" >
    <if test="beginCreateTime != null and  beginCreateTime != ''">
      <![CDATA[ and t.CREATE_TIME >= TO_DATE(#{beginCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
    </if>
    <if test="endCreateTime != null and  endCreateTime != ''">
      <![CDATA[ and t.CREATE_TIME <= TO_DATE(#{endCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
    </if>
    <if test="batchNo != null and batchNo != ''">
      AND t.batch_no = #{batchNo,jdbcType=VARCHAR}
    </if>
    <if test="cardAccountHash != null and cardAccountHash != ''">
      AND t.CARD_ACCOUNT_HASH = #{cardAccountHash,jdbcType=VARCHAR}
    </if>
    <if test="verificationResults != null and verificationResults != ''">
      and t.VERIFICATION_RESULTS = #{verificationResults,jdbcType=VARCHAR}
    </if>
    <if test="joinBlacklist != null and joinBlacklist != ''">
      and t.JOIN_BLACKLIST = #{joinBlacklist,jdbcType=VARCHAR}
    </if>
    <if test="gamblingOrNot != null and gamblingOrNot != ''">
      and t.GAMBLING_OR_NOT = #{gamblingOrNot,jdbcType=VARCHAR}
    </if>
    <if test="pyramidOrNot != null and pyramidOrNot != ''">
      and t.PYRAMID_OR_NOT = #{pyramidOrNot,jdbcType=VARCHAR}
    </if>
    <if test="fraudOrNot != null and fraudOrNot != ''">
      and t.FRAUD_OR_NOT = #{fraudOrNot,jdbcType=VARCHAR}
    </if>
    <if test="channelName != null and channelName != ''">
      and t.CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
    </if>
    <if test="customerNo != null and customerNo != ''">
      and t.CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    </if>
    <if test="source != null and source == 'singleCheck'">
      and t.USER_ID = #{userId,jdbcType=DECIMAL}
      and t.BATCH_NO is null
    </if>
    <if test="cashoutOrNot != null and cashoutOrNot != ''">
      and t.CASHOUT_OR_NOT = #{cashoutOrNot,jdbcType=VARCHAR}
    </if>
    <if test="violationOrNot != null and violationOrNot != ''">
      and t.VIOLATION_OR_NOT = #{violationOrNot,jdbcType=VARCHAR}
    </if>
    <if test="cardAccountType != null and cardAccountType != ''">
      and t.CARD_ACCOUNT_TYPE = #{cardAccountType,jdbcType=VARCHAR}
    </if>
    <if test="riskTag != null and riskTag == 'none' ">
      and t.RISK_TAG is null
    </if>
    <if test="riskTag != null and riskTag != 'none'">
      and t.RISK_TAG like '%' || #{riskTag,jdbcType=VARCHAR} || '%'
    </if>
    <if test="opeSource != null and opeSource != ''">
      and t.SOURCE = #{opeSource,jdbcType=VARCHAR}
    </if>
    <if test="userCompanyId != null">
      AND t.CUSTOMER_NO in (
        select cc.customer_no from cust_customer cc where cc.company_id in (
        select company_id from pas_company
        start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
        connect by parent_id = prior company_id
        )
      )
    </if>
    <if test="businessManId != null">
      AND t.CUSTOMER_NO in (
        select cc.customer_no from cust_customer cc
        where cc.business_man_id = #{businessManId,jdbcType=DECIMAL}
      )
    </if>
    <if test="companyIds != null">
    and exists (
      select 1 from cust_customer_draft cd
      where t.CUSTOMER_NO = cd.CUSTOMER_NO
      and cd.company_id in
      <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    )
    </if>
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.VRcNoTxnMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.VRcNoTxn">
    <!--@mbg.generated-->
    <!--@Table V_RC_NO_TXN-->
    <result column="ARCHIVE_ID" jdbcType="DECIMAL" property="archiveId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName" />
    <result column="CUSTOMER_CATEGORY" jdbcType="VARCHAR" property="customerCategory" />
    <result column="CUSTOMER_TYPE" jdbcType="DECIMAL" property="customerType" />
    <result column="RULE_CODE" jdbcType="VARCHAR" property="ruleCode" />
    <result column="LIMIT_DAYS" jdbcType="VARCHAR" property="limitDays" />
    <result column="NO_TXN_DAYS" jdbcType="DECIMAL" property="noTxnDays" />
    <result column="ACCOUNT_LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="accountLastUpdateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ARCHIVE_ID, CUSTOMER_CODE, CUSTOMER_NAME, CUSTOMER_CATEGORY, CUSTOMER_TYPE, RULE_CODE, 
    LIMIT_DAYS, NO_TXN_DAYS, ACCOUNT_LAST_UPDATE_TIME
  </sql>

<!--auto generated by MybatisCodeHelper on 2023-06-25-->
  <select id="selectFirstN" resultMap="BaseResultMap">
    select * from (
    select
    <include refid="Base_Column_List" />
    from V_RC_NO_TXN ) where rownum <![CDATA[<=]]> #{n,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-06-25-->
  <select id="count" resultType="java.lang.Integer">
    select count(1)
    from V_RC_NO_TXN
  </select>
</mapper>
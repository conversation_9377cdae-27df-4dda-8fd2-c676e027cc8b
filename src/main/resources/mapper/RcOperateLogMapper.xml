<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RcOperateLogMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcOperateLog">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="PERM_ID" property="permId" jdbcType="DECIMAL"/>
        <result column="CODE" property="code" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="DECIMAL"/>
        <result column="OPERATE_CONTENT" property="operateContent" jdbcType="VARCHAR"/>
        <result column="ORIG_VALUE" property="origValue" jdbcType="VARCHAR"/>
        <result column="NEW_VALUE" property="newValue" jdbcType="VARCHAR"/>
        <result column="OPERATOR" property="operator" jdbcType="VARCHAR"/>
        <result column="OPERATE_TIME" property="operateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="All_Column_List">
        ID, PERM_ID, CODE, NAME, TYPE, OPERATE_CONTENT, ORIG_VALUE, NEW_VALUE, OPERATOR, OPERATE_TIME
    </sql>
    <sql id="OPERATELOG_VO_TABLE">
      (
          select l.perm_id as permId,
           p.alias as permName,
           l.code as code,
           l.name as name,
           l.type as type,
             case
                 when l.type = 2 then
                     listagg(to_char('参数名称：' || l.operate_content || '，修改前：' || l.orig_value || '；修改后：' || l.new_value),',') WITHIN GROUP (ORDER BY l.operate_content)
              when l.type = 1 and l.perm_id = 80205 then
              listagg(to_char(l.operate_content || ':' || l.new_value),',') WITHIN GROUP (ORDER BY l.operate_content)
              else
              listagg(to_char('' || l.operate_content),',') WITHIN GROUP (ORDER BY l.operate_content)
           end as operateContent,
           u.name as operator,
           u.real_name as operatorName,
           to_char(l.operate_time, 'yyyy-MM-dd HH24:mi:ss') as operateTime
      from RC_OPERATE_LOG l, PAS_PERM p, PAS_USER u
     where l.PERM_ID = p.PERM_ID
       and l.OPERATOR = u.USER_ID || ''
        <if test="params.startTime != null">
            and l.operate_time <![CDATA[ >= ]]> to_date(#{params.startTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="params.endTime != null">
            and l.operate_time <![CDATA[ <= ]]> to_date(#{params.endTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="params.permId != null">
            and l.perm_id = #{params.permId}
        </if>
        <if test="params.code != null">
            and l.code = #{params.code}
        </if>
        <if test="params.name != null">
            and l.name = #{params.name}
        </if>
    group by u.real_name,l.code,l.name,l.type,l.operate_time,l.perm_id,p.alias,u.name
      )
    </sql>
    <select id="getByPrimaryId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="All_Column_List"/> from RC_OPERATE_LOG where ID = #{id, jdbcType=NUMERIC}
    </select>
    <select id="getVoListByParams" resultType="com.epaylinks.efps.rc.vo.OperateLogVo">
        select * from
            (select T.*, rownum as RN from
                (select * from <include refid="OPERATELOG_VO_TABLE"/> where 1 = 1
                order by operateTime desc) T
            where rownum <![CDATA[ <= ]]> #{params.endRowNo})
        where RN <![CDATA[ >= ]]> #{params.beginRowNo}
    </select>
    <select id="totalVo" resultType="java.lang.Integer">
        select count(1) from <include refid="OPERATELOG_VO_TABLE"/> where 1 = 1
    </select>
    <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcOperateLog">
        insert into RC_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                ID,
            </if>
            <if test="permId != null">
                PERM_ID,
            </if>
            <if test="code != null">
                CODE,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="operateContent != null">
                OPERATE_CONTENT,
            </if>
            <if test="origValue != null">
                ORIG_VALUE,
            </if>
            <if test="newValue != null">
                NEW_VALUE,
            </if>
            <if test="operator != null">
                OPERATOR,
            </if>
            <if test="operateTime != null">
                OPERATE_TIME,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id, jdbcType=DECIMAL},
            </if>
            <if test="permId != null">
                #{permId, jdbcType=DECIMAL},
            </if>
            <if test="code != null">
                #{code, jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name, jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type, jdbcType=DECIMAL},
            </if>
            <if test="operateContent != null">
                #{operateContent, jdbcType=VARCHAR},
            </if>
            <if test="origValue != null">
                #{origValue, jdbcType=VARCHAR},
            </if>
            <if test="newValue != null">
                #{newValue, jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator, jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                #{operateTime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryId" parameterType="com.epaylinks.efps.rc.domain.RcOperateLog">
        update RC_OPERATE_LOG
        <set>
            <if test="permId != null">
                PERM_ID = #{permId, jdbcType=DECIMAL},
            </if>
            <if test="code != null">
                CODE = #{code, jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name, jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                TYPE = #{type, jdbcType=DECIMAL},
            </if>
            <if test="operateContent != null">
                OPERATE_CONTENT = #{operateContent, jdbcType=VARCHAR},
            </if>
            <if test="origValue != null">
                ORIG_VALUE = #{origValue, jdbcType=VARCHAR},
            </if>
            <if test="newValue != null">
                NEW_VALUE = #{newValue, jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                OPERATOR = #{operator, jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                OPERATE_TIME = #{operateTime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id, jdbcType=NUMERIC}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.rc.dao.RiskEventRuleMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RiskEventRule" >
    <!--@Table RC_RISK_EVENT_RULE-->
    <id column="RULE_ID" property="ruleId" jdbcType="DECIMAL" />
    <result column="RULE_CODE" property="ruleCode" jdbcType="VARCHAR" />
    <result column="RULE_TYPE" property="ruleType" jdbcType="VARCHAR" />
    <result column="RULE_DESC" property="ruleDesc" jdbcType="VARCHAR" />
    <result column="RULE_PARAM1" property="ruleParam1" jdbcType="VARCHAR" />
    <result column="RULE_PARAM2" property="ruleParam2" jdbcType="VARCHAR" />
    <result column="TRIGGER_ACTION" property="triggerAction" jdbcType="VARCHAR" />
    <result column="RULE_STATUS" property="ruleStatus" jdbcType="VARCHAR" />
    <result column="ON_TIME" property="onTime" jdbcType="VARCHAR" />
    <result column="OFF_TIME" property="offTime" jdbcType="VARCHAR" />
    <result column="PARAM_TYPE" property="paramType" jdbcType="VARCHAR" />
    <result column="CHANGE_PERSON" property="changePerson" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="DECIMAL" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="uniqueId" property="uniqueId" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="scene" property="scene" jdbcType="VARCHAR" />
    <result column="TRIGGER_TYPE" property="triggerType" jdbcType="DECIMAL" />
    <result column="RULE_CONDITION" property="ruleCondition" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    RULE_ID, RULE_CODE, RULE_TYPE, RULE_DESC, RULE_PARAM1, RULE_PARAM2,
    TRIGGER_ACTION, RULE_STATUS, ON_TIME, OFF_TIME, PARAM_TYPE, CHANGE_PERSON, UPDATE_TIME, AUDIT_STATUS, REMARKS,uniqueId,file_name, scene,TRIGGER_TYPE,RULE_CONDITION
  </sql>
  <select id="selectRuleByID" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from RC_RISK_EVENT_RULE
    where RULE_ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectRuleByCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from RC_RISK_EVENT_RULE
    where RULE_CODE = #{ruleCode,jdbcType=VARCHAR}
  </select>
  <update id="updateParamById" parameterType="com.epaylinks.efps.rc.domain.RiskEventRule" >
    update RC_RISK_EVENT_RULE
    <set >
      <if test="ruleType != null" >
        RULE_TYPE = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null" >
        RULE_DESC = #{ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="ruleParam1 != null" >
        RULE_PARAM1 = #{ruleParam1,jdbcType=VARCHAR},
      </if>
      <if test="ruleParam2 != null" >
        RULE_PARAM2 = #{ruleParam2,jdbcType=VARCHAR},
      </if>
      <if test="triggerAction != null" >
        TRIGGER_ACTION = #{triggerAction,jdbcType=VARCHAR},
      </if>
      <if test="ruleStatus != null" >
        RULE_STATUS = #{ruleStatus,jdbcType=VARCHAR},
      </if>
      <if test="onTime != null" >
        ON_TIME = #{onTime,jdbcType=VARCHAR},
      </if>
      <if test="offTime != null" >
        OFF_TIME = #{offTime,jdbcType=VARCHAR},
      </if>
      <if test="changePerson != null" >
        CHANGE_PERSON = #{changePerson,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS = #{auditStatus,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=DECIMAL},
      </if>
      <if test="uniqueId != null" >
        uniqueId = #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="ruleCondition != null" >
        RULE_CONDITION = #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="scene != null" >
        SCENE = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="triggerType != null" >
        TRIGGER_TYPE = #{triggerType,jdbcType=DECIMAL},
      </if>
    </set>
    where RULE_ID = #{ruleId,jdbcType=DECIMAL}
  </update>
  <update id="setRuleON" parameterType="com.epaylinks.efps.rc.domain.RiskEventRule" >
    update RC_RISK_EVENT_RULE
    set RULE_STATUS = '01',ON_TIME = #{onTime,jdbcType=VARCHAR},OFF_TIME = NULL,CHANGE_PERSON = #{changePerson,jdbcType=VARCHAR},UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where RULE_ID = #{ruleId,jdbcType=DECIMAL}
  </update>
  <update id="setRuleOFF" parameterType="com.epaylinks.efps.rc.domain.RiskEventRule" >
    update RC_RISK_EVENT_RULE
    set RULE_STATUS = '02',ON_TIME = NULL,OFF_TIME = #{offTime,jdbcType=VARCHAR},CHANGE_PERSON = #{changePerson,jdbcType=VARCHAR},UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where RULE_ID = #{ruleId,jdbcType=DECIMAL}
  </update>

  <select id="countRulePage" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT
    count(*)
    FROM RC_RISK_EVENT_RULE
    <where>
      <trim>
        <if test="ruleStatus != null and ruleStatus != ''">
          and RULE_STATUS = #{ruleStatus,jdbcType = VARCHAR}
        </if>
        <if test="auditStatus != null">
          and AUDIT_STATUS = #{auditStatus,jdbcType = DECIMAL}
        </if>
      </trim>
    </where>
  </select>

  <select id="selectRulePage" parameterType="java.util.Map" resultMap="BaseResultMap">
    select *
    from (
      select A.*,rownum rn
      from (
        SELECT
        <include refid="Base_Column_List" />
        FROM RC_RISK_EVENT_RULE
        <where>
          <trim>
            <if test="ruleStatus != null and ruleStatus != ''">
              and RULE_STATUS = #{ruleStatus,jdbcType = VARCHAR}
            </if>
            <if test="auditStatus != null">
              and AUDIT_STATUS = #{auditStatus,jdbcType = DECIMAL}
            </if>
          </trim>
        </where>
        ORDER BY RULE_CODE
      ) A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

 <select id="selectRuleList" resultMap="BaseResultMap">
   SELECT 
   <include refid="Base_Column_List" />
   FROM RC_RISK_EVENT_RULE
   <where>
    <trim>
      <if test="ruleStatus != null and ruleStatus != ''">
         and RULE_STATUS = #{ruleStatus,jdbcType = VARCHAR}
      </if>
      <if test="auditStatus != null">
         and AUDIT_STATUS = #{auditStatus,jdbcType = DECIMAL}
      </if>
    </trim>
   </where>
   ORDER BY RULE_CODE
 </select>


    <!-- 碰一碰预警规则设置 -->
    <update id="updateNFCByRuleCodeSelective" parameterType="com.epaylinks.efps.rc.vo.TxsNfcRcMode" >
        update txs_nfc_rc_mode t
        <set >
          <if test="value != null" >
            t.value = #{value, jdbcType=DECIMAL},
          </if>
          <if test="bandOutCome != null" >
            t.band_out_come = #{bandOutCome, jdbcType=VARCHAR},
          </if>
          <if test="bandBusiness != null" >
            t.band_business = #{bandBusiness, jdbcType=VARCHAR},
          </if>
          <if test="valid != null" >
            t.valid = #{valid, jdbcType=VARCHAR},
          </if>
        </set>
        where t.rc_rule_code = #{rcRuleCode,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <insert id="insertSelective">
    INSERT INTO RC_RISK_EVENT_RULE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        RULE_ID,
      </if>
      <if test="ruleCode != null">
        RULE_CODE,
      </if>
      <if test="ruleType != null">
        RULE_TYPE,
      </if>
      <if test="ruleDesc != null">
        RULE_DESC,
      </if>
      <if test="ruleParam1 != null">
        RULE_PARAM1,
      </if>
      <if test="ruleParam2 != null">
        RULE_PARAM2,
      </if>
      <if test="triggerAction != null">
        TRIGGER_ACTION,
      </if>
      <if test="ruleStatus != null">
        RULE_STATUS,
      </if>
      <if test="onTime != null">
        ON_TIME,
      </if>
      <if test="offTime != null">
        OFF_TIME,
      </if>
      <if test="paramType != null">
        PARAM_TYPE,
      </if>
      <if test="changePerson != null">
        CHANGE_PERSON,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="remarks != null">
        REMARKS,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="uniqueId != null">
        uniqueId,
      </if>
      <if test="ruleCondition != null">
        RULE_CONDITION,
      </if>
      <if test="scene != null">
        SCENE,
      </if>
      <if test="triggerType != null">
        TRIGGER_TYPE,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=DECIMAL},
      </if>
      <if test="ruleCode != null">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null">
        #{ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="ruleParam1 != null">
        #{ruleParam1,jdbcType=VARCHAR},
      </if>
      <if test="ruleParam2 != null">
        #{ruleParam2,jdbcType=VARCHAR},
      </if>
      <if test="triggerAction != null">
        #{triggerAction,jdbcType=VARCHAR},
      </if>
      <if test="ruleStatus != null">
        #{ruleStatus,jdbcType=VARCHAR},
      </if>
      <if test="onTime != null">
        #{onTime,jdbcType=VARCHAR},
      </if>
      <if test="offTime != null">
        #{offTime,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="changePerson != null">
        #{changePerson,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="ruleCondition != null">
        #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="triggerType != null">
        #{triggerType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <delete id="deleteByRuleId">
    delete from RC_RISK_EVENT_RULE
    where RULE_ID=#{ruleId,jdbcType=DECIMAL}
  </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.rc.dao.RcDroolsWhenMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.rc.domain.RcDroolsWhen">
    <!--@mbg.generated-->
    <!--@Table RC_DROOLS_WHEN-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="LEFT_TEXT" jdbcType="VARCHAR" property="leftText" />
    <result column="LEFT_DRL" jdbcType="VARCHAR" property="leftDrl" />
    <result column="OPERATOR_TEXT" jdbcType="VARCHAR" property="operatorText" />
    <result column="OPERATOR_DRL" jdbcType="VARCHAR" property="operatorDrl" />
    <result column="RIGHT_TEXT" jdbcType="VARCHAR" property="rightText" />
    <result column="RIGHT_DRL" jdbcType="VARCHAR" property="rightDrl" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LEFT_TEXT, LEFT_DRL, OPERATOR_TEXT, OPERATOR_DRL, RIGHT_TEXT, RIGHT_DRL, CREATE_TIME, 
    UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from RC_DROOLS_WHEN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from RC_DROOLS_WHEN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.rc.domain.RcDroolsWhen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_WHEN (ID, LEFT_TEXT, LEFT_DRL, 
      OPERATOR_TEXT, OPERATOR_DRL, RIGHT_TEXT, 
      RIGHT_DRL, CREATE_TIME, UPDATE_TIME
      )
    values (#{id,jdbcType=DECIMAL}, #{leftText,jdbcType=VARCHAR}, #{leftDrl,jdbcType=VARCHAR}, 
      #{operatorText,jdbcType=VARCHAR}, #{operatorDrl,jdbcType=VARCHAR}, #{rightText,jdbcType=VARCHAR}, 
      #{rightDrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsWhen">
    <!--@mbg.generated-->
    insert into RC_DROOLS_WHEN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="leftText != null">
        LEFT_TEXT,
      </if>
      <if test="leftDrl != null">
        LEFT_DRL,
      </if>
      <if test="operatorText != null">
        OPERATOR_TEXT,
      </if>
      <if test="operatorDrl != null">
        OPERATOR_DRL,
      </if>
      <if test="rightText != null">
        RIGHT_TEXT,
      </if>
      <if test="rightDrl != null">
        RIGHT_DRL,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="leftText != null">
        #{leftText,jdbcType=VARCHAR},
      </if>
      <if test="leftDrl != null">
        #{leftDrl,jdbcType=VARCHAR},
      </if>
      <if test="operatorText != null">
        #{operatorText,jdbcType=VARCHAR},
      </if>
      <if test="operatorDrl != null">
        #{operatorDrl,jdbcType=VARCHAR},
      </if>
      <if test="rightText != null">
        #{rightText,jdbcType=VARCHAR},
      </if>
      <if test="rightDrl != null">
        #{rightDrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.rc.domain.RcDroolsWhen">
    <!--@mbg.generated-->
    update RC_DROOLS_WHEN
    <set>
      <if test="leftText != null">
        LEFT_TEXT = #{leftText,jdbcType=VARCHAR},
      </if>
      <if test="leftDrl != null">
        LEFT_DRL = #{leftDrl,jdbcType=VARCHAR},
      </if>
      <if test="operatorText != null">
        OPERATOR_TEXT = #{operatorText,jdbcType=VARCHAR},
      </if>
      <if test="operatorDrl != null">
        OPERATOR_DRL = #{operatorDrl,jdbcType=VARCHAR},
      </if>
      <if test="rightText != null">
        RIGHT_TEXT = #{rightText,jdbcType=VARCHAR},
      </if>
      <if test="rightDrl != null">
        RIGHT_DRL = #{rightDrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.rc.domain.RcDroolsWhen">
    <!--@mbg.generated-->
    update RC_DROOLS_WHEN
    set LEFT_TEXT = #{leftText,jdbcType=VARCHAR},
      LEFT_DRL = #{leftDrl,jdbcType=VARCHAR},
      OPERATOR_TEXT = #{operatorText,jdbcType=VARCHAR},
      OPERATOR_DRL = #{operatorDrl,jdbcType=VARCHAR},
      RIGHT_TEXT = #{rightText,jdbcType=VARCHAR},
      RIGHT_DRL = #{rightDrl,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <select id="selectAllOrderById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RC_DROOLS_WHEN order by ID asc
    </select>
</mapper>
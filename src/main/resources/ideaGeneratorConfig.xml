<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<!--location=本地ojbdc6的地址 -->
	<classPathEntry
		location="F:\MavenWarehouse\com\oracle\ojdbc6\11.1.0.7.0\ojdbc6-11.1.0.7.0.jar" />
	<context id="context1"> 
		<property name="javaFileEncoding" value="UTF-8" />
		<!-- type=:CustomCommentGenerator类的地址 -->
		<commentGenerator type="com.epaylinks.efps.rc.CustomCommentGenerator" >
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

		<jdbcConnection connectionURL="*******************************************"
			driverClass="oracle.jdbc.driver.OracleDriver" password="efps#123"
			userId="efps">
			<!-- 针对oracle数据库 -->
			<property name="remarksReporting" value="true"></property>
		</jdbcConnection>


		<javaModelGenerator targetPackage="com.epaylinks.efps.rc.domain"
			targetProject="E:\test" />

		<sqlMapGenerator targetPackage="com.epaylinks.efps.rc.domain"
			targetProject="E:\test" />

		<javaClientGenerator targetPackage="com.epaylinks.efps.rc.domain"
			targetProject="E:\test" type="XMLMAPPER" />


		<table tableName="rc_define_group" domainObjectName="RcDefineGroup"
			   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false"
			   enableSelectByExample="false" selectByExampleQueryId="false">
		</table>
	</context>
</generatorConfiguration>
package com.epaylinks.efps;

import com.epaylinks.efps.rc.drools.DroolsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.kafka.annotation.EnableKafka;

import com.epaylinks.efps.common.util.SpringContextUtils;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PostConstruct;

/**
 * 项目启动类
 * <AUTHOR>
 *
 */

@RefreshScope
@EnableEurekaClient
@EnableFeignClients
@SpringBootApplication
@EnableCaching
@EnableScheduling
@EnableAspectJAutoProxy(proxyTargetClass=true , exposeProxy=true)
@Configuration
public class RcApplication {
	@Autowired
	private DroolsService droolsService;

	@PostConstruct
	public void initRcDrools() {
		droolsService.init();
	}

	public static void main(String[] args) {
		SpringApplication springApplication = new SpringApplication(RcApplication.class);
        SpringContextUtils.setApplicationContext(springApplication.run(args));
	}
}

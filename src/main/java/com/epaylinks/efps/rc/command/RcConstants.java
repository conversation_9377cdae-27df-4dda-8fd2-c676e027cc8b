package com.epaylinks.efps.rc.command;

import com.epaylinks.efps.common.util.Constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface RcConstants extends Constants {
    /**
     * 风控指标
     */
    enum RcIndex {
        AMOUNT("amount", "金额指标"),
        TRADE_NUM("tradeNum", "交易笔数指标"),
        BANK_USER_NAME("bankUserName", "银行卡户名"),
        WITHDRAW_CHANNEL_TYPE("withdrawChannelType", "提现渠道类型"),
        POS_TXN_TYPE("posTxnType", "POS交易类型"),
        CARD_TYPE("cardType", "卡类型,C-贷记卡,D-借记卡,S-准贷记卡"),
        BANK_ACCOUNT("bankAccount", "银行卡户号"),
        ;

        public final String code;
        public final String comment;

        RcIndex(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 风控计算对象类型
     */
    enum BusinessTagerType {
        IDENTITY_CARD("001", "身份证", DefineCode.ID_BLACK_LIST),
        CELL_PHONE_NUMBER("002", "手机号", DefineCode.PHONE_BLACK_LIST),
        BUSINESS_LICENSE("003", "统一社会信用代码", DefineCode.USCC_BLACK_LIST),
        BANK_CARD("004", "银行卡", DefineCode.BANK_BLACK_LIST),
        CUSTOMER_CODE("005", "商户编号", DefineCode.CUSTOMER_CODE_BLACK_LIST),
        IDENTITY_CARD_PREFIX("006", "身份证前4位", DefineCode.ID_4_BLACK_LIST),
        BUSINESS_ADDRESS("007", "经营地址", DefineCode.BIZ_ADDR_BLACK_LIST),
        TERM("008", "终端", null),
        PERSON("009", "个人账户", null),
        PLAT_CUSTOMER("010", "平台商", null),
        WEB("011", "网站", null),
        CLIENT_NO("012", "客户号", null),
        INDUSTRY("013", "行业", null),
        CUSTOMER_NAME("014", "商户名称", DefineCode.CUSTOMER_NAME_BLACK_LIST),
        ORDER_NO("015","订单号",null)
        ;
        public final String code;
        public final String message;
        public final DefineCode blackType;

        private BusinessTagerType(String code, String message, DefineCode blackType) {
            this.code = code;
            this.message = message;
            this.blackType = blackType;
        }

        public static boolean contains(String code) {
            BusinessTagerType[] businessTagerTypes = BusinessTagerType.values();
            for (BusinessTagerType businessTagerType : businessTagerTypes) {
                if (businessTagerType.code.equals(code)) {
                    return true;
                }
            }
            return false;
        }

        public static String getCommentByCode(String code) {
            for (BusinessTagerType enumV : BusinessTagerType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return null;
        }

        public static String getCodeByComment(String comment) {
            for (BusinessTagerType enumV : BusinessTagerType.values()) {
                if (enumV.message.equals(comment)) {
                    return enumV.code;
                }
            }
            return null;
        }

        public static BusinessTagerType getBusinessTagerTypeByCode(String code) {
            for (BusinessTagerType enumV : BusinessTagerType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV;
                }
            }
            return null;
        }
    }

    /**
     * 前端风控日志查询翻译查询风控对象类型
     */
    enum RCTargetType {
        IDENTITY_CARD("001", "身份证"),
        CELL_PHONE_NUMBER("002", "手机号"),
        BUSINESS_LICENSE("003", "统一社会信用代码"),
        BANK_CARD("004", "银行卡"),
        CUSTOMER_CODE("005", "商户"),
        TERM("008", "终端"),
        PERSON("009", "个人"),
        PLAT_CUSTOMER("010", "平台商"),
        IDENTITY_CARD_PREFIX("006", "身份证前4位"),
        BUSINESS_ADDRESS("007", "经营地址"),
        CLIENT_NO("012", "客户号"),
        INDUSTRY("013", "行业"),
        CUSTOMER_NAME("014", "商户名称"),
        ;

        public final String code;
        public final String message;

        private RCTargetType(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static RCTargetType getTargetType(String code) {
            for (RCTargetType rcTargetType : RCTargetType.values()) {
                if (rcTargetType.code.equals(code)) {
                    return rcTargetType;
                }
            }
            return null;
        }
    }


    enum RcStatus {
        NORMAL("0", "正常"),
        FROZEN("1", "冻结");
        public final String code;
        public final String message;

        private RcStatus(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    /**
     * EFPS业务编码
     */
    enum BusinessCode {
        YMF("YMF", "一码付"),
        AlIJSAPI("AliJSAPI", "支付宝生活号支付"),
        AlIMICRO("AliMicro", "支付宝被扫支付"),
        WXMICRO("WxMicro", "微信被扫支付"),
        AlINATIVE("AliNative", "支付宝主扫支付"),
        QUICKPAY("QuickPay", "储蓄卡快捷支付"),
        QUICKPAYCREDIT("QuickPayCredit", "信用卡快捷支付"),
        PROTOCOLPAY("ProtocolPay", "储蓄卡协议支付"),
        PROTOCOLPAYCREDIT("ProtocolPayCredit", "信用卡协议支付"),
        WXMWEB("WxMWEB", "微信H5支付"),
        WXJSAPI("WxJSAPI", "微信公众号支付"),
        WXNATIVE("WxNatvie", "微信主扫支付"),
        WXAPP("WxAPP", "微信APP支付"),
        SAVINGCARDPAY("SavingCardPay", "个人网银储蓄卡支付"),
        CREDITCARDPAY("CreditCardPay", "个人网银信用卡支付"),
        ENTERPRISEUNION("EnterpriseUnion", "企业网银支付"),
        REFUND_USING_FLOAT("RefundUsingFloat", "支付_在途金额退款"),
        // 以下4个普通代付
        WITHDRAW("Withdraw", "代付到储蓄卡"),
        WITHDRAW_SAVINGCARD_BATCH("Withdraw-SavingCard-Batch", "批量代付到储蓄卡"),
        WITHDRAW_CREDITCARD("Withdraw-CreditCard", "代付到信用卡"),
        WITHDRAW_CREDITCARD_BATCH("Withdraw-CreditCard-Batch", "批量代付到信用卡"),
        // 提现业务 ，原代付分离出来业务 2020.4.2
        WITHDRAWTOSETTMENT_DEBITCARD("WithdrawToSettmentDebit", "提现(到结算储蓄卡)"),
        WITHDRAWTOSETTMENT_CREDITCARD("WithdrawToSettmentCredit", "提现到结算信用卡"),

        // 以下4个垫资代付
        DZ_WITHDRAW_CREDITCARD("DZ-Withdraw-CreditCard", "垫资代付到信用卡"),
        DZ_WITHDRAW_CREDITCARD_BATCH("DZ-Withdraw-CreditCard-Batch", "批量垫资代付到信用卡"),
        DZ_WITHDRAW_SAVINGCARD("DZ-Withdraw-SavingCard", "垫资代付到储蓄卡"),
        DZ_WITHDRAW_SAVINGCARD_BATCH("DZ-Withdraw-SavingCard-Batch", "批量垫资代付到储蓄卡"),

        D0_QUICKPAY("D0-QuickPay", "接口垫资代付-储蓄卡快捷支付"), // 垫资代付已排除，20181213
        D0_WITHDRAW_AUTO("D0-Withdraw-Auto", "接口垫资代付");    // 垫资代付已排除，20181213

        public final String code;
        public final String comment;

        BusinessCode(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum KafkaKey {
        WITHDRAW("Withdrawals", "代付"),
        GATEWAY_PAYMENT("GatewayPayment", "入金支付");
        public final String code;
        public final String message;

        private KafkaKey(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    enum RcLevel {
        LOW_LEVEL("LOW_LEVEL", "低风险"),
        MIDDLE_LEVEL("MIDDLE_LEVEL", "中风险"),
        HIGHT_LEVEL("HIGHT_LEVEL", "高风险");

        public final String code;
        public final String message;

        RcLevel(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (RcLevel enumV : RcLevel.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return "";
        }
    }

    enum RcToCustLevel {
        LOW_LEVEL("LOW_LEVEL", "1"),
        MIDDLE_LEVEL("MIDDLE_LEVEL", "2"),
        HIGHT_LEVEL("HIGHT_LEVEL", "3");

        public final String code;
        public final String message;

        RcToCustLevel(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (RcToCustLevel enumV : RcToCustLevel.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return "";
        }

        public static String getCodeByMessage(String message) {
            for (RcToCustLevel enumV : RcToCustLevel.values()) {
                if (enumV.message.equals(message)) {
                    return enumV.code;
                }
            }
            return "";
        }
    }

    enum RcLimitType {
        AMOUNT("001", "限额类"),
        STATUS("002", "状态类"),
        BLACK("003", "黑名单类"),
        ;
        public final String code;
        public final String message;

        RcLimitType(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static RcLimitType getLimitType(String code) {
            for (RcLimitType rcLimitType : RcLimitType.values()) {
                if (rcLimitType.code.equals(code)) {
                    return rcLimitType;
                }
            }
            return null;
        }
    }

    enum RcLimitDefineType {
        SINGLT_IN("Single-Max-IN-Amount", "001", "限额类"),
        DAY_IN("Day-Max-IN-Amount", "001", "限额类"),
        MONTH_IN("Month-Max-IN-Amount", "001", "限额类"),
        YEAR_IN("Year-Max-IN-Amount", "001", "限额类"),
        DAY_IN_COUNT("Day-Max-IN-Count", "001", "限额类"),
        MONTH_IN_COUNT("Month-Max-IN-Count", "001", "限额类"),
        YEAR_IN_COUNT("Year-Max-IN-Count", "001", "限额类"),
        SINGLT_OUT("Single-Max-OUT-Amount", "001", "限额类"),
        DAY_OUT("Day-Max-OUT-Amount", "001", "限额类"),
        DAY_OUT_COUNT("Day-Max-OUT-Count", "001", "限额类"),
        MONTH_OUT("Month-Max-OUT-Amount", "001", "限额类"),
        YEAR_OUT("Year-Max-OUT-Amount", "001", "限额类"),
        RC_STATUS("RC-STATUS", "002", "状态类"),
        ACCOUNT_STATUS("ACCOUNT-STATUS", "002", "状态类"),
        RC_LEVEL("RC-LEVEL", "/", "/"),
        DAY_WITHDRAW("Withdraw-Day-Max-Amount", "001", "限额类"),
        DAY_WITHDRAW_COUNT("Withdraw-Day-Max-Count", "001", "限额类"),
        CARD_DAY_OUT_AMOUNT("Card-Day-Max-OUT-Amount", "001", "限额类"),
        CARD_DAY_OUT_COUNT("Card-Day-Max-OUT-Count", "001", "限额类"),
        CARD_MONTH_OUT_AMOUNT("Card-Month-Max-OUT-Amount", "001", "限额类"),
        CARD_MONTH_OUT_COUNT("Card-Month-Max-OUT-Count", "001", "限额类"),
        RC_BALANCE("RC-BALANCE", "/", "/"),
        CARD_DAY_IN_AMOUNT("Card-Day-Max-IN-Amount", "001", "限额类"),
        CARD_DAY_IN_COUNT("Card-Day-Max-IN-Count", "001", "限额类"),
        CARD_MONTH_IN_AMOUNT("Card-Month-Max-IN-Amount", "001", "限额类"),
        CARD_MONTH_IN_COUNT("Card-Month-Max-IN-Count", "001", "限额类"),
        USER_DAY_IN_AMOUNT("User-Day-Max-IN-Amount", "001", "限额类"),
        USER_DAY_IN_COUNT("User-Day-Max-IN-Count", "001", "限额类"),
        USER_MONTH_IN_AMOUNT("User-Month-Max-IN-Amount", "001", "限额类"),
        USER_MONTH_IN_COUNT("User-Month-Max-IN-Count", "001", "限额类"),
        BANK_BLACK_LIST("BANK_BLACK_LIST", "003", "黑名单类型"),
        PHONE_BLACK_LIST("PHONE_BLACK_LIST", "003", "黑名单类型"),
        ID_BLACK_LIST("ID_BLACK_LIST", "003", "黑名单类型"),
        USCC_BLACK_LIST("USCC_BLACK_LIST", "003", "黑名单类型"),
        CUSTOMER_CODE_BLACK_LIST("CUSTOMER_CODE_BLACK_LIST", "003", "黑名单类型"),

        // ******** 新增，不配置没日志。。
        SINGLE_MAX_WITHDRAW_AMOUNT("Single-Max-Withdraw-Amount", "001", "限额类"),
        DAY_MAX_WITHDRAW_AMOUNT("Day-Max-Withdraw-Amount", "001", "限额类"),
        MONTH_MAX_WITHDRAW_AMOUNT("Month-Max-Withdraw-Amount", "001", "限额类"),
        YEAR_MAX_WITHDRAW_AMOUNT("Year-Max-Withdraw-Amount", "001", "限额类"),
        CARD_DAY_IN_TOTAL_COUNT("Card-Day-Max-IN-Total-Count", "001", "限额类"),
        CARD_MONTH_IN_TOTAL_COUNT("Card-Month-Max-IN-Total-Count", "001", "限额类"),
        USER_DAY_IN_TOTAL_COUNT("User-Day-Max-IN-Total-Count", "001", "限额类"),
        USER_MONTH_IN_TOTAL_COUNT("User-Month-Max-IN-Total-Count", "001", "限额类"),
        // 20210810 新增，不配置没日志。。
        CERT_DAY_MAX_IN_AMOUNT("Cert-Day-Max-IN-Amount", "001", "限额类"),
        CERT_MONTH_MAX_IN_AMOUNT("Cert-Month-Max-IN-Amount", "001", "限额类"),
        CERT_DAY_MAX_CREDIT_AMOUNT("Cert-Day-Max-Credit-Amount", "001", "限额类"),
        CERT_MONTH_MAX_CREDIT_AMOUNT("Cert-Month-Max-Credit-Amount", "001", "限额类"),
        TOTAL_MAX_OUT_AMOUNT("Total-Max-OUT-Amount", "001", "限额类"),
        USER_DAY_MAX_OUT_AMOUNT("User-Day-Max-OUT-Amount", "001", "限额类"),
        USER_YEAR_MAX_OUT_AMOUNT("User-Year-Max-OUT-Amount", "001", "限额类"),
        ACCOUNT_NAME_LIMIT("ACCOUNT-NAME-LIMIT", "002", "状态类"),

        DALIY_OUT_LIMIT("Daliy-Out-Limit", "001", "限额类"),
        SINGLE_OUT_LIMIT("Single-Out-Limit", "001", "限额类"),

        ID_4_BLACK_LIST("ID_4_BLACK_LIST", "003", "黑名单类型"),
        BIZ_ADDR_BLACK_LIST("BIZ_ADDR_BLACK_LIST", "003", "黑名单类型"),

        PERSON_TRANS_COMPANY("PERSON_TRANS_COMPANY", "002", "状态类"),
        PERSON_TRANS_PERSON("PERSON_TRANS_PERSON", "002", "状态类"),
        BALANCE_TRANS_AMOUNT("BALANCE_TRANS_AMOUNT", "001", "限额类"),
        BALANCE_TRANS_DAY_AMOUNT("BALANCE_TRANS_DAY_AMOUNT", "001", "限额类"),
        BALANCE_WITHDRAW_AMOUNT("BALANCE_WITHDRAW_AMOUNT", "001", "限额类"),
        BALANCE_WITHDRAW_DAY_AMOUNT("BALANCE_WITHDRAW_DAY_AMOUNT", "001", "限额类"),
        BALANCE_CONSUME_AMOUNT("BALANCE_CONSUME_AMOUNT", "001", "限额类"),
        BALANCE_CONSUME_DAY_AMOUNT("BALANCE_CONSUME_DAY_AMOUNT", "001", "限额类"),
        BALANCE_IN_AMOUNT("BALANCE_IN_AMOUNT", "001", "限额类"),
        BALANCE_IN_DAY_AMOUNT("BALANCE_IN_DAY_AMOUNT", "001", "限额类"),

        CREDIT_CARD_IN_AMOUNT("CREDIT_CARD_IN_AMOUNT", "001", "限额类"),
        CREDIT_CARD_IN_DAY_AMOUNT("CREDIT_CARD_IN_DAY_AMOUNT", "001", "限额类"),
        CREDIT_CARD_IN_MON_AMOUNT("CREDIT_CARD_IN_MON_AMOUNT", "001", "限额类"),
        DEBIT_CARD_IN_AMOUNT("DEBIT_CARD_IN_AMOUNT", "001", "限额类"),
        DEBIT_CARD_IN_DAY_AMOUNT("DEBIT_CARD_IN_DAY_AMOUNT", "001", "限额类"),
        DEBIT_CARD_IN_MON_AMOUNT("DEBIT_CARD_IN_MON_AMOUNT", "001", "限额类"),

        TRANSFER_QUOTA("TRANSFER_QUOTA", "001", "限额类"),
        TRANSFER_TIME("TRANSFER_TIME", "001", "限额类"),

        CUSTOMER_NAME_BLACK_LIST("CUSTOMER_NAME_BLACK_LIST", "003", "黑名单类型"),

        INST_CARD_DAY_IN_AMOUNT("INST_CARD_DAY_IN_AMOUNT", "001", "限额类"),
        INST_USER_DAY_IN_AMOUNT("INST_USER_DAY_IN_AMOUNT", "001", "限额类"),

        ;

        public final String defineCode;
        public final String code;
        public final String name;

        RcLimitDefineType(String defineCode, String code, String name) {
            this.defineCode = defineCode;
            this.code = code;
            this.name = name;
        }

        public static RcLimitDefineType getRcLimitType(String defineCode) {
            for (RcLimitDefineType rclimitDefineType : RcLimitDefineType.values()) {
                if (rclimitDefineType.defineCode.equals(defineCode)) {
                    return rclimitDefineType;
                }
            }
            return null;
        }
    }

    /**
     * 商户角色
     *
     * <AUTHOR>
     * @date 2020-07-08
     */
    enum CustomerCategory {
        ORDINARY((short) 0, "普通商户"),
        PLATFORM((short) 2, "平台商户"),
        SERVICE((short) 3, "服务商");
        public final Short code;
        public final String comment;

        CustomerCategory(Short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 商户限额审核状态
     *
     * <AUTHOR>
     * @date 2020-08-06
     */
    enum AudStatus {
        WAITING("01", "待审核"),
        FAIL("02", "审核不通过"),
        SUCCESS("03", "审核通过");
        public final String code;
        public final String comment;

        AudStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 风控商户状态
     *
     * <AUTHOR>
     */
    enum RcCustomerStatus {
        NORMAL("1", "正常"),
        //      FROZEN("2", "冻结"),  // 未使用
        CANCEL("3", "注销");
        public final String code;
        public final String comment;

        RcCustomerStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getMessageByCode(String code) {
            for (RcCustomerStatus enumV : RcCustomerStatus.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return "";
        }
    }

    /**
     * 名单状态：0：未启用，1：已启用
     *
     * <AUTHOR>
     * @date 2020-11-17
     */
    enum UseStatus {
        DISABLE(new Short("0"), "未启用"),
        ENABLE(new Short("1"), "已启用");
        public final Short code;
        public final String comment;

        UseStatus(Short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 名单审核状态：0：待审核，1：正常
     *
     * <AUTHOR>
     * @date 2020-11-13
     */
    enum BwListStatus {
        WAITING(new Short("0"), "待审核"),
        NORMAL(new Short("1"), "正常");
        public final Short code;
        public final String comment;

        BwListStatus(Short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 审核记录状态：0：待审核，1：审核通过，2：审核不通过
     *
     * <AUTHOR>
     * @date 2020-11-11
     */
    enum AuditStatus {
        WAITING(new Short("0"), "待审核"),
        SUCCESS(new Short("1"), "审核通过"),
        FAIL(new Short("2"), "审核不通过");
        public final Short code;
        public final String comment;

        AuditStatus(Short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 审核记录审核类型
     *
     * <AUTHOR>
     * @date 2020-11-11
     */
    enum AuditTargetType {
        BW_LIST("BW_LIST", "黑名单"),
        ACCOUNT_STATUS("ACCOUNT_STATUS", "账户状态"),
        RISK_CONTROL_STATUS("RISK_CONTROL_STATUS", "风控状态"),
        RISK_RULE("RISK_RULE", "可疑事件规则"),
        PHONE_BW_LIST("PHONE_BW_LIST", "手机白名单"),
        RISK_LEVEL("RISK_LEVEL", "风险等级"),
        RISK_CONTROL_FREEZING("RISK_CONTROL_FREEZING", "风控冻结金额"),
        WITHDRAWAL_REPORT("WITHDRAWAL_REPORT", "出金报备"),
        AUTHENTICATION_FINANCING("AUTHENTICATION_FINANCING", "鉴权理财"),
        RECHARGE("RECHARGE", "充值"),
        TRANSACTION("TRANSACTION", "交易"),
        VERIFY_CONFIG("VERIFY_CONFIG", "核验配置"),
        TEMPORARY_DEPOSIT_LIMIT("TEMPORARY_DEPOSIT_LIMIT", "临时入金限制"),
        PAYMENT_SETTING("PAYMENT_SETTING", "代付设置"),
        ORDER_TRANS_PAY_SETTING("ORDER_TRANS_PAY_SETTING","订单转账支付设置");
        public final String code;
        public final String comment;

        AuditTargetType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum VerifyConfig {
        DEFAULT("0", "系统默认"),
        XL("7", "信联"),
        YS("8", "羽山"),
        LR("5", "联润-涉赌涉传销B"),
        YS_XL("87", "羽山+信联"),
        YS_LR("85", "羽山+联润涉赌涉传销B"),
        LR_G("10", "联润-涉赌涉欺诈G");
        public final String code;
        public final String comment;

        VerifyConfig(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getMessageByCode(String code) {
            for (VerifyConfig enumV : VerifyConfig.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return "";
        }

        public static String getCodeByMessage(String message) {
            for (VerifyConfig enumV : VerifyConfig.values()) {
                if (enumV.comment.equals(message)) {
                    return enumV.code;
                }
            }
            return DEFAULT.code;
        }

        public static List<Map<String, String>> toList() {
            List<Map<String, String>> list = new ArrayList<>();
            for (VerifyConfig item : VerifyConfig.values()) {
                Map<String, String> map = new HashMap<>();
                map.put("code", item.code);
                map.put("comment", item.comment);
                list.add(map);
            }
            return list;
        }
    }

    /**
     * 风险核验上游名称列表
     */
    enum UpstreamName {
        XL("7", "信联"),
        YS("8", "羽山"),
        LR("5", "联润-涉赌涉传销B"),
        YL_HMD("9", "银联-商户黑名单"),
        LR_G("10", "联润-涉赌涉欺诈G"),
        YL_HGJC("11", "银联-网站合规检测");
        public final String code;
        public final String comment;

        UpstreamName(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static List<Map<String, String>> toList() {
            List<Map<String, String>> list = new ArrayList<>();
            for (UpstreamName item : UpstreamName.values()) {
                Map<String, String> map = new HashMap<>();
                map.put("code", item.code);
                map.put("comment", item.comment);
                list.add(map);
            }
            return list;
        }
    }

    /**
     * 风险标签
     */
    enum RiskTag {
        GAMBLING("gambling", "涉赌","15"),
        PYRAMID("pyramid", "涉传销",null),
        FRAUD("fraud", "涉诈",null),
        CASHOUT("cashout", "疑似套现",null),
        VIOLATION("violation", "疑似违规",null),
        UNION_11("union11","禁入商户类型","11"),
        UNION_12("union12","中国银联日常监控识别并调查确认的“高风险商户”","12"),
        UNION_13("union13","被银联网络相关风险规则认定为“高风险商户”","13"),
        UNION_14("union14","电信诈骗","14"),
        UNION_16("union16","跨境移机","16"),
        UNION_17("union17","其他非法交易","17"),
        UNION_18("union18","其他违规行为","18"),
        UNION_21("union21","虚假申请","21"),
        UNION_22("union22","侧录","22"),
        UNION_23("union23","泄露账户及交易信息","23"),
        UNION_24("union24","套现","24"),
        UNION_25("union25","洗单","25"),
        UNION_26("union26","恶意倒闭","26"),
        UNION_27("union27","虚假交易","27"),
        UNION_28("union28","伪冒交易超过一定比率","28"),
        UNION_29("union29","名义经营范围与实际情况不符","29"),
        UNION_30("union30","因银行卡欺诈交易已被司法机关立案或介入调查","30"),
        UNION_31("union31","违规移机","31"),
        UNION_32("union32","易或纵容、包庇、协助银联卡欺诈交易","32"),
        UNION_33("union33","已被工商管理部门列为经营异常商户、黑名单商户或者已注销的商户","33"),
        UNION_34("union34","商户营销套利","34"),
        UNION_35("union35","洗钱","35"),
        UNION_41("union41","中国银联已书面通知收单机构强制解约","41"),
        UNION_42("union42","已被其他卡组织认定为“高风险商户”","42"),
        UNION_43("union43","经营不善，已破产或停业","43"),
        UNION_44("union44","其他风险原因","44")
        ;
        public final String code;
        public final String comment;
        public final String unionNum; // 银联返回

        RiskTag(String code, String comment,String unionNum) {
            this.code = code;
            this.comment = comment;
            this.unionNum = unionNum;
        }

        public static String trans(String riskTag) {
            if (riskTag == null || "".equals(riskTag)) {
                return null;
            }
            for (RiskTag enumV : RiskTag.values()) {
                riskTag = riskTag.replace(enumV.code, enumV.comment);
            }
            return riskTag;
        }

        public static String getCode(String unionNum) {
            if (unionNum == null || "".equals(unionNum)) {
                return "";
            }
            for (RiskTag enumV : RiskTag.values()) {
                if (unionNum.equals(enumV.unionNum)) {
                    return enumV.code;
                }
            }
            return "";
        }

        public static List<Map<String, String>> riskTagList() {
            List<Map<String, String>> list = new ArrayList<>();
            for (RiskTag item : RiskTag.values()) {
                Map<String, String> map = new HashMap<>();
                map.put("code", item.code);
                map.put("comment", item.comment);
                list.add(map);
            }
            return list;
        }

        public static Map<String, String> getRiskTagMap() {
            Map<String, String> riskTagMap = new HashMap<>();
            riskTagMap.put("none","无风险标签");
            for (RiskTag item : RiskTag.values()) {
                riskTagMap.put(item.code,item.comment);
            }
            return riskTagMap;
        }
    }

    /**
     * 黑名单库-风险标签 RC_BLACK_LIST
     */
    enum BlackListRiskTag {
        UNION_ADJUST("RT001","银联-调单"),
        UNION_QUERY("RT002","银联-查询"),
        INNER_COMPLAIN("RT003","内部投诉"),
        PB_REPORT("RT004","人行举报"),
        CONSUMER_PROTECTION("RT005","消保"),
        INNER_COMPLAIN_CONSUMER_PROTECTION("RT006","内部客诉/消保"),
        UNIONPAY_COMPLAINTS("RT007","银联投诉")
        ;
        public final String code;
        public final String comment;

        BlackListRiskTag(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (BlackListRiskTag enumV : BlackListRiskTag.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return code;
        }

        public static String getCodeByComment(String comment) {
            for (BlackListRiskTag enumV : BlackListRiskTag.values()) {
                if (enumV.comment.equals(comment)) {
                    return enumV.code;
                }
            }
            return comment;
        }
    }

    /**
     * 黑名单库-渠道 RC_BLACK_LIST
     */
    enum BlackListChannel {
        YL("01","银联"),
        XB("02","消保"),
        ZT_RH("03","专投/人行"),
        NBTS("04","内部投诉")
        ;
        public final String code;
        public final String comment;

        BlackListChannel(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (BlackListChannel enumV : BlackListChannel.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return code;
        }

        public static String getCodeByComment(String comment) {
            for (BlackListChannel enumV : BlackListChannel.values()) {
                if (enumV.comment.equals(comment)) {
                    return enumV.code;
                }
            }
            return comment;
        }
    }

    /**
     * 风险核验结果
     */
    enum CheckStatus {
        SUCCESS("1", "成功"),
        FAIL("0", "失败"),
        NOT_CHECK("2", "未核验");
        public final String code;
        public final String comment;

        CheckStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getMessageByCode(String code) {
            for (CheckStatus enumV : CheckStatus.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return code;
        }
    }

    /**
     * 审核记录操作方法：C:添加；U：更新；D:删除
     *
     * <AUTHOR>
     * @date 2020-11-11
     */
    enum AuditActionType {
        CREATE("C", "新增"),
        UPDATE("U", "更新"),
        DELETE("D", "删除");
        public final String code;
        public final String comment;

        AuditActionType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum RiskEventRuleStatus {
        ON("01", "启用"),
        OFF("02", "停用");
        public final String code;
        public final String message;

        private RiskEventRuleStatus(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    /**
     * 鉴权理财:0不可授权；1:可授权
     *
     * <AUTHOR>
     * @date 2021-04-06
     */
    enum AuthFinancialType {
        NO_AUTH("0", "不可授权"),
        AUTH("1", "可授权");
        public final String code;
        public final String message;

        private AuthFinancialType(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (AuthFinancialType enumV : AuthFinancialType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return "";
        }
    }

    /**
     * 风控预警实践规则
     *
     * <AUTHOR>
     * @date 2020-11-26
     */
    enum RiskEventRule {
        R001("R001", "交易监控, 商户网络支付单笔入金交易≥[param1]元"), // 预警  （风控校验时）
        R002("R002", "交易监控, 商户银行卡收单笔入金交易≥[param1]元"), //  预警    （风控校验时）
        R003("R003", "交易监控, 商户网络支付（除收单外）日交易量≥[param1]元"), // 预警 （风控记录订单时）
        R004("R004", "交易监控, 商户银行卡收单日交易量≥[param1]元"), //  预警 （风控记录订单时）
        R005("R005", "交易监控, 商户代付交易日交易笔数≥[param1]次"), // 预警  （风控记录订单时）
        R006("R006", "高频登陆, 商户在[param1]小时内登陆次数≥[param2]次"), // 关注 （风控校验登录时）
        R007("R007", "高退款率, 商户当日退款交易与总交易占比≥[param1]"), // 关注 （风控记录订单时）
        R008("R008", "黑名单, 商户当日命中黑名单≥[param1]次"),       // 关注  （风控命中黑名单时）
        R009("R009", "交易限额, 商户当日触发限额类风控≥[param1]次"), //关注 （风控触发限额时）
        R014("R014", "交易监控, 商户收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), // ---除线下银行卡收单，预警 （风控记录订单时）
        R015("R015", "交易监控, POS终端收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), // ---线下银行卡收单，预警 （风控记录订单时）
        R017("R017", "商户同一张银行卡且属于借记卡单日银联二维码交易笔数[param1]笔"),
        R018("R018", "商户同一张银行卡且属于信用卡单日银联二维码交易笔数[param1]笔"),
        R019("R019", "商户单日银联二维码交易金额[param1]元"),
        R020("R020", "入账商户与付款人账户名不一致的商户收单充值/银行转账交易，单笔入金交易>=[param1]元"),
        R021("R021", "入账商户与付款人账户名不一致的商户收单充值/银行转账交易，同一银行卡当日入金次数>=[param1]笔"),
        R022("R022", "POS终端单日实名支付失败次数≥[param1]"),
        R023("R023", "小微性质的收单商户及平台商户≥[param1]天无交易"),
        R024("R024", "非小微性质的收单商户及平台商户≥[param1]天无交易"),
        R025("R025", "银联二维码交易卡号命中内部黑名单"),
        ;
        public final String code;
        public final String message;

        private RiskEventRule(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    /**
     * 网银业务设置：1：收单；2：充值；3：取上级
     *
     * <AUTHOR>
     * @date 2021-5-26
     */
    enum EBankSetting {
        ACCEPT_ORDER("1", "收单"),
        RECHARGE("2", "充值"),
        PARENT("3", "取上级");
        public final String code;
        public final String message;

        private EBankSetting(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (EBankSetting enumV : EBankSetting.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return "";
        }
    }

    /**
     * 充值银行卡校验(0：不校验；1：需校验（非同名卡）；2：需校验所有卡)
     *
     * <AUTHOR>
     * @date 2021-5-26
     */
    enum CheckBankCard {
        UNNEED("0", "不校验"),
        CHECK_OTHER("1", "校验非同名卡"),
        CHECK_ALL("2", "校验所有银行卡");
        public final String code;
        public final String message;

        private CheckBankCard(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (CheckBankCard enumV : CheckBankCard.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.message;
                }
            }
            return "";
        }
    }

    /**
     * 限额指标状态
     */
    enum LimitAmountStatus {
        OPEN("1", "打开"),
        CLOSE("2", "关闭");
        public final String code;
        public final String message;

        private LimitAmountStatus(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    enum SuccessFail {
        SUCCESS((short) 0, "成功"),
        FAIL((short) 1, "失败");
        public final Short code;
        public final String comment;

        SuccessFail(short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }


    /**
     * 投诉交易记录导入类型
     *
     * <AUTHOR>
     * @date 2020-11-11
     */
    enum ComplainImportType {
        ALI_RISK_GO("ALI_RISK_GO", "支付宝风险交易明细记录"),
        WECHAT_RISK_STORE("WECHAT_RISK_STORE", "微信风险商户处理记录"),
        WECHAT_COMPLAIN("WECHAT_COMPLAIN", "微信投诉信息");
        public final String code;
        public final String comment;

        ComplainImportType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 出金报备设置类型
     */
    enum WithdrawalReport {
        NO_REPORT("0", "不需要报备"),
        REPORT_ARTIFICIAL("1", "需要报备+人工审核"),
        REPORT_CHECK("2", "需要报备+核验审核");
        public final String code;
        public final String comment;

        WithdrawalReport(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code, Integer ratio) {
            String message = "";
            for (WithdrawalReport withdrawalReport : WithdrawalReport.values()) {
                if (withdrawalReport.code.equals(code)) {
                    message = withdrawalReport.comment;
                }
                if (WithdrawalReport.REPORT_CHECK.code.equals(code)) {
                    message += "(抽查比例：" + ratio + "%)";
                }
            }
            return message;
        }
    }

    enum TemporaryLimit {
        Alipay_Single_Max_Limit("alipaySingleMaxLimit", "alipaySingleMaxLimitState", "支付宝单日入金最高限额", "支付宝"),
        Wechat_Single_Max_Limit("wechatSingleMaxLimit", "wechatSingleMaxLimitState", "微信单日入金最高限额", "微信"),
        Union_Code_Single_Max_Limit("unionCodeSingleMaxLimit", "unionCodeSingleMaxLimitState", "银联二维码单日入金最高限额", "银联二维码"),
        Union_Online_Single_Max_Limit("unionOnlineSingleMaxLimit", "unionOnlineSingleMaxLimitState", "银联在线单日入金最高限额", "银联在线"),
        Quick_Pay_Single_Max_Limit("quickPaySingleMaxLimit", "quickPaySingleMaxLimitState", "快捷支付单日入金最高限额", "快捷支付"),
        Bank_Pay_Single_Max_Limit("bankPaySingleMaxLimit", "bankPaySingleMaxLimitState", "网银支付单日入金最高限额", "网银支付"),
        Union_App_Single_Max_Limit("unionAppSingleMaxLimit", "unionAppSingleMaxLimitState", "银联云闪付单日入金最高限额", "银联云闪付"),
        All_Single_Max_Limit("allSingleMaxLimit", "allSingleMaxLimitState", "单日入金最高限额", "单日入金");
        public final String code;
        public final String codeState;
        public final String comment;
        public final String shortName;

        TemporaryLimit(String code, String codeState, String comment, String shortName) {
            this.code = code;
            this.codeState = codeState;
            this.comment = comment;
            this.shortName = shortName;
        }

        public static String getCommentByCode(String code) {
            for (TemporaryLimit temporaryLimit : TemporaryLimit.values()) {
                if (temporaryLimit.code.equals(code)) {
                    return temporaryLimit.comment;
                }
            }
            return null;
        }

        public static String getCodeStateByCode(String code) {
            for (TemporaryLimit temporaryLimit : TemporaryLimit.values()) {
                if (temporaryLimit.code.equals(code)) {
                    return temporaryLimit.codeState;
                }
            }
            return null;
        }


        public static List<String> getTemporaryLimitBusiness(String code) {
            List<String> businessCodeList = new ArrayList<>();
            switch (code) {
                case "alipaySingleMaxLimit":
                    businessCodeList.add("AliJSAPI");
                    businessCodeList.add("AliJSAPIDebitCard");
                    businessCodeList.add("AliMicro");
                    businessCodeList.add("AliMicroDebitCard");
                    businessCodeList.add("AliMultiRate");
                    businessCodeList.add("AliMultiRateDebitCard");
                    businessCodeList.add("AliNative");
                    businessCodeList.add("AliNativeDebitCard");
                    businessCodeList.add("AuthAliMicro");
                    businessCodeList.add("AuthAliMicroDebitCard");
                    businessCodeList.add("FZ-AliJSAPI");
                    businessCodeList.add("FZ-AliMicro");
                    businessCodeList.add("FZ-AliNative");
                    break;
                case "wechatSingleMaxLimit":
                    businessCodeList.add("AuthWxMicro");
                    businessCodeList.add("AuthWxMicroDebitCard");
                    businessCodeList.add("FZ-WxAPP");
                    businessCodeList.add("FZ-WxJSAPI");
                    businessCodeList.add("FZ-WxMWEB");
                    businessCodeList.add("FZ-WxMicro");
                    businessCodeList.add("FZ-WxMiniProgram");
                    businessCodeList.add("FZ-WxNatvie");
                    businessCodeList.add("WxAPP");
                    businessCodeList.add("WxAPPDebitCard");
                    businessCodeList.add("WxJSAPI");
                    businessCodeList.add("WxJSAPIDebitCard");
                    businessCodeList.add("WxMicro");
                    businessCodeList.add("WxMicroDebitCard");
                    businessCodeList.add("WxMiniProgram");
                    businessCodeList.add("WxMiniProgramDebitCard");
                    businessCodeList.add("WxMultiRule");
                    businessCodeList.add("WxMultiRuleDebitCard");
                    businessCodeList.add("WxNatvie");
                    businessCodeList.add("WxNatvieDebitCard");
                    break;
                case "unionCodeSingleMaxLimit":
                    businessCodeList.add("FZ-UnionQrcode");
                    businessCodeList.add("FZ-UnionSweep");
                    businessCodeList.add("POSUnionQrcodeCreditCard");
                    businessCodeList.add("POSUnionQrcodeDebitCard");
                    businessCodeList.add("UnionJS");
                    businessCodeList.add("UnionJSDebit");
                    businessCodeList.add("UnionQrcode");
                    businessCodeList.add("UnionQrcodeDebitCard");
                    businessCodeList.add("UnionSweep");
                    businessCodeList.add("UnionSweepDebit");
                    break;
                case "unionOnlineSingleMaxLimit":
                    businessCodeList.add("FZ-UnionOnline");
                    businessCodeList.add("FZ-UnionOnlineCredit");
                    businessCodeList.add("UnionOnline");
                    businessCodeList.add("UnionOnlineCredit");
                    break;
                case "quickPaySingleMaxLimit":
                    businessCodeList.add("FZ-NocardPay");
                    businessCodeList.add("FZ-NocardPayCredit");
                    businessCodeList.add("FZ-QuickPay");
                    businessCodeList.add("FZ-QuickPayCredit");
                    businessCodeList.add("ProtocolPay");
                    businessCodeList.add("ProtocolPayCredit");
                    businessCodeList.add("QuickPay");
                    businessCodeList.add("QuickPayCredit");
                    break;
                case "bankPaySingleMaxLimit":
                    businessCodeList.add("CreditCardPay");
                    businessCodeList.add("EnterpriseUnion");
                    businessCodeList.add("FZ-CreditCardPay");
                    businessCodeList.add("FZ-EnterpriseUnion");
                    businessCodeList.add("FZ-SavingCardPay");
                    businessCodeList.add("SavingCardPay");
                    break;
                case "unionAppSingleMaxLimit":
                    businessCodeList.add("UnionApp");
                    businessCodeList.add("UnionAppCredit");
                    businessCodeList.add("UnionAppCreditUs");
                    businessCodeList.add("UnionAppUs");
                    break;
                case "allSingleMaxLimit":
                    break;
            }
            return businessCodeList;
        }
    }

    /**
     * 临时入金状态
     */
    enum TemporaryStatus {
        NORMAL("1", "正常"),
        TEMPORARY_LIMIT("2", "限制临时入金");
        public final String code;
        public final String comment;

        TemporaryStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            if (code == null || code.equals("")) {
                return null;
            }
            for (TemporaryStatus temporaryStatus : TemporaryStatus.values()) {
                if (temporaryStatus.code.equals(code)) {
                    return temporaryStatus.comment;
                }
            }
            return null;
        }
    }

    enum TemporaryEnableStatus {
        ENABLE("1", "启用"),
        DISABLE("2", "禁用");
        public final String code;
        public final String comment;

        TemporaryEnableStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            if (code == null || code.equals("")) {
                return null;
            }
            for (TemporaryEnableStatus temporaryEnableStatus : TemporaryEnableStatus.values()) {
                if (temporaryEnableStatus.code.equals(code)) {
                    return temporaryEnableStatus.comment;
                }
            }
            return null;
        }
    }

    enum AccountStatus {
        NORMAL("0", "正常"),
        FROZEN("1", "冻结"),
        BANDOUT("2", "止付"),
        BANDIN("3", "禁止入金");
        public final String code;
        public final String comment;

        AccountStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            if (code == null || code.equals("")) {
                return null;
            }
            for (AccountStatus accountStatus : AccountStatus.values()) {
                if (accountStatus.code.equals(code)) {
                    return accountStatus.comment;
                }
            }
            return null;
        }
    }

    /**
     * 是否有风险
     */
    enum AnyRisk {
        YES("1","是"),
        NO("0","否")
        ;
        public final String code;
        public final String comment;

        AnyRisk(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
}

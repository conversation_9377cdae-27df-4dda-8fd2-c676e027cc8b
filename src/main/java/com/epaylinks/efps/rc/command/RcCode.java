package com.epaylinks.efps.rc.command;

import com.epaylinks.efps.common.systemcode.SystemCode;
import com.epaylinks.efps.common.tool.error.code.EpCode;

@SystemCode
public enum RcCode implements EpCode {
	SYSTEM_EXCEPTION("2801" , "rc系统内部错误"),
	INDEX_NOT_ENOUGH("2802" , "入参指标不足"),
	ADD_BWLIST_EXCEPTION("2803" , "添加黑白名单失败"),
	ADD_BWLIST_EXIS("2804" , "添加或修改黑白名单失败,黑白名单已存在，请勿重复添加"),
	ADD_RCLIMT_EXCEPTION("2805","添加风控指标失败，不存在该风控指标定义编码"),
	QUERY_BWLIST_EXCECTION("2806","分页查询失败"),
	UPDATE_BWLIST_EXCECTION("2807","修改黑白名单失败"),
	USER_NOT_EXIXT("2808","用户不存在"),
	ARCHIVE_QUERY_EXCEPTION("2809","编码和名称不能同时为空"),
	AUD_RECORD_NOT_EXISTS_EXCEPTION("2810","审核记录不存在"),
	AUD_STATUS_EXCEPTION("2811","状态不允许撤销"),
	ARCHIVE_STATUS_EXCEPTION("2812","状态名称不存在"),
	LIMIT_ADD_EXCEPTION("2813","风控指标添加失败，当前商户已存在该风控指标，请勿重复添加"),
	AUD_STATUS_UPDATE_EXCEPTION("2814","审核中不允许修改"),
	RECORD_NOT_EXISTS_EXCEPTION("2815","记录不存在"),
	AUD_EXCEPTION("2816","该状态不允许审核"),
	AUD_OPTION_EXCEPTION("2817","审核意见不能为空"),
	RC_BALANCE_AMOUNT_EXCEPTION("2818","解冻金额不能大于已冻结金额"),
	RC_FEIGIN_EXCEPTION("2819","调用外部系统错误"),
	RC_ARG_ERROR("2820","入参参数错误"),
	RC_LEVLE_ERROR("2821","风控等级错误"),
	RC_CACLCUELATE_ERROR("2822","风控不通过"),
	RC_ARCHIVE_NOT_EXISTS("2823","风控档案不存在"),
	QUERY_OPERATELOG_EXCEPTION("2824", "查询操作日志异常"),
	IS_BLACKLIST_EXCEPTION("2825", "判断是否黑名单异常"),
	PARAM_ERROR("2826", "参数错误"),
    CUSTOMER_HAS_CANCELED("2827", "商户已注销"),
    EBANK_SETTING_ERROR("2828","网银业务设置有误"),
    TRADE_TIME_ERROR("2829","交易时间段设置有误"),
	UNAPPROVED_RECORDS_EXIST("2830","当前值设置失败，存在未审核的修改记录"),
	NO_AUDIT_PERMISSION("2831","没有审核权限"),
	CALL_SUBSYSTEM_EXCEPTIOIN("2832","调用子系统异常"),
	VERIFICATION_PERIOD("2833","银行卡号已存在有效的核验（90天）"),
	DATA_DUPLICATION("2834","入库失败，原因是重复数据"),
	DATA_TOO_LONG("2835","长度超过限制"),
	FILE_DOWNLOAD_FAIL("2836","获取文件失败"),
	MISSING_ATTACHMENT("2837","缺少附件"),
	CHECK_CARD_FAIL("2838","卡号核验失败"),
	ORDER_NO_NOT_EXISTS("2839","订单号不存在"),
	UPLOAD_FAIL("2839","上传失败"),
	RECORD_EXISTS_EXCEPTION("2840","记录已存在"),
	PAY_SERVICE_FEE_NOT_CONFIG("2841","未配置支付服务费"),
	WEBSITE_NOT_AVAILABLE("2842","无法访问网站"),
	BLACK_BWLIST_LIMIT("2843","黑名单限制"),
	CUSTOMER_MANAGE_LIMIT("2844","交易权限不足"),
	STATE_ERROR("2845","状态错误"),
	REPEAT_OPERATION("2846","重复操作"),
	URL_TYPE_ERROR_OR_SIZE_EXCEEDING_LIMIT("2847","网址类型错误或内容大小超过限制"),
	AUDIT_FAIL("2848","审核失败")
	;

	public final String code;

	public final String message;

	RcCode(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public static String getMessageByCode(String code)
	{
		for(RcCode v: RcCode.values())
		{
			if(v.code.equalsIgnoreCase(code)){
				return v.message;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}
	public String getMessage() {
		return message;
	}
	
}

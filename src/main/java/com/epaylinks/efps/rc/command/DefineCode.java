package com.epaylinks.efps.rc.command;


/**
 * <AUTHOR>
 * 与数据库中的风控定义编码同步
 */
public enum DefineCode {
    SINGLT_IN(0L, "Single-Max-IN-Amount", "SD", "分", "入金单笔最大限额"),
    DAY_IN(1L, "Day-Max-IN-Amount", "SD", "分", "入金单日最大限额"),
    MONTH_IN(2L, "Month-Max-IN-Amount", "SD", "分", "入金单月最大限额"),
    YEAR_IN(3L, "Year-Max-IN-Amount", "SD", "分", "入金单年最大限额"),
    SINGLT_OUT(4L, "Single-Max-OUT-Amount", "CJ", "分", "提现单笔最高限额"), // 出金代付、提现分离，原指标保留做提现 ********
    DAY_OUT(5L, "Day-Max-OUT-Amount", "CJ", "分", "提现单日最高限额"),
    MONTH_OUT(6L, "Month-Max-OUT-Amount", "CJ", "分", "提现单月最高限额"),
    YEAR_OUT(7L, "Year-Max-OUT-Amount", "CJ", "分", "提现单年最高限额"),
    RC_STATUS(8L, "RC-STATUS", "RC", "/", "风控状态"),
    ACCOUNT_STATUS(9L, "ACCOUNT-STATUS", "RC", "/", "账户状态"),
    RC_LEVEL(10L, "RC-LEVEL", "RC", "/", "风控等级"),
    DAY_WITHDRAW(11L, "Withdraw-Day-Max-Amount", "WD", "分", "垫资代付单日最高限额"),
    CARD_DAY_OUT_AMOUNT(12L, "Card-Day-Max-OUT-Amount", "DF", "分", "同卡代付单日最高限额"),
    CARD_DAY_OUT_COUNT(13L, "Card-Day-Max-OUT-Count", "DF", "/", "同卡代付单日最多笔数"),
    CARD_MONTH_OUT_AMOUNT(14L, "Card-Month-Max-OUT-Amount", "DF", "分", "同卡代付单月最高限额"),
    CARD_MONTH_OUT_COUNT(15L, "Card-Month-Max-OUT-Count", "DF", "/", "同卡代付单月最多笔数"),
    RC_BALANCE(16L, "RC-BALANCE", "RC", "分", "风控限额"),
    CARD_DAY_IN_AMOUNT(17L, "Card-Day-Max-IN-Amount", "SD", "分", "同卡入金单日最高限额"),
    CARD_DAY_IN_COUNT(18L, "Card-Day-Max-IN-Count", "SD", "/", "同卡入金单日最多笔数"),
    CARD_MONTH_IN_AMOUNT(19L, "Card-Month-Max-IN-Amount", "SD", "分", "同卡入金单月最高限额"),
    CARD_MONTH_IN_COUNT(20L, "Card-Month-Max-IN-Count", "SD", "/", "同卡入金单月最多笔数"),
    USER_DAY_IN_AMOUNT(21L, "User-Day-Max-IN-Amount", "SD", "分", "同人入金单日最高限额"),
    USER_DAY_IN_COUNT(22L, "User-Day-Max-IN-Count", "SD", "/", "同人入金单日最多笔数"),
    USER_MONTH_IN_AMOUNT(23L, "User-Month-Max-IN-Amount", "SD", "分", "同人入金单月最高限额"),
    USER_MONTH_IN_COUNT(24L, "User-Month-Max-IN-Count", "SD", "/", "同人入金单月最多笔数"),
    WITHDRAW_DAY_MAX_COUNT(26L, "Withdraw-Day-Max-Count", "WD", "/", "商户(证件)当日累计转账限次"),
    CUS_STATUS(27L, "CUS-STATUS", "RC", "/", "商户状态"),
    DAY_IN_COUNT(28L, "Day-Max-IN-Count", "SD", "/", "入金单日最大笔数"),
    MONTH_IN_COUNT(29L, "Month-Max-IN-Count", "SD", "/", "入金单月最大笔数"),
    YEAR_IN_COUNT(30L, "Year-Max-IN-Count", "SD", "/", "入金单年最大笔数"),
    USER_DAY_MAX_OUT_AMOUNT(31L, "User-Day-Max-OUT-Amount", "CJ", "分", "同人单日出金累计限额"),
    USER_YEAR_MAX_OUT_AMOUNT(32L, "User-Year-Max-OUT-Amount", "CJ", "分", "同人单年出金累计限额"),
    ACCOUNT_NAME_LIMIT(33L, "ACCOUNT-NAME-LIMIT", "RC", "/", "非同名银行账户转账限制"),
    TOTAL_MAX_OUT_AMOUNT(34L, "Total-Max-OUT-Amount", "CJ", "分", "终身出金累计限额"),

    // 新加指标 ******** fwy
    SINGLE_MAX_WITHDRAW_AMOUNT(35L, "Single-Max-Withdraw-Amount", "CJ", "分", "代付单笔最高限额"),
    DAY_MAX_WITHDRAW_AMOUNT(36L, "Day-Max-Withdraw-Amount", "CJ", "分", "代付单日最高限额"),
    MONTH_MAX_WITHDRAW_AMOUNT(37L, "Month-Max-Withdraw-Amount", "CJ", "分", "代付单月最高限额"),
    YEAR_MAX_WITHDRAW_AMOUNT(38L, "Year-Max-Withdraw-Amount", "CJ", "分", "代付单年最高限额"),
    CARD_DAY_IN_TOTAL_COUNT(39L, "Card-Day-Max-IN-Total-Count", "SD", "/", "同卡入金单日最多交易总笔数（成功+失败）"),
    CARD_MONTH_IN_TOTAL_COUNT(40L, "Card-Month-Max-IN-Total-Count", "SD", "/", "同卡入金单月最多交易总笔数（成功+失败）"),
    USER_DAY_IN_TOTAL_COUNT(41L, "User-Day-Max-IN-Total-Count", "SD", "/", "同人入金单日最多交易总笔数（成功+失败）"),
    USER_MONTH_IN_TOTAL_COUNT(42L, "User-Month-Max-IN-Total-Count", "SD", "/", "同人入金单月最多交易总笔数（成功+失败）"),
    // 新加指标 20210810 fwy
    CERT_DAY_MAX_IN_AMOUNT(43L, "Cert-Day-Max-IN-Amount", "SD", "分", "同证件单日交易累计限额"),
    CERT_MONTH_MAX_IN_AMOUNT(44L, "Cert-Month-Max-IN-Amount", "SD", "分", "同证件单月交易累计限额"),
    CERT_DAY_MAX_CREDIT_AMOUNT(45L, "Cert-Day-Max-Credit-Amount", "SD", "分", "同证件单日信用卡交易累计限额"),
    CERT_MONTH_MAX_CREDIT_AMOUNT(46L, "Cert-Month-Max-Credit-Amount", "SD", "分", "同证件单月信用卡交易累计限额"),
    DAY_MAX_OUT_COUNT(47L, "Day-Max-OUT-Count", "CJ", "/", "商户(证件)单日提现最多笔数"), //  ********

    BANK_BLACK_LIST(999L, "BANK_BLACK_LIST", "ALL", "/", "银行卡是否黑名单"),
    PHONE_BLACK_LIST(999L, "PHONE_BLACK_LIST", "ALL", "/", "手机号是否黑名单"),
    ID_BLACK_LIST(999L, "ID_BLACK_LIST", "ALL", "/", "身份证是否黑名单"),
    USCC_BLACK_LIST(999L, "USCC_BLACK_LIST", "ALL", "/", "统一社会信用码是否黑名单"),
    CUSTOMER_CODE_BLACK_LIST(999L, "CUSTOMER_CODE_BLACK_LIST", "ALL", "/", "商户编号是否黑名单"),

    // 新增指标 ********
    DALIY_OUT_LIMIT(48L, "Daliy-Out-Limit", "CJ", "分", "商户门户单日出金限额"),
    SINGLE_OUT_LIMIT(49L, "Single-Out-Limit", "CJ", "分", "商户门户单笔出金限额"),
    ID_4_BLACK_LIST(999L, "ID_4_BLACK_LIST", "ALL", "/", "身份证前4位是否黑名单"),
    BIZ_ADDR_BLACK_LIST(999L, "BIZ_ADDR_BLACK_LIST", "ALL", "/", "经营地址是否黑名单"),

    PERSON_TRANS_COMPANY(50L, "PERSON_TRANS_COMPANY", "CJ", "/", "向单位支付账户转账"), //0-不限制 1-限制
    PERSON_TRANS_PERSON(51L, "PERSON_TRANS_PERSON", "CJ", "/", "向个人支付账户转账"), //0-不限制 1-限制
    BALANCE_TRANS_AMOUNT(52L, "BALANCE_TRANS_AMOUNT", "CJ", "/", "个人余额转账单笔最高限额"),
    BALANCE_TRANS_DAY_AMOUNT(53L, "BALANCE_TRANS_DAY_AMOUNT", "CJ", "/", "余额转账单日累计限额"),
    BALANCE_WITHDRAW_AMOUNT(54L, "BALANCE_WITHDRAW_AMOUNT", "CJ", "/", "单笔提现最高限额"),
    BALANCE_WITHDRAW_DAY_AMOUNT(55L, "BALANCE_WITHDRAW_DAY_AMOUNT", "CJ", "/", "单日提现累计限额"),
    BALANCE_CONSUME_AMOUNT(56L, "BALANCE_CONSUME_AMOUNT", "CJ", "/", "单笔余额消费最高限额"),
    BALANCE_CONSUME_DAY_AMOUNT(57L, "BALANCE_CONSUME_DAY_AMOUNT", "CJ", "/", "单日余额消费最高限额"),
    BALANCE_IN_AMOUNT(58L, "BALANCE_IN_AMOUNT", "SD", "/", "单笔入金最高限额"),
    BALANCE_IN_DAY_AMOUNT(59L, "BALANCE_IN_DAY_AMOUNT", "SD", "/", "单日入金最高限额"),

    CREDIT_CARD_IN_AMOUNT(60L, "CREDIT_CARD_IN_AMOUNT", "SD", "/", "信用卡单笔入金最高限额"),
    CREDIT_CARD_IN_DAY_AMOUNT(61L, "CREDIT_CARD_IN_DAY_AMOUNT", "SD", "/", "信用卡单日入金最高限额"),
    CREDIT_CARD_IN_MON_AMOUNT(62L, "CREDIT_CARD_IN_MON_AMOUNT", "SD", "/", "信用卡单月入金最高限额"),

    DEBIT_CARD_IN_AMOUNT(63L, "DEBIT_CARD_IN_AMOUNT", "SD", "/", "储蓄卡单笔入金最高限额"),
    DEBIT_CARD_IN_DAY_AMOUNT(64L, "DEBIT_CARD_IN_DAY_AMOUNT", "SD", "/", "储蓄卡单日入金最高限额"),
    DEBIT_CARD_IN_MON_AMOUNT(65L, "DEBIT_CARD_IN_MON_AMOUNT", "SD", "/", "储蓄卡单月入金最高限额"),

    INST_CARD_DAY_IN_AMOUNT(66L, "INST_CARD_DAY_IN_AMOUNT", "SD", "分", "同卡入金单日最高限额"), // 机构维度
    INST_USER_DAY_IN_AMOUNT(67L, "INST_USER_DAY_IN_AMOUNT", "SD", "分", "同人入金单日最高限额"), // 机构维度

    INST_CARD_TOTAL_IN_AMOUNT(68L, "INST_CARD_TOTAL_IN_AMOUNT", "SD", "分", "近180天同卡累计入金最高限额"),
    INST_USER_TOTAL_IN_AMOUNT(69L, "INST_USER_TOTAL_IN_AMOUNT", "SD", "分", "近180天同人累计入金最高限额"),

    TRANSFER_QUOTA(999L, "TRANSFER_QUOTA", "WD", "/", "代付额度"),
    TRANSFER_TIME(999L, "TRANSFER_TIME", "WD", "/", "代付时间"),

    CUSTOMER_NAME_BLACK_LIST(999L, "CUSTOMER_NAME_BLACK_LIST", "ALL", "/", "商户名称是否黑名单"),

    ;
    public final String defineCode;
    public final Long defineId;
    public final String businessType;
    public final String unit;
    public final String name;

    DefineCode(Long defineId, String defineCode, String businessType, String unit, String name) {
        this.defineCode = defineCode;
        this.defineId = defineId;
        this.businessType = businessType;
        this.unit = unit;
        this.name = name;
    }

    public static DefineCode getDefineCode(String defineCode) {
        for (DefineCode defineCode1 : DefineCode.values()) {
            if (defineCode1.defineCode.equals(defineCode)) {
                return defineCode1;
            }
        }
        return null;
    }
}

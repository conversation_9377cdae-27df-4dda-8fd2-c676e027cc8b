package com.epaylinks.efps.rc.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.rc.service.monitor.DayBatchRuleMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DayBatchRuleMonitorJob implements TaskJob {

    @Autowired
    private DayBatchRuleMonitor dayBatchRuleMonitor;


    @Override
    public void execute(TaskRequest request) {
        dayBatchRuleMonitor.monitor();
    }
}

package com.epaylinks.efps.rc.job;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.rc.service.monitor.NoTxnMonitor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class NoTxnMonitorJob implements TaskJob {

    @Autowired
    private NoTxnMonitor noTxnMonitor;


    @Override
    public void execute(TaskRequest request) {
        if (StringUtils.isNotBlank(request.getJobParams())) {
            JobParams jobParams = JSON.parseObject(request.getJobParams(), JobParams.class);
            noTxnMonitor.monitor(jobParams.getN());
        } else {
            noTxnMonitor.monitor();
        }
    }

    @Data
    public static class JobParams {
        private int n;
    }
}

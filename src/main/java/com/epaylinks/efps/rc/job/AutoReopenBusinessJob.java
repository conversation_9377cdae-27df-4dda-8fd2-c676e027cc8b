package com.epaylinks.efps.rc.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.rc.service.monitor.UnionQrTransactionMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class AutoReopenBusinessJob implements TaskJob {

    @Autowired
    private UnionQrTransactionMonitor unionQrTransactionMonitor;


    @Override
    public void execute(TaskRequest request) {
        unionQrTransactionMonitor.autoReopenBusiness(Timex.now().start().toDate());
    }
}

package com.epaylinks.efps.rc.job;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.TemporaryLimitMapper;
import com.epaylinks.efps.rc.service.impl.ExportWebsiteEffectivenessService;
import com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ExportWebsiteEffectivenessJob implements TaskJob {
    @Autowired
    private CommonLogger logger;

    @Autowired
    private RcArchiveMapper rcArchiveMapper;

    @Autowired
    private TemporaryLimitMapper temporaryLimitMapper;

    @Autowired
    private OtherMapper otherMapper;

    @Autowired
    private ExportWebsiteEffectivenessService exportWebsiteEffectivenessService;

    @Override
    @Logable(businessTag = "ExportWebsiteEffectivenssJob.execute")
    public void execute(TaskRequest taskRequest) throws Exception {
        List<ExportWebsiteEffectivenessVo> voList = rcArchiveMapper.exportWebsiteEffectiveness(null);
        if (!voList.isEmpty()) {
            try {
            /*
                不符合以下条件则过滤：
                未限制临时入金
                或
                临时入金限制，其中任一未勾选 且 存在关联业务启用和审核通过的
             */
            voList.removeIf(vo -> "2".equals(vo.getTemporaryStatus()) && judgeTemporaryLimit(vo.getCustomerId(),vo.getCustomerNo()));

            logger.printMessage("voList-size：" + voList.size());

            exportWebsiteEffectivenessService.build(voList,"task");
            } catch (Exception e) {
                logger.printMessage("导出商户网站有效性错误：" + e.getMessage());
                logger.printLog(e);
                throw new AppException(RcCode.SYSTEM_EXCEPTION.code,RcCode.SYSTEM_EXCEPTION.message + "：" + e.getMessage());
            }
        }
    }

    /**
     * 任一未勾选 且 存在关联业务启用和审核通过的
     * 存在则不移除出列表
     * @param customerId
     * @param customerCode
     * @return
     */
    private Boolean judgeTemporaryLimit(Long customerId,String customerCode) {
        List<String> unCheckList = temporaryLimitMapper.queryLimitTypeByCode(customerCode);
        if (unCheckList.isEmpty() || !unCheckList.contains("allSingleMaxLimit")) {
            return true;
        }
        for (String limitType : unCheckList) {
            List<String> linkBusinessCode = RcConstants.TemporaryLimit.getTemporaryLimitBusiness(limitType);
            if (linkBusinessCode.isEmpty()) {
                continue;
            }
            Map<String,Object> param = new HashMap<>();
            param.put("customerId",customerId);
            param.put("businessCodes",linkBusinessCode);
            Boolean flag = otherMapper.queryBusinessByIdAndCode(param);
            if (flag) { // 存在则不移出列表
                return false;
            }
        }
        return true;
    }
}

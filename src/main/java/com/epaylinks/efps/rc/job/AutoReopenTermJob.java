package com.epaylinks.efps.rc.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class AutoReopenTermJob implements TaskJob {

    @Autowired
    private RcMonitorService rcMonitorService;


    @Override
    public void execute(TaskRequest request) {
        rcMonitorService.autoReopenTerm(Timex.now().start().toDate());
    }
}

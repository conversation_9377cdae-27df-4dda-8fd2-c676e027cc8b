package com.epaylinks.efps.rc.job;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.rc.dao.RcTxsOrderMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.RcLimitService;
import com.epaylinks.efps.rc.util.RcDateUtils;

/**
 * @Author: fwy
 * @Description 指标重置定时任务,（配置每日0点执行）
 * @since 2020-01-15
 */
@Component("RcIndexResetJob")
public class RcIndexResetJob implements TaskJob {

    @Autowired
    private RcLimitService rcLimitService;
    @Autowired
    private RcTxsOrderMapper rcTxsOrderMapper;
    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;
    
    
    @Override
    @Logable(businessTag = "execute")
    public void execute(TaskRequest request) {
        String dateStr = RcDateUtils.getLastDay();
        List<String> customerCodeList = rcTxsOrderMapper.queryTxsCustomerCodeList(dateStr);
        Set<String> customerCodeSet = new HashSet<>(customerCodeList);
        List<String> platCustomerCodeList = rcTxsOrderMapper.queryTxsPlatCustomerCodeList(dateStr);
        customerCodeSet.addAll(platCustomerCodeList);

        for (String customerCode : customerCodeSet) {
            try {
                List<RcLimit> rcLimits = rcLimitService.queryByBusinessTaregetId(customerCode);
                if (rcLimits == null) {
                    continue;
                }
                
                // 风控对象增加商户证件号  20210816
                CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
                if (customerInfo != null && customerInfo.getBusinessLicenseNo() != null // 小微不需要校验营业执照号
                        && !Constants.CustomerType.MICRO.code.toString().equals(String.valueOf(customerInfo.getCustomerType()))) {
                    List<RcLimit> licenseLimits = rcLimitService.queryByBusinessTaregetId(customerInfo.getBusinessLicenseNo());
                    rcLimits.addAll(licenseLimits);
                }
                if (customerInfo != null && customerInfo.getLeaPersoniDentificationNo() != null
                        && Constants.CertificateType.ID.code.toString().equals(String.valueOf(customerInfo.getLeaPersoniDentificationType()))) {
                    List<RcLimit> certLimits = rcLimitService.queryByBusinessTaregetId(customerInfo.getLeaPersoniDentificationNo());
                    rcLimits.addAll(certLimits);
                }

                for (RcLimit rcLimit : rcLimits) {
                    try {
                        RcIndexReset rcIndexReset = (RcIndexReset) SpringContextUtils.getBean(rcLimit.getDefineCode());
                        rcIndexReset.reset(rcLimit);
                    } catch (Exception e) {
                        // 每个指标异常不应该互相影响
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                // 每个商户异常不应该互相影响
                e.printStackTrace();
            }
        }

    }
}

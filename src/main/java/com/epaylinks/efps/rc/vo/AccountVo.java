package com.epaylinks.efps.rc.vo;

import com.alibaba.fastjson.JSON;

import java.util.Date;
import java.util.List;

/**
 * 账户VO
 * <AUTHOR>
 *
 */
public class AccountVo {
	/**
	 * 账户编码
	 */
    private String code;
    /**
     * 所属客户编码
     */
    private String customerCode;
    /**
     * 总余额，单位分
     */
    private Long balance;
    /**
     * 可用余额，单位分
     */
    private Long availableBalance;
    /**
     * 冻结金额，单位分
     */
    private Long frozenBalance;
    /**
     * 在途金额，单位分
     */
    private Long floatBalance;
	/**
	 * 待分账金额即簿记账户余额
	 */
	private Long splitBalance = 0L;
	/**
	 * 可出金金额
	 */
	private Long withdrawBalance;
    /**
     * 创建时间
     */
    private Date createDateTime;
    /**
     * 更新时间
     */
    private Date updateDateTime;
    /**
     * 过期时间
     */
    private Date expriedDateTime;
    /**
     * 状态 正常：1 注销：2 冻结：3
     */
    private Integer status;
    /**
     * 账户类型编码，主账户无此字段，其他所有账户均有
     */
    private String accountTypeCode;
    /**
     * 账户类型名称，主账户无此字段，其他所有账户均有
     */
    private String accountTypeName;
    /**
     * 1: 基本账户 2：业务账户
     */
    private Integer accountTypeCategory;
    /**
     * CNY：人民币，账户类型的币种
     */
    private String currency;
    /**
     * 账户类型对应业务代码，仅业务账户有该字段
     */
    private String businessCode;
    /**
     * 冻结状态
     */
    private String frozenStatus;
    /**
     * 商户全称
     */
    private String name;
    /**
     * 商户简称
     */
    private String shortName;
    /**
     * 子账户列表，每个子账户为一个account对象
     */
    private List<AccountVo> subAccounts;
    /**
	 * 主键
	 */
    private Long id;
    /**
     * 父账户ID
     */
    private Long parentAccountId;
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public Long getBalance() {
		return balance;
	}
	public void setBalance(Long balance) {
		this.balance = balance;
	}
	public Long getAvailableBalance() {
		return availableBalance;
	}
	public void setAvailableBalance(Long availableBalance) {
		this.availableBalance = availableBalance;
	}
	public Long getFrozenBalance() {
		return frozenBalance;
	}
	public void setFrozenBalance(Long frozenBalance) {
		this.frozenBalance = frozenBalance;
	}
	public Long getFloatBalance() {
		return floatBalance;
	}
	public void setFloatBalance(Long floatBalance) {
		this.floatBalance = floatBalance;
	}
	public Date getCreateDateTime() {
		return createDateTime;
	}
	public void setCreateDateTime(Date createDateTime) {
		this.createDateTime = createDateTime;
	}
	public Date getUpdateDateTime() {
		return updateDateTime;
	}
	public void setUpdateDateTime(Date updateDateTime) {
		this.updateDateTime = updateDateTime;
	}
	public Date getExpriedDateTime() {
		return expriedDateTime;
	}
	public void setExpriedDateTime(Date expriedDateTime) {
		this.expriedDateTime = expriedDateTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getAccountTypeCode() {
		return accountTypeCode;
	}
	public void setAccountTypeCode(String accountTypeCode) {
		this.accountTypeCode = accountTypeCode;
	}
	public String getAccountTypeName() {
		return accountTypeName;
	}
	public void setAccountTypeName(String accountTypeName) {
		this.accountTypeName = accountTypeName;
	}
	public Integer getAccountTypeCategory() {
		return accountTypeCategory;
	}
	public void setAccountTypeCategory(Integer accountTypeCategory) {
		this.accountTypeCategory = accountTypeCategory;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public List<AccountVo> getSubAccounts() {
		return subAccounts;
	}
	public void setSubAccounts(List<AccountVo> subAccounts) {
		this.subAccounts = subAccounts;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getParentAccountId() {
		return parentAccountId;
	}
	public void setParentAccountId(Long parentAccountId) {
		this.parentAccountId = parentAccountId;
	}
	public String getFrozenStatus() {
		return frozenStatus;
	}
	public void setFrozenStatus(String frozenStatus) {
		this.frozenStatus = frozenStatus;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getShortName() {
		return shortName;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public Long getSplitBalance() {
		return splitBalance;
	}

	public void setSplitBalance(Long splitBalance) {
		this.splitBalance = splitBalance;
	}

	public Long getWithdrawBalance() {
		return withdrawBalance;
	}

	public void setWithdrawBalance(Long withdrawBalance) {
		this.withdrawBalance = withdrawBalance;
	}

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}

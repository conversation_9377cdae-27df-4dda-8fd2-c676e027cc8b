package com.epaylinks.efps.rc.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@ApiModel
public class TemporaryLimitVo {
    private Long archiveId;

    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @ApiModelProperty(value = "临时入金状态【1：正常；2：限制临时入金】",dataType = "String")
    @NotBlank(message = "临时入金状态设置不能为空")
    private String temporaryStatus;

    @ApiModelProperty(value = "临时入金状态变更原因",dataType = "String")
    @NotBlank(message = "变更原因不能为空")
    @Size(max = 150,message = "临时入金状态变更原因长度超过限制")
    private String temporaryStatusReason;

    @ApiModelProperty(value = "支付宝单日入金最高限额",dataType = "Long")
    private Long alipaySingleMaxLimit;

    @ApiModelProperty(value = "支付宝单日入金最高限额启用状态【1：启用；2：禁用】",dataType = "String")
    private String alipaySingleMaxLimitState = "2";

    @ApiModelProperty(value = "微信单日入金最高限额",dataType = "Long")
    private Long wechatSingleMaxLimit;

    private String wechatSingleMaxLimitState = "2";

    @ApiModelProperty(value = "银联二维码单日入金最高限额",dataType = "Long")
    private Long unionCodeSingleMaxLimit;

    private String unionCodeSingleMaxLimitState = "2";

    @ApiModelProperty(value = "银联在线单日入金最高限额",dataType = "Long")
    private Long unionOnlineSingleMaxLimit;

    private String unionOnlineSingleMaxLimitState = "2";

    @ApiModelProperty(value = "快捷支付单日入金最高限额",dataType = "Long")
    private Long quickPaySingleMaxLimit;

    private String quickPaySingleMaxLimitState = "2";

    @ApiModelProperty(value = "网银支付单日入金最高限额",dataType = "Long")
    private Long bankPaySingleMaxLimit;

    private String bankPaySingleMaxLimitState = "2";

    @ApiModelProperty(value = "银联云闪付单日入金最高限额",dataType = "Long")
    private Long unionAppSingleMaxLimit;

    private String unionAppSingleMaxLimitState = "2";

    @ApiModelProperty(value = "单日入金最高限额",dataType = "Long")
    private Long allSingleMaxLimit;

    private String allSingleMaxLimitState = "2";

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "1:商户初审",dataType = "String")
    private String source;

    private String properties; // 用于导入时标记当前行修改的业务
}

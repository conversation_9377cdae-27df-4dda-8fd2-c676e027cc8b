package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
public class PhoneBwListVo implements Serializable {
    @ApiModelProperty( value = "ID", dataType = "Long")
    private Long id;

    @ApiModelProperty( value = "名单值", dataType = "String")
    @FieldAnnotation(fieldName = "手机号")
    private String bwValue;

    @ApiModelProperty( value = "创建时间", dataType = "Date")
    @FieldAnnotation(fieldName = "创建日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty( value = "备注", dataType = "String")
    @FieldAnnotation(fieldName = "备注")
    private String remark;

    @ApiModelProperty( value = "手机状态", dataType = "Short")
    private Short phoneStatus;

}

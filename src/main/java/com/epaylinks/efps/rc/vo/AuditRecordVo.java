package com.epaylinks.efps.rc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class AuditRecordVo implements Serializable {
    private Long id;

    private String oldValue;

    private String newValue;

    private String code;

    private String auditTargetType;

    private Long userId;
}

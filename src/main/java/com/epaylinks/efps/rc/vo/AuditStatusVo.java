package com.epaylinks.efps.rc.vo;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 风控档案状态变更审核对象
 * <AUTHOR>
 * @date 2020-12-29
 *
 */
@Data
@ApiModel
public class AuditStatusVo {

    private Long userId;

    /**
     * 状态名称：rcStatus：风控状态；accountStatus：账户状态
     */
    @ApiModelProperty( value = "状态名称[rcStatus：风控状态；accountStatus：账户状态]" , dataType = "String")
    private String statusName;
    
    /**
     * 账户状态： 0正常 1账户冻结 2止付 3禁止入金
     */
    @ApiModelProperty( value = "账户状态[0:正常; 1:账户冻结; 2:止付; 3:禁止入金]" , dataType = "String")
    private String status;

    /**
     * 变更原因
     */
    @ApiModelProperty( value = "变更原因" , dataType = "String")
    private String reason;


    @Override
    public String toString() {
    	return JSON.toJSONString(this);
    }
    
}
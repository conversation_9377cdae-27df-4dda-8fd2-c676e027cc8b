package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExportWebsiteEffectivenessVo implements Serializable {
    @FieldAnnotation(fieldName = "序号")
    private String rownum;

    @FieldAnnotation(fieldName = "商户号")
    private String customerNo;

    @FieldAnnotation(fieldName = "商户名称")
    private String customerName;

    @FieldAnnotation(fieldName = "所属平台商编号")
    private String platCustomerNo;

    @FieldAnnotation(fieldName = "所属平台商名称")
    private String platCustomerName;

    @FieldAnnotation(fieldName = "所属代理商编号")
    private String serviceCustomerNo;

    @FieldAnnotation(fieldName = "所属代理商名称")
    private String serviceCustomerName;

    @FieldAnnotation(fieldName = "角色")
    private String category;

    @FieldAnnotation(fieldName = "业务员")
    private String businessMan;

    @FieldAnnotation(fieldName = "所属分公司")
    private String companyName;

    @FieldAnnotation(fieldName = "网址")
    private String siteUrl;

    @FieldAnnotation(fieldName = "网站或APP名称")
    private String websiteApp;

    @FieldAnnotation(fieldName = "网址检验状态")
    private String checkStatus;

    @FieldAnnotation(fieldName = "风险标签")
    private String riskTag;

    @FieldAnnotation(fieldName = "核验结果备注")
    private String remark;

    @FieldAnnotation(fieldName = "命中的敏感词")
    private String hits;

    /**
     * 临时入金状态【1 or null：正常；2：限制临时入金】
     */
    private String temporaryStatus;

    private Long customerId;
}

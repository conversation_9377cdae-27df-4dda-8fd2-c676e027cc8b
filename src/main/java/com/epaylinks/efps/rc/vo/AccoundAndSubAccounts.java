package com.epaylinks.efps.rc.vo;
/**
 * 封装了主账户以及子孙账户
 * <AUTHOR>
 *
 */
public class AccoundAndSubAccounts {
    private String result;
    private AccountVo account;
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public AccountVo getAccount() {
		return account;
	}
	public void setAccount(AccountVo account) {
		this.account = account;
	}
}

package com.epaylinks.efps.rc.vo;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class AuditBwListVo {

    /**
     * 业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡
     */
    @ApiModelProperty(value = "业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡", dataType ="String")
    private String businessTagerType;

    /**
     * 业务对象ID
     */
    private String businessTagerId;

    /**
     * 黑白名单 0黑名单 1白名单
     */
    @ApiModelProperty(value = "黑白名单 0黑名单 1白名单", dataType ="String")
    private String bwType;
    
    /**
     * 备注
     */
    private String remark;

    /**
     * 风险标签
     */
    private String riskTag;

    /**
     * 操作人
     */
    private Long userId;

    private String userName;
    

    @Override
    public String toString() {
    	return JSON.toJSONString(this);
    }
    
    
}
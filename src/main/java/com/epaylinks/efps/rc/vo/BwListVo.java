package com.epaylinks.efps.rc.vo;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.rc.domain.BwList;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
public class BwListVo {
    
    /**
     * 
     */
    private static final long serialVersionUID = -6241752592963761951L;

    /**
     * ID
     */
    private Long bwId;

    @FieldAnnotation(fieldName="类型名称")
    private String businessTagerTypeName = getBusinessTagerName();

    /**
     * 适用业务类型
     */
    private String businessType;

    /**
     * 业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡
     */
    @ApiModelProperty(value = "业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡 014 商户名称", dataType ="String")
    private String businessTagerType;

    /**
     * 业务对象ID
     */
    private String businessTagerId;

    /**
     * 对象值名称，如省市需要翻译
     */
    @FieldAnnotation(fieldName="类型值")
    private String businessTagerName;

    /**
     * 黑白名单 0黑名单 1白名单
     */
    @ApiModelProperty(value = "黑白名单 0黑名单 1白名单", dataType ="String")
    @FieldAnnotation(fieldName="名单状态",dictionaries = "0:黑,1:白,2:灰")
    private String bwType;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyyMMdd HHmmss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern="yyyyMMdd HHmmss")
    private Date endTime;

    /**
     * 用户ID
     */
    private Long userId;

    @FieldAnnotation(fieldName="风险标签")
    private String riskTag;

    /**
     * 用户名称
     */
    @FieldAnnotation(fieldName="操作人")
    private String userName;

    /**
     * 备注
     */
    @FieldAnnotation(fieldName="备注")
    private String remark;

    /**
     * 创建时间
     */
    @FieldAnnotation(fieldName="添加时间")
    private String createTime;

    /**
     * 业务对象hash
     */
    private String tagerHash;

    /**
     * 审核状态：0待审核，1正常
     */
    @ApiModelProperty(value = "状态：0待审核，1正常", dataType ="Short")
    @FieldAnnotation(fieldName="审核状态",dictionaries = "0:待审核,1:正常")
    private Short status;


    /**
     * 使用状态：0未启用，1启用
     */
    @ApiModelProperty(value = " 使用状态：0未启用，1启用", dataType ="Short")
    private Short useStatus;

    private Object oldValue;

    private Object newValue;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public String getBusinessTagerTypeName() {
        String name;
        // 001身份证 002手机号 003社会统一信用代码 004银行卡
        switch (this.businessTagerType) {
            case "001":
                name = "身份证";
                break;
            case "002":
                name = "手机号";
                break;
            case "003":
                name = "社会统一信用代码";
                break;
            case "004":
                name = "银行卡";
                break;
            case "005":
                name = "商户编号";
                break;
            case "006":
                name = "身份证前4位";
                break;
            case "007":
                name = "经营地址";
                break;
            case "014":
                name = "商户名称";
                break;
            default:
                name = "";
                break;
        }
        return name;
    }

    /*
    审核意见
     */
    private String comments;
    
}
package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import lombok.Data;

/**
 * 操作日志vo对象
 *
 * <AUTHOR>
 * @date 2020-06-10 17:45
 */
@Data
public class OperateLogVo {

    /**
     * 菜单ID,关联PAS_PERM表
     */
    private String permId;

    /**
     * 菜单名称
     */
    @FieldAnnotation(fieldName="菜单名称")
    private String permName;

    /**
     * 商户编号/其他号码,修改默认限额该字段填"/"
     */
    @FieldAnnotation(fieldName="商户编号/其他号码")
    private String code;

    /**
     * 商户名称/其他名称,修改默认限额该字段填"/"
     */
    @FieldAnnotation(fieldName="商户名称")
    private String name;

    /**
     * 类型：1-新增；2-修改；3-删除
     */
    private String type;

    /**
     * 操作内容
     */
    @FieldAnnotation(fieldName="操作内容")
    private String operateContent;

    /**
     * 操作人
     */
    @FieldAnnotation(fieldName="用户名")
    private String operator;

    /**
     * 操作人名称
     */
    @FieldAnnotation(fieldName="真实名称")
    private String operatorName;

    /**
     * 操作时间
     */
    @FieldAnnotation(fieldName="操作时间")
    private String operateTime;
}

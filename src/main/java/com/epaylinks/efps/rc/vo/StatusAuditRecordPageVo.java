package com.epaylinks.efps.rc.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 状态变更审核记录分页结果
 * <AUTHOR>
 * @date 2020-12-29
 *
 */
@Data
@ApiModel
public class StatusAuditRecordPageVo{

    @ApiModelProperty( value = "审核记录audId" , dataType = "Long")
    private Long id;

    @ApiModelProperty( value = "商户编号" , dataType = "String")
    private String customerCode;
    
    @ApiModelProperty( value = "商户名称" , dataType = "String")
    private String customerName;
    
    @ApiModelProperty( value = "创建时间" , dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty( value = "操作员ID" , dataType = "Long")
    private Long operId;

    @ApiModelProperty(value = "操作人姓名",dataType = "String")
    private String operName;

    @ApiModelProperty( value = "审核状态[0待审核，1审核通过，2驳回]" , dataType = "Long")
    private Short auditResult;

    @ApiModelProperty( value = "审核员ID" , dataType = "Long")
    private Long auditOperId;

    @ApiModelProperty( value = "审核员名称" , dataType = "String")
    private String auditOperName;
    
    @ApiModelProperty( value = "审核时间" , dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @ApiModelProperty( value = "变更原因" , dataType = "String")
    private String reason;
    
    @ApiModelProperty( value = "变更前信息对象" , dataType = "Object")
    private Object oldRecord;

    @ApiModelProperty( value = "变更后信息对象" , dataType = "Object")
    private Object newRecord;

    @ApiModelProperty(value = "审核类型",dataType = "String")
    private String auditType;

    @ApiModelProperty(value = "审核意见",dataType = "String")
    private String remarks;

    private String uniqued;

    private String fileName;

    private String fileUrl;


}
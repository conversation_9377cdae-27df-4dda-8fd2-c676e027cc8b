package com.epaylinks.efps.rc.vo;

import java.util.List;
import java.util.Map;

/**
 * 投诉交易记录导入数据对象
 */
public class ComplainImportDataVo {
    /**
     * 导入标题数据
     */
    private List<String> titleList;

    /**
     * 导入内容数据
     */
    private Map<Integer, List<String>> dataMap;

    /**
     * 导入文件类型：ALI_RISK_GO、WECHAT_RISK_STORE、WECHAT_COMPLAIN
     */
    private String importFileType;

    /**
     * 操作人
     */
    private Long userId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 文件名
     */
    private String fileName;

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public Map<Integer, List<String>> getDataMap() {
        return dataMap;
    }

    public void setDataMap(Map<Integer, List<String>> dataMap) {
        this.dataMap = dataMap;
    }

    public String getImportFileType() {
        return importFileType;
    }

    public void setImportFileType(String importFileType) {
        this.importFileType = importFileType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}

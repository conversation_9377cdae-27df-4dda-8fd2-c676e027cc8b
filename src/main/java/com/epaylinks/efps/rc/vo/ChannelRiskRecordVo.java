package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.rc.domain.ChannelRiskRecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class ChannelRiskRecordVo extends ChannelRiskRecord {
    
    @ApiModelProperty(value="商户名称", dataType = "String")
    private String customerName;
    
    @ApiModelProperty(value="所属平台商户编号", dataType = "String")
    private String platCustomerCode;
    
    @ApiModelProperty(value="所属平台商户名称", dataType = "String")
    private String platCustomerName;
    
    @ApiModelProperty(value="所属代理商户编号", dataType = "String")
    private String serviceCustomerCode;
    
    @ApiModelProperty(value="所属代理商户名称", dataType = "String")
    private String serviceCustomerName;
    
}

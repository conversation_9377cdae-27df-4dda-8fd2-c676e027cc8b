package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.rc.domain.RiskEventRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class AuditRiskRuleQueryResponse {

    @ApiModelProperty(value = "规则ID", dataType = "Long")
    private Long ruleId;

    @ApiModelProperty(value = "操作类型：C:增 D:删 U改", dataType = "String")
    private String actionType;

    @ApiModelProperty(value = "修改前对象", dataType = "Object")
    private Object oldObject;

    @ApiModelProperty(value = "修改后对象", dataType = "Object")
    private Object newObject;

}

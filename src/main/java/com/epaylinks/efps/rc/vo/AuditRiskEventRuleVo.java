package com.epaylinks.efps.rc.vo;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class AuditRiskEventRuleVo {

    private String ruleParam1;      //参数一
    
    private String ruleParam2;      //参数二
    
    private String triggerAction;    //措施
    
    private String ruleStatus;      //规则状态，01启用，02停用
    
    private String changePerson;     //修改人员
    
    private Date updateTime;    //修改时间
    
    private String onTime;      //启用时间
    
    private String offTime;     //停用时间


    private String fileName;
    private String uniqueId;
    private List<String> customerNoList;

    @Override
    public String toString() {
    	return JSON.toJSONString(this);
    }
    
    
}
package com.epaylinks.efps.rc.vo;

import java.util.Date;

public class TxsNfcRcMode {
    
    /**
     * 风控规则编码:比如R012等
     */
    private String rcRuleCode;
    
    /**
     * 风控模型:比如TYPEA,TYPEB等
     */
    private String rcMode;

    /**
     * 记录创建时间,默认为记录插入的时间
     */
    private Date createTime;

    /**
     * 是否有效.1-有效
     */
    private String valid;

    /**
     * 是否按商户号,1表示是
     */
    private String byCustomerCode;

    /**
     * 是否按卡类型,1表示是
     */
    private String byCardType;

    /**
     * 是否按加密的卡与,1表示是
     */
    private String byCardNoEnc;

    /**
     * 判断依据:1-按笔数,2-按amount
     */
    private String judgeType;

    /**
     * 阈值，金额时，单位是分
     */
    private Long value;

    /**
     * 配置表的相同字段
     */
    private String markString;
    
    /**
     * 禁止出金, 1表示是
     */
    private String bandOutCome;
    
    /**
     * 禁止业务, 1表示是
     */
    private String bandBusiness;
    
    /**
     * 按什么来禁止, 1按卡交易过的商户号; 2按当前交易的商户号
     */
    private String bandType;
    
    /*
     * 是否自动打开止村
     */
    private String autoOpenPay;
    
    /*
     * 是否自动打开业务
     */
    private String autoOpenBiz;
    

    public String getRcMode() {
        return rcMode;
    }

    public void setRcMode(String rcMode) {
        this.rcMode = rcMode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public String getByCustomerCode() {
        return byCustomerCode;
    }

    public void setByCustomerCode(String byCustomerCode) {
        this.byCustomerCode = byCustomerCode;
    }

    public String getByCardType() {
        return byCardType;
    }

    public void setByCardType(String byCardType) {
        this.byCardType = byCardType;
    }

    public String getByCardNoEnc() {
        return byCardNoEnc;
    }

    public void setByCardNoEnc(String byCardNoEnc) {
        this.byCardNoEnc = byCardNoEnc;
    }

    public String getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(String judgeType) {
        this.judgeType = judgeType;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public String getMarkString() {
        return markString;
    }

    public void setMarkString(String markString) {
        this.markString = markString;
    }

	public String getBandOutCome() {
		return bandOutCome;
	}

	public void setBandOutCome(String bandOutCome) {
		this.bandOutCome = bandOutCome;
	}

	public String getBandBusiness() {
		return bandBusiness;
	}

	public void setBandBusiness(String bandBusiness) {
		this.bandBusiness = bandBusiness;
	}

	public String getBandType() {
		return bandType;
	}

	public void setBandType(String bandType) {
		this.bandType = bandType;
	}

	public String getAutoOpenPay() {
		return autoOpenPay;
	}

	public void setAutoOpenPay(String autoOpenPay) {
		this.autoOpenPay = autoOpenPay;
	}

	public String getAutoOpenBiz() {
		return autoOpenBiz;
	}

	public void setAutoOpenBiz(String autoOpenBiz) {
		this.autoOpenBiz = autoOpenBiz;
	}

    public String getRcRuleCode() {
        return rcRuleCode;
    }

    public void setRcRuleCode(String rcRuleCode) {
        this.rcRuleCode = rcRuleCode;
    }
	
	
    
}
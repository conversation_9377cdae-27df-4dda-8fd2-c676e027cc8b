package com.epaylinks.efps.rc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EBankConfigVo {

    @ApiModelProperty( value = "网银业务设置：1：收单；2：充值；3：取上级" , dataType = "String")
    private String eBankSetting;
    
    @ApiModelProperty( value = "充值银行卡校验(0：不校验；1：需校验（非同名卡）；2：需校验所有卡)" , dataType = "String")
    private String checkRechargeCard;

    @ApiModelProperty(value = "订单转账支付校验(0：不校验；1：需校验（非同名卡）；2：需校验所有卡；3：只允许对公账户)", dataType = "String")
    private String checkTransferPay;
    
    @ApiModelProperty( value = "交易起始时段（格式：HH:mm）" , dataType = "String")
    private String tradeStartTime;
    
    @ApiModelProperty( value = "交易截止时段（格式：HH:mm）" , dataType = "String")
    private String tradeEndTime;
    
    
}

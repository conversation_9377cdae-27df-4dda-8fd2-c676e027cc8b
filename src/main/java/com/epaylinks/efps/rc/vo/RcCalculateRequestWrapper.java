package com.epaylinks.efps.rc.vo;

import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/19 11:59
 */
public class RcCalculateRequestWrapper {
    private final RcCalculateRequest request;
    private String customerCode;
    private Constants.rcBusinessType businessType;

    private RcCalculateRequestWrapper(RcCalculateRequest request) {
        this.request = request;
    }

    public static RcCalculateRequestWrapper wrap(RcCalculateRequest request) {
        return new RcCalculateRequestWrapper(request);
    }

    public String getTarget(RcConstants.BusinessTagerType tagerType) {
        return request.getBusinessTargetIds().get(tagerType.code);
    }

    public String getIndex(RcConstants.RcIndex index) {
        if (request.getIndexs() == null) {
            return null;
        }
        return request.getIndexs().get(index.code);
    }

    public Optional<String> getCustomerCode() {
        if (customerCode == null) {
            customerCode = getTarget(RcConstants.BusinessTagerType.CUSTOMER_CODE);
        }
        return Optional.ofNullable(customerCode);
    }

    public String getOutTradeNo() {
        return request.getOutTradeNo();
    }

    public String getTransactionNo() {
        return request.getTransactionNo();
    }

    public Optional<Constants.rcBusinessType> getBusinessType() {
        if (businessType == null) {
            for (Constants.rcBusinessType value : Constants.rcBusinessType.values()) {
                if (value.code.equals(request.getBusinessType())) {
                    businessType = value;
                    break;
                }
            }
        }
        return Optional.ofNullable(businessType);
    }

    public String getBusinessCode() {
        return request.getBusinessCode();
    }

    public String getPayMethod() {
        return request.getPayMethod();
    }
}

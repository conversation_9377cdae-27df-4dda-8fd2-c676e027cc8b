package com.epaylinks.efps.rc.vo;

import java.util.Map;

import com.alibaba.fastjson.JSON;

/**
 * 风险计算请求
 * <AUTHOR>
 *
 */
public class RcCalculateRequest {
	//商户订单号
	private String outTradeNo;
	//内部订单号
	private String transactionNo;
	//适用业务类型
	private String businessType;
	//入参指标，如{"amount" : "100" , "tradeNum" : "1"}
	private Map<String, String> indexs;
	//业务对象id，如{"005" : "xxxx151" , "004" : "***********"}  005：商户号；004:银行卡号
	private Map<String, String> businessTargetIds;
	// 具体业务编码
	private String businessCode;
	//支付方式
	private String payMethod;

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getBusinessType() {
		return businessType;
	}
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	public Map<String, String> getIndexs() {
		return indexs;
	}
	public void setIndexs(Map<String, String> indexs) {
		this.indexs = indexs;
	}
	public Map<String, String> getBusinessTargetIds() {
		return businessTargetIds;
	}
	public void setBusinessTargetIds(Map<String, String> businessTargetIds) {
		this.businessTargetIds = businessTargetIds;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
    public String getBusinessCode() {
        return businessCode;
    }
    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
	
	
}

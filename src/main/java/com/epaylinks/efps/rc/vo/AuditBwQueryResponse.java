package com.epaylinks.efps.rc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class AuditBwQueryResponse {

    @ApiModelProperty(value = "黑名单ID", dataType = "Long")
    private Long bwId;

    @ApiModelProperty(value = "操作类型：C:增 D:删 U改", dataType = "String")
    private String actionType;

    @ApiModelProperty(value = "修改前对象", dataType = "Object")
    private AuditBwListVo oldBwObject;

    @ApiModelProperty(value = "修改后对象", dataType = "Object")
    private AuditBwListVo newBwObject;

}

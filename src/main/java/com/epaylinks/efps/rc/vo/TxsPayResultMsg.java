package com.epaylinks.efps.rc.vo;

import java.util.Map;

import com.alibaba.fastjson.JSON;

public class TxsPayResultMsg {
	/**
	 * 外部订单号
	 */
	private String outTradeNo;
	/**
	 * 易票联订单号
	 */
	private String transactionNo;
	/**
	 * 业务类型
	 */
	private String businessType;
	/**
	 * 业务编码
	 */
	private String businessCode;
	
	/**
	 * 支付方式
	 */
	private String payMethod;
	
	/**
	 * 订单状态: 00 :成功；01：失败
	 */
	private String payState;
	/**
	 * 指标参数，如{"amount" : "100" , "tradeNum" : "1"}
	 */
	private Map<String, String> indexs;
	/**
	 * 风控的业务对象id，如：{"005" : "562XXXXX001" , "004" : "***********"}
	 */
	private Map<String, String> businessTargetIds;

	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getBusinessType() {
		return businessType;
	}
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	public Map<String, String> getIndexs() {
		return indexs;
	}
	public void setIndexs(Map<String, String> indexs) {
		this.indexs = indexs;
	}
	public Map<String, String> getBusinessTargetIds() {
		return businessTargetIds;
	}
	public void setBusinessTargetIds(Map<String, String> businessTargetIds) {
		this.businessTargetIds = businessTargetIds;
	}
	
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public String getPayState() {
		return payState;
	}
	public void setPayState(String payState) {
		this.payState = payState;
	}
	
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
	
}

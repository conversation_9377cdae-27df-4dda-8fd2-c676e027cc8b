package com.epaylinks.efps.rc.drools;

import com.epaylinks.efps.common.tool.bytes.Bytes;
import com.epaylinks.efps.common.tool.error.exception.EpException;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.drools.compiler.compiler.io.memory.MemoryFileSystem;
import org.drools.compiler.kie.builder.impl.InternalKieModule;
import org.drools.compiler.kie.builder.impl.KieContainerImpl;
import org.drools.compiler.kie.builder.impl.KieFileSystemImpl;
import org.kie.api.KieBase;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.Message;
import org.kie.api.builder.Results;
import org.kie.api.builder.model.KieBaseModel;
import org.kie.api.builder.model.KieModuleModel;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DroolsManager {

    @Data
    public static class DroolsRule {

        /**
         * 规则id
         */
        private Long ruleId;
        /**
         * kbase的名字
         */
        private String kieBaseName;
        /**
         * 设置该kbase需要从那个目录下加载文件，这个是一个虚拟的目录，相对于 `src/main/resources`
         * 比如：kiePackageName=rules/rule01 那么当前规则文件写入路径为： kieFileSystem.write("src/main/resources/rules/rule01/1.drl")
         */
        private String kiePackageName;
        /**
         * 规则内容
         */
        private String ruleContent;
    }

    // 此类本身就是单例的
    private final KieServices kieServices = KieServices.get();
    // kie文件系统，需要缓存，如果每次添加规则都是重新new一个的话，则可能出现问题。即之前加到文件系统中的规则没有了
    private final KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
    // 可以理解为构建 kmodule.xml
    private final KieModuleModel kieModuleModel = kieServices.newKieModuleModel();
    // 需要全局唯一一个，如果每次加个规则都新创建一个，那么旧需要销毁之前创建的kieContainer，如果此时有正在使用的KieSession，则可能有问题
    private KieContainer kieContainer;

    public MemoryFileSystem getMemoryFileSystem() {
        return ((KieFileSystemImpl)kieFileSystem).asMemoryFileSystem();
    }

    public Optional<Long> getRuleIdFromFilename(String filename) {
        if (filename.endsWith(".drl")) {
            return Optional.of(Long.parseLong(FilenameUtils.removeExtension(FilenameUtils.getBaseName(filename))));
        } else {
            return Optional.empty();
        }
    }

    public String getFilename(DroolsRule droolsRule) {
        return "src/main/resources/" + droolsRule.getKiePackageName() + "/" + droolsRule.getRuleId() + ".drl";
    }

    /**
     * 判断该kbase是否存在
     */
    public boolean existsKieBase(String kieBaseName) {
        if (null == kieContainer) {
            return false;
        }
        Collection<String> kieBaseNames = kieContainer.getKieBaseNames();
        if (kieBaseNames.contains(kieBaseName)) {
            return true;
        }
        log.info("需要创建KieBase:{}", kieBaseName);
        return false;
    }

    public void deleteRule(DroolsRule droolsRule) {
        // 获取kbase的名称
        String kieBaseName = droolsRule.getKieBaseName();
        // 判断该kbase是否存在
        EpAssert.state(existsKieBase(kieBaseName), RcCode.PARAM_ERROR.msg("kie base 不存在"));

        KieBaseModel kieBaseModel = kieModuleModel.getKieBaseModels().get(kieBaseName);

        EpAssert.state(kieBaseModel.getPackages().contains(droolsRule.getKiePackageName()), RcCode.PARAM_ERROR.msg("包不存在"));

        String file = getFilename(droolsRule);
        byte[] fileBytes = kieFileSystem.read(file);
        if (fileBytes == null) { //文件不存在直接返回
            return;
        }
//        EpAssert.notNull(fileBytes,  RcCode.PARAM_ERROR.msg("文件不存在"));
        log.info("删除虚拟规则文件:{}", file);
        kieFileSystem.delete(file);

        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        // 通过KieBuilder构建KieModule下所有的KieBase
        kieBuilder.buildAll();
        // 获取构建过程中的结果
        Results results = kieBuilder.getResults();
        // 获取错误信息
        List<Message> messages = results.getMessages(Message.Level.ERROR);
        if (null != messages && !messages.isEmpty()) {
            //删除文件后加载异常，把文件写回去
            kieFileSystem.write(file, fileBytes);
            for (Message message : messages) {
                log.error(message.getText());
            }
            throw new EpException(RcCode.PARAM_ERROR.msg("加载规则出现异常:" + messages.stream().map(Message::getText).collect(Collectors.joining(","))));
        }
        // 实现动态更新
        ((KieContainerImpl) kieContainer).updateToKieModule((InternalKieModule) kieBuilder.getKieModule());
    }

    /**
     * 添加或更新 drools 规则
     */
    public void addOrUpdateRule(DroolsRule droolsRule) {
        // 获取kbase的名称
        String kieBaseName = droolsRule.getKieBaseName();
        // 判断该kbase是否存在
        boolean existsKieBase = existsKieBase(kieBaseName);
        // 该对象对应kmodule.xml中的kbase标签
        KieBaseModel kieBaseModel;
        if (!existsKieBase) {
            // 创建一个kbase
            kieBaseModel = kieModuleModel.newKieBaseModel(kieBaseName);
            // 不是默认的kieBase
            kieBaseModel.setDefault(false);
            // 设置该KieBase需要加载的包路径
            kieBaseModel.addPackage(droolsRule.getKiePackageName());
            // 设置kieSession
            kieBaseModel.newKieSessionModel(kieBaseName + "-session")
                    // 不是默认session
                    .setDefault(false);
        } else {
            // 获取到已经存在的kbase对象
            kieBaseModel = kieModuleModel.getKieBaseModels().get(kieBaseName);
            // 获取到packages
            List<String> packages = kieBaseModel.getPackages();
            if (!packages.contains(droolsRule.getKiePackageName())) {
                kieBaseModel.addPackage(droolsRule.getKiePackageName());
                log.info("kieBase:{}添加一个新的包:{}", kieBaseName, droolsRule.getKiePackageName());
            } else {
                kieBaseModel = null;
            }
        }
        String file = "src/main/resources/" + droolsRule.getKiePackageName() + "/" + droolsRule.getRuleId() + ".drl";
        log.info("加载虚拟规则文件:{}", file);
        byte[] oldFileBytes = kieFileSystem.read(file);
        kieFileSystem.write(file, droolsRule.getRuleContent());

        if (kieBaseModel != null) {
            String kmoduleXml = kieModuleModel.toXML();
            log.info("加载kmodule.xml:[\n{}]", kmoduleXml);
            kieFileSystem.writeKModuleXML(kmoduleXml);
        }
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        // 通过KieBuilder构建KieModule下所有的KieBase
        kieBuilder.buildAll();
        // 获取构建过程中的结果
        Results results = kieBuilder.getResults();
        // 获取错误信息
        List<Message> messages = results.getMessages(Message.Level.ERROR);
        if (null != messages && !messages.isEmpty()) {
            if (oldFileBytes != null) { //如果是更新的文件错误时需要恢复
                kieFileSystem.write(file, oldFileBytes);
            } else {
                kieFileSystem.delete(file); //如果是新增文件错误时删除文件，不然下次还会报错
            }
            for (Message message : messages) {
                log.error(message.getText());
            }
            throw new EpException(RcCode.PARAM_ERROR.msg("加载规则出现异常:" + messages.stream().map(Message::getText).collect(Collectors.joining(","))));
        }
        // KieContainer只有第一次时才需要创建，之后就是使用这个
        if (null == kieContainer) {
            kieContainer = kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
        } else {
            // 实现动态更新
            ((KieContainerImpl) kieContainer).updateToKieModule((InternalKieModule) kieBuilder.getKieModule());
        }
    }

    /**
     * 触发规则
     */
    public void fireRule(String kieBaseName, TxsPayResultMsg payResultMsg) {
        //将payr
        Map<String, String> businessTargetIds = payResultMsg.getBusinessTargetIds();

        String person = businessTargetIds.remove(RcConstants.BusinessTagerType.PERSON.code);
        if (person != null) {
            businessTargetIds.put(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, person);
        }

        // 创建kieSession
        KieSession kieSession = kieContainer.newKieSession(kieBaseName + "-session");
        kieSession.insert(payResultMsg);
        try {
            kieSession.fireAllRules();
        } finally {
            kieSession.dispose();
            if (person != null) {
                businessTargetIds.remove(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
                businessTargetIds.put(RcConstants.BusinessTagerType.PERSON.code, person);
            }
        }
    }
}

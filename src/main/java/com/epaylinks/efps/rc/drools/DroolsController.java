package com.epaylinks.efps.rc.drools;

import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.response.EpResponse;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.drools.model.AddOrUpdateRuleRequest;
import com.epaylinks.efps.rc.drools.model.AddOrUpdateDrlRequest;
import com.epaylinks.efps.rc.drools.model.GetRuleSceneResponse;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/11 10:19
 */
@RestController
@RequestMapping("/drools")
public class DroolsController {
    @Autowired
    private DroolsSceneService sceneService;

    @Autowired
    private DroolsService droolsService;

    @ApiOperation(value = "获取所有DRL文件名", notes = "获取所有DRL文件名")
    @GetMapping("getDrools")
    public Collection<String> getDrools() {
        return sceneService.getDrools();
    }

    @ApiOperation(value = "新增或更新DRL文件", notes = "新增或更新DRL文件")
    @PostMapping("/addOrUpdateDrools")
    public EpResponse<?> addOrUpdateDrools(@RequestBody AddOrUpdateDrlRequest addOrUpdateDrlRequest) {
        sceneService.addOrUpdateDrools(addOrUpdateDrlRequest.getDrlId(), addOrUpdateDrlRequest.getDrlContent());
        return EpResponse.success();
    }

    @ApiOperation(value = "删除DRL文件", notes = "删除DRL文件")
    @PostMapping("/deleteDrools")
    public EpResponse<?> deleteDrools(Long drlId) {
        sceneService.deleteDrools(drlId);
        return EpResponse.success();
    }

    @ApiOperation(value = "更新DRL函数文件", notes = "更新DRL函数文件")
    @PostMapping("/updateFunctionDrools")
    public EpResponse<?> updateFunctionDrools(
            @RequestPart("file") MultipartFile file
    ) throws IOException {
        sceneService.updateFunctionDrools(file);
        return EpResponse.success();
    }

    @ApiOperation(value = "获取规则场景", notes = "获取规则场景")
    @GetMapping("getRuleScene")
    public EpResponse<GetRuleSceneResponse> getRuleScene() {
        return EpResponse.success(sceneService.getRuleScene());
    }

    @ApiOperation(value = "触发规则", notes = "触发规则")
    @PostMapping("/fireRule")
    public void fireRule(@RequestBody TxsPayResultMsg payResult) {
        droolsService.fireRule(payResult);
    }

    @ApiOperation(value = "新增规则", notes = "新增规则")
    @PostMapping("/addRule")
    public EpResponse<?> addRule(@RequestHeader("x-userid") Long userId, @RequestBody @Valid AddOrUpdateRuleRequest request) {
        EpAssert.runOrThrow(() -> sceneService.addOrUpdateRule(request, userId), RcCode.SYSTEM_EXCEPTION);
        return EpResponse.success();
    }

    @ApiOperation(value = "修改规则", notes = "修改规则")
    @PostMapping("/updateRule")
    public EpResponse<?> updateRule(@RequestHeader("x-userid") Long userId, @RequestBody @Valid AddOrUpdateRuleRequest request) {
        sceneService.addOrUpdateRule(request, userId);
        return EpResponse.success();
    }

    @ApiOperation(value = "删除规则", notes = "删除规则")
    @PostMapping("/deleteRule")
    public EpResponse<?> deleteRule(@RequestHeader("x-userid") Long userId, Long ruleId) {
        sceneService.deleteRule(ruleId, userId);
        return EpResponse.success();
    }
}

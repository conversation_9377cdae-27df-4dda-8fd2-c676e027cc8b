package com.epaylinks.efps.rc.drools.model;

import com.epaylinks.efps.rc.domain.RcDroolsWhen;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/16 13:43
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddOrUpdateRuleRequest {
    @ApiModelProperty("规则ID，更新时候填写")
    private Long ruleId;

    @NotBlank(message = "规则描述不能为空")
    @ApiModelProperty("规则描述")
    private String ruleDesc;

    @NotBlank(message = "应用场景不能为空")
    @ApiModelProperty("应用场景")
    private String sceneText;

    @NotEmpty
    @ApiModelProperty("条件列表")
    private List<When> whenList;

    @NotBlank
    @ApiModelProperty("措施")
    private String thenText;

    @ApiModelProperty("免触发文件ID")
    private String uniqueId;

    @ApiModelProperty("免触发文件名")
    private String fileName;

    @NotBlank(message = "规则分类不能为空")
    @ApiModelProperty("规则分类")
    private String ruleType;

    private String ruleStatus;

    @NotNull(message = "触发规则不能为空")
    @Min(0)
    @Max(2)
    @ApiModelProperty("触发规则：0：全部商户 1：清单内监控 2：清单内不监控")
    private Integer triggerType;

    public enum TriggerType {
        ALL,
        INCLUDE,
        EXCLUDE;

        public static TriggerType of(Integer triggerType) {
            for (TriggerType value : TriggerType.values()) {
                if (value.ordinal() == triggerType) {
                    return value;
                }
            }
            throw new IllegalArgumentException("触发规则码错误");
        }
    }

    @JsonIgnore
    public TriggerType getTriggerTypeEnum() {
        return TriggerType.of(triggerType);
    }

    @Data
    public static class When {
        @NotBlank
        @ApiModelProperty("条件左边文本")
        private String leftText;

        @NotBlank
        @ApiModelProperty("条件操作符文本")
        private String operatorText;

        @NotBlank
        @ApiModelProperty("条件右边文本")
        private String rightText;

        private RcDroolsWhen.RightText.Type rightTextType;

        private RcDroolsWhen.RightText.Format rightTextFormat;
    }
}

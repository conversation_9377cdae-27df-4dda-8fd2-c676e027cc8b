package com.epaylinks.efps.rc.drools;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.tool.bytes.Bytes;
import com.epaylinks.efps.common.tool.error.exception.EpException;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.*;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.drools.model.AddOrUpdateRuleRequest;
import com.epaylinks.efps.rc.drools.model.GetRuleSceneResponse;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcAuditRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.vo.AuditRiskRuleQueryResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:43
 */
@Service
@Slf4j
public class DroolsSceneService {
    @Autowired
    private RcDroolsSceneThenMapper sceneThenMapper;

    @Autowired
    private RcDroolsSceneWhenMapper sceneWhenMapper;

    @Autowired
    private RcDroolsThenMapper thenMapper;

    @Autowired
    private RcDroolsWhenMapper whenMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcDroolsMapper droolsMapper;

    @Autowired
    private DroolsService droolsService;

    @Autowired
    private RiskEventRuleMapper riskEventRuleMapper;

    @Autowired
    private RcAuditRecordService rcAuditRecordService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RcRuleSqlTemplateMapper sqlTemplateMapper;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private OtherService otherService;

    public Collection<String> getDrools() {
        return droolsService.getDrlFiles();
    }

    /**
     * 新增或更新DRL，先更新内存中的DRL文件，无异常后再更新数据库中的记录
     *
     * @param drlId      DRL文件ID
     * @param drlContent DRL文件内容
     */
    public void addOrUpdateDrools(Long drlId, String drlContent) {
        if (drlId == null) {
            drlId = sequenceService.nextValue("RC_DROOLS");
        }

        //更新内存中的DRL文件
        droolsService.update(drlId, drlContent);

        RcDrools rcDrools = droolsMapper.selectByPrimaryKey(drlId);
        if (rcDrools == null) {
            rcDrools = new RcDrools();
            rcDrools.setId(drlId);
            rcDrools.setCreateTime(new Date());
            rcDrools.setUpdateTime(rcDrools.getCreateTime());
            rcDrools.setDrlContent(drlContent.getBytes(StandardCharsets.UTF_8));
            droolsMapper.insert(rcDrools);
        } else {
            rcDrools.setUpdateTime(new Date());
            rcDrools.setDrlContent(drlContent.getBytes(StandardCharsets.UTF_8));
            droolsMapper.updateByPrimaryKey(rcDrools);
        }
    }

    /**
     * 删除DRL，先删除内存中的DRL文件，无异常后再删除数据库中的记录
     *
     * @param drlId
     */
    public void deleteDrools(Long drlId) {
        //删除内存中的DRL文件
        droolsService.delete(drlId);

        droolsMapper.deleteByPrimaryKey(drlId);
    }

    /**
     * 专门更新drlId为0的基础函数文件
     *
     * @param file 接口上传的函数文件
     * @throws IOException
     */
    public void updateFunctionDrools(MultipartFile file) throws IOException {
        this.addOrUpdateDrools(0L, Bytes.fromBytes(file.getBytes()).toText(StandardCharsets.UTF_8));
    }

    /**
     * 获取规则场景
     *
     * @return 规则场景
     */
    public GetRuleSceneResponse getRuleScene() {
        List<RcDroolsSceneWhen> sceneWhenList = sceneWhenMapper.selectAllOrderById();
        List<RcDroolsSceneThen> sceneThenList = sceneThenMapper.selectAllOrderById();
        List<RcDroolsWhen> whenList = whenMapper.selectAllOrderById();
        List<GetRuleSceneResponse.Scene> sceneList = sceneWhenList.stream().map(RcDroolsSceneWhen::getSceneText).distinct().map((sceneText -> {
            GetRuleSceneResponse.Scene scene = new GetRuleSceneResponse.Scene();
            scene.setScene(sceneText);
            scene.setWhenList(sceneWhenList.stream()
                    .filter(sw -> sw.getSceneText().equals(sceneText))
                    .map(sw -> {
                        GetRuleSceneResponse.When when = new GetRuleSceneResponse.When();
                        when.setLeftText(sw.getLeftText());
                        when.setOperatorTextList(whenList.stream()
                                .filter(w -> w.getLeftText().equals(sw.getLeftText()))
                                .flatMap(w -> {
                                    RcDroolsWhen.OperatorText ot = JsonUtils.jsonToObj(w.getOperatorText(), RcDroolsWhen.OperatorText.class);
                                    return ot.getTextList().stream();
                                })
                                .distinct()
                                .collect(Collectors.toList()));
                        when.setRightTextList(whenList.stream()
                                .filter(w -> w.getLeftText().equals(sw.getLeftText()) && w.getRightText() != null)
                                .flatMap(w -> {
                                    RcDroolsWhen.RightText rt = JsonUtils.jsonToObj(w.getRightText(), RcDroolsWhen.RightText.class);
                                    return rt.getTextList().stream();
                                })
                                .distinct()
                                .collect(Collectors.toList()));

                        when.setRightTextFormat(whenList.stream()
                                .filter(w -> w.getLeftText().equals(sw.getLeftText()) && w.getRightText() != null)
                                .map(w -> JsonUtils.jsonToObj(w.getRightText(), RcDroolsWhen.RightText.class))
                                .distinct()
                                .collect(Collectors.toList()).get(0).getFormat());

                        when.setRightTextType(whenList.stream()
                                .filter(w -> w.getLeftText().equals(sw.getLeftText()) && w.getRightText() != null)
                                .map(w -> JsonUtils.jsonToObj(w.getRightText(), RcDroolsWhen.RightText.class))
                                .distinct()
                                .collect(Collectors.toList()).get(0).getType());

                        when.setPriority(sw.getPriority());
                        if (when.getRightTextList().isEmpty()) {
                            when.setRightTextList(null);
                        }
                        return when;
                    }).collect(Collectors.toList()));
            scene.setThenList(sceneThenList.stream()
                    .filter(t -> t.getSceneText().equals(sceneText))
                    .map(RcDroolsSceneThen::getThenText)
                    .collect(Collectors.toList()));
            return scene;
        })).collect(Collectors.toList());

        GetRuleSceneResponse response = new GetRuleSceneResponse();
        response.setSceneList(sceneList);
        return response;
    }

    private String getWhenDrl(List<RcDroolsWhen> rcDroolsWhens, AddOrUpdateRuleRequest.When when) {
        List<String> orDrl = new ArrayList<>();
        for (RcDroolsWhen rcDroolsWhen : rcDroolsWhens) {
            if (rcDroolsWhen.getLeftText().equals(when.getLeftText())) {
                RcDroolsWhen.OperatorText ot = JsonUtils.jsonToObj(rcDroolsWhen.getOperatorText(), RcDroolsWhen.OperatorText.class);
                if (ot.getTextList().contains(when.getOperatorText())) {
                    RcDroolsWhen.OperatorDrl od = JsonUtils.jsonToObj(rcDroolsWhen.getOperatorDrl(), RcDroolsWhen.OperatorDrl.class);
                    RcDroolsWhen.RightText rt = JsonUtils.jsonToObj(rcDroolsWhen.getRightText(), RcDroolsWhen.RightText.class);
                    RcDroolsWhen.RightDrl rd = JsonUtils.jsonToObj(rcDroolsWhen.getRightDrl(), RcDroolsWhen.RightDrl.class);
                    String leftDrl = Optional.ofNullable(rcDroolsWhen.getLeftDrl()).orElse("");
                    String oprDrl = od.getTextDrlMap().get(when.getOperatorText());

                    if (rt.getType() == RcDroolsWhen.RightText.Type.SINGLE_TEXT) {
                        String rtDrl = Optional.ofNullable(rd.getPrefix()).orElse("")
                                + when.getRightText()
                                + Optional.ofNullable(rd.getSuffix()).orElse("");

                        return leftDrl + " " + oprDrl + " " + rtDrl;
                    }

                    if (rt.getType() == RcDroolsWhen.RightText.Type.MULTI_TEXT || rt.getType() == RcDroolsWhen.RightText.Type.TIME_PERIOD) {
                        Set<String> s = JsonUtils.jsonToObj(when.getRightText(), new TypeReference<LinkedHashSet<String>>() {
                        });

                        String rtDrl = Optional.ofNullable(rd.getPrefix()).orElse("")
                                + Joiner.on(rd.getSeparator()).join(s)
                                + Optional.ofNullable(rd.getSuffix()).orElse("");
                        return leftDrl + " " + oprDrl + " " + rtDrl;
                    }

                    if (rt.getType() == RcDroolsWhen.RightText.Type.SINGLE_CHOICE && rt.getTextList().contains(when.getRightText())) {
                        String rtDrl = Optional.ofNullable(rd.getPrefix()).orElse("")
                                + rd.getTextDrlMap().get(when.getRightText())
                                + Optional.ofNullable(rd.getSuffix()).orElse("");
                        orDrl.add("(" + leftDrl + " " + oprDrl + " " + rtDrl + ")");
                    }

                    if (rt.getType() == RcDroolsWhen.RightText.Type.MULTI_CHOICE) {
                        Set<String> s = JsonUtils.jsonToObj(when.getRightText(), new TypeReference<LinkedHashSet<String>>() {
                        });
                        s = s.stream().filter(t -> rd.getTextDrlMap().containsKey(t)).map(t -> rd.getTextDrlMap().get(t)).collect(Collectors.toSet());
                        if (!s.isEmpty()) {
                            String rtDrl = Optional.ofNullable(rd.getPrefix()).orElse("")
                                    + Joiner.on(rd.getSeparator()).join(s)
                                    + Optional.ofNullable(rd.getSuffix()).orElse("");
                            orDrl.add("(" + leftDrl + " " + oprDrl + " " + rtDrl + ")");
                        }
                    }
                }
            }
        }

        if (!orDrl.isEmpty()) {
            return "(" + Joiner.on("||").join(orDrl) + ")";
        }

        throw new EpException(RcCode.PARAM_ERROR.msg("找不到条件" + when.getLeftText() + when.getOperatorText() + when.getRightText() + "对应的规则"));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void deleteRule(Long ruleId, Long userId) {
        RiskEventRule riskEventRule = riskEventRuleMapper.selectRuleByID(ruleId);
        if (riskEventRule != null && riskEventRule.getRuleCode().startsWith("T")) {
            // 保存待审核记录
            rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code,
                    ruleId, JSON.toJSONString(riskEventRule), null, RcConstants.AuditActionType.DELETE.code, userId, null);
            Long drlId = Long.parseLong(riskEventRule.getRuleCode().substring(1));
            deleteDrools(drlId);
            riskEventRuleMapper.deleteByRuleId(ruleId);
        }
    }

    public String getRuleCodeByDrlId(Long drlId) {
        return "T" + drlId;
    }

    public Long getDrlIdByRuleCode(String ruleCode) {
        return Long.parseLong(ruleCode.substring(1));
    }

    /**
     * 查找允许修改的规则
     *
     * @param ruleId
     * @return
     */
    public RiskEventRule getRuleAllowUpdate(Long ruleId) {
        RiskEventRule riskEventRule = riskEventRuleMapper.selectRuleByID(ruleId);
        EpAssert.notNull(riskEventRule, RcCode.PARAM_ERROR.msg("未找到修改的规则"));
        EpAssert.state(!RcConstants.AuditStatus.WAITING.code.equals(riskEventRule.getAuditStatus()), RcCode.PARAM_ERROR.msg("待审核规则不允许修改"));
        EpAssert.state(riskEventRule.getRuleCode().startsWith("T"), RcCode.PARAM_ERROR.msg("旧规则不允许修改"));
        return riskEventRule;
    }

    private void saveUpdateAuditRecord(Long ruleId, String oldVal, String newVal, Long userId) {
        EpAssert.state(!newVal.equals(oldVal), RcCode.PARAM_ERROR.msg("请修改信息后再提交"));
        rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code,
                ruleId, oldVal, newVal, RcConstants.AuditActionType.UPDATE.code, userId, null);

        //更新规则状态为待审核
        RiskEventRule forUpdate = new RiskEventRule();
        forUpdate.setRuleId(ruleId);
        forUpdate.setAuditStatus(RcConstants.AuditStatus.WAITING.code);
        forUpdate.setUpdateTime(new Date());

        riskEventRuleMapper.updateParamById(forUpdate);
    }

    public AuditRiskRuleQueryResponse getAuditDetail(Long ruleId) {
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code, ruleId);
        EpAssert.notNull(auditRecord, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION);

        AuditRiskRuleQueryResponse response = new AuditRiskRuleQueryResponse();
        response.setRuleId(ruleId);
        response.setActionType(auditRecord.getActionType());
        if (auditRecord.getOldValue() != null) {
            response.setOldObject(JsonUtils.jsonToObj(auditRecord.getOldValue(), AddOrUpdateRuleRequest.class));
        }
        response.setNewObject(JsonUtils.jsonToObj(auditRecord.getNewValue(), AddOrUpdateRuleRequest.class));
        return response;
    }

    @Logable(businessTag = "audit")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void audit(Long ruleId, Short auditResult, String remarks, Long userId) {
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code, ruleId);
        EpAssert.notNull(auditRecord, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION);

        RiskEventRule riskEventRule = riskEventRuleMapper.selectRuleByID(ruleId);
        EpAssert.notNull(riskEventRule, RcCode.PARAM_ERROR.msg("未找到规则"));
        EpAssert.state(RcConstants.AuditStatus.WAITING.code.equals(riskEventRule.getAuditStatus()), RcCode.PARAM_ERROR.msg("非待审核规则"));
        EpAssert.state(riskEventRule.getRuleCode().startsWith("T"), RcCode.PARAM_ERROR.msg("旧规则不允许审核"));

        User user = otherService.selectUserById(auditRecord.getOperId());
        EpAssert.notNull(user, RcCode.USER_NOT_EXIXT);

        RiskEventRule forUpdate = new RiskEventRule();
        forUpdate.setRuleId(ruleId);
        forUpdate.setRemarks(remarks);
        forUpdate.setChangePerson(user.getName());
        forUpdate.setUpdateTime(new Date());

        if (RcConstants.AuditStatus.SUCCESS.code.equals(auditResult)) {
            //新增
            if (RcConstants.AuditActionType.CREATE.code.equals(auditRecord.getActionType())) {
                EpAssert.runOrThrow(() -> riskEventRuleService.saveExcludeCustomer(riskEventRule), RcCode.SYSTEM_EXCEPTION.msg("保存商户列表出错"));
                AddOrUpdateRuleRequest addOrUpdateRuleRequest = JsonUtils.jsonToObj(auditRecord.getNewValue(), AddOrUpdateRuleRequest.class);
                if (!isDayRule(addOrUpdateRuleRequest.getSceneText())) {
                    addOrUpdateDrools(addOrUpdateRuleRequest, riskEventRule.getRuleCode());
                }
            }
            //修改
            if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())) {
                AddOrUpdateRuleRequest oldValue = JsonUtils.jsonToObj(auditRecord.getOldValue(), AddOrUpdateRuleRequest.class);
                AddOrUpdateRuleRequest newValue = JsonUtils.jsonToObj(auditRecord.getNewValue(), AddOrUpdateRuleRequest.class);

                forUpdate.setRuleCondition(auditRecord.getNewValue());

                if (!newValue.getRuleStatus().equals(oldValue.getRuleStatus())) { //启用停用
                    if (RcConstants.RiskEventRuleStatus.ON.code.equals(newValue.getRuleStatus())) { //启用
                        if (!isDayRule(newValue.getSceneText())) {
                            addOrUpdateDrools(newValue, riskEventRule.getRuleCode());
                        }
                    } else { //停用
                        if (!isDayRule(newValue.getSceneText())) {
                            deleteDrools(getDrlIdByRuleCode(riskEventRule.getRuleCode()));
                        }
                        forUpdate.setOffTime(Timex.now().to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
                    }
                    forUpdate.setRuleStatus(newValue.getRuleStatus());
                } else {
                    forUpdate.setRuleDesc(newValue.getRuleDesc());
                    forUpdate.setTriggerAction(newValue.getThenText());
                    forUpdate.setUniqueId(newValue.getUniqueId());
                    forUpdate.setFileName(newValue.getFileName());
                    forUpdate.setScene(newValue.getSceneText());
                    forUpdate.setRuleType(newValue.getRuleType());
                    forUpdate.setTriggerType(newValue.getTriggerType());
                    forUpdate.setRuleCondition(auditRecord.getNewValue());

                    if (!Objects.equals(newValue.getUniqueId(), oldValue.getUniqueId())) { //商户清单有变更
                        EpAssert.runOrThrow(() -> riskEventRuleService.saveExcludeCustomer(riskEventRule), RcCode.SYSTEM_EXCEPTION.msg("保存商户清单出错"));
                    }
                    if (!isDayRule(newValue.getSceneText())) {
                        if (!Objects.equals(newValue.getThenText(), oldValue.getThenText()) ||
                                !Objects.equals(newValue.getWhenList(), oldValue.getWhenList()) ||
                                !Objects.equals(newValue.getTriggerType(), oldValue.getTriggerType())) {
                            addOrUpdateDrools(newValue, riskEventRule.getRuleCode());
                        }
                    }
                }
            }

            forUpdate.setAuditStatus(RcConstants.AuditStatus.SUCCESS.code);
        } else { //审核不通过
            forUpdate.setAuditStatus(RcConstants.AuditStatus.FAIL.code);
        }

        riskEventRuleMapper.updateParamById(forUpdate);
        rcAuditRecordService.updateAuditRecordStatus(RcConstants.AuditTargetType.RISK_RULE.code, ruleId, auditResult, userId, remarks);
    }

    @Logable(businessTag = "addOrUpdateRule")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void addOrUpdateRule(AddOrUpdateRuleRequest request, Long userId) {
        String ruleTypeName = otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", request.getRuleType(), true);
        EpAssert.notBlank(ruleTypeName, RcCode.PARAM_ERROR.msg("规则类型不存在"));

        List<RcDroolsSceneWhen> sceneWhenList = sceneWhenMapper.selectAllBySceneTextOrderByPriority(request.getSceneText());
        EpAssert.notEmpty(sceneWhenList, RcCode.PARAM_ERROR.msg("应用场景不存在"));
        EpAssert.state(sceneWhenList.stream().map(RcDroolsSceneWhen::getLeftText).collect(Collectors.toSet()).containsAll(
                        request.getWhenList().stream().map(AddOrUpdateRuleRequest.When::getLeftText).collect(Collectors.toSet())),
                RcCode.PARAM_ERROR.msg("条件设置错误"));

        List<RcDroolsSceneThen> sceneThenList = sceneThenMapper.selectAllBySceneText(request.getSceneText());
        EpAssert.notEmpty(sceneThenList, RcCode.PARAM_ERROR.msg("应用场景不存在"));
        EpAssert.state(
                sceneThenList.stream().map(RcDroolsSceneThen::getThenText).anyMatch(s -> s.equals(request.getThenText())),
                RcCode.PARAM_ERROR.msg("措施不存在")
        );

        if (request.getTriggerTypeEnum() != AddOrUpdateRuleRequest.TriggerType.ALL) {
            EpAssert.notBlank(request.getUniqueId(), RcCode.PARAM_ERROR.msg("商户清单不能为空"));
            EpAssert.notBlank(request.getFileName(), RcCode.PARAM_ERROR.msg("商户清单不能为空"));
        }

        if (isDayRule(request.getSceneText())) {
            EpAssert.runOrThrow(() -> executeSql(request, true), RcCode.PARAM_ERROR.msg("日终规则参数出错"));
        }

        if (request.getRuleId() == null) {
            request.setRuleStatus(RcConstants.RiskEventRuleStatus.ON.code);
            long drlId = sequenceService.nextValue("RC_DROOLS");

            RiskEventRule riskEventRule = new RiskEventRule();
            riskEventRule.setRuleId(sequenceService.nextValue("RC_RISK_EVENT_RULE"));
            riskEventRule.setRuleCode(getRuleCodeByDrlId(drlId));
            riskEventRule.setOnTime(Timex.now().to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
            riskEventRule.setAuditStatus(RcConstants.AuditStatus.WAITING.code);
            riskEventRule.setRuleStatus(request.getRuleStatus());
            riskEventRule.setRuleDesc(request.getRuleDesc());
            riskEventRule.setUpdateTime(new Date());
            riskEventRule.setTriggerAction(request.getThenText());
            riskEventRule.setUniqueId(request.getUniqueId());
            riskEventRule.setFileName(request.getFileName());
            riskEventRule.setScene(request.getSceneText());
            riskEventRule.setRuleType(request.getRuleType());
            riskEventRule.setTriggerType(request.getTriggerType());

            request.setRuleId(riskEventRule.getRuleId());

            riskEventRule.setRuleCondition(JSON.toJSONString(request));

            rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code,
                    riskEventRule.getRuleId(), null, riskEventRule.getRuleCondition(), RcConstants.AuditActionType.CREATE.code, userId, null);
            riskEventRuleMapper.insertSelective(riskEventRule);
        } else {
            RiskEventRule riskEventRule = getRuleAllowUpdate(request.getRuleId());

            request.setRuleStatus(riskEventRule.getRuleStatus());

            saveUpdateAuditRecord(riskEventRule.getRuleId(), riskEventRule.getRuleCondition(), JSON.toJSONString(request), userId);
        }
    }

    @Logable(businessTag = "on")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void on(Long ruleId, Long userId) {
        RiskEventRule riskEventRule = getRuleAllowUpdate(ruleId);
        EpAssert.state(riskEventRule.getRuleStatus().equals(RcConstants.RiskEventRuleStatus.OFF.code), RcCode.PARAM_ERROR.msg("停用的规则才允许启用"));

        AddOrUpdateRuleRequest old = JsonUtils.jsonToObj(riskEventRule.getRuleCondition(), AddOrUpdateRuleRequest.class);
        old.setRuleStatus(RcConstants.RiskEventRuleStatus.ON.code); //仅仅修改规则状态为启用

        saveUpdateAuditRecord(riskEventRule.getRuleId(), riskEventRule.getRuleCondition(), JSON.toJSONString(old), userId);
    }

    @Logable(businessTag = "off")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void off(Long ruleId, Long userId) {
        RiskEventRule riskEventRule = getRuleAllowUpdate(ruleId);
        EpAssert.state(riskEventRule.getRuleStatus().equals(RcConstants.RiskEventRuleStatus.ON.code), RcCode.PARAM_ERROR.msg("启用的规则才允许停用"));

        AddOrUpdateRuleRequest old = JsonUtils.jsonToObj(riskEventRule.getRuleCondition(), AddOrUpdateRuleRequest.class);
        old.setRuleStatus(RcConstants.RiskEventRuleStatus.OFF.code); //仅仅修改规则状态为停用

        saveUpdateAuditRecord(riskEventRule.getRuleId(), riskEventRule.getRuleCondition(), JSON.toJSONString(old), userId);
    }


    public void addOrUpdateDrools(AddOrUpdateRuleRequest request, String ruleCode) {
        List<RcDroolsWhen> whenList = whenMapper.selectAllOrderById();
        List<RcDroolsThen> thenList = thenMapper.selectAllOrderById();

        List<String> whenDrlList = new ArrayList<>();
        String thenDrl = "";

        if (request.getTriggerTypeEnum() == AddOrUpdateRuleRequest.TriggerType.EXCLUDE) { //2：清单内不监控
            whenDrlList.add(String.format("!isCustomerExclude(businessTargetIds[\"005\"], \"%s\")", ruleCode));
        }
        if (request.getTriggerTypeEnum() == AddOrUpdateRuleRequest.TriggerType.INCLUDE) { //1：清单内监控
            whenDrlList.add(String.format("isCustomerExclude(businessTargetIds[\"005\"], \"%s\")", ruleCode));
        }

        List<RcDroolsSceneWhen> sceneWhenList = sceneWhenMapper.selectAllBySceneTextOrderByPriority(request.getSceneText());
        for (RcDroolsSceneWhen rcDroolsSceneWhen : sceneWhenList) {
            for (AddOrUpdateRuleRequest.When when : request.getWhenList()) {
                if (when.getLeftText().equals(rcDroolsSceneWhen.getLeftText())) {
                    whenDrlList.add(getWhenDrl(whenList, when));
                }
            }
        }

        for (RcDroolsThen rcDroolsThen : thenList) {
            if (rcDroolsThen.getThenText().equals(request.getThenText())) {
                thenDrl = rcDroolsThen.getThenDrl();
            }
        }

        String drlContent = String.format("package monitor;\n" +
                "import com.epaylinks.efps.rc.vo.TxsPayResultMsg\n" +
                "rule \"%s\"\n" +
                "    when\n" +
                "        $order:TxsPayResultMsg(ruleCode : \"%s\" && %s)\n" +
                "    then\n" +
                "        %s\n" +
                "end", ruleCode, ruleCode, Joiner.on("&&").join(whenDrlList), thenDrl);


        addOrUpdateDrools(getDrlIdByRuleCode(ruleCode), drlContent);
    }

    public boolean isDroolsRule(Long ruleId) {
        RiskEventRule riskEventRule = riskEventRuleMapper.selectRuleByID(ruleId);
        EpAssert.notNull(riskEventRule, RcCode.PARAM_ERROR.msg("未找到规则"));
        return riskEventRule.getRuleCode().startsWith("T");
    }

    public boolean isDayRule(String sceneText) {
        return sceneText != null && sceneText.startsWith("日终-");
    }

    public List<Map<String, Object>> executeSql(AddOrUpdateRuleRequest request, boolean isExplain) {
        Map<String, Object> sqlParams = new HashMap<>();

        List<RcDroolsWhen> whenList = whenMapper.selectAllOrderById();

        RcRuleSqlTemplate rcRuleSqlTemplate = sqlTemplateMapper.selectByPrimaryKey(request.getSceneText());
        EpAssert.notNull(rcRuleSqlTemplate, RcCode.PARAM_ERROR.msg("未找到规则SQL模板"));

        String sqlTemplate = rcRuleSqlTemplate.getSqlTemplate();

        Map<String, AddOrUpdateRuleRequest.When> whenMap = EpAssert.getOrThrow(() -> request.getWhenList().stream().collect(Collectors.toMap(AddOrUpdateRuleRequest.When::getLeftText, Function.identity())),
                RcCode.PARAM_ERROR.msg("条件重复"));
        Map<String, RcDroolsWhen> droolsWhenMap = EpAssert.getOrThrow(() -> whenList.stream().filter(w -> whenMap.containsKey(w.getLeftText())).collect(Collectors.toMap(RcDroolsWhen::getLeftText, Function.identity())),
                RcCode.PARAM_ERROR.msg("条件配置重复"));

        List<RcDroolsSceneWhen> sceneWhenList = sceneWhenMapper.selectAllBySceneTextOrderByPriority(request.getSceneText());
        for (RcDroolsSceneWhen rcDroolsSceneWhen : sceneWhenList) {
            AddOrUpdateRuleRequest.When w = whenMap.get(rcDroolsSceneWhen.getLeftText());
            if (rcDroolsSceneWhen.getRequired() == 0 && w == null) {
                sqlTemplate = sqlTemplate.replace("${" + rcDroolsSceneWhen.getLeftText() + "}", "");
                continue;
            }
            EpAssert.notNull(w, RcCode.PARAM_ERROR.msg("缺少必要条件" + rcDroolsSceneWhen.getLeftText()));
            RcDroolsWhen dw = droolsWhenMap.get(w.getLeftText());
            EpAssert.notNull(dw, RcCode.PARAM_ERROR.msg("条件操作错误" + w.getLeftText() + w.getOperatorText()));

            RcDroolsWhen.OperatorDrl od = JsonUtils.jsonToObj(dw.getOperatorDrl(), RcDroolsWhen.OperatorDrl.class);
            RcDroolsWhen.RightText rt = JsonUtils.jsonToObj(dw.getRightText(), RcDroolsWhen.RightText.class);
            RcDroolsWhen.RightDrl rd = JsonUtils.jsonToObj(dw.getRightDrl(), RcDroolsWhen.RightDrl.class);
            String leftSql = Optional.ofNullable(dw.getLeftDrl()).orElse("");
            String oprSql = Optional.ofNullable(od.getTextDrlMap().get(w.getOperatorText())).orElse("");

            if (rt.getType() == RcDroolsWhen.RightText.Type.SINGLE_TEXT) {
                String rightSql = Optional.ofNullable(rd.getPrefix()).orElse("") +
                        Optional.ofNullable(rd.getSuffix()).orElse("");

                String sql = leftSql + " " + oprSql + " " + rightSql;
                sqlTemplate = sqlTemplate.replace("${" + dw.getLeftText() + "}", sql);
                if (rt.getFormat() == RcDroolsWhen.RightText.Format.INTEGER) {
                    sqlParams.put(rd.getSqlParamName(), Integer.parseInt(w.getRightText()));
                } else {
                    sqlParams.put(rd.getSqlParamName(), w.getRightText());
                }
            }

            if (rt.getType() == RcDroolsWhen.RightText.Type.MULTI_CHOICE) {
                Set<String> s = JsonUtils.jsonToObj(w.getRightText(), new TypeReference<LinkedHashSet<String>>() {
                });
                s = s.stream().filter(t -> rd.getTextDrlMap().containsKey(t)).map(t -> rd.getTextDrlMap().get(t)).collect(Collectors.toSet());
                if (!s.isEmpty()) {
                    String rightSql = Optional.ofNullable(rd.getPrefix()).orElse("")
                            + Joiner.on(rd.getSeparator()).join(s)
                            + Optional.ofNullable(rd.getSuffix()).orElse("");
                    String sql = leftSql + " " + oprSql + " " + rightSql;
                    sqlTemplate = sqlTemplate.replace("${" + dw.getLeftText() + "}", sql);
                }
            }
        }

        log.info("sql:{}", sqlTemplate);
        log.info("sqlParams:{}", sqlParams);

        if (isExplain) {
            namedParameterJdbcTemplate.execute("EXPLAIN PLAN FOR " + sqlTemplate, sqlParams, ps -> {
                ps.execute();
                return null;
            });
            return null;
        } else {
            return namedParameterJdbcTemplate.queryForList(sqlTemplate, sqlParams);
        }
    }
}

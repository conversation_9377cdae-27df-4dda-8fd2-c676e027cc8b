package com.epaylinks.efps.rc.drools;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.bytes.Bytes;
import com.epaylinks.efps.rc.dao.RcDroolsMapper;
import com.epaylinks.efps.rc.domain.RcDrools;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 8:59
 */
@Service
@Slf4j
public class DroolsService {
    @Autowired
    private DroolsManager droolsManager;

    @Autowired
    private RcDroolsMapper rcDroolsMapper;

    @Scheduled(fixedDelay = 15 * 1000, initialDelay = 120 * 1000)
    public void updateAll() {
        List<RcDrools> rcDrools = rcDroolsMapper.selectAllOrderById();
        deleteAllNotIn(rcDrools);
        updateAllIn(rcDrools);
    }

    public void deleteAllNotIn(List<RcDrools> rcDrools) {
        List<String> drlFiles = new ArrayList<>(getDrlFiles()); //先获取，不然遍历的时候删除会报错
        for (String drlFile : drlFiles) {
            Optional<Long> ruleIdOption = droolsManager.getRuleIdFromFilename(drlFile);
            if (ruleIdOption.isPresent()) {
                if (rcDrools.stream().noneMatch(d -> d.getId().equals(ruleIdOption.get()))) {
                    delete(ruleIdOption.get());
                }
            }
        }
    }

    public void updateAllIn(List<RcDrools> rcDrools) {
        for (RcDrools rcDrool : rcDrools) {
            String filename = getFilename(rcDrool.getId());
            byte[] fileBytes = droolsManager.getMemoryFileSystem().read(filename);
            if (fileBytes == null) {
                log.info("新增规则");
                update(rcDrool.getId(), Bytes.fromBytes(rcDrool.getDrlContent()).toText());
            } else {
                if (!Arrays.equals(fileBytes, rcDrool.getDrlContent())) {
                    update(rcDrool.getId(), Bytes.fromBytes(rcDrool.getDrlContent()).toText());
                }
            }
        }
    }

    public void init() {
        List<RcDrools> rcDrools = rcDroolsMapper.selectAllOrderById();
        for (RcDrools rcDrool : rcDrools) {
            update(rcDrool.getId(), Bytes.fromBytes(rcDrool.getDrlContent()).toText());
        }
    }

    public Collection<String> getDrlFiles() {
        return droolsManager.getMemoryFileSystem().getFileNames();
    }

    @Logable(businessTag = "fireRule")
    public void fireRule(TxsPayResultMsg payResult) {
        droolsManager.fireRule("risk", payResult);
    }

    @Logable(businessTag = "fireRuleIgnoreException")
    public void fireRuleIgnoreException(TxsPayResultMsg payResult) {
        try {
            droolsManager.fireRule("risk", payResult);
        } catch (Exception e) {
            log.error("触发规则异常", e);
        }
    }

    private String getFilename(Long ruleId) {
        DroolsManager.DroolsRule droolsRule = new DroolsManager.DroolsRule();
        droolsRule.setRuleId(ruleId);
        droolsRule.setKieBaseName("risk");
        droolsRule.setKiePackageName("monitor");
        return droolsManager.getFilename(droolsRule);
    }

    public void delete(Long ruleId) {
        log.info("删除规则:[{}]", ruleId);
        DroolsManager.DroolsRule droolsRule = new DroolsManager.DroolsRule();
        droolsRule.setRuleId(ruleId);
        droolsRule.setKieBaseName("risk");
        droolsRule.setKiePackageName("monitor");
        droolsManager.deleteRule(droolsRule);
    }

    public void update(Long ruleId, String ruleContent) {
        log.info("更新规则:[{}][{}]", ruleId, ruleContent);
        DroolsManager.DroolsRule droolsRule = new DroolsManager.DroolsRule();
        droolsRule.setRuleId(ruleId);
        droolsRule.setRuleContent(ruleContent);
        droolsRule.setKieBaseName("risk");
        droolsRule.setKiePackageName("monitor");
        droolsManager.addOrUpdateRule(droolsRule);
    }
}

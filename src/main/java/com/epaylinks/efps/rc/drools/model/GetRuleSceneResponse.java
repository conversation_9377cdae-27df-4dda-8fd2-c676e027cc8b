package com.epaylinks.efps.rc.drools.model;

import com.epaylinks.efps.rc.domain.RcDroolsWhen;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:35
 */
@Data
public class GetRuleSceneResponse {
    @Data
    public static class When {
        @ApiModelProperty("条件左边文本")
        private String leftText;
        @ApiModelProperty("条件操作符列表")
        private List<String> operatorTextList;
        @ApiModelProperty("条件右边文本列表，为null时，则是输入值非下拉选择")
        private List<String> rightTextList;
        @ApiModelProperty("条件右边文本类型 MULTI_CHOICE:下拉多选 SINGLE_CHOICE:下拉单选 MULTI_TEXT:多个文本 SINGLE_TEXT:单个文本 TIME_PERIOD:时间段")
        private RcDroolsWhen.RightText.Type rightTextType;
        @ApiModelProperty("条件右边文本格式 INTEGER:整数 STRING:字符串")
        private RcDroolsWhen.RightText.Format rightTextFormat;
        @ApiModelProperty("条件优先级")
        private Long priority;
    }

    @Data
    public static class Scene {
        @ApiModelProperty("场景")
        private String scene;

        @ApiModelProperty("条件列表")
        private List<When> whenList;

        @ApiModelProperty("措施列表")
        private List<String> thenList;
    }

    @ApiModelProperty("规则场景列表")
    private List<Scene> sceneList;
}

package com.epaylinks.efps.rc.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcTxsOrderMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.pas.BkCardBin;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.service.impl.ExportWebsiteEffectivenessService;
import com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class KafkaConsumer {
    @Autowired
    private RcLimitService rcLimitService;
    @Autowired
    private RcTxsOrderMapper rcTxsOrderMapper;
    @Autowired
    private EarlyWarningService earlyWarningService;
    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;
    @Autowired
    private RiskEventMonitorService riskEventMonitorService;

    @Autowired
    private CommonLogger logger;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ExportWebsiteEffectivenessService exportWebsiteEffectivenessService;
    @Autowired
    private TempLimitService tempLimitService;
    @Autowired
    private PasClient pasClient;


    @Logable(businessTag = "txsPayResultConsume")
    @KafkaListener(topics = {"TXS_RC_RESULT"}, containerFactory = "customContainerFactory")
    public void txsPayResultConsume(ConsumerRecord<?, ?> record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());

        if (kafkaMessage.isPresent()) {

		   /* 该方式处理时，若message为字符串会报错  优化为后面的处理，兼容字符串与对象  2021.4.19
		    Object message = kafkaMessage.get();
	        List<TxsPayResultMsg> txsPayResultMsgs = JSON.parseArray((String)message, TxsPayResultMsg.class);
		   */
            String message = (String) kafkaMessage.get();
            Object obj = JSONObject.parse(message);
            List<TxsPayResultMsg> txsPayResultMsgs = JSON.parseArray(obj.toString(), TxsPayResultMsg.class);

            for (TxsPayResultMsg txsPayResultMsg : txsPayResultMsgs) {

                if (Constants.PayState.SUCCESS.code.equals(txsPayResultMsg.getPayState())
                        || Constants.PayState.FAIL.code.equals(txsPayResultMsg.getPayState())) {

                    String businessType = txsPayResultMsg.getBusinessType();
                    Map<String, String> businessTargetIds = txsPayResultMsg.getBusinessTargetIds();
                    Map<String, String> paramsMap = txsPayResultMsg.getIndexs();

                    try {
                        if (paramsMap != null) {
                            String cardType = paramsMap.get(RcConstants.RcIndex.CARD_TYPE.code);
                            String cardNo = businessTargetIds.get(RcConstants.BusinessTagerType.BANK_CARD.code);
                            if (cardType == null && cardNo != null) {
                                CommonOuterResponse<List<BkCardBin>> rs = pasClient.queryByCardNo(cardNo);
                                if (rs.getData() != null && !rs.getData().isEmpty()) {
                                    String cardTypeName = rs.getData().get(0).getCardType();
                                    if (cardTypeName.contains("借")) {
                                        paramsMap.put(RcConstants.RcIndex.CARD_TYPE.code, "D");
                                    } else if (cardTypeName.contains("准")) {
                                        paramsMap.put(RcConstants.RcIndex.CARD_TYPE.code, "S");
                                    } else if (cardTypeName.contains("贷")) {
                                        paramsMap.put(RcConstants.RcIndex.CARD_TYPE.code, "C");
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.printLog(e);
                    }

                    //先将交易数据入库
                    RcTxsOrder rcTxsOrder = new RcTxsOrder();
                    rcTxsOrder.setOutTradeNo(txsPayResultMsg.getOutTradeNo());
                    rcTxsOrder.setTransactionNo(txsPayResultMsg.getTransactionNo());
                    rcTxsOrder.setBusinessType(businessType);
                    rcTxsOrder.setIndexs(JSON.toJSONString(txsPayResultMsg.getIndexs()));
                    rcTxsOrder.setBusinessCode(txsPayResultMsg.getBusinessCode());
                    rcTxsOrder.setCustomerCode(businessTargetIds.get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code));
                    rcTxsOrder.setBusinessTargetIds(JSON.toJSONString(txsPayResultMsg.getBusinessTargetIds()));
                    rcTxsOrder.setCreateTime(new Date());
                    rcTxsOrder.setPayState(txsPayResultMsg.getPayState());
                    try {
                        rcTxsOrder.setAmount(Long.parseLong(txsPayResultMsg.getIndexs().get("amount")));
                    } catch (NumberFormatException ne) {
                    }
                    try {
                        rcTxsOrderMapper.insert(rcTxsOrder);
                    } catch (DuplicateKeyException e) {
                        //此处防止处理重复消息
                        continue;
                    }

                    // 风控对象增加商户证件号  20210816
                    String customerCode = businessTargetIds.get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);

                    if (!Constants.rcBusinessType.ACCT_QUOTA.code.equals(businessType)) {

                        // 风控计算参数组装
                        paramsMap.put("businessCode", txsPayResultMsg.getBusinessCode());
                        paramsMap.put("customerCode", businessTargetIds.get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code));
                        paramsMap.put("payState", txsPayResultMsg.getPayState());
                        paramsMap.put("payMethod", txsPayResultMsg.getPayMethod());

                        String txnIdCard = businessTargetIds.get(RcConstants.BusinessTagerType.IDENTITY_CARD.code);

                        if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)) {
                            tempLimitService.calculateValue(paramsMap);
                        }

                        CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
                        if (customerInfo != null && customerInfo.getBusinessLicenseNo() != null // 小微不需要校验营业执照号
                                && !Constants.CustomerType.MICRO.code.toString().equals(String.valueOf(customerInfo.getCustomerType()))) {
                            businessTargetIds.put(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code, customerInfo.getBusinessLicenseNo());
                        }
                        if (customerInfo != null && customerInfo.getLeaPersoniDentificationNo() != null
                                && Constants.CertificateType.ID.code.toString().equals(String.valueOf(customerInfo.getLeaPersoniDentificationType()))) {
                            businessTargetIds.put(RcConstants.BusinessTagerType.IDENTITY_CARD.code, customerInfo.getLeaPersoniDentificationNo());
                        }
                        //个人账户转换为个人风控
                        if (customerInfo != null && Constants.customerCategory.EFPS_PERSON.code.equals(customerInfo.getCustomerCategory())) {
                            businessTargetIds.remove(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
                            businessTargetIds.put(RcConstants.BusinessTagerType.PERSON.code, customerCode);
                        }


                        Set<String> keySet = businessTargetIds.keySet();
                        for (String key : keySet) {
                            String businessTargetId = businessTargetIds.get(key);
                            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(key)) {// 迭代商户的情况增加银行卡号，同卡代付限额处理
                                paramsMap.put("bankCardNo", businessTargetIds.get(RcConstants.BusinessTagerType.BANK_CARD.code));
                                paramsMap.put("identityCard", txnIdCard);
                            } else if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(key)) {// 迭代身份证号
                                paramsMap.put("identityCard", txnIdCard);
                            } else if (RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(key)) {// 迭代营业执照号
                                paramsMap.put("identityCard", txnIdCard);
                            } else {
                                paramsMap.remove("bankCardNo");
                                paramsMap.remove("identityCard");
                            }
                            List<RcLimit> rcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(businessType, key, businessTargetId);
                            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(key) && customerInfo != null) {
                                if (!Constants.BusinessCode.BFZ.code.equals(txsPayResultMsg.getBusinessCode())) { //不是分账才入平台商
                                    //商户本身是平台商
                                    if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) {
                                        List<RcLimit> platRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(businessType,
                                                RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, customerCode);
                                        rcLimits.addAll(platRcLimits);
                                    } else { //商户有平台商
                                        if (customerInfo.getPlatCustomerCode() != null) { //商户的平台商加入风控
                                            List<RcLimit> platRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(businessType,
                                                    RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, customerInfo.getPlatCustomerCode());
                                            rcLimits.addAll(platRcLimits);
                                        }
                                    }
                                }
                            }

                            for (RcLimit rcLimit : rcLimits) {
                                RcIndexAddValue rcIndexAddValue = (RcIndexAddValue) SpringContextUtils.getBean(rcLimit.getDefineCode());
                                rcIndexAddValue.calculateValue(rcLimit, paramsMap);
                            }
                        }
                    }

                    //通用预警处理 add by www
                    riskEventMonitorService.asyncMonitor(rcTxsOrder);
                    riskEventMonitorService.asyncDroolsMonitor(txsPayResultMsg);

                    // 预警处理 2020.11.26
                    if (Constants.PayState.SUCCESS.code.equals(txsPayResultMsg.getPayState())) {
                        earlyWarningService.checkTxsOrderAmountWarning(customerCode, businessType,
                                txsPayResultMsg.getBusinessCode(), rcTxsOrder.getAmount());
                    }
                }
            }
        }
    }

    @Logable(businessTag = "Consumer.webCheck")
    @KafkaListener(topics = {"web_valid_check"})
    public void consumeWebCheck(ConsumerRecord<?, ?> record) {
        try {
            logger.printLog("web_valid_check网站核验商户列表开始消费");
            Optional<?> kafkaMessage = Optional.ofNullable(record.value());
            if (kafkaMessage.isPresent()) {
                String message = (String) kafkaMessage.get();
                Object obj = JSONObject.parse(message);
                JSONObject jsonObject = JSON.parseObject(obj.toString());
                String batchNo = (String) jsonObject.get("batchNo");
                List<String> webValidList = redisTemplate.opsForValue().get("webValid" + batchNo) == null ? new ArrayList<>() : (List<String>) redisTemplate.opsForValue().get("webValid" + batchNo);
                logger.printLog(batchNo + "验证网站的商户数量：" + webValidList.size());
                if (webValidList.size() == 0) {
                    return;
                }
                exportWebsiteEffectivenessService.sendCheckList(webValidList);
            }
        } catch (Exception e) {
            logger.printMessage("web_valid_check消费异常：" + e.getMessage());
            logger.printLog(e);
        }
    }

}

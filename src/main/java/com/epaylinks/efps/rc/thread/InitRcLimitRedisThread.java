package com.epaylinks.efps.rc.thread;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.map.HashedMap;

import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.RcLimitMapper;
import com.epaylinks.efps.rc.domain.RcLimit;

/**
 * 风控指标缓存初始化线程
 * 
 * <AUTHOR>
 * @date 2021-06-09
 *
 */
public class InitRcLimitRedisThread implements Runnable {

    private int startNum = 0; // 开始行数
    private int endNum = 0; // 读取记录数

    public InitRcLimitRedisThread(int startNum, int endNum) {
        super();
        this.startNum = startNum;
        this.endNum = endNum;
    }

    @Override
    public void run() {

        try {
            LogService logService = SpringContextUtils.getBean(LogService.class);
            logService.printLog("====== InitRcLimitRedisThread : endNum: " + endNum + " running ... =======");

            RcLimitMapper rcLimitMapper = SpringContextUtils.getBean(RcLimitMapper.class);
            MyRedisTemplateService myRedisTemplate = SpringContextUtils.getBean(MyRedisTemplateService.class);
            MyRedisTemplate redisTemplate = myRedisTemplate.getMyRedisTemplate();
            
            // 风控指标
            List<RcLimit> rcLimitList = rcLimitMapper.pageLimit(startNum, endNum);
    
            for (RcLimit rcLimit : rcLimitList) {
    
                String redisKey = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
                if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型/终端维度扩展
                    redisKey = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
                }
    
                Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(redisKey, rcLimit.getBusinnesType());
                if (map == null) {
                    // 说明这个商户已经存在某个风控指标
                    redisTemplate.expire(redisKey, -1, TimeUnit.SECONDS);
                    map = new HashedMap();
                }
                map.put(rcLimit.getDefineCode(), rcLimit);
    
                redisTemplate.opsForHash().put(redisKey, rcLimit.getBusinnesType(), map);
            }
            
            logService.printLog("====== InitRcLimitRedisThread : endNum: " + endNum + " finished .======= ");

        } catch ( Exception e) {
            e.printStackTrace();
        }

    }

}

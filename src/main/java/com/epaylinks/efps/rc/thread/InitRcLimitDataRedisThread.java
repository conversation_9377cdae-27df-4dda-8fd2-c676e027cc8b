package com.epaylinks.efps.rc.thread;

import java.util.List;

import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;

public class InitRcLimitDataRedisThread implements Runnable {

    private int startNum = 0; // 开始行数
    private int endNum = 0; // 读取记录数

    public InitRcLimitDataRedisThread(int startNum, int endNum) {
        super();
        this.startNum = startNum;
        this.endNum = endNum;
    }

    @Override
    public void run() {

        try {
            LogService logService = SpringContextUtils.getBean(LogService.class);
            logService.printLog("====== InitRcLimitDataRedisThread : endNum: " + endNum + " running ... =======");
            
            RcLimitDataMapper rcLimitDataMapper = SpringContextUtils.getBean(RcLimitDataMapper.class);
            RcCalculteBasic rcCalculteBasic = SpringContextUtils.getBean(RcCalculteBasic.class);
            MyRedisTemplateService myRedisTemplate = SpringContextUtils.getBean(MyRedisTemplateService.class);
            MyRedisTemplate redisTemplate = myRedisTemplate.getMyRedisTemplate();
            
            // 风控限额数据
            List<RcLimitData> rcLimitDataList = rcLimitDataMapper.pageInitRedisData(startNum, endNum);
    
            for (RcLimitData rcLimitData : rcLimitDataList) {
                
                int index = rcLimitData.getBusinesstargetid().indexOf("_");
                if (index >= 0) {
                    
                    String args[] = rcLimitData.getBusinesstargetid().split("_");
                    if (args[0].length() < 4) {  // 前缀是类型
                        String businessTargetType = args[0];        
                        String key = rcCalculteBasic.getKey(rcLimitData.getBusinesstargetid(), rcLimitData.getDefinecode(), rcLimitData.getDatetime());
                        redisTemplate.opsForValue().set(key, Long.parseLong(rcLimitData.getValue()));
                        
                    } else { // 否则为卡限额指标
                        String targetId = args[0];
                        String bankCardNo = args[1];
                        String hashKey = rcCalculteBasic.getKey(targetId , rcLimitData.getDefinecode(), rcLimitData.getDatetime());
                        redisTemplate.opsForHash().put(hashKey, bankCardNo, Long.parseLong(rcLimitData.getValue()));
                    }
               
                } else {
                    String key = rcCalculteBasic.getKey(rcLimitData.getBusinesstargetid(), rcLimitData.getDefinecode(), rcLimitData.getDatetime());
                    redisTemplate.opsForValue().set(key,  Long.parseLong(rcLimitData.getValue()));
                }
            }
            
            logService.printLog("====== InitRcLimitDataRedisThread: endNum: " + endNum + " finished .======= ");

        } catch ( Exception e) {
            e.printStackTrace();
        }

    }
    
}

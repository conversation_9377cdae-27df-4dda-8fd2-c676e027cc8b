package com.epaylinks.efps.rc.thread;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.BwListMapper;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.BwList.BwType;

/**
 * 黑白名单缓存初始化线程
 * <AUTHOR>
 * @date 2021-06-09
 *
 */
public class InitBwListRedisThread implements Runnable {

    private int startNum = 0; // 开始行数
    private int endNum = 0; // 读取记录数
    private BwType bwType = BwList.BwType.BLACK; // 读取记录数

    public InitBwListRedisThread(int startNum, int endNum, BwType bwType) {
        super();
        this.startNum = startNum;
        this.endNum = endNum;
        this.bwType = bwType;
    }

    @Override
    public void run() {

        try {
            LogService logService = SpringContextUtils.getBean(LogService.class);
            logService.printLog("====== InitBwListRedisThread : endNum: " + endNum + " running ... =======");

            BwListMapper bwListMapper = SpringContextUtils.getBean(BwListMapper.class);
            MyRedisTemplateService myRedisTemplate = SpringContextUtils.getBean(MyRedisTemplateService.class);
            MyRedisTemplate redisTemplate = myRedisTemplate.getMyRedisTemplate();
            
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("bwType", bwType.code);
            paramMap.put("startNum", startNum);
            paramMap.put("endNum", endNum);
            // 查询黑名单
            List<BwList> balckList = bwListMapper.queryByPage(paramMap);
    
            // 存到redis中去
            if (balckList != null && !balckList.isEmpty()) {
                for (BwList bwList : balckList) {
                    redisTemplate.opsForHash().put(bwType.message, bwList.getBusinessTagerId(), bwList);
                }
            }
            
            logService.printLog("====== InitBwListRedisThread : endNum: " + endNum + " finished .======= ");

        } catch ( Exception e) {
            e.printStackTrace();
        }
    }

}

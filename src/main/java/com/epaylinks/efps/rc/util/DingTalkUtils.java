package com.epaylinks.efps.rc.util;

import java.util.HashMap;
import java.util.Map;

import com.epaylinks.efps.common.net.http.RestClient;

/**
 * 钉钉消息发送工具类
 * <AUTHOR>
 * @date 2020-11-25
 *
 */
public class DingTalkUtils {
    
    public static String sendMessage(String postUrl, String token, String message) {
        
        RestClient restClient =  RestClient
                .builder()
                .poolMaxTotal(5)
                .poolMaxPerRoute(5)
                .build();
        Map<String, Object> sendObj = new HashMap<String, Object>();
        sendObj.put("msgtype","text");
        Map<String, String> msgObj = new HashMap<String, String>();
        msgObj.put("content", message);
        sendObj.put("text", msgObj);

        // 请求报文：{"msgtype": "text","text": {"content": "交易告警, 是不一样的烟火"}}
        String url = postUrl + "?access_token=" + token;
        String result =  restClient.post(url).body(sendObj).header("contentType", "application/json").exchange(String.class);

        return result;
    }
    
    public static void main(String[] args) {
        
        sendMessage("https://oapi.dingtalk.com/robot/send", "7c27a3b944c5c56f2afb65f19ef0dbe96d039408d0adc4f1cb7a230e5bf91006", "RC冻结测试"); 
    }
}

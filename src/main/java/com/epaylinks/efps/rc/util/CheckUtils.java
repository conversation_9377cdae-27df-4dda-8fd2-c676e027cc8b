package com.epaylinks.efps.rc.util;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.rc.command.RcCode;

public class CheckUtils {
    public static void paramCheckLength(String param,int length,String desc) {
        if (getByteLength(param) > length) {
            throw new AppException(RcCode.DATA_TOO_LONG.code,RcCode.DATA_TOO_LONG.message + "：" + desc);
        }
    }

    public static Integer getByteLength(String param) {
        try {
            if (null == param) {
                return 0;
            }
            return param.getBytes("GBK").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 999;
    }
}

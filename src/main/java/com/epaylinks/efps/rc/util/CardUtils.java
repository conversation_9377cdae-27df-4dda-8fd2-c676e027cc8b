package com.epaylinks.efps.rc.util;

import org.apache.commons.lang3.StringUtils;

public class CardUtils {


    /**
     * 获取屏蔽身份证号
     * @param creditCardNo
     * @return
     */
    public static String getHiddenCreditCardNo(String creditCardNo) {
        return creditCardNo != null?creditCardNo.replaceAll("^(.{4}).+(.{4})$", "$1******$2"):creditCardNo;
    }

    /**
     * 获取屏蔽银行卡号
     * @param bankCardNo
     * @return
     */
    public static String getHiddenBankCardNo(String bankCardNo) {
        return bankCardNo!=null?bankCardNo.replaceAll("^(.{6}).+(.{4})$", "$1******$2"):bankCardNo;
    }

    /**
     * 获取屏蔽姓名
     * @param name
     * @return
     */
    public static String getHiddenCardOwner(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() == 2) {
            name = name.replaceFirst(name.substring(1), "*");
        }
        if (name.length() > 2) {
            name = name.replaceAll("^(.{1}).+(.{1})$", "$1" + createAsterisk(name.length() - 2) + "$2");
        }
        return name;

    }

    /**
     * 获取屏蔽手机号
     * @param mobilePhone
     * @return
     */
    public static String getHiddenMobilePhone(String mobilePhone) {
        return mobilePhone!=null?mobilePhone.replaceAll("^(.{3}).+(.{4})$", "$1****$2"):mobilePhone;
    }

    //生成多个*号
    public static String createAsterisk(int length) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length && i < 6; i++) {
            stringBuffer.append("*");
        }
        return stringBuffer.toString();
    }

    /**
     * 返回带前缀的cardNoHash值，如$a001$1860b263233a18df0435248b7da58fcf0673f93dd874fcd5c6f800a895302ee1
     * @param cardNo
     * @return
     */
    public static String getCardNoHash(String cardNo)  {
        return "$a001$" + Sm3Util.sm3Hex(cardNo);
    }

    /**
     * 返回有空判断的哈希方法
     * @param cardNo
     * @return
     */
    public static String getHash(String cardNo)  {
        if(StringUtils.isBlank(cardNo)){
            return cardNo;
        }
        return "$a001$" + Sm3Util.sm3Hex(cardNo);
    }
}

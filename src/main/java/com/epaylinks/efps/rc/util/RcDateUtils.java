package com.epaylinks.efps.rc.util;

import java.util.Calendar;
import java.util.Date;

import com.epaylinks.efps.common.util.DateUtils;

public class RcDateUtils {

	private static String DAY_FOMAT = "yyyyMMdd";
	private static String MONTH_FOMAT = "yyyyMM";
	private static String YEAR_FOMAT = "yyyy";

	/**
	 * @param amount 天数，正数往后，负数往前
	 * @return
	 */
	public static String addDay(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, amount);
        Date lastDay = calendar.getTime();
        return getDayString(lastDay);
    }
	   
	public static String getLastDay() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		Date lastDay = calendar.getTime();
		return getDayString(lastDay);
	}

	public static String getCurrentDay() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		Date now = calendar.getTime();
		return getDayString(now);
	}

	public static String getCurrentMonth() {
		return getMonthString(new Date());
	}

	public static String getCurrentYear() {
		return getYearString(new Date());
	}

	
	/**
	 * 获取当前日期日格式
	 */
	public static String getDayString(Date date) {
		return DateUtils.formatDate(date, DAY_FOMAT);
	}

	/**
	 * 获取当前日期月格式
	 */
	public static String getMonthString(Date date) {
		return DateUtils.formatDate(date, MONTH_FOMAT);
	}

	/**
	 * 获取当前日期年格式
	 */
	public static String getYearString(Date date) {
		return DateUtils.formatDate(date, YEAR_FOMAT);
	}
	
	public static void main(String[] args) {
        System.out.println(addDay(0));
        System.out.println(addDay(1));
        System.out.println(addDay(-1));
        System.out.println(addDay(-2));
        System.out.println(addDay(-3));
    }

}


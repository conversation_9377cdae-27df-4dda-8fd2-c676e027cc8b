package com.epaylinks.efps.rc.util;

import java.io.*;
import java.util.List;

public class RcCvsUtils {
	
	  /** CSV文件列分隔符 */
    private static final String CSV_COLUMN_SEPARATOR = ",";

    /** CSV文件列分隔符 */
    private static final String CSV_RN = "\r\n";
    /** 制表符 */
    private static final String CSV_TAB = "\t";
    
    private static String FILE_ENCODING = "GBK";

    /**
     * 批量处理结果
     * @param columnNames
     * @param dataList	顺序排列数据
     * @param file
     * @return
     */
	public static boolean doExport(String columnNames , List< List<String>> dataList, File file) {
		return doExport(columnNames, dataList, file, null);
	}

    /**
     * 批量处理结果
     * @param columnNames
     * @param dataList	顺序排列数据
     * @param file
     * @param oldContent 在此内容后拼接
     * @return
     */
	public static boolean doExport(String columnNames , List< List<String>> dataList, File file, String oldContent) {
		
        try {
    		OutputStream os = new FileOutputStream(file);
            StringBuffer buf = new StringBuffer();
            String[] colNamesArr = null;
            colNamesArr = columnNames.split(",");
            // 输出列头
    		buf.append(CSV_TAB);
            for (String aColNamesArr : colNamesArr) {
                buf.append(aColNamesArr).append(CSV_COLUMN_SEPARATOR);
            }
            buf.append(CSV_RN);
            if (dataList != null) { // 输出数据
                for (List<String> list : dataList) {
                	if(list != null) {
                		buf.append(CSV_TAB);
	                    for (String data : list) {
	                        buf.append(data).append(CSV_COLUMN_SEPARATOR);
	                    }
	                    buf.append(CSV_RN);
                	}
                }
            }
            buf.append(CSV_RN);
            // 写出响应
            String content =   buf.toString().replace(",", "," + CSV_TAB); // 避免excel打开转出科学计数法
            if(oldContent != null) {
            	content = oldContent.concat(content);
            }
            os.write(content.getBytes(FILE_ENCODING));
            os.flush();
            return true;
        } catch (Exception e) {
        	e.printStackTrace();
        }
        return false;
    }
	
	/***
	 * 读取文件内容返回
	 * @param file
	 * @return
	 */
	public static String readFile(File file) {

		BufferedReader reader = null;
		String line = null;
		StringBuffer contentSb = new StringBuffer();
		try {
			try {
				reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), FILE_ENCODING));
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
				return "";
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			return "";
		}
		try {
			while ((line = reader.readLine()) != null) {
				contentSb.append(line);
				contentSb.append(CSV_RN);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return contentSb.toString();

      
	}
	
	
	
}

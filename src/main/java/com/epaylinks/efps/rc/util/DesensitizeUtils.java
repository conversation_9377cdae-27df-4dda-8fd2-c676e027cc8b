package com.epaylinks.efps.rc.util;

import com.epaylinks.efps.common.util.MD5Utils;
import org.apache.commons.lang3.StringUtils;
import java.io.UnsupportedEncodingException;

public class DesensitizeUtils {

	/**
	 * 去除字符串空格
	 * @param input
	 * @return
	 */
	public static String getTrim(String input){
		if(input!=null){
			return input.trim();
		}
		return input;
	}
	
	/**
	 * 获取屏蔽身份证号
	 * @param creditCardNo
	 * @return
	 */
	public static String getHiddenCreditCardNo(String creditCardNo) {
		return creditCardNo != null?creditCardNo.replaceAll("^(.{4}).+(.{4})$", "$1******$2"):creditCardNo;
	}
	
	/**
	 * 获取屏蔽银行卡号
	 * @param bankCardNo
	 * @return
	 */
	public static String getHiddenBankCardNo(String bankCardNo) {
		return bankCardNo!=null?bankCardNo.replaceAll("^(.{6}).+(.{4})$", "$1******$2"):bankCardNo;
	}
	
	/**
	 * 获取屏蔽手机号
	 * @param mobilePhone
	 * @return
	 */
	public static String getHiddenMobilePhone(String mobilePhone) {
		return mobilePhone!=null?mobilePhone.replaceAll("^(.{3}).+(.{4})$", "$1****$2"):mobilePhone;
	}
	
	/**
	 * 获取银行卡号hash值
	 * @param bankCardNo
	 * @return
	 */
	public static String getBankCardNoHashCode(String bankCardNo) {
		return bankCardNo!=null?MD5Utils.getMD5(bankCardNo):bankCardNo;	
	}
	
	/**
	 * 获取屏蔽姓名
	 * @param name
	 * @return
	 */
	public static String getHiddenCardOwner(String name) {
	    if (StringUtils.isBlank(name)) {
	        return name;
	    }
		if (name.length() == 2) {
			name = name.replaceFirst(name.substring(1), "*");
		}
		if (name.length() > 2) {
			name = name.replaceAll("^(.{1}).+(.{1})$", "$1" + createAsterisk(name.length() - 2) + "$2");
		}
	    return name;

	}

	//生成多个*号
	public static String createAsterisk(int length) {
	    StringBuffer stringBuffer = new StringBuffer();
	    for (int i = 0; i < length && i < 6; i++) {
	        stringBuffer.append("*");
	    }
	    return stringBuffer.toString();
	}

	/**
	 * 按字节长度截取中英混合的字符串
	 * @param text 源字符串
	 * @param length 字节长度
	 * @param encode 字符编码, GBK, UTF-8
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String subStringByByte(String text, int length, String encode)
			throws UnsupportedEncodingException {
		if (text == null) {
			return null;
		}
		StringBuilder sb = new StringBuilder();
		int currentLength = 0;
		for (char c : text.toCharArray()) {
			currentLength += String.valueOf(c).getBytes(encode).length;
			if (currentLength <= length) {
				sb.append(c);
			} else {
				break;
			}
		}
		return sb.toString();
	}
}


package com.epaylinks.efps.rc.util;

import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import java.nio.charset.Charset;

public class Sm3Util {

    private static final String encoding = "UTF-8";

    /**
     * 计算SM3摘要值
     *
     * @param srcData 原文
     * @return 摘要值，对于SM3算法来说是32字节
     */
    public static byte[] hash(byte[] srcData) {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    /**
     * 16进制便利方法
     * @param data
     * @return
     */
    public static String sm3Hex(String data) {
        byte[] sign = hash(data.getBytes(Charset.forName(encoding)));
        return ByteUtils.toHexString(sign);
    }
}

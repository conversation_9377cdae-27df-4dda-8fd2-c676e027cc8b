package com.epaylinks.efps.rc.util;

import com.epaylinks.efps.rc.service.impl.GamblingPyramidServiceImpl;

import java.net.HttpURLConnection;
import java.net.URL;

public class  GetProtocol {
    private final  static String HTTP = "http://";
    private final  static  String HTTPS = "https://";
    private String newurl;

    //判断协议 能连接上则协议正确
    public String getProtocol(String url) {
        newurl = clearUrl(url);
        url = HTTP + newurl;
        if (exists(url)) {
            return url;
        } else {
            url = HTTPS + newurl;
            if (exists(url)) {
                return url;
            } else {
                return null;
            }
        }
    }

    //清除URL里多余的符号
    private String clearUrl(String url) {
        if (url.contains(HTTP)) {
            url = url.substring(url.lastIndexOf(HTTP) + HTTP.length());
            for (int i = 0; i < url.length(); i++) {
                if (url.charAt(i) == '/' || url.charAt(i) == '.'
                        || url.charAt(i) == '\\') {
                } else {
                    url = url.substring(i);
                    return url;
                }
            }
        } else if (url.contains(HTTPS)) {
            url = url.substring(url.lastIndexOf(HTTPS) + HTTPS.length());
            for (int i = 0; i < url.length(); i++) {
                if (url.charAt(i) == '/' || url.charAt(i) == '.'
                        || url.charAt(i) == '\\') {
                } else {
                    url = url.substring(i);
                    return url;
                }
            }
        } else {
            for (int i = 0; i < url.length(); i++) {
                if (url.charAt(i) == '/' || url.charAt(i) == '.'
                        || url.charAt(i) == '\\') {
                } else {
                    url = url.substring(i);
                    return url;
                }
            }
        }
        return url;
    }
    //是否能连接上
    private static boolean exists(String url) {
        try {
            HttpURLConnection con = (HttpURLConnection) new URL(url).openConnection();
            con.setConnectTimeout(5000);
            con.setReadTimeout(5000);
            if (url.startsWith("https:")) {
                GamblingPyramidServiceImpl.ignoreCertificateValidation(con);
            }
            return (con.getResponseCode() == 200 || con.getResponseCode() == 403);
        } catch (Exception e) {
            return false;
        }
    }


    public static String getUrl(String url) {
        String newUrl = HTTPS + url;
//        System.out.println(newUrl);
        if (exists(newUrl)) {
            return newUrl;
        } else {
            newUrl = HTTP + url;
            if (exists(newUrl)) {
                return newUrl;
            } else {
                return null;
            }
        }
    }

    public static void main(String[] args) {
        String result = getUrl("www.zliancloud.com");
        System.out.println("result:" + result);
    }
}

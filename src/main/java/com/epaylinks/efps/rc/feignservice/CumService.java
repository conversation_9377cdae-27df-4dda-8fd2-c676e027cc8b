package com.epaylinks.efps.rc.feignservice;

import java.util.List;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.epaylinks.efps.rc.feignservice.dto.CustomerInfo;

@FeignClient("CUM")
public interface CumService {

	@RequestMapping(value = "/Customer/queryAllCustomerInfo" , method = RequestMethod.POST)
	public List<CustomerInfo> queryAllCustomerInfo();
}

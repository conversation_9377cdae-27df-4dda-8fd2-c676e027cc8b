package com.epaylinks.efps.rc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 钉钉通知接口相关配置
 * <AUTHOR>
 * @date 2020-11-25
 *
 */
@Data
@Component
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {
    
    private boolean sendFlag; // 发送标识，“1”发送
    
    private String url; // 请求接口地址
    
    private String token; // 推送通知token
    
}

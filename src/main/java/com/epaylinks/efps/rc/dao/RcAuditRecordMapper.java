package com.epaylinks.efps.rc.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.rc.domain.RcAuditRecord;

@Mapper
public interface RcAuditRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcAuditRecord record);

    int insertSelective(RcAuditRecord record);

    RcAuditRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcAuditRecord record);

    int updateByPrimaryKey(RcAuditRecord record);
    
    RcAuditRecord queryByTargetTypeAndId(@Param("targetType") String targetType, @Param("targetId") Long targetId);

    List<Long> queryListByTargetTypeAndId(@Param("targetType") String targetType, @Param("targetId") Long targetId,@Param("id") Long id);

    RcAuditRecord queryHistoryAuditRecord(@Param("targetType") String targetType, @Param("targetId") Long targetId,@Param("auditTime") String auditTime,@Param("field") String field);

    int countByParam(Map<String, Object> paramMap);

    List<RcAuditRecord> listByParam(Map<String, Object> paramMap);

}
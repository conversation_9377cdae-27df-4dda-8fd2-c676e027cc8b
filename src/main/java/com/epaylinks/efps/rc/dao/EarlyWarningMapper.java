package com.epaylinks.efps.rc.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 预警规则监控查询
 * <AUTHOR>
 * @date 2020-11-25
 *
 */
@Mapper
public interface EarlyWarningMapper {

    /**
     * 查询当日代付次数
     * @param businessTargetId
     * @return
     */
    int countWithDraw(@Param("businessTargetId") String businessTargetId);
    
    /**
     * 查询当日命中限额次数
     * @param businessTargetId
     * @return
     */
    int countAmountLimit(@Param("businessTargetId") String businessTargetId);
    
    /**
     * 查询当日命中黑名单次数
     * @param businessTargetId
     * @return
     */
    int countBlackListLimit(@Param("businessTargetId") String businessTargetId);

    /**
     * 查询当日交易数量
     * @param businessTargetId
     * @param businessType
     * @return
     */
    int countTxsByBusinessType(@Param("businessTargetId") String businessTargetId, @Param("businessType") String businessType);
    
    /**
     * 查询X小时内登录次数 
     * @param businessTargetId
     * @param hours
     * @return
     */
    int countLoginTimes(@Param("businessTargetId") String businessTargetId, @Param("hours") Integer hours);
    
    /**
     * 统计银行卡收单当日交易金额（分）
     * @param businessTargetId
     * @return
     */
//    Long sumPOSUnionAmount(@Param("businessTargetId") String businessTargetId);
    

    /**
     * 统计商户网络支付（除收单外）日交易量（分）
     * @param businessTargetId
     * @return
     */
//    Long sumOnlineTradingAmount(@Param("businessTargetId") String businessTargetId);
    
    
    /**
     * 统计查询前3天（非收单）交易金额（不包含当天，即 -1 , -2 , -3 三天） （分）
     * @param businessTargetId
     * @return
     */
//    Long sumLastThreeDayOnlineTradingAmount(@Param("businessTargetId") String businessTargetId);
    
    
    /**
     * 统计查询前3天（收单）交易金额（不包含当天，即 -1 , -2 , -3 三天） （分）
     * @param businessTargetId
     * @return
     */
//    Long sumLastThreeDayPOSUnionAmount(@Param("businessTargetId") String businessTargetId);

    
}

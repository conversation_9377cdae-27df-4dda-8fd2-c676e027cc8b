package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcAudLimit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcAudLimitMapper {
    int deleteByPrimaryKey(Long limitId);

    int insert(RcAudLimit record);

    int insertSelective(RcAudLimit record);

    RcAudLimit selectByPrimaryKey(Long limitId);

    int updateByPrimaryKeySelective(RcAudLimit record);

    int updateByPrimaryKey(RcAudLimit record);

    List<Map<String,String>> selectList(@Param("audId") Long audId,@Param("valueName")String valueName);

    RcAudLimit queryAudLimit(@Param("audId")Long audId, @Param("defineCode")String defineCode);
    
}
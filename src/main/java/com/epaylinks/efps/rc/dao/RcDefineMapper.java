package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcDefine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcDefineMapper {
    int deleteByPrimaryKey(Long defineId);

    int insert(RcDefine record);

    int insertSelective(RcDefine record);

    RcDefine selectByPrimaryKey(Long defineId);

    int updateByPrimaryKeySelective(RcDefine record);

    int updateByPrimaryKey(RcDefine record);

    /**
     * 根据风控指标定义编码查找
     * @param code
     * @return
     */
    RcDefine selectByCode(String code);

    int totalDefineQuery(@Param("defineIdentifier")String defineIdentifier,@Param("bigId")Long bigId,@Param("smallId")Long smallId,@Param("startTime")String startTime,@Param("endTime")String endTime);

    List<Map<String,Object>> pageDefineQuery(@Param("defineIdentifier")String defineIdentifier, @Param("bigId")Long bigId, @Param("smallId")Long smallId, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("startNum")int startNum, @Param("endNum")int entNum);
}
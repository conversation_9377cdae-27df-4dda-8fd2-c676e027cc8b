package com.epaylinks.efps.rc.dao;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Date;

import com.epaylinks.efps.rc.domain.RcCloseBusiness;
import java.math.BigDecimal;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date  2022/10/11 14:33
 * @version 1.0
 */
@Mapper
public interface RcCloseBusinessMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(RcCloseBusiness record);

    int insertSelective(RcCloseBusiness record);

    RcCloseBusiness selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(RcCloseBusiness record);

    int updateByPrimaryKey(RcCloseBusiness record);

    List<RcCloseBusiness> selectByReopenTime(@Param("reopenTime")Date reopenTime);


}
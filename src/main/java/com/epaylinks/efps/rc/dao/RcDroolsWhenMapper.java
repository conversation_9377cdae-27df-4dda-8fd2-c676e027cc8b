package com.epaylinks.efps.rc.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.epaylinks.efps.rc.domain.RcDroolsWhen;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:33
 */
@Mapper
public interface RcDroolsWhenMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcDroolsWhen record);

    int insertSelective(RcDroolsWhen record);

    RcDroolsWhen selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcDroolsWhen record);

    int updateByPrimaryKey(RcDroolsWhen record);

    List<RcDroolsWhen> selectAllOrderById();

}
package com.epaylinks.efps.rc.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.epaylinks.efps.rc.domain.RcDroolsThen;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:33
 */
@Mapper
public interface RcDroolsThenMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcDroolsThen record);

    int insertSelective(RcDroolsThen record);

    RcDroolsThen selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcDroolsThen record);

    int updateByPrimaryKey(RcDroolsThen record);

    List<RcDroolsThen> selectAllOrderById();


}
package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.controller.response.RcBalanceResponse;
import com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcArchivePageResponse;
import com.epaylinks.efps.rc.vo.CustomerStateInfoVo;
import com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcArchiveMapper {
    int deleteByPrimaryKey(Long archiveId);

    int insert(RcArchive record);

    int insertSelective(RcArchive record);

    RcArchive selectByPrimaryKey(Long archiveId);

    int updateByPrimaryKeySelective(RcArchive record);

    int updateByPrimaryKey(RcArchive record);

    RcArchive selectByCodeOrName(@Param("levelCode")String code,@Param("name")String name);

    RcArchive selectByMap(Map map);

    String selectInfoId(String customerCode);

    int totalLimitQuery(String archiveCode);

    List<Map<String,String>> listLimitQuery(@Param("archiveCode")String archiveCode,@Param("startNum")int startNum,@Param("endNum")int endNum);

	RcArchive selectByArchiveCode(@Param("archiveCode")String archiveCode);

    void updateCusStatus(@Param("customerCode")String cutomerCode,@Param("oldStatus")String oldStatus,@Param("newStatus")String newStatus);

    int totalPageQuery(Map<String, Object> paramMap);

    List<RcArchivePageResponse> pageQuery(Map<String, Object> paramMap);

    RcArchive selectByTypeAndCode(@Param("archiveType")String type, @Param("archiveCode") String code);
    
    
    List<RcArchive> selectByCertificateNo(@Param("certificateNo") String certificateNo);
    
    String queryTermMachineCode(String terminalCode);

    String queryClientNo(String archiveCode);

    List<RiskInfoExportResponse> queryRiskInfo(Map map);

    List<ExportWebsiteEffectivenessVo> exportWebsiteEffectiveness(List<String> list);

    List<ExportWebsiteEffectivenessVo> exportWebByImport (List<String> list);

    CustomerStateInfoVo queryCustomerStateInfo(@Param("customerNo") String customerNo);

    @Select("select jy.AVAILABLEBALANCE                                                                      as availableBalance,\n" +
            "       jy.FLOATBALANCE                                                                          as floatBalance,\n" +
            "       jy.RCBALANCE                                                                         as frozenBalance,\n" +
            "       nvl(bj.AVAILABLEBALANCE, 0)                                                              as splitBalance,\n" +
            "       case\n" +
            "           when jy.AVAILABLEBALANCE < jy.RCBALANCE then 0\n" +
            "           else jy.AVAILABLEBALANCE - jy.RCBALANCE end                                          as withdrawBalance,\n" +
            "       nvl(cr.TOTAL_BALANCE, case\n" +
            "                                 when c.RC_STATUS = 1 or c.STATUS in (2, 4)\n" +
            "                                     then jy.AVAILABLEBALANCE + jy.FLOATBALANCE + nvl(bj.AVAILABLEBALANCE, 0)\n" +
            "                                 else (case\n" +
            "                                           when jy.AVAILABLEBALANCE + jy.FLOATBALANCE >= jy.RCBALANCE then jy.RCBALANCE\n" +
            "                                           else jy.AVAILABLEBALANCE + jy.FLOATBALANCE end) end) as actualFrozenBalance,\n" +
            "       jy.RCBALANCE                                                                         as rcBalance\n" +
            "from cust_customer c\n" +
            "         left join ACC_ACCOUNT jy on c.customer_no = jy.CUSTOMERCODE and jy.ACCOUNTTYPEID = 2\n" +
            "         left join ACC_ACCOUNT bj on c.customer_no = bj.CUSTOMERCODE and bj.ACCOUNTTYPEID = 3\n" +
            "         left join V_ACC_CANCEL_RECORD cr on c.CUSTOMER_NO = cr.CUSTOMER_CODE\n" +
            "where c.CUSTOMER_NO = #{customerCode,jdbcType=VARCHAR}")
    RcBalanceResponse selectRcBalance(@Param("customerCode") String customerCode);

    int updatePaySetting(Map map);
}
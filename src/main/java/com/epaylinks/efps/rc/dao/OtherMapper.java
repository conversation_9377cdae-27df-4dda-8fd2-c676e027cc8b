package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.cust.MccBusinessType;
import com.epaylinks.efps.rc.domain.txs.TxsPreOrder;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *这个地方，一开始是pas_user表的mapper
 * 后面用于其他的一些模块的表查询
 */
@Mapper
public interface OtherMapper {

	User selectUserById(Long uid);

	String selectAuditAuthRC(@Param("userId")Long userId,@Param("auditName") String auditName);

	List<String> selectQueryAuthRC(@Param("userId")Long userId);

	String selectPayMethodByBusinessCode(String businessCode);

    Customer queryCustomerByCustomerNo(String customerNo);

    Customer queryCustomerDraftByCustomerNo(String customerNo);

    Integer queryCustomerDraftByCustomerNoList(List list);

    Customer queryCustomerByCustomerId(@Param("customerId") Long customerId);

    TxsPreOrder queryTxsOrderByChannelOrder(String channelOrder);
    
    String selectParamValueByTypeAndName(@Param("paramType") String paramType, @Param("paramName") String paramName);
    
    List<String> selectCustomerByBusinessLicenseNo(@Param("businessLicenseCode") String businessLicenseCode);
    
    List<String> selectCustomerByCertificateNo(@Param("certificateNo") String certificateNo);

    int updateAuditStatusByBwId(@Param("auditStatus")Short auditStatus,@Param("id")Long id);

    int updateAuditByBwId(Map<String,Object> paramsMap);

    int deleteByBwId(@Param("id") Long id);

    Integer selectServiceFee(Map paramMap);

    String queryVerifyBusiness(List<String> list);

    String selectInnerBizType(@Param("customerCode") String customerCode);

    int updateDraftTemporary(@Param("temporaryStatus") String temporaryStatus,@Param("temporaryStatusReason") String temporaryStatusReason,@Param("customerNo") String customerNo);

    long selectBalance(@Param("customerCode") String customerCode);

    long isChannelTransfer(@Param("transactionNo") String transactionNo);
	
	MccBusinessType queryMccInfo(@Param("mcc") String mcc);

    String queryDictBusinessType(@Param("industry") String industry);

    Long queryRegistCapital(@Param("customerId") Long customerId);

    Integer whetherOpenWithdraw(@Param("customerNo") String customerNo);

    String queryWithdrawParam(@Param("paramName") String paramName);

    String queryCertNoEnc(@Param("cardNoEnc") String cardNoEnc);

    String queryCardNoEnc(@Param("orderNo") String orderNo);

    Boolean queryBusinessByIdAndCode(Map map);
}

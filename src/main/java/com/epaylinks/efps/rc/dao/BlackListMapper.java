package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.BlackList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BlackListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BlackList record);

    int insertSelective(BlackList record);

    BlackList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BlackList record);

    int updateByPrimaryKey(BlackList record);

    Long querySeq();

    BlackList queryOneByTypeAndValue(@Param("type") String type,@Param("value") String value,@Param("valueHash") String valueHash);
}
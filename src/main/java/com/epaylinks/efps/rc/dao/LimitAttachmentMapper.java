package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.LimitAttachment;
import com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface LimitAttachmentMapper {
    int deleteByPrimaryKey(Long attachmentId);

    int insert(LimitAttachmentWithBLOBs record);

    int insertSelective(LimitAttachmentWithBLOBs record);

    LimitAttachmentWithBLOBs selectByPrimaryKey(Long attachmentId);

    int updateByPrimaryKeySelective(LimitAttachmentWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(LimitAttachmentWithBLOBs record);

    int updateByPrimaryKey(LimitAttachment record);

    Long selectSeq();

    List<LimitAttachmentWithBLOBs> selectByArchiveTypeAndCode(Map paramMap);
}
package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.BwList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface BwListMapper {
    int deleteByPrimaryKey(Long bwId);

    int insert(BwList record);

    int insertSelective(BwList record);

    BwList selectByPrimaryKey(Long bwId);

    int updateByPrimaryKeySelective(BwList record);

    int updateByPrimaryKey(BwList record);

    int queryExistence(BwList record);

    int queryTotal(Map<String, Object> paramMap);

    List<BwList> queryByPage(Map<String, Object> paramMap);

    List<BwList> selectAll(@Param("bwType")String bwType);

    List<BwList> getByTagerTypeAndTagerId(@Param("businessTagerType")String businessTagerType, @Param("businessTagerId")String businessTagerId);

    List<BwList> getByTagerType(@Param("businessTagerType")String businessTagerType);
    List<BwList> selectNoHashByPage(Map map);

    int updateHashInfoBySelective(BwList record);

}
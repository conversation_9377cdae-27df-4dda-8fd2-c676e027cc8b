package com.epaylinks.efps.rc.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.rc.domain.RiskEventRecord;
import com.epaylinks.efps.rc.domain.RiskEventSummary;

/**
 * 风控管理-可疑事件监控记录数据访问类
 */
@Mapper
public interface RiskEventRecordMapper {
    int deleteByPrimaryKey(Long eventId);

    int insert(RiskEventRecord record);

    int insertSelective(RiskEventRecord record);

    RiskEventRecord selectByPrimaryKey(Long eventId);

    int updateByPrimaryKeySelective(RiskEventRecord record);

    int updateByPrimaryKey(RiskEventRecord record);

    /**
     * 获取主键ID
     * @return
     */
    Long selectIdFromSeq();

    /**
     * 新增可疑事件监控记录
     * @param record
     * @return
     */
    int addEventRecord(RiskEventRecord record);

    /**
     * 分页查询可疑事件监控记录
     * @return
     */
    List<RiskEventRecord> pageQueryEventRecord(Map map);

    /**
     * 分页统计可疑事件监控记录数
     * @return
     */
    int pageCountEventRecord(Map map);

    /**
     * 查询可疑事件汇总条数
     * @param paramMap
     * @return
     */
    int countEventSummary(Map<String, Object> paramMap);
    
    /**
     * 查询可疑事件汇总列表
     * @param paramMap
     * @return
     */
    List<RiskEventSummary> pageEventSummary(Map<String, Object> paramMap);
}

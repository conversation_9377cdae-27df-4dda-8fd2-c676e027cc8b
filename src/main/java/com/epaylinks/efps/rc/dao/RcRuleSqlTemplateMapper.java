package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcRuleSqlTemplate;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date  2024/1/8 10:27
 * @version 1.0
 */
@Mapper
public interface RcRuleSqlTemplateMapper {
    int deleteByPrimaryKey(String scene);

    int insert(RcRuleSqlTemplate record);

    int insertSelective(RcRuleSqlTemplate record);

    RcRuleSqlTemplate selectByPrimaryKey(String scene);

    int updateByPrimaryKeySelective(RcRuleSqlTemplate record);

    int updateByPrimaryKey(RcRuleSqlTemplate record);
}
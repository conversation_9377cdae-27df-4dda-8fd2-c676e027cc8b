package com.epaylinks.efps.rc.dao;


import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.vo.OperateLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * RcOperateLog 映射器
 *
 * <AUTHOR> 2020-6-9
 */
@Mapper
public interface RcOperateLogMapper {

    /**
     * 获取RcOperateLog实体对象
     *
     * @param id 对象主键
     */
    RcOperateLog getByPrimaryId(@Param("id") int id);

    /**
     * 创建RcOperateLog实体对象
     *
     * @param entity RcOperateLog对象
     */
    int insert(RcOperateLog entity);

    /**
     * 更新RcOperateLog实体对象
     *
     * @param entity RcOperateLog对象
     */
    int updateByPrimaryId(RcOperateLog entity);

    /**
     * 根据条件获取VO对象列表
     *
     * @param params
     * @return
     */
    List<OperateLogVo> getVoListByParams(@Param("params") Map<String, String> params);

    /**
     * 根据条件获取VO对象总数
     *
     * @param params
     * @return
     */
    int totalVo(@Param("params") Map<String, String> params);

}
package com.epaylinks.efps.rc.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.epaylinks.efps.rc.domain.RcDroolsSceneThen;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:32
 */
@Mapper
public interface RcDroolsSceneThenMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcDroolsSceneThen record);

    int insertSelective(RcDroolsSceneThen record);

    RcDroolsSceneThen selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcDroolsSceneThen record);

    int updateByPrimaryKey(RcDroolsSceneThen record);

    List<RcDroolsSceneThen> selectAllOrderById();

    List<RcDroolsSceneThen> selectAllBySceneText(@Param("sceneText")String sceneText);


}
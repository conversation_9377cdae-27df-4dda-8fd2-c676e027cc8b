package com.epaylinks.efps.rc.dao;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.rc.domain.RcLimitLog;

@Mapper
public interface RcLimitLogMapper {
	
    int deleteByPrimaryKey(Short id);

    int insert(RcLimitLog record);

    int insertSelective(RcLimitLog record);

    RcLimitLog selectByPrimaryKey(Short id);

    int updateByPrimaryKeySelective(RcLimitLog record);

    int updateByPrimaryKey(RcLimitLog record);
}
package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.TemporaryLimit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TemporaryLimitMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TemporaryLimit record);

    int insertSelective(TemporaryLimit record);

    TemporaryLimit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TemporaryLimit record);

    int updateByPrimaryKey(TemporaryLimit record);

    TemporaryLimit queryByCodeAndType(Map map);

    List<TemporaryLimit> queryListByCode(Map map);

    Long querySeq();

    List<String> queryLimitTypeByCode(@Param("code") String code);
}
package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcLimitAud;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcLimitAudMapper {
    int deleteByPrimaryKey(Long audId);

    int insert(RcLimitAud record);

    int insertSelective(RcLimitAud record);

    RcLimitAud selectByPrimaryKey(Long audId);

    int updateByPrimaryKeySelective(RcLimitAud record);

    int updateByPrimaryKeyWithBLOBs(RcLimitAud record);

    int updateByPrimaryKey(RcLimitAud record);


    int pageTotal(@Param("customerCode")String customerCode,@Param("status")String status,@Param("startTime")String startTime,@Param("endTime")String endTime);


    List<RcLimitAud> queryByPage(@Param("customerCode")String customerCode,@Param("status")String status,@Param("startTime")String startTime,@Param("endTime")String endTime,@Param("startNum")int startNum,@Param("endNum")int endNum);

    RcLimitAud queryLastRecord(@Param("customerCode")String customerCode, @Param("targetType")String targetType);

    RcLimitAud queryRecordLog(@Param("customerCode")String customerCode, @Param("targetType")String targetType,@Param("auditTime") String auditTime);
}
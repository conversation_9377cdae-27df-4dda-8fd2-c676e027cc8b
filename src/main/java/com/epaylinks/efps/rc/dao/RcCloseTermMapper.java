package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcCloseTerm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 11:49
 */
@Mapper
public interface RcCloseTermMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcCloseTerm record);

    int insertSelective(RcCloseTerm record);

    RcCloseTerm selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcCloseTerm record);

    int updateByPrimaryKey(RcCloseTerm record);

    List<RcCloseTerm> selectByReopenTime(@Param("reopenTime") Date reopenTime);
}
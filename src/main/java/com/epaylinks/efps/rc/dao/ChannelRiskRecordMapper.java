package com.epaylinks.efps.rc.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.rc.domain.ChannelRiskRecord;

@Mapper
public interface ChannelRiskRecordMapper {
    
    int deleteByPrimaryKey(Long id);

    int insert(ChannelRiskRecord record);

    int insertSelective(ChannelRiskRecord record);

    ChannelRiskRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChannelRiskRecord record);

    int updateByPrimaryKey(ChannelRiskRecord record);

    int queryCount(Map map);

    List<ChannelRiskRecord> queryList(Map map);
}
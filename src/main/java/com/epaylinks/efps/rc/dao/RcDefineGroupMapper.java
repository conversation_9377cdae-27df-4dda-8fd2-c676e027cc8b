package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcDefineGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcDefineGroupMapper {
    int deleteByPrimaryKey(Long groupId);

    int insert(RcDefineGroup record);

    int insertSelective(RcDefineGroup record);

    RcDefineGroup selectByPrimaryKey(Long groupId);

    int updateByPrimaryKeySelective(RcDefineGroup record);

    int updateByPrimaryKey(RcDefineGroup record);

    int totalPageQuery(@Param("bigId") Long bigId, @Param("identifier") String identifier);

    List<Map<String,Object>> pageQuery(@Param("bigId") Long bigId, @Param("identifier") String identifier,@Param("startNum")int startNum,@Param("endNum")int endNum);

    List<Map<String,Object>> selectGroupByParentId(Long parentId);

    String queryName(String smallName);

    int totalBigPageQuery(@Param("identifier") String identifier);

    List<RcDefineGroup> pageBigQuery(@Param("identifier")String identifier,@Param("startNum")int startNum,@Param("endNum")int endNum);
}

package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.controller.response.GamblingPyramidResponse;
import com.epaylinks.efps.rc.domain.GamblingPyramidRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface GamblingPyramidRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GamblingPyramidRecord record);

    int insertSelective(GamblingPyramidRecord record);

    GamblingPyramidRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GamblingPyramidRecord record);

    int updateByPrimaryKeyWithBLOBs(GamblingPyramidRecord record);

    int updateByPrimaryKey(GamblingPyramidRecord record);

    Integer judgeCheckRecordTime();

    Long querySeqNext();

    Integer selectPageCount(Map<String, Object> paramsMap);

    List<GamblingPyramidResponse> selectGamblingPyramidList(Map<String, Object> paramsMap);
}
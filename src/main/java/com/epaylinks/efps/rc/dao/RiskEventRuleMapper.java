package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.vo.TxsNfcRcMode;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 风控管理-可疑事件监控规则数据访问类
 */
@Mapper
public interface RiskEventRuleMapper {

    /**
     * 根据ID查询可疑事件规则
     * @param id
     * @return
     */
    RiskEventRule selectRuleByID(Long id);

    /**
     * 根据代号查询可疑事件规则
     * @param ruleCode
     * @return
     */
    RiskEventRule selectRuleByCode(String ruleCode);

    /**
     * 根据ID修改可疑事件规则参数
     * @param record
     * @return
     */
    int updateParamById(RiskEventRule record);

    /**
     * 启用可疑事件规则
     * @param record
     * @return
     */
    int setRuleON(RiskEventRule record);

    /**
     * 停用可疑事件规则
     * @param record
     * @return
     */
    int setRuleOFF(RiskEventRule record);

    /**
     * 查询可疑事件规则列表
     * @param ruleStatus
     * @param auditStatus
     * @return
     */
    List<RiskEventRule> selectRuleList(@Param("ruleStatus") String ruleStatus, @Param("auditStatus") Short auditStatus);

    Integer countRulePage(Map map);

    List<RiskEventRule> selectRulePage(Map map);
    
    /**
     * 更新碰一碰规则阈值
     * @param nfcRcMode
     * @return
     */
    int updateNFCByRuleCodeSelective(TxsNfcRcMode nfcRcMode);

    int insertSelective(RiskEventRule riskEventRule);

    int deleteByRuleId(@Param("ruleId")Long ruleId);

}

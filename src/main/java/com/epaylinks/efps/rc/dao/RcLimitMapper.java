package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcLimit;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RcLimitMapper {
    int deleteByPrimaryKey(Long limitId);

    int insert(RcLimit record);

    int insertSelective(RcLimit record);

    RcLimit selectByPrimaryKey(Long limitId);

    int updateByPrimaryKeySelective(RcLimit record);

    int updateByPrimaryKey(RcLimit record);

    /**
     * 查找风控指标是否存在
     * @param rcLimit
     * @return
     */
    int queryExistence(RcLimit rcLimit);
    /**
     * 根据业务类型以及业务对象ID来查询指标
     * @param businessType
     * @param businessTagerId
     * @return
     */
    List<RcLimit> listByBusinessTypeAndTagerId(@Param("businessType")String businessType , 
    		@Param("businessTagerId")String businessTagerId);

    /**
     * 根据code查找额度限制列表
     * return 返回DEFINE_CODE,LIMIT_VALUE
     */
    List<Map<String,String>> queryAmountLimitMap(@Param("code") String code,  @Param("businessTagerType")String businessTagerType);

	List<Map<String,String>> queryAddAmountLimitMap(@Param("code")String code,@Param("businessTagerType")String businessTagerType,@Param("level") String level);

	List<Map<String,String>> queryAddIndustryAmountLimitMap(@Param("code")String code,@Param("businessTagerType")String businessTagerType,@Param("level") String level);

	/**
	 * 根据风控指标定义ID和对象ID查询风控指标
	 * @param defineId
	 * @param businessTagerId
	 * @return
	 */
	RcLimit queryLimit(@Param("defineId")Long defineId, @Param("businessTagerType")String businessTagerType, 
	        @Param("businessTagerId")String businessTagerId);

	/**
	 * 统计该商户所有的风险限制
	 * @param customerCode
	 * @return
	 */
	int totalLimitByCustomer(String customerCode);
	

	/**
	 * 根据商户号分页查询风风控制指标
	 * @param customerCode
	 * @param startNum
	 * @param endNum
	 * @return
	 */
	List<Map<String,Object>> pageLimitByCustomer(@Param("customerCode")String customerCode,@Param("startNum")int startNum,@Param("endNum")int endNum);

	/**
	 * 根据商户号，查询所有风控指标
	 * @param customerCode
	 * @return
	 */
	List<RcLimit> queryAllLimit(String customerCode);


	List<RcLimit> queryAmountLimits(String customerCode);

	List<RcLimit> selectALL();

    void deleteLevelLimit(String businessTagerId);
    
    
    /**
     * 统计所有的风控指标
     * @return
     */
    int totalLimit();
    
    /**
     * @param startNum
     * @param endNum
     * @return
     */
    List<RcLimit> pageLimit(@Param("startNum")int startNum, @Param("endNum")int endNum);

	List<RcLimit> selectAllByBusinnesTypeAndBusinessTagerTypeAndBusinessTagerId(@Param("businnesType")String businnesType,@Param("businessTagerType")String businessTagerType,@Param("businessTagerId")String businessTagerId);


}
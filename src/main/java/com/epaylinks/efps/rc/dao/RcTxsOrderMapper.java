package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RcTxsOrderKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface RcTxsOrderMapper {
    int deleteByPrimaryKey(RcTxsOrderKey key);

    int insert(RcTxsOrder record);

    int insertSelective(RcTxsOrder record);

    RcTxsOrder selectByPrimaryKey(RcTxsOrderKey key);

    int updateByPrimaryKeySelective(RcTxsOrder record);

    int updateByPrimaryKey(RcTxsOrder record);
    
    /**
     * 查询date后交易订单商户编号
     * @param dateStr 格式：yyyymmdd
     * @return
     */
    List<String> queryTxsCustomerCodeList(@Param("dateStr") String dateStr);

    List<String> queryTxsPlatCustomerCodeList(@Param("dateStr") String dateStr);

    RcTxsOrder findOneByTransactionNo(@Param("transactionNo")String transactionNo);


}
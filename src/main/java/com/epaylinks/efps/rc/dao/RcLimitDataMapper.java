package com.epaylinks.efps.rc.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.rc.domain.RcLimitData;
@Mapper
public interface RcLimitDataMapper {
    int deleteByPrimaryKey(Short id);

    int insert(RcLimitData record);

    int insertSelective(RcLimitData record);

    RcLimitData selectByPrimaryKey(Short id);

    int updateByPrimaryKeySelective(RcLimitData record);

    int updateByPrimaryKey(RcLimitData record);

	RcLimitData selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(@Param("defineCode")String defineCode, 
			@Param("businessTargetId")String businessTagerId, @Param("datetime")String datetime , 
			@Param("status") String status);

	
    /**
     * 统计所有的风控指标
     * @return
     */
    int totalInitRedis();
    
    /**
     * @param startNum
     * @param endNum
     * @return
     */
    List<RcLimitData> pageInitRedisData(@Param("startNum")int startNum, @Param("endNum")int endNum);

    RcLimitData selectOneByBusinesstargetidAndDefinecodeAndDatetime(@Param("businesstargetid")String businesstargetid,@Param("definecode")String definecode,@Param("datetime")String datetime);

}
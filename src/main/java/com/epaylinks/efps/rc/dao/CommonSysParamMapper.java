package com.epaylinks.efps.rc.dao;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.rc.domain.CommonSysParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/11 11:07
 */
@Mapper
public interface CommonSysParamMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CommonSysParam record);

    int insertSelective(CommonSysParam record);

    CommonSysParam selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommonSysParam record);

    int updateByPrimaryKey(CommonSysParam record);

    CommonSysParam selectOneByOwnerAndKey(@Param("owner")String owner,@Param("key")String key);

    List<CommonSysParam> selectByOwner(@Param("owner")String owner);

}
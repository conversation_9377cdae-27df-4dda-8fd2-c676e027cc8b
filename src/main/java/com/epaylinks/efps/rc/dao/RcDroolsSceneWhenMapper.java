package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcDroolsSceneWhen;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 10:25
 */
@Mapper
public interface RcDroolsSceneWhenMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcDroolsSceneWhen record);

    int insertSelective(RcDroolsSceneWhen record);

    RcDroolsSceneWhen selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcDroolsSceneWhen record);

    int updateByPrimaryKey(RcDroolsSceneWhen record);

    List<RcDroolsSceneWhen> selectAllOrderById();

    List<RcDroolsSceneWhen> selectAllBySceneTextOrderByPriority(@Param("sceneText") String sceneText);
}
package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.rc.domain.ComplainTrade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
public interface ComplainTradeMapper {
    /**
     * 获取主键ID
     * @return
     */
    Long selectIdFromSeq();

    /**
     * 判断上游投诉记录是否存在
     * @param uniRemark
     * @return
     */
    boolean hasComplainRemark(@Param("uniRemark") String uniRemark);

    /**
     * 插入上游投诉记录
     * @param record
     * @return
     */
    int insert(ComplainTrade record);

    /**
     * 添加导入明细
     * @param record
     * @return
     */
    int insertBatchDetail(BatchDetail record);

    /**
     * 获取导入明细主键ID
     * @return
     */
    Long selectBatchDetailId();

    /**
     * 根据上游号查询商户
     * @param channelMchId
     * @return
     */
    List<ComplainTrade> selectCustByChannel(String channelMchId);

    /**
     * 根据商户号关联商户信息
     * @param customerCode
     * @return
     */
    ComplainTrade selectByCust(String customerCode);
}
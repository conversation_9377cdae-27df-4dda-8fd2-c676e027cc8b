package com.epaylinks.efps.rc.dao;
import java.util.List;

import com.epaylinks.efps.rc.domain.LimitSwitch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LimitSwitchMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LimitSwitch record);

    int insertSelective(LimitSwitch record);

    LimitSwitch selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LimitSwitch record);

    int updateByPrimaryKey(LimitSwitch record);

    LimitSwitch selectByCustomerNoAndIndex(@Param("customerNo") String customerNo, @Param("limitIndex") String limitIndex);

    Long selectPriKey();

    List<LimitSwitch> selectOpenByCustomerNo(@Param("customerNo")String customerNo);


}
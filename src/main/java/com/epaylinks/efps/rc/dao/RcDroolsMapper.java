package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcDrools;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/17 15:16
 */
@Mapper
public interface RcDroolsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RcDrools record);

    int insertSelective(RcDrools record);

    RcDrools selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RcDrools record);

    int updateByPrimaryKey(RcDrools record);

    List<RcDrools> selectAllOrderById();
}
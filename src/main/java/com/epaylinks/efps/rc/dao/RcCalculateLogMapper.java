package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.domain.RcCalculateLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface RcCalculateLogMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(RcCalculateLog record);

    int insertSelective(RcCalculateLog record);

    RcCalculateLog selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(RcCalculateLog record);

    int updateByPrimaryKey(RcCalculateLog record);



    List<RcCalculateLog> pageQuery(@Param("startNum") int startNum, @Param("endNum") int endNum, @Param("targetTypeCode") String targetTypeCode,@Param("businessTargetId") String businessTargetId,@Param("transactionNo") String transactionNo,@Param("limitTypeCode") String limitTypeCode,@Param("startTime") String startTime,@Param("endTime") String endTime);

    int pageQueryCount(@Param("targetTypeCode") String targetTypeCode,@Param("businessTargetId") String businessTargetId,@Param("transactionNo") String transactionNo,@Param("limitTypeCode") String limitTypeCode,@Param("startTime") String startTime,@Param("endTime") String endTime);
}
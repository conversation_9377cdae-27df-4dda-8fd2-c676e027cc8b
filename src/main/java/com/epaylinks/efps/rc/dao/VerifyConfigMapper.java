package com.epaylinks.efps.rc.dao;

import com.epaylinks.efps.rc.controller.response.VerifyConfigResponse;
import com.epaylinks.efps.rc.domain.VerifyConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface VerifyConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(VerifyConfig record);

    int insertSelective(VerifyConfig record);

    VerifyConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(VerifyConfig record);

    int updateByPrimaryKey(VerifyConfig record);

    VerifyConfig queryByCustomerNo(@Param("customerNo") String customerNo);

    VerifyConfig queryByCustomerNoValid(@Param("customerNo") String customerNo);

    Long querySEQ();

    List<VerifyConfigResponse> pageQuery(Map paramMap);

    Integer count(Map paramMap);
}
package com.epaylinks.efps.rc.controller;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.controller.response.*;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.domain.cum.CustomerInfo;
import com.epaylinks.efps.rc.domain.cum.CustomerObjects;
import com.epaylinks.efps.rc.domain.cum.CustomerSettleInfo;
import com.epaylinks.efps.rc.job.ExportWebsiteEffectivenessJob;
import com.epaylinks.efps.rc.kafka.CusCustomerConsumer;
import com.epaylinks.efps.rc.kafka.KafkaProducer;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.service.export.ExportRcArchiveService;
import com.epaylinks.efps.rc.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rcArchive")
@Api(value = "RcArchiveController", description = "风控档案相关")
public class RcArchiveController {

    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID 3009001 huangyuekun 黄月坤
	@Autowired
	private RcArchiveService rcArchiveService;
	@Autowired
	private RcLimitService rcLimitService;
	@Autowired
	private CumService cumService;
	@Autowired
	private BwListService bwListService;
	@Autowired
	private OtherService ortherService;
	@Autowired
	private AmountLimitAudService amountLimitAudService;
    @Autowired
    private OtherService otherService;
	@Autowired
	private LogService logService;
	@Autowired
	private RcOperateLogService rcOperateLogService;
	@Autowired
	private RcAuditRecordService rcAuditRecordService;
	@Autowired
	private CustService custService;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Autowired
	private CommonLogger logger;

	@Autowired
	private FsService fsService;

	@Autowired
	private ExportFileService exportFileService;

	@Autowired
	private ExportRcArchiveService exportRcArchiveService;

	@Value("${archiveExactQuery:notOpen}")
	private String archiveExactQuery;

	@Autowired
	private RcArchiveMapper rcArchiveMapper;

	@Autowired
	private ExportWebsiteEffectivenessJob exportWebsiteEffectivenessJob;

	@Autowired
	private DataAuthService dataAuthService;
	
    private static final String RC_BIND_CARD_CHANGE_KEY = "rcBindCardChange"; // 风控系统更新报备标识kafka key

	private static final DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    
    
    @PostMapping("/saveRcArchive")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.saveRcArchive")
    @ApiOperation(value ="保存或更新风控档案（暂增加、更新终端使用）")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "archiveType",value = "档案类型（001:身份证；003：统一社会信用代码；005：商户编号；008：终端号）", required = true, dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveCode",value = "风控档案编码", required = true, dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveName", value = "风控档案名称", required = true, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse saveRcArchive(
            @RequestParam("archiveType") String archiveType,
            @RequestParam("archiveCode") String archiveCode,
            @RequestParam("archiveName") String archiveName,
            @RequestHeader("x-userid")Long userId){
        
        //用于记录操作日志
        rcArchiveService.saveRcArchive(archiveType, archiveCode, archiveName, userId);

        return CommonOuterResponse.success();
    }
    
    
	@GetMapping("/limitQuery")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.limitQuery")
	@ApiOperation(value ="指标配置查询")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "archiveCode",value = "商户/个人/代理商编号",dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "archiveName", value = "商户/个人/代理商名称", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "pageNum", value = "当前页面", required = true, dataType = "Integer", paramType = "query"),
					@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "Integer", paramType = "query")
			})
	public PageResult<Map<String,String>> limitQuery(String archiveCode, String archiveName,Integer pageNum,Integer pageSize){
		PageResult<Map<String,String>> pageResult = new PageResult<>();

		//两个参数不能同时为空
		if(StringUtils.isBlank(archiveCode) && StringUtils.isBlank(archiveName)){
			pageResult.setCode(RcCode.ARCHIVE_QUERY_EXCEPTION.code);
			pageResult.setMessage(RcCode.ARCHIVE_QUERY_EXCEPTION.message);
			return pageResult;
		}

		//如果商户号为空，查找商户号
		if(StringUtils.isBlank(archiveCode)){
			RcArchive archive = rcArchiveService.selectByCodeOrName(archiveCode,archiveName);
			archiveCode = archive.getArchiveCode();
		}

		//分页查询
		pageResult = rcArchiveService.pageLimitQuery(archiveCode,pageNum,pageSize);

		pageResult.setCode(CommonOuterResponse.SUCCEE);
		pageResult.setCode("查询成功");

		return pageResult;
	}


	@GetMapping("/query")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.query")
	@ApiOperation(value ="查询风控档案")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "archiveCode",value = "商户/个人/代理商编号",dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "archiveName", value = "商户/个人/代理商名称", required = false, dataType = "String", paramType = "query")
			})
	public RcArchive query(String archiveCode,String archiveName,
						   @RequestHeader(value = "x-userid",required = false) Long userId){
		RcArchive rcArchive = new RcArchive();

		//两个参数不能同时为空
		if(StringUtils.isBlank(archiveCode) && StringUtils.isBlank(archiveName)){
			rcArchive.setReturnCode(RcCode.ARCHIVE_QUERY_EXCEPTION.code);
			rcArchive.setReturnMsg(RcCode.ARCHIVE_QUERY_EXCEPTION.message);
			return rcArchive;
		}

		//先根据code或者name查询
		rcArchive = rcArchiveService.selectByCodeOrName(archiveCode,archiveName,userId);
		if(rcArchive == null || StringUtils.isBlank(rcArchive.getArchiveCode())){
			rcArchive = new RcArchive();
			rcArchive.setReturnCode(RcCode.USER_NOT_EXIXT.code);
			rcArchive.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
			return rcArchive;
		}

		//根据code查找额度限制。 风控档案不在展示商户限额（原不全），2021.1.18
//		Map<String,Long> amountLimit = rcLimitService.queryAmountLimit(rcArchive.getArchiveCode(), rcArchive.getParentCode(), rcArchive.getRcLevel());
		rcArchive.setAmountLimit(new HashMap<String, Long>()); // 初始化返回前端，避免前端异常
		
		CustomerInfo customerInfo = null;
		CustomerSettleInfo customerSettleInfo = null;
		//根据商户编号查询四要素
		try {
			CustomerObjects customerObjects = cumService.viewCustomerAllInfo(rcArchive.getArchiveCode());
			customerInfo = customerObjects.getBasicInfo();
			customerSettleInfo = customerObjects.getCustomerSettmentInfo();
			if (Constants.customerCategory.EFPS_PERSON.code.endsWith(customerInfo.getCustomerCategory()) &&
			customerSettleInfo == null) {
				customerSettleInfo = new CustomerSettleInfo();
			}
		} catch (Exception e) {
			if (e instanceof AppException) {
				rcArchive.setReturnCode(((AppException) e).getErrorCode());
				rcArchive.setReturnCode((((AppException) e).getErrorMsg()));
			} else {
				logger.printMessage("查询风控档案错误：" + e.getMessage());
				logger.printLog(e);
				rcArchive.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
				rcArchive.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
			}
			return rcArchive;
		}

		//封装数据
		List<String> list = new ArrayList<>();
		list.add(customerInfo.getCustomerCode());
		list.add(customerInfo.getLeaPersoniDentificationNo());
		list.add(customerInfo.getMobile());
		if(StringUtils.isNotBlank(customerInfo.getBusinessLicenseNo())){
			list.add(customerInfo.getBusinessLicenseNo());
		}

		if (StringUtils.isNotBlank(customerSettleInfo.getBankAccountNo())){
			list.add(customerSettleInfo.getBankAccountNo());
		}
		//查询是否是黑名单
		Map<String,Boolean> bwMap = bwListService.queryValueInBw(BwList.BwType.BLACK.code,list);

		Map<String,Boolean> map = new HashedMap();
		map.put("001",bwMap.get(customerInfo.getLeaPersoniDentificationNo()));
		map.put("002",bwMap.get(customerInfo.getMobile()));
		map.put("003",bwMap.get(customerInfo.getBusinessLicenseNo()) == null ? false :  bwMap.get(customerInfo.getBusinessLicenseNo()));
		map.put("004",bwMap.get(customerSettleInfo.getBankAccountNo()) == null ? false : bwMap.get(customerSettleInfo.getBankAccountNo()));
		map.put("005",bwMap.get(customerInfo.getCustomerCode()));

		rcArchive.setBwList(map);

		//查找customerInfoId
		String customerInfoId = rcArchiveService.selectInfoId(rcArchive.getArchiveCode());
		rcArchive.setCustomerInfoId(customerInfoId);
		if (Constants.customerCategory.EFPS_CUSTOMER_SERVICE.code.endsWith(customerInfo.getCustomerCategory())) {
		    rcArchive.setCustomerCategory(RcConstants.CustomerCategory.SERVICE.code);
		} else if(Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())){
	        rcArchive.setCustomerCategory(RcConstants.CustomerCategory.PLATFORM.code);
		} else{ // 普通商户
		    rcArchive.setCustomerCategory(RcConstants.CustomerCategory.ORDINARY.code);		
		}
		
		return rcArchive;
	}


	@PostMapping("/updateStatus")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.update")
	@ApiOperation(value ="更新风控档案")
	@OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="修改状态")
	@ApiImplicitParams(
	{
			@ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
			@ApiImplicitParam(name = "statusName", value = "状态名称：rcStatus：风控状态；accountStatus：账户状态", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "status", value = "状态（statusName为风控状态：0：正常;1：冻结; statusName为账户状态：0:正常;1:冻结;2:止付;3:禁止入金）", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query"),
		     @ApiImplicitParam(name = "autoAud", value = "是否自动审核：1：是", required = false, dataType = "String", paramType = "query")
	})
	public CommonOuterResponse updateStatus(
	        String statusName,
	        String status,
	        Long rcArchiveId,
	        String reason,
			@RequestParam(value = "remark",required = false) String remark,
			String uniqueId,
	        @RequestParam(value = "autoAud", required = false) String autoAud,
            @RequestHeader("x-userid") Long userId
	){
		CommonOuterResponse response = new CommonOuterResponse();
	    //根据Id查找风控档案
	    try {
            RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
            if (rcArchive == null) {
                return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
            }
    		if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
    		         && RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				if (statusName.equals("cusStatus")) {
					rcArchiveService.saveRcArchiveStatus(rcArchiveId, statusName, status, reason,remark, userId,false,null);
				}
    	       return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
    		}
    		if (statusName.equals("rcStatus")) {
				RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code,rcArchiveId);
				if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
					throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
				}
			}
    		if (statusName.equals("accountStatus")) {
				RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.ACCOUNT_STATUS.code,rcArchiveId);
				if (!userId.equals(0L) && rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
					throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
				}
			}
			if (statusName.equals("cusStatus")) {
				return rcArchiveService.saveRcArchiveStatus(rcArchiveId, statusName, status, reason,remark, userId,false,null);
			}
    		// 新增审核流程 ********
			RcAuditRecord rcAuditRecord = rcArchiveService.saveAuditRecord(rcArchive, statusName, status, reason, userId,defaultAuditUserId,uniqueId);

//			// 添加待办
//			response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核",
//					"RISK_CONTROL_AUDIT",rcArchive.getArchiveCode(),"record-" + rcAuditRecord.getId(),userId);
//			System.out.println("返回待办信息：" + JSON.toJSONString(response));
    		// 判断是否需要审核，不需审核走原逻辑  2020.11.10
//    		String audFlag = null;
//            if ("1".equals(autoAud)) { // 碰一碰不需审核，不需按照开关走审核流程 ******** fwy
//                audFlag = "0";
//            } else {
//                if (RcArchive.Status.ACCOUNT.statusName.equals(statusName)) {// 账户状态才查询要不要审核
//                    audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
//                }
//            }
//            if ("1".equals(audFlag)) { // 需要审核
//                rcArchiveService.saveChangeStatusAuditRecord(rcArchiveId, statusName, status, reason, userId);
//                return CommonOuterResponse.success("已提交审核");
//            } else if ("0".equals(audFlag)) { // 账户状态免审
//                // 需保持审核记录，免审直接更新改为自动审核 ********
//                RcAuditRecord auditRecord = rcArchiveService.saveChangeStatusAuditRecord(rcArchiveId, statusName, status, reason, userId);
//                rcArchiveService.auditChangeStatus(auditRecord.getId(), new Short("1"), defaultAuditUserId);
//                return CommonOuterResponse.success();
//            } else { // 其他状态
//                return rcArchiveService.saveRcArchiveStatus(rcArchiveId, statusName, status, reason, userId);
//            }
			User user = ortherService.selectUserById(userId);
			if(user == null){
				return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}

			String source = "rcStatus".equals(statusName) ? "风控状态" : "账户状态";
			OpLogHandle.setOpContent("用户：" + user.getName() + "修改了商户：" + rcArchive.getArchiveCode()
					+ "的" + source + "改为:" + RcConstants.AccountStatus.getCommentByCode(status)   + ",原因是:" + reason);
			return CommonOuterResponse.success(rcAuditRecord.getId()); // 返回的ID使用在反诈平台冻结优化
	    } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
			System.out.println("更新状态错误：" + e.getMessage());
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        
	}
	
	
	@PostMapping("/updateBindCard")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RcArchiveController.updateBindCard")
    @ApiOperation(value ="更改出金报备标识")
    @OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="更改出金报备标识")
    @ApiImplicitParams({
			@ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
			@ApiImplicitParam(name = "bindCard", value = "绑定标记:0:不需报备；1：需要报备", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "ratio", value = "抽查比例", required = false, dataType = "Integer", paramType = "query"),
			@ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query")
    })
	@Transactional
    public CommonOuterResponse updateBindCard(
            @RequestParam("rcArchiveId") Long rcArchiveId,
            @RequestParam("bindCard")String bindCard,
			@RequestParam(value = "ratio",required = false) Integer ratio,
            @RequestParam("reason")String reason,
			@RequestParam(value = "uniqueId",required = false) String uniqueId,
            @RequestHeader("x-userid")Long userId){
	    CommonOuterResponse response = new CommonOuterResponse();
        try {
			if (RcConstants.WithdrawalReport.REPORT_CHECK.code.equals(bindCard)) {
				if (ratio == null) {
					throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：选择核验审核时抽查比例必填");
				}
				if (ratio < 1 || ratio > 100) {
					throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：仅可填入[1，100]集合里的整数");
				}
			}
			User user = ortherService.selectUserById(userId);
			if(user == null){
				return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			// 注销不允许操作
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.WITHDRAWAL_REPORT.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			//	获取原值
//			String origValue = "1".equals(rcArchive.getBindCard()) ? "需要报备" : "不需报备";
			String origValue = RcConstants.WithdrawalReport.getCommentByCode(rcArchive.getBindCard(),rcArchive.getBindCardRatio());
			String origReason = rcArchive.getBindCardReason();
			// 新增审核流程
			BindCardVo oldValue = new BindCardVo();
			oldValue.setId(rcArchiveId);
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setUserName(rcArchive.getUserName());
			oldValue.setBindCard(rcArchive.getBindCard());
			oldValue.setReason(rcArchive.getBindCardReason());
			oldValue.setBindCardRatio(rcArchive.getBindCardRatio());

			BindCardVo newValue = new BindCardVo();
			newValue.setId(rcArchiveId);
			newValue.setUserId(userId);
			newValue.setUserName(user.getName());
			newValue.setBindCard(bindCard);
			newValue.setReason(reason);
			newValue.setBindCardRatio(ratio);
			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.WITHDRAWAL_REPORT.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqueId);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode()   + "," + RcConstants.AuditTargetType.WITHDRAWAL_REPORT.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				throw new AppException(response.getReturnCode(),response.getReturnMsg());
			}
//        rcArchive.setUserId(userId);
//        rcArchive.setUserName(user.getName());
//        rcArchive.setBindCard(bindCard);
//        rcArchive.setBindCardReason(reason);
			String remark = RcConstants.WithdrawalReport.getCommentByCode(bindCard,ratio); //用于日志展示
//        boolean flag = rcArchiveService.updateRcArchiveSelective(rcArchive);
//        if (!flag){
//            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, "更新失败");
//        }

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			Date modifyDate = new Date();
			// 在风险档案菜单里
			log.setPermId("80201");
			log.setCode(rcArchive.getArchiveCode());
			log.setName(rcArchive.getArchiveName());
			log.setType("2");
			log.setOperator(String.valueOf(userId));
			log.setOperateTime(modifyDate);
			log.setOperateContent("报备卡标识");
			log.setOrigValue(origValue);
			log.setNewValue(RcConstants.WithdrawalReport.getCommentByCode(bindCard,ratio));
			rcOperateLogService.insert(log);

			if (origReason == null || !origReason.equals(reason)) {
				log = new RcOperateLog();
				// 在风险档案菜单里
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("报备卡标识修改原因");
				log.setOrigValue(origReason);
				log.setNewValue(reason);
				rcOperateLogService.insert(log);
			}
			// endregion

			//记录操作日志
			OpLogHandle.setOpContent("用户：" + user.getName() + "修改了商户：" + rcArchive.getArchiveCode()
					+ "的报备卡设置，改为:" + remark   + ",原因是:" + reason);

			// kafka发送状态更新消息
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("customerCode", rcArchive.getArchiveCode());
//        jsonObject.put("bindCard", bindCard);
//        jsonObject.put("reason", reason);
//        jsonObject.put("userId", userId);
//        kafkaProducer.send("CUS_CustomerChange", RC_BIND_CARD_CHANGE_KEY, jsonObject);

			return CommonOuterResponse.success("更新成功");
		} catch (AppException e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
    }
	

	@GetMapping("updateRcBalance")
	@Logable(businessTag = "rcArchive.addRcBalace")
	@ApiOperation(value ="修改风控冻结金额")
	@OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="修改冻结金额")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
					@ApiImplicitParam(name = "amount", value = "金额", required = true, dataType = "Long", paramType = "query"),
					@ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query"),
			})
	@Transactional
	public CommonOuterResponse updateRcBalance(@RequestParam("rcArchiveId")Long rcArchiveId,@RequestHeader("x-userid")Long userId,
											  @RequestParam("amount")Long amount,@RequestParam("reason")String reason,
											   @RequestParam(value = "uniqueId",required = false) String uniqueId){
		CommonOuterResponse response = new CommonOuterResponse();
    	try {
			//根据Id查找风控档案
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			// 注销不允许操作
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_FREEZING.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			//根据UserId查找user
			User user = ortherService.selectUserById(userId);
			if (rcArchive == null || user == null){
				return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
			}

			String origReason = rcArchive.getRcBalanceReason();

//        rcArchive.setRcBalanceReason(reason);
//        CommonOuterResponse response = rcArchiveService.updateRcBalance(rcArchive,amount,user);

			RcLimit rcLimit = rcLimitService.queryLimit(DefineCode.RC_BALANCE.defineId,rcArchive.getArchiveCode());
			String value = "0";
			if(rcLimit != null){
				value = rcLimit.getLimitValue();
			}
			// 新增审核流程
			RcFreeingVo oldValue = new RcFreeingVo();
			oldValue.setId(rcArchiveId);
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setAmount(Long.parseLong(value));
			oldValue.setReason(rcArchive.getRcBalanceReason());

			RcFreeingVo newValue = new RcFreeingVo();
			newValue.setId(rcArchiveId);
			newValue.setUserId(userId);
			newValue.setAmount(amount);
			newValue.setReason(reason);

			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_FREEZING.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqueId);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode()   + "," + RcConstants.AuditTargetType.RISK_CONTROL_FREEZING.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				throw new AppException(response.getReturnCode(),response.getReturnMsg());
			}
//        if (response.getReturnCode().equals(CommonOuterResponse.SUCCEE)){
			OpLogHandle.setOpContent("用户："+user.getName()+",修改了商户："+rcArchive.getArchiveCode()+"的风控冻结金额成功,修改后的值为:"+value+"分,修改原因是:"+reason);

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			Date modifyDate = new Date();
			// 在风险档案菜单里
			log.setPermId("80201");
			log.setCode(rcArchive.getArchiveCode());
			log.setName(rcArchive.getArchiveName());
			log.setType("2");
			log.setOperator(String.valueOf(userId));
			log.setOperateTime(modifyDate);
			log.setOperateContent("风控冻结金额");
			log.setOrigValue(String.valueOf(Long.valueOf(value) - amount));
			log.setNewValue(value);
			rcOperateLogService.insert(log);

			if (origReason == null || !origReason.equals(reason)) {
				log = new RcOperateLog();
				// 在风险档案菜单里
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("风控冻结金额修改原因");
				log.setOrigValue(origReason);
				log.setNewValue(reason);
				rcOperateLogService.insert(log);
			}
			// endregion

//        }else {
//            OpLogHandle.setOpContent("用户："+user.getName()+",修改了商户："+rcArchive.getArchiveCode()+"的风控冻结金额失败,修改后的值为:"+value+"分,修改原因是:"+reason);
//        }
//        response.setData(value);
//		return response;
			return CommonOuterResponse.success();
		} catch (AppException e) {
    		logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
    		logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@GetMapping("queryRcBalance")
    @ApiOperation(value ="查看风控冻结金额")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query")
            })
    public CommonOuterResponse<RcBalanceResponse> queryRcBalance(@RequestParam("rcArchiveId")Long rcArchiveId){
		//根据Id查找风控档案
        RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
        //根据UserId查找user
        if (rcArchive == null){
            return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
        }

		RcBalanceResponse rcBalanceResponse = rcArchiveMapper.selectRcBalance(rcArchive.getArchiveCode());

		return CommonOuterResponse.success(rcBalanceResponse);

	}

	@ApiOperation(value ="业务审核更新风险等级")
	@PostMapping("/businessUpdateRcLevel")
	@Logable(businessTag = "rcArchive.businessUpdateRcLevel")
	@Transactional
	public CommonOuterResponse businessUpdateRcLevel(@RequestParam("customerNo") String customerNo,
													 @RequestParam("level") String level,
													 @RequestHeader("x-userid") Long userId) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			RcArchive rcArchive = rcArchiveService.selectByCodeOrName(customerNo,null);
			if (rcArchive == null) {
				throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
			}
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				throw new AppException(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}
			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_LEVEL.code,rcArchive.getArchiveId());
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}
			level = RcConstants.RcToCustLevel.getCodeByMessage(level);
			String levelName = RcConstants.RcLevel.getMessageByCode(level);
			if (StringUtils.isBlank(levelName)){
				throw new AppException(RcCode.RC_ARG_ERROR.code,RcCode.RC_ARG_ERROR.message);
			}
			String oldLevel = rcArchive.getRcLevel();
			if (oldLevel.equals(level)){
				return CommonOuterResponse.success();
			}
			User user = ortherService.selectUserById(userId);
			String reason = "业务审核人工初始化";
			// 新增审核流程
			RcLevelVo oldValue = new RcLevelVo();
			oldValue.setId(rcArchive.getArchiveId());
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setUserName(rcArchive.getUserName());
			oldValue.setRcLevel(rcArchive.getRcLevel());
			oldValue.setRcLevelReason(rcArchive.getRcLevelReason());
			//
			RcLevelVo newValue = new RcLevelVo();
			newValue.setId(rcArchive.getArchiveId());
			newValue.setUserId(userId);
			newValue.setUserName(user.getName());
			newValue.setRcLevel(level);
			newValue.setRcLevelReason(reason);
			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_LEVEL.code,rcArchive.getArchiveId(),JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,null);
			Date modifyDate = new Date();
			if (!Objects.equals(rcArchive.getRcLevel(),level)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"风险等级",
						RcConstants.RcLevel.getMessageByCode(rcArchive.getRcLevel()),levelName);
			}
			if (!Objects.equals(rcArchive.getRcLevelReason(),reason)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"风险等级修改原因",
						rcArchive.getRcLevelReason(),reason);
			}
			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode() + "," + RcConstants.AuditTargetType.RISK_LEVEL.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			return CommonOuterResponse.success();
		} catch (AppException e) {
			logger.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("业务审核更新风险等级错误：" + e.getMessage());
			logger.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}


	@PostMapping("/updateRcLevel")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.updateRcLevel")
	@ApiOperation(value ="更新风控等级")
	@OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="修改风控等级")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
					@ApiImplicitParam(name = "rcLevel", value = "风控等级", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query"),

			})
	@Transactional
	public CommonOuterResponse updateRcLevel(@RequestHeader("x-userid")Long userId,
											 @RequestParam("rcArchiveId") Long rcArchiveId ,
											 @RequestParam("rcLevel")String rcLevel,
											 @RequestParam("reason")String reason,
											 @RequestParam(value = "uniqueId",required = false) String uniqueId){
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			//根据Id查找风控档案
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			// 注销不允许操作
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_LEVEL.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				return CommonOuterResponse.fail(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			//用于日志展示
			//根据UserId查找user
			User user = ortherService.selectUserById(userId);
			if(user == null || rcArchive == null){
				response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
				response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
				return response;
			}
			String oldLevel = rcArchive.getRcLevel();

			if (oldLevel.equals(rcLevel)){
				response.setReturnCode(RcCode.RC_LEVLE_ERROR.code);
				response.setReturnMsg(RcCode.RC_LEVLE_ERROR.message);
				return response;
			}

			//校验参数
			//用于记录操作日志
			String levelName = RcConstants.RcLevel.getMessageByCode(rcLevel);
			if (StringUtils.isBlank(levelName)){
				response.setReturnCode(RcCode.RC_ARG_ERROR.code);
				response.setReturnMsg(RcCode.RC_ARG_ERROR.message);
				return response;
			}

			String origReason = rcArchive.getRcLevelReason();
			// 新增审核流程
			RcLevelVo oldValue = new RcLevelVo();
			oldValue.setId(rcArchive.getArchiveId());
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setUserName(rcArchive.getUserName());
			oldValue.setRcLevel(rcArchive.getRcLevel());
			oldValue.setRcLevelReason(rcArchive.getRcLevelReason());

			RcLevelVo newValue = new RcLevelVo();
			newValue.setId(rcArchive.getArchiveId());
			newValue.setUserId(userId);
			newValue.setUserName(user.getName());
			newValue.setRcLevel(rcLevel);
			newValue.setRcLevelReason(reason);

			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_LEVEL.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqueId);

//		rcArchive.setUserId(userId);
//		rcArchive.setUserName(user.getName());
//		rcArchive.setRcLevel(rcLevel);
//        rcArchive.setRcLevelReason(reason);
//
//		rcArchiveService.updateRcLevel(rcArchive,rcLevel);

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			Date modifyDate = new Date();
			// 在风险档案菜单里
			log.setPermId("80201");
			log.setCode(rcArchive.getArchiveCode());
			log.setName(rcArchive.getArchiveName());
			log.setType("2");
			log.setOperator(String.valueOf(userId));
			log.setOperateTime(modifyDate);
			log.setOperateContent("风险等级");
			log.setOrigValue(RcConstants.RcLevel.getMessageByCode(oldLevel));
			log.setNewValue(levelName);
			rcOperateLogService.insert(log);

			if (origReason == null || !origReason.equals(reason)) {
				log = new RcOperateLog();
				// 在风险档案菜单里
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("风险等级修改原因");
				log.setOrigValue(origReason);
				log.setNewValue(reason);
				rcOperateLogService.insert(log);
			}

			// endregion\
			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode() + "," + RcConstants.AuditTargetType.RISK_LEVEL.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}

			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			OpLogHandle.setOpContent("用户："+user.getName()+",修改了风控档案："+oldLevel+"的风控等级,修改前的值为："+rcArchive.getRcLevel()+"修改后的值为:"+rcLevel+",修改原因是:"+reason);
			return CommonOuterResponse.success();
		} catch (AppException e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}



    @GetMapping("/pageQuyer")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.pageQuyer")
    @ApiOperation(value ="分页查询风控档案")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "archiveType",value = "档案类型（001:身份证；003：统一社会信用代码；005：商户编号）",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveCode",value = "风控档案编号",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveName", value = "风控档案名称",dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "audStatus", value = "审核状态（01：待审核，02：审核不通过，03：审核通过）",dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "exactQuery", value = "是否精确查询，yes是", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "页面码数", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
    })
	public PageResult<RcArchivePageResponse> pageQuyer(
	        @RequestParam(value = "archiveType", required = false)String archiveType, 
	        @RequestParam(value = "archiveCode", required = false)String archiveCode,
	        @RequestParam(value = "archiveName", required = false)String archiveName,
	        @RequestParam(value = "audStatus", required = false)String audStatus,
			@RequestParam(value = "exactQuery", required = false) String exactQuery,
			@RequestParam(value = "type",required = false) Integer type,
	        @RequestParam(value = "pageNum", required = true)Integer pageNum,
            @RequestParam(value = "pageSize", required = true)Integer pageSize,
			@RequestHeader("x-userid") Long userId
	    ){
        PageResult<RcArchivePageResponse>  result = new PageResult<>();

        if (pageSize <=0 || pageNum <= 0){
           result.setCode(RcCode.RC_ARG_ERROR.code);
           result.setErrorMsg(RcCode.RC_ARG_ERROR.message);
           return result;
        }
        int endNum = pageSize * pageNum;
        int startNum = endNum - pageSize + 1;

        if (StringUtils.isNotBlank(archiveType) && !RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(archiveType)) { //  非商户类型搜索商户名称无效
            archiveName = null;
        }
		// 商户限额管理分页-类型
		if (Constants.CustomerType.BUSINESSMAN.code.toString().equals(archiveType)
				|| Constants.CustomerType.ENTERPRISE.code.toString().equals(archiveType)
				|| Constants.CustomerType.ABROAD.code.toString().equals(archiveType)
				|| Constants.CustomerType.MICRO.code.toString().equals(archiveType)
				|| Constants.CustomerType.OTHERS.code.toString().equals(archiveType)
				|| Constants.CustomerType.GOVERNMENT.code.toString().equals(archiveType)) {
			type = Integer.parseInt(archiveType);
			archiveType = RcConstants.RCTargetType.CUSTOMER_CODE.code;
		}
        
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("archiveType", archiveType);
        paramMap.put("archiveCode", archiveCode);
		paramMap.put("type",type);
		if("yes".equals(archiveExactQuery) || "yes".equals(exactQuery)){
			paramMap.put("archiveNameExact", archiveName);    //精确查询
		}else {
			paramMap.put("archiveName", archiveName);    //模糊查询
		}
        paramMap.put("audStatus", audStatus);
        paramMap.put("startNum", startNum);
        paramMap.put("endNum", endNum);

		dataAuthService.setMapParam(paramMap,userId);
        
        //先分页查询风控档案
        result = rcArchiveService.pageQuery(paramMap);

        //根据查询出来的结果查询风控记录
        List<RcArchivePageResponse> list = result.getRows();
        list.forEach(item ->{
			Integer nature = item.getType();
			if (Constants.CustomerType.BUSINESSMAN.code == nature || Constants.CustomerType.MICRO.code == nature) {
				item = otherService.queryMccInfo(item);
			} else if (Constants.CustomerType.ENTERPRISE.code == nature || Constants.CustomerType.ABROAD.code == nature
					|| Constants.CustomerType.GOVERNMENT.code == nature || Constants.CustomerType.OTHERS.code == nature) {
				item.setIndustry(otherService.queryDictBusinessType(item.getIndustry()));
			}

            // 证件类型商户名称展示为“/”
            if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(item.getArchiveType())){
                item.setArchiveName("/");
            }
			RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(item.getArchiveCode(), item.getArchiveType());
			if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
				item.setAudId(limitAudRecord.getAudId());
			}
            // 审核意见
            item.setAudOpinion(amountLimitAudService.queryLastOpinion(item.getArchiveCode(), item.getArchiveType(), item.getAudStatus()));
        });

        result.setCode(CommonOuterResponse.SUCCEE);
        result.setMessage("查询成功");
	    return result;
    }
    
    
    @GetMapping("/pageTerm")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.pageTerm")
    @ApiOperation(value ="分页查询终端风控档案")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "machineCode",value = "终端序列号",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveCode",value = "终端号",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveName", value = "终端名称",dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "audStatus", value = "审核状态（01：待审核，02：审核不通过，03：审核通过）",dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "exactQuery", value = "是否精确查询，yes是", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "页面码数", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
    })
    public PageResult<RcArchivePageResponse> pageTerm(
            @RequestParam(value = "machineCode", required = false)String machineCode, 
            @RequestParam(value = "archiveCode", required = false)String archiveCode,
            @RequestParam(value = "archiveName", required = false)String archiveName,
            @RequestParam(value = "audStatus", required = false)String audStatus,
			@RequestParam(value = "exactQuery", required = false) String exactQuery,
            @RequestParam(value = "pageNum", required = true)Integer pageNum,
            @RequestParam(value = "pageSize", required = true)Integer pageSize,
			@RequestHeader(value = "x-userid") Long userId
        ){
        PageResult<RcArchivePageResponse>  result = new PageResult<>();
        if (pageSize <=0 || pageNum <= 0){
           result.setCode(RcCode.RC_ARG_ERROR.code);
           result.setErrorMsg(RcCode.RC_ARG_ERROR.message);
           return result;
        }
        int endNum = pageSize * pageNum;
        int startNum = endNum - pageSize + 1;

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("archiveType", RcConstants.BusinessTagerType.TERM.code);
        paramMap.put("archiveCode", archiveCode);
		if("yes".equals(archiveExactQuery) || "yes".equals(exactQuery)){
			paramMap.put("archiveNameExact", archiveName);    //精确查询
		}else {
			paramMap.put("archiveName", archiveName);    //模糊查询
		}
        paramMap.put("audStatus", audStatus);
        paramMap.put("machineCode", machineCode);
        paramMap.put("startNum", startNum);
        paramMap.put("endNum", endNum);

		dataAuthService.setMapParam(paramMap,userId);

        //先分页查询风控档案
        result = rcArchiveService.pageQuery(paramMap);

        //根据查询出来的结果查询风控记录
        List<RcArchivePageResponse>  list = result.getRows();
        list.forEach(item ->{
			RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(item.getArchiveCode(), item.getArchiveType());
			if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
				item.setAudId(limitAudRecord.getAudId());
			}
            // 审核意见
            item.setAudOpinion(amountLimitAudService.queryLastOpinion(item.getArchiveCode(), item.getArchiveType(), item.getAudStatus()));
        });

        result.setCode(CommonOuterResponse.SUCCEE);
        result.setMessage("查询成功");
        return result;
    }

	@GetMapping("/pageClient")
	@Logable(businessTag = "rcArchive.pageClient",outputResult = false)
	@ApiOperation(value ="客户号限额管理分页列表")
	public PageResult<RcArchivePageResponse> pageClient(@RequestParam(value = "audStatus", required = false) String audStatus,
														@RequestParam(value = "clientNo",required = false) String clientNo,
														@RequestParam(value = "pageNum") Integer pageNum,
														@RequestParam(value = "pageSize") Integer pageSize,
														@RequestHeader(value = "x-userid",required = false) Long userId) {
		PageResult  result = new PageResult<>();
		if (pageSize <= 0 || pageNum <= 0){
			result.setCode(RcCode.RC_ARG_ERROR.code);
			result.setErrorMsg(RcCode.RC_ARG_ERROR.message);
			return result;
		}
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;

		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("archiveType", RcConstants.BusinessTagerType.CLIENT_NO.code);
		paramMap.put("archiveCode", clientNo);
		paramMap.put("audStatus", audStatus);
		paramMap.put("startNum", startNum);
		paramMap.put("endNum", endNum);

		dataAuthService.setMapParam(paramMap,userId);

		//先分页查询风控档案
		result = rcArchiveService.pageQuery(paramMap);

		//根据查询出来的结果查询风控记录
		List<RcArchivePageResponse> list = result.getRows();
		list.forEach(item ->{
			//
			Integer nature = item.getType();
			if (Constants.CustomerType.BUSINESSMAN.code == nature || Constants.CustomerType.MICRO.code == nature) {
				item = otherService.queryMccInfo(item);
			} else if (Constants.CustomerType.ENTERPRISE.code == nature || Constants.CustomerType.ABROAD.code == nature
					|| Constants.CustomerType.GOVERNMENT.code == nature || Constants.CustomerType.OTHERS.code == nature) {
				item.setIndustry(otherService.queryDictBusinessType(item.getIndustry()));
			}
			RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(item.getArchiveCode(), item.getArchiveType());
			if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
				item.setAudId(limitAudRecord.getAudId());
			}
			// 审核意见
			item.setAudOpinion(amountLimitAudService.queryLastOpinion(item.getArchiveCode(), item.getArchiveType(), item.getAudStatus()));
		});
		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功");
		return result;
	}

	@GetMapping("/pageDefaultIndustry")
	@Logable(businessTag = "rcArchive.pageDefaultIndustry",outputResult = false)
	@ApiOperation(value ="行业默认限额管理")
	public PageResult<RcArchivePageResponse> pageDefaultIndustry(@RequestParam(value = "audStatus", required = false) String audStatus,
										  @RequestParam(value = "type",required = false) Integer type,
										  @RequestParam(value = "mcc",required = false) String mcc,
										  @RequestParam(value = "innerBusinessType",required = false) String innerBusinessType,
										  @RequestParam(value = "pageNum", required = true) Integer pageNum,
										  @RequestParam(value = "pageSize", required = true) Integer pageSize) {
		PageResult  result = new PageResult<>();
		if (pageSize <= 0 || pageNum <= 0){
			result.setCode(RcCode.RC_ARG_ERROR.code);
			result.setErrorMsg(RcCode.RC_ARG_ERROR.message);
			return result;
		}
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;

		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("archiveType", RcConstants.BusinessTagerType.INDUSTRY.code);
		paramMap.put("audStatus", audStatus);
		paramMap.put("type",type);
		paramMap.put("mcc",mcc);
		paramMap.put("innerBusinessType",innerBusinessType);
		paramMap.put("startNum", startNum);
		paramMap.put("endNum", endNum);
		paramMap.put("source","industry");
		//先分页查询风控档案
		result = rcArchiveService.pageQuery(paramMap);

		//根据查询出来的结果查询风控记录
		List<RcArchivePageResponse> list = result.getRows();
		list.forEach(item ->{
			Integer nature = item.getType();
			if (Constants.CustomerType.BUSINESSMAN.code == nature || Constants.CustomerType.MICRO.code == nature) {
				item = otherService.queryMccInfo(item);
			} else if (Constants.CustomerType.ENTERPRISE.code == nature || Constants.CustomerType.ABROAD.code == nature
					|| Constants.CustomerType.GOVERNMENT.code == nature || Constants.CustomerType.OTHERS.code == nature) {
				item.setIndustry(otherService.queryDictBusinessType(item.getIndustry()));
			}
			RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(item.getArchiveCode(), item.getArchiveType());
			if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
				item.setAudId(limitAudRecord.getAudId());
			}
			// 审核意见
			item.setAudOpinion(amountLimitAudService.queryLastOpinion(item.getArchiveCode(), item.getArchiveType(), item.getAudStatus()));
		});
		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功");
		return result;
	}
    
    
    @GetMapping("/pagePerson")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.pagePerson")
    @ApiOperation(value ="分页查询个人风控档案")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "clientNo",value = "客户号",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "certificateNo",value = "证件号码",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveCode",value = "编号",dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "archiveName", value = "名称",dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "rcLevel", value = "账户等级:LOW_LEVEL:I类；MIDDLE_LEVEL:II类；HIGHT_LEVEL:III类",dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "audStatus", value = "审核状态（01：待审核，02：审核不通过，03：审核通过）",dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "exactQuery", value = "是否精确查询，yes是", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "页面码数", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
    })
    public PageResult<RcArchivePageResponse> pagePerson(
            @RequestParam(value = "clientNo", required = false)String clientNo, 
            @RequestParam(value = "certificateNo", required = false)String certificateNo,
            @RequestParam(value = "archiveCode", required = false)String archiveCode,
            @RequestParam(value = "archiveName", required = false)String archiveName,
            @RequestParam(value = "rcLevel", required = false)String rcLevel,
            @RequestParam(value = "audStatus", required = false)String audStatus,
			@RequestParam(value = "exactQuery", required = false) String exactQuery,
            @RequestParam(value = "pageNum", required = true)Integer pageNum,
            @RequestParam(value = "pageSize", required = true)Integer pageSize,
			@RequestHeader(value = "x-userid") Long userId
        ){

        PageResult<RcArchivePageResponse>  result = new PageResult<>();
        if (pageSize <=0 || pageNum <= 0){
           result.setCode(RcCode.RC_ARG_ERROR.code);
           result.setErrorMsg(RcCode.RC_ARG_ERROR.message);
           return result;
        }
        int endNum = pageSize * pageNum;
        int startNum = endNum - pageSize + 1;

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("archiveType", RcConstants.BusinessTagerType.PERSON.code);
        paramMap.put("archiveCode", archiveCode);
		if("yes".equals(archiveExactQuery) || "yes".equals(exactQuery)){
			paramMap.put("archiveNameExact", archiveName);    //精确查询
		}else {
			paramMap.put("archiveName", archiveName);    //模糊查询
		}
        paramMap.put("audStatus", audStatus);
        paramMap.put("clientNo", clientNo);
        paramMap.put("rcLevel", rcLevel);
        paramMap.put("certificateNo", certificateNo);
        paramMap.put("startNum", startNum);
        paramMap.put("endNum", endNum);

		dataAuthService.setMapParam(paramMap,userId);

        //先分页查询风控档案
        result = rcArchiveService.pageQuery(paramMap);

        //根据查询出来的结果查询风控记录
        List<RcArchivePageResponse>  list = result.getRows();
        list.forEach(item ->{
			RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(item.getArchiveCode(), item.getArchiveType());
			if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
				item.setAudId(limitAudRecord.getAudId());
			}
            // 审核意见
            item.setAudOpinion(amountLimitAudService.queryLastOpinion(item.getArchiveCode(), item.getArchiveType(), item.getAudStatus()));
        });

        result.setCode(CommonOuterResponse.SUCCEE);
        result.setMessage("查询成功");
        return result;
    }
    
    

	@GetMapping("/getRcTargetType")
	@ApiOperation(value ="查询风控对象类型")
	public CommonOuterResponse getRcTargetType(){
		List<Map<String,String>> rcTargetTypeList = new ArrayList<>();
		for (RcConstants.RCTargetType type : RcConstants.RCTargetType.values()){
            Map<String,String> rcTargetTypeMap = new HashMap<>();
		    rcTargetTypeMap.put("key",type.code);
		    rcTargetTypeMap.put("name",type.message);
            rcTargetTypeList.add(rcTargetTypeMap);
		}
		CommonOuterResponse response = new CommonOuterResponse();
        response.setData(rcTargetTypeList);
		return response;
	}

    @GetMapping("/getRcLimitType")
    @ApiOperation(value ="查询护法类型")
    public CommonOuterResponse getRcLimitType(){
        List<Map<String,String>> rcLimitTypeList = new ArrayList<>();
        for (RcConstants.RcLimitType type : RcConstants.RcLimitType.values()){
            Map<String,String> rcLimitTypeMap = new HashMap<>();
            rcLimitTypeMap.put("key",type.code);
            rcLimitTypeMap.put("name",type.message);
            rcLimitTypeList.add(rcLimitTypeMap);
        }
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(rcLimitTypeList);
        return response;
    }

    
	@PostMapping("/updateCustomerRcStatus")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.updateCustomerRcStatus")
	@ApiOperation(value ="修改商户风控状态")
	@OpLog(opModule ="修改商户风控状态",opMethod ="修改商户风控状态")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "customerCode",value = "商户编号",dataType = "String",required = true,paramType = "query"),
		@ApiImplicitParam(name = "statusName", value = "状态名称：rcStatus：风控状态；accountStatus：账户状态;cusStatus:商户状态", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "status", value = "状态（statusName为风控状态：0：正常;1：冻结; statusName为账户状态：0:正常;1:冻结;2:止付;3:禁止入金；statusName为商户状态:1:正常;3:注销）", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "autoAud", value = "是否自动审核：1：是", required = false, dataType = "String", paramType = "query")
	})
	public CommonOuterResponse updateCustomerRcStatus(
			@RequestParam("customerCode") String customerCode,
			@RequestParam("statusName") String statusName,
			@RequestParam("status") String status,
			@RequestParam("reason") String reason,
			@RequestParam(value = "remark",required = false) String remark,
			@RequestParam(value = "autoAud", required = false) String autoAud,
			@RequestHeader("x-userid") Long userId){
		
		try {
//			RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code ,customerCode);
			RcArchive rcArchive = rcArchiveService.selectByTypeAndCode("",customerCode); // 个人账户注销出现风控档案不存在 bug-9801
			if(rcArchive == null){
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			return updateStatus(statusName, status, rcArchive.getArchiveId(), reason,remark,null, autoAud, userId);
		}catch (AppException e){
        	return CommonOuterResponse.fail(e.getErrorCode(),e.getMessage());
		}catch (Exception e){
		    logService.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code,RcCode.SYSTEM_EXCEPTION.message);
		}
		
	}
	
	
    @PostMapping("/saveCustomerRcLevel")
    @Logable(businessTag = "rcArchive.saveCustomerRcLevel")
    @ApiOperation(value ="保存商户风控等级")
    @OpLog(opModule ="保存商户风控等级",opMethod ="保存商户风控等级")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "customerCode",value = "商户编号",dataType = "String",required = true,paramType = "query"),
        @ApiImplicitParam(name = "rcLevel", value = "风控等级", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse saveCustomerRcLevel(
            @RequestHeader("x-userid")Long userId,
            @RequestParam("customerCode") String customerCode ,
            @RequestParam("rcLevel")String rcLevel,
            @RequestParam("reason")String reason){
       
        try {
            //根据Id查找风控档案
            CommonOuterResponse response = new CommonOuterResponse();

            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
            if (rcArchive == null){
                return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
            }
            
            return this.updateRcLevel(userId, rcArchive.getArchiveId(), rcLevel, reason,null);
            
        }catch (AppException e){
            return CommonOuterResponse.fail(e.getErrorCode(),e.getMessage());
        }catch (Exception e){
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code,RcCode.SYSTEM_EXCEPTION.message);
        }
    }

	@Validatable
	@Exceptionable
    @PostMapping("/audit")
	@Logable(businessTag = "RcArchiveController.batchAudit")
	@ApiOperation(value = "批量审核", notes = "批量审核", httpMethod = "POST")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "audIds", value = "审核记录ID", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "auditResult", value = "审核状态：1审核通过，2驳回", valueRange = "{1,2}", required = true, dataType = "int", paramType = "query"),
					@ApiImplicitParam(name = "remark", value = "审核意见", dataType = "String", paramType = "query")
			})
    public CommonOuterResponse batchAudit(@RequestParam(value = "audIds") String audIds,
										  @RequestParam(value = "auditResult") Short auditResult,
										  @RequestParam(value = "remark") String remark,
										  @RequestHeader(value = "x-userid") Long userId) {

		try {
			rcArchiveService.batchAuditRecord(audIds,auditResult,remark,userId,"1");
			return CommonOuterResponse.success();
		} catch (AppException e) {
			System.out.println("错误信息：" + e.getMessage());
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			System.out.println("错误信息：" + e.getMessage());
			logService.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}
    
    
    @GetMapping("/pageAuditRecord")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RcArchiveController.pageAuditRecord", outputResult = false)
    @ApiOperation(value = "分页查询审核记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "audStatus", value = "审核结果(0待审核，1审核通过，2驳回)", required = false, valueRange = "{0,1,2}", dataType = "Long", paramType = "query"),
			@ApiImplicitParam(name = "targetType", value = "审核类型（RISK_LEVEL:风险等级；RISK_CONTROL_STATUS:风控状态；ACCOUNT_STATUS:账户状态；RISK_CONTROL_FREEZING:风控冻结金额；WITHDRAWAL_REPORT:出金报备设置；AUTHENTICATION_FINANCING:鉴权理财；RECHARGE:充值设置；TRANSACTION:交易设置；PAYMENT_SETTING：代付设置）", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "startTime", value = "开始时间yyyy-MM-dd HH:mm:ss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyy-MM-dd HH:mm:ss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页面码数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"), })
    public PageResult<StatusAuditRecordPageVo> pageAuditRecord(
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "audStatus", required = false) Long audStatus,
			@RequestParam(value = "targetType", required = false) String targetType,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
			@RequestHeader(value = "x-userid") Long userId) {

        PageResult<StatusAuditRecordPageVo> page = new PageResult<>();
        try {

            int endNum = pageSize * pageNum;
            int startNum = endNum - pageSize + 1;

            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("customerCode", customerCode);
            paramMap.put("audStatus", audStatus);
            paramMap.put("startTime", startTime);
            paramMap.put("endTime", endTime);
            paramMap.put("startNum", startNum);
            paramMap.put("endNum", endNum);
            paramMap.put("targetType", targetType);

			dataAuthService.setMapParam(paramMap,userId);

            // 先分页查询风控档案
            return rcAuditRecordService.pageQuery(paramMap,userId);

        } catch (AppException e) {
            page.setCode(e.getErrorCode());
            page.setErrorMsg(e.getErrorMsg());
            return page;
        } catch (Exception e) {
            logService.printLog(e);
            page.setCode(RcCode.SYSTEM_EXCEPTION.code);
            page.setErrorMsg(RcCode.SYSTEM_EXCEPTION.message);
            return page;
        }
    }
    
    
    
    @PostMapping("/updateAuthFinacial")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.updateAuthFinacial")
    @ApiOperation(value ="修改鉴权理财设置")
    @OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="鉴权理财设置")
    @ApiImplicitParams(
    {
            @ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
            @ApiImplicitParam(name = "authFinacial", value = "鉴权理财:0不可授权；1:可授权", valueRange = "{0,1}", required = true, dataType = "String", paramType = "query"),
    })
	@Transactional
    public CommonOuterResponse updateAuthFinacial(
            @RequestHeader("x-userid")Long userId,
            @RequestParam("rcArchiveId") Long rcArchiveId ,
            @RequestParam("authFinacial")String authFinacial,
			@RequestParam(value = "uniqueId",required = false) String uniqueId){
        CommonOuterResponse response = new CommonOuterResponse();
        try {
			//根据Id查找风控档案
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			// 注销不允许操作
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.AUTHENTICATION_FINANCING.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			//用于日志展示
			//根据UserId查找user
			User user = ortherService.selectUserById(userId);
			if(user == null || rcArchive == null){
				return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}

			//校验参数
			//用于记录操作日志
			String oldAuthFinacialName = RcConstants.AuthFinancialType.getMessageByCode(rcArchive.getAuthFinacial());
			String authFinacialName = RcConstants.AuthFinancialType.getMessageByCode(authFinacial);

//        rcArchive.setUserId(userId);
//        rcArchive.setUserName(user.getName());
//        rcArchive.setAuthFinacial(authFinacial);
//
//        rcArchiveService.updateRcArchiveSelective(rcArchive);
			// 新增审核流程
			AuthFinVo oldValue = new AuthFinVo();
			oldValue.setId(rcArchive.getArchiveId());
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setUserName(rcArchive.getUserName());
			oldValue.setAuthFinacial(rcArchive.getAuthFinacial());

			AuthFinVo newValue = new AuthFinVo();
			newValue.setId(rcArchive.getArchiveId());
			newValue.setUserId(userId);
			newValue.setUserName(user.getName());
			newValue.setAuthFinacial(authFinacial);
			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.AUTHENTICATION_FINANCING.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,userId,uniqueId);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode()   + "," + RcConstants.AuditTargetType.AUTHENTICATION_FINANCING.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				throw new AppException(response.getReturnCode(),response.getReturnMsg());
			}
			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			Date modifyDate = new Date();
			// 在风险档案菜单里
			log.setPermId("80201");
			log.setCode(rcArchive.getArchiveCode());
			log.setName(rcArchive.getArchiveName());
			log.setType("2");
			log.setOperator(String.valueOf(userId));
			log.setOperateTime(modifyDate);
			log.setOperateContent("鉴权理财");
			log.setOrigValue(oldAuthFinacialName);
			log.setNewValue(authFinacialName);
			rcOperateLogService.insert(log);

			OpLogHandle.setOpContent("用户："+user.getName()+",修改了风控档案："+ rcArchive.getArchiveCode()
					+ "的鉴权理财设置,修改前为："+ oldAuthFinacialName + ",修改后为:"+authFinacialName);

			return CommonOuterResponse.success();
		} catch (AppException e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
    }

    
    @GetMapping("/queryAuthFinacial")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.queryAuthFinacial")
    @ApiOperation(value ="查询鉴权理财设置")
    @ApiImplicitParams(
    {
            @ApiImplicitParam(name = "archiveCode",value = "风控档案编号",dataType = "String",required = true,paramType = "query"),
    })
    public CommonOuterResponse<String> queryAuthFinacial(
            @RequestParam("archiveCode") String archiveCode){
        
        //根据Id查找风控档案
        RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.RCTargetType.CUSTOMER_CODE.code, archiveCode);
        if (rcArchive == null) {
			rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.RCTargetType.PERSON.code, archiveCode);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
        }

        CommonOuterResponse<String> resp = CommonOuterResponse.success();
        resp.setData(rcArchive.getAuthFinacial() != null ? rcArchive.getAuthFinacial() : "0");  // 默认是不可鉴权
        return resp;
    }
    
    
    @PostMapping("/updateCheckRechargeCard")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RcArchiveController.updateCheckRechargeCard")
    @ApiOperation(value ="修改网银业务设置")
    @OpLog(opModule ="风控管理-风控监控-风控档案",opMethod ="修改网银业务设置")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "rcArchiveId",value = "风控档案Id",dataType = "Long",required = true,paramType = "query"),
        @ApiImplicitParam(name = "eBankSetting", value = "网银业务设置：1：收单；2：充值；3：取上级", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "checkRechargeCard", value = "充值银行卡校验(0：不校验；1：需校验（非同名卡）；2：需校验所有卡)", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "tradeStartTime", value = "交易起始时段（格式：HH:mm）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "tradeEndTime", value = "交易截止时段（格式：HH:mm）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "reason", value = "原因", required = true, dataType = "String", paramType = "query")
    })
	@Transactional
    public CommonOuterResponse updateCheckRechargeCard(
            @RequestParam(value = "rcArchiveId", required = true) Long rcArchiveId,
            @RequestParam(value = "eBankSetting", required = true)String eBankSetting,
            @RequestParam(value = "checkRechargeCard", required = false)String checkRechargeCard,
            @RequestParam(value = "tradeStartTime", required = false)String tradeStartTime,
            @RequestParam(value = "tradeEndTime", required = false)String tradeEndTime,
            @RequestParam(value = "reason", required = true)String reason,
			@RequestParam(value = "uniqueId",required = false) String uniqueId,
            @RequestHeader("x-userid")Long userId){
        CommonOuterResponse response = new CommonOuterResponse();
        try {
			if (!redisTemplate.opsForValue().setIfAbsent(rcArchiveId + "updateCheckRechargeCard","1")) {
				throw new AppException(RcCode.REPEAT_OPERATION.code,RcCode.REPEAT_OPERATION.message);
			}
			redisTemplate.expire(rcArchiveId + "updateCheckRechargeCard",2,TimeUnit.SECONDS);
			User user = ortherService.selectUserById(userId);
			if(user == null){
				return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			// 注销不允许操作
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}
			if (RcConstants.EBankSetting.PARENT.code.equals(eBankSetting)) { // 取上级的情况，需要校验上级是否为空，为空则表示顶级，不能设为取上级
				String parentCustomerCode = otherService.queryUpperCustomerCode(rcArchive.getArchiveCode());
				if (StringUtils.isBlank(parentCustomerCode)) {
					return CommonOuterResponse.fail(RcCode.EBANK_SETTING_ERROR.code, RcCode.EBANK_SETTING_ERROR.message);
				}
			}

			// 校验交易时间段
			if (StringUtils.isNotBlank(tradeStartTime) && StringUtils.isNotBlank(tradeEndTime)
					&& !checkTradeTime(tradeStartTime, tradeEndTime)) {
				return CommonOuterResponse.fail(RcCode.TRADE_TIME_ERROR.code, RcCode.TRADE_TIME_ERROR.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RECHARGE.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			//  获取原值
			String oldCheckValue = RcConstants.CheckBankCard.getMessageByCode(rcArchive.getCheckRechargeCard());
			String newCheckValue = RcConstants.CheckBankCard.getMessageByCode(checkRechargeCard); //用于日志展示
			String oldReason = rcArchive.getCheckCardReason();
			String oldEBankSetting = RcConstants.EBankSetting.getMessageByCode(rcArchive.getEBankSetting());
			String newEBankSetting = RcConstants.EBankSetting.getMessageByCode(eBankSetting);
			String oldStartTime = rcArchive.getTradeStartTime();
			String oldEndTime = rcArchive.getTradeEndTime();

			// 新增审核流程
			RechargeVo oldValue = new RechargeVo();
			oldValue.setId(rcArchiveId);
			oldValue.setUserId(rcArchive.getUserId());
			oldValue.setUserName(rcArchive.getUserName());
			oldValue.setEBankSetting(rcArchive.getEBankSetting());
			oldValue.setCheckRechargeCard(rcArchive.getCheckRechargeCard());
			oldValue.setTradeStartTime(rcArchive.getTradeStartTime());
			oldValue.setTradeEndTime(rcArchive.getTradeEndTime());
			oldValue.setReason(oldReason);

			RechargeVo newValue = new RechargeVo();
			newValue.setId(rcArchiveId);
			newValue.setUserId(userId);
			newValue.setUserName(user.getName());
			newValue.setEBankSetting(eBankSetting);
			newValue.setCheckRechargeCard(checkRechargeCard);
			newValue.setTradeStartTime(tradeStartTime);
			newValue.setTradeEndTime(tradeEndTime);
			newValue.setReason(reason);
			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RECHARGE.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqueId);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode()   + "," + RcConstants.AuditTargetType.RECHARGE.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				throw new AppException(response.getReturnCode(),response.getReturnMsg());
			}
//        rcArchive.setUserId(userId);
//        rcArchive.setUserName(user.getName());
//        rcArchive.setCheckRechargeCard(checkRechargeCard);
//        rcArchive.setCheckCardReason(reason);
//        rcArchive.setEBankSetting(eBankSetting);
//        rcArchive.setTradeStartTime(tradeStartTime);
//        rcArchive.setTradeEndTime(tradeEndTime);
//
//        boolean flag = rcArchiveService.updateRcArchiveByPrimaryKey(rcArchive);
//        if (!flag){
//            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, "更新失败");
//        }

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			Date modifyDate = new Date();
			// 在风险档案菜单里
			if (oldEBankSetting == null || !oldEBankSetting.equals(newEBankSetting)) {
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("修改网银业务设置");
				log.setOrigValue(oldEBankSetting);
				log.setNewValue(newEBankSetting);
				rcOperateLogService.insert(log);
			}

			if (oldCheckValue == null || !oldCheckValue.equals(newCheckValue)) {
				log = new RcOperateLog();
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("修改银行卡校验设置");
				log.setOrigValue(oldCheckValue);
				log.setNewValue(newCheckValue);
				rcOperateLogService.insert(log);
			}

			if (oldStartTime == null || !oldStartTime.equals(tradeStartTime)) {
				log = new RcOperateLog();
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("修改交易起始时间");
				log.setOrigValue(oldStartTime);
				log.setNewValue(tradeStartTime);
				rcOperateLogService.insert(log);
			}

			if (oldEndTime == null || !oldEndTime.equals(tradeEndTime)) {
				log = new RcOperateLog();
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("修改交易截止时间");
				log.setOrigValue(oldEndTime);
				log.setNewValue(tradeEndTime);
				rcOperateLogService.insert(log);
			}

			if (oldReason == null || !oldReason.equals(reason)) {
				log = new RcOperateLog();
				// 在风险档案菜单里
				log.setPermId("80201");
				log.setCode(rcArchive.getArchiveCode());
				log.setName(rcArchive.getArchiveName());
				log.setType("2");
				log.setOperator(String.valueOf(userId));
				log.setOperateTime(modifyDate);
				log.setOperateContent("修改网银业务设置变更原因");
				log.setOrigValue(oldReason);
				log.setNewValue(reason);
				rcOperateLogService.insert(log);
			}

      /*  //记录操作日志
        OpLogHandle.setOpContent("用户：" + user.getName() + "修改了商户：" + rcArchive.getArchiveCode()
                               + "的企业网银充值校验银行卡标识，改为:" + newCheckValue   + ",原因是:" + reason);*/

			return CommonOuterResponse.success("更新成功");
		} catch (AppException e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logService.printLog(e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
    }

	@GetMapping("/whetherOpenWithdraw")
	@Logable(businessTag = "RcArchiveController.whetherOpenWithdraw")
	@ApiOperation(value ="判断是否开通代付业务")
	public CommonOuterResponse<ProportionResponse> whetherOpenWithdraw(Long rcArchiveId,
									  @RequestHeader(value = "x-userid") Long userId) {
		try {
			if (rcArchiveId == null) {
				throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message);
			}
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
//			Boolean result = otherService.whetherOpenWithdraw(rcArchive.getArchiveCode());
			ProportionResponse proportionResponse = new ProportionResponse();
			proportionResponse.setWhetherOpenWithdraw(true);
			proportionResponse.setProportion(rcArchive.getProportion());
			proportionResponse.setMinWithdrawRatio(rcArchive.getMinWithdrawRatio());
			proportionResponse.setWithdrawStartTime(rcArchive.getWithdrawStartTime());
			proportionResponse.setWithdrawEndTime(rcArchive.getWithdrawEndTime());
			proportionResponse.setUniqueId(rcArchive.getWithdrawUniqueId());
			if (StringUtils.isNotBlank(rcArchive.getWithdrawUniqueId())) {
				Map<String,String> urlMap = fsService.filePath(rcArchive.getWithdrawUniqueId(),30,100,"download");
				if (urlMap != null) {
					proportionResponse.setFileUrl(urlMap.get("filePath"));
					proportionResponse.setFileName(urlMap.get("fileName"));
				}
			}

			return CommonOuterResponse.success(proportionResponse);
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("判断是否开通代付业务错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@PostMapping("/updateWithdrawProportion")
	@Logable(businessTag = "RcArchiveController.updateWithdrawProportion")
	@ApiOperation(value ="代付设置")
	public CommonOuterResponse updateWithdrawProportion(@RequestBody WithdrawProportionResponse request,
														@RequestHeader(value = "x-userid") Long userId) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			Long rcArchiveId = request.getRcArchiveId();
			String withdrawStartTime = request.getWithdrawStartTime();
			String withdrawEndTime = request.getWithdrawEndTime();
			String withdrawReason = request.getWithdrawReason();
//			Integer proportion = request.getProportion();
			Integer minWithdrawRatio = request.getMinWithdrawRatio();
			String uniqueId = request.getUniqueId();
			if (rcArchiveId == null || StringUtils.isBlank(request.getWithdrawReason())) {
				throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message);
			}

			checkRepeatOperation(rcArchiveId,"updateWithdrawProportion");
			User user = ortherService.selectUserById(userId);
			if(user == null){
				throw new AppException(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}
			RcArchive rcArchive = rcArchiveService.selectById(rcArchiveId);
			if (rcArchive == null) {
				throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				throw new AppException(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}
			// 校验交易时间段
			if (StringUtils.isNotBlank(withdrawStartTime) && StringUtils.isNotBlank(withdrawEndTime)
					&& !checkTradeTime(withdrawStartTime, withdrawEndTime)) {
				throw new AppException(RcCode.TRADE_TIME_ERROR.code, RcCode.TRADE_TIME_ERROR.message);
			}
			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.PAYMENT_SETTING.code,rcArchiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}

			ProportionVo oldValue = new ProportionVo();
			oldValue.setProportion(rcArchive.getMinWithdrawRatio()); // 审核管理列表-代付设置变更前后使用的是本字段
			oldValue.setMinWithdrawRatio(rcArchive.getMinWithdrawRatio());
			oldValue.setWithdrawStartTime(rcArchive.getWithdrawStartTime());
			oldValue.setWithdrawEndTime(rcArchive.getWithdrawEndTime());
			oldValue.setWithdrawReason(rcArchive.getWithdrawReason());
			oldValue.setUniqueId(rcArchive.getWithdrawUniqueId());

			ProportionVo newValue = new ProportionVo();
			newValue.setProportion(minWithdrawRatio);
			newValue.setMinWithdrawRatio(minWithdrawRatio);
			newValue.setWithdrawStartTime(withdrawStartTime);
			newValue.setWithdrawEndTime(withdrawEndTime);
			newValue.setWithdrawReason(withdrawReason);
			newValue.setUniqueId(uniqueId);

			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.PAYMENT_SETTING.code,rcArchiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,withdrawReason,userId,null);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {
				response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
						rcArchive.getArchiveCode()   + "," + RcConstants.AuditTargetType.PAYMENT_SETTING.code,"record-" + rcAuditRecord.getId(),userId);
			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
				throw new AppException(response.getReturnCode(),response.getReturnMsg());
			}
			// 日志
			Date modifyDate = new Date();
//			if (!Objects.equals(rcArchive.getProportion(),proportion)) {
//				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"代付设置可用额度占比",
//						rcArchive.getProportion() != null ? String.valueOf(rcArchive.getProportion()) : null,proportion != null ? String.valueOf(proportion) : null);
//			}
			if (!Objects.equals(rcArchive.getMinWithdrawRatio(),minWithdrawRatio)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"最低提现出金比例设置",
						rcArchive.getMinWithdrawRatio() != null ? String.valueOf(rcArchive.getMinWithdrawRatio()) : null,minWithdrawRatio != null ? String.valueOf(minWithdrawRatio) : null);
			}
			if (!Objects.equals(rcArchive.getWithdrawStartTime(),withdrawStartTime)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"代付设置开始时间",
						rcArchive.getWithdrawStartTime(),withdrawStartTime);
			}
			if (!Objects.equals(rcArchive.getWithdrawEndTime(),withdrawEndTime)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"代付设置结束时间",
						rcArchive.getWithdrawEndTime(),withdrawEndTime);
			}
			if (!Objects.equals(rcArchive.getWithdrawReason(),withdrawReason)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"代付设置变更原因",
						rcArchive.getWithdrawReason(),withdrawReason);
			}
			return CommonOuterResponse.success();
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("风控档案代付设置错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}
    
    /**
     * 校验交易时间（不夸日校验）
     * @param startTime  起始时间，格式HH:mm
     * @param endTime 截止时间，格式HH:mm
     * @return
     */
    private boolean checkTradeTime(String startTime, String endTime) {
        
        String[] startArray = startTime.split(":");
        String[] endArray = endTime.split(":");
        if (startArray.length != 2 || endArray.length != 2) {
            return false;
        }
        try {
            int startHour = Integer.parseInt(startArray[0]);
            int startMin = Integer.parseInt(startArray[1]);
            int endHour = Integer.parseInt(endArray[0]);
            int endMin = Integer.parseInt(endArray[1]);
            
            if (startHour > endHour) {
                return false;
            }
            if (startHour == endHour && startMin >= endMin) {
                return false;
            }
        } catch (Exception e) {
            logService.printLog(e);
            return false;
        }
        return true;
    }
    
    
    @GetMapping("/queryEBankConfig")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.queryEBankConfig")
    @ApiOperation(value ="查询网银业务充值相关设置")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "customerCode",value = "商户编号",dataType = "String",required = true,paramType = "query"),
    })
    public CommonOuterResponse<EBankConfigVo> queryEBankConfig(
            @RequestParam("customerCode") String customerCode){
        
        try {
            EBankConfigVo vo = rcArchiveService.queryEBankConfig(customerCode);
            return CommonOuterResponse.success(vo);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

    
    
    @GetMapping("/rcArchive/queryCheckRechargeCard")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "rcArchive.queryCheckRechargeCard")
    @ApiOperation(value ="查询企业网银充值校验银行卡设置")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "customerCode",value = "商户编号",dataType = "String",required = true,paramType = "query"),
    })
    public CommonOuterResponse<String> queryCheckRechargeCard(
            @RequestParam("customerCode") String customerCode){
        
        //根据Id查找风控档案
        RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.RCTargetType.CUSTOMER_CODE.code, customerCode);
        if (rcArchive == null) {
            return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }

        CommonOuterResponse<String> resp = CommonOuterResponse.success();
        resp.setData(rcArchive.getCheckRechargeCard()); // 配置什么返回什么，空则交易在调用端处理
        return resp;
    }

	@Autowired
	CusCustomerConsumer cusCustomerConsumer;

	@GetMapping("/rcArchive/customerChange")
	public void customerChange(String key, String message) {
		cusCustomerConsumer.consumeMsg(key, message);
	}

	@GetMapping("/exportRiskInfo")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "timeType", value = "选择时间（1：注册时间；2：风控冻结时间；3：账户异常时间）", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "customerType", value = "商户类型（0：商户；2：所属平台商；3：所属代理商）", required = false, dataType = "String", paramType = "query")
	})
	@Logable(businessTag = "exportRiskInfo",outputResult = false)
	public PageResult<RiskInfoExportResponse> exportRiskInfo(@RequestParam(value = "timeType") String timeType,
															 @RequestParam(value = "startTime") String startTime,
															 @RequestParam(value = "endTime") String endTime,
															 @RequestParam(value = "customerType",required = false) String customerType,
															 @RequestParam(value = "customerNo",required = false) String customerNo,
															 @RequestParam(required = false,defaultValue = "false") boolean download,
															 @RequestParam(required = false) String fileName,
															 @RequestParam(required = false,defaultValue = "csv") String type,
															 @RequestParam(required = false) String fileSource,
															 @RequestHeader(value = "x-userid") Long userId,
															 HttpServletRequest httpServletRequest) {
		PageResult page = new PageResult();
		try {
			Map<String,Object> paramMap = new HashMap<>();
			paramMap.put("timeType",timeType);
			if ("1".equals(timeType)) {
				paramMap.put("sort","regTime");
			} else if ("2".equals(timeType)) {
				paramMap.put("sort","lastFrozenTime");
			} else if ("3".equals(timeType)) {
				paramMap.put("sort","lastAbnormalTime");
			} else {
				throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
			}
			if (StringUtils.isBlank(startTime)) {
				startTime = "19700101000000";
			}
			if (StringUtils.isBlank(endTime)) {
				endTime = sdf.format(new Date());
			}
			paramMap.put("startTime",startTime);
			paramMap.put("endTime",endTime);
			paramMap.put("customerType",customerType);
			paramMap.put("customerNo",customerNo);
			if (download) {
				return exportFileService.download(paramMap,fileName,httpServletRequest,exportRcArchiveService);
			}
			page = rcArchiveService.exportRiskInfo(paramMap,download);
		} catch (Exception e) {
			logger.printMessage("导出风控信息查询error：" + e.getMessage());
			logger.printLog(e);
			if (e instanceof AppException) {
				page.setCode(((AppException) e).getErrorCode());
				page.setMessage(((AppException) e).getErrorMsg());
			} else {
				page.setCode(RcCode.SYSTEM_EXCEPTION.code);
				page.setMessage(RcCode.SYSTEM_EXCEPTION.message);
			}
		}
		return page;
	}

	@GetMapping("/getBindCardVo")
	@Logable(businessTag = "getBindCardVo")
	public CommonOuterResponse getBindCardVo(@RequestParam("archiveCode") String archiveCode,
											 @RequestParam("card") String card) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			RcArchive rcArchive = rcArchiveService.selectByCodeOrName(archiveCode,null);
			if(rcArchive == null || StringUtils.isBlank(rcArchive.getArchiveCode())){
				response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
				response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
				return response;
			}
			Map<String,Object> map = new HashMap<>();
			map.put("bindCard",rcArchive.getBindCard());
			map.put("bindCardRatio",rcArchive.getBindCardRatio());
			List<BwList> bwLists = bwListService.getByTagerTypeAndTagerId(RcConstants.RCTargetType.BANK_CARD.code,card);
			if (bwLists.isEmpty()) {
				map.put("isBlack",false);
			} else {
				BwList bwList = bwLists.get(0);
				if ("0".equals(bwList.getBwType()) && !RcConstants.UseStatus.DISABLE.code.equals(bwList.getUseStatus())) {
					map.put("isBlack",true);
					map.put("riskTag",RcConstants.RiskTag.trans(bwList.getRiskTag()));
				}
			}
//			boolean isBlack = bwListService.isBlackList(RcConstants.RCTargetType.BANK_CARD.code,card);
//			map.put("isBlack",isBlack);
			return CommonOuterResponse.success(map);
		}  catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logService.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@GetMapping("/getTemporaryDepositLimit")
	@ApiOperation(value ="获取临时入金限制",notes = "获取临时入金限制")
	@Logable(businessTag = "RcArchiveController.getTemporaryDepositLimit")
	public CommonOuterResponse<TemporaryLimitVo> getTemporaryDepositLimit(@RequestParam(value = "archiveId",required = false) Long archiveId,
																		  @RequestParam(value = "customerNo",required = false) String customerNo,
																		  @RequestParam(value = "source",required = false) String source) {
		try {
			if ("1".equals(source)) {
				return CommonOuterResponse.success(rcArchiveService.preGetTemporaryDepositLimit(customerNo,"1"));
			}
			return CommonOuterResponse.success(rcArchiveService.getTemporaryDepositLimit(archiveId));
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("设置临时入金限制错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@PostMapping("/releaseTemporary")
	@ApiOperation(value ="解除临时入金限制（商户信息管理）",notes = "解除临时入金限制（商户信息管理）")
	@Logable(businessTag = "RcArchiveController.releaseTemporary")
	public CommonOuterResponse releaseTemporary(@RequestParam(value = "customerNo",required = true) String customerNo,
												@RequestHeader(value = "x-userid",required = true) Long userId) {
		try {
			// 获取临时入金限额
			TemporaryLimitVo temporaryLimitVo = rcArchiveService.preGetTemporaryDepositLimit(customerNo,"1");
			if (!RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())) {
				throw new AppException(RcCode.STATE_ERROR.code,RcCode.STATE_ERROR.message);
			}
			temporaryLimitVo.setTemporaryStatus(RcConstants.TemporaryStatus.NORMAL.code);
			temporaryLimitVo.setTemporaryStatusReason("解除风控临时入金限制");
			return rcArchiveService.preTemporaryDepositLimit(temporaryLimitVo,userId);
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("解除风控临时入金限制错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}


	@ApiOperation(value ="设置临时入金限制",notes = "设置临时入金限制")
	@PostMapping("/temporaryDepositLimit")
	@Logable(businessTag = "RcArchiveController.temporaryDepositLimit")
	public CommonOuterResponse temporaryDepositLimit(@Valid @RequestBody TemporaryLimitVo temporaryLimitVo,
													 BindingResult bindingResult,
													 @RequestHeader(value = "x-userid")Long userId) {
		try {
			if (bindingResult.hasErrors()) {
				throw new AppException(RcCode.RC_ARG_ERROR.code,bindingResult.getAllErrors().get(0).getDefaultMessage());
			}
			if ("1".equals(temporaryLimitVo.getSource())) {
				return rcArchiveService.preTemporaryDepositLimit(temporaryLimitVo,userId);
			}
			return rcArchiveService.temporaryDepositLimit(temporaryLimitVo,userId);
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("设置临时入金限制错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@GetMapping("/getTemporaryLimit")
	@Logable(businessTag = "RcArchiveController.getTemporaryLimit")
	public Long getTemporaryLimit(@RequestParam String customerNo,
								  @RequestParam String limitType) {
			return rcArchiveService.getTemporaryLimit(customerNo,limitType);
	}

	/**
	 * Redis限制重复访问
	 * @param rcArchiveId
	 * @param operation
	 */
	private void checkRepeatOperation(Long rcArchiveId,String operation) {
		if (!redisTemplate.opsForValue().setIfAbsent(rcArchiveId + operation,"1")) {
			throw new AppException(RcCode.REPEAT_OPERATION.code,RcCode.REPEAT_OPERATION.message);
		}
		redisTemplate.expire(rcArchiveId + operation,2,TimeUnit.SECONDS);
	}

	private void insertLog(String permId,String code,String name,String type, String operator,
						   Date modifyDate,String content,String oldValue,String newValue) {
		RcOperateLog log = new RcOperateLog();
		log.setPermId(permId);
		log.setCode(code);
		log.setName(name);
		log.setType(type);
		log.setOperator(operator);
		log.setOperateTime(modifyDate);
		log.setOperateContent(content);
		log.setOrigValue(oldValue);
		log.setNewValue(newValue);
		rcOperateLogService.insert(log);
	}

	@PostMapping("/paySetting")
	@Logable(businessTag = "rcArchive.paySetting")
	@ApiOperation(value ="订单转账支付设置",notes = "订单转账支付设置")
	public CommonOuterResponse paySetting(@RequestParam Long archiveId,
										  @RequestParam String setting,
										  @RequestParam String reason,
										  @RequestParam(required = false) String uniqueId,
										  @RequestHeader("x-userid") Long userId) {
		CommonOuterResponse response = new CommonOuterResponse<>();
		try {
			checkRepeatOperation(archiveId,"paySetting");
			User user = ortherService.selectUserById(userId);
			if(user == null){
				throw new AppException(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
			}
			RcArchive rcArchive = rcArchiveService.selectById(archiveId);
			if (rcArchive == null) {
				throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
			}
			if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
					&& RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
				throw new AppException(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
			}

			RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.ORDER_TRANS_PAY_SETTING.code,archiveId);
			if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
				throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
			}
			PaySettingVo oldValue = new PaySettingVo();
			oldValue.setPaySetting(rcArchive.getPaySetting());
			oldValue.setPaySettingReason(rcArchive.getPaySettingReason());
			oldValue.setPaySettingUniqueId(rcArchive.getPaySettingUniqueId());

			PaySettingVo newValue = new PaySettingVo();
			newValue.setPaySetting(setting);
			newValue.setPaySettingReason(reason);
			newValue.setPaySettingUniqueId(uniqueId);

			rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.ORDER_TRANS_PAY_SETTING.code,archiveId,JSON.toJSONString(oldValue)
					,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqueId);

			// 添加待办
			String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
			if ("1".equals(flag)) {

			} else if ("0".equals(flag)){
				rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
			}
			// 日志
			Date modifyDate = new Date();
			if (!Objects.equals(rcArchive.getPaySetting(),setting)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"订单转账支付设置银行卡校验设置",
						rcArchive.getPaySetting() != null ? String.valueOf(rcArchive.getPaySetting()) : null,setting != null ? String.valueOf(setting) : null);
			}
			if (!Objects.equals(rcArchive.getPaySettingReason(),reason)) {
				insertLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",String.valueOf(userId),modifyDate,"订单转账支付设置变更原因",
						rcArchive.getPaySettingReason(),reason);
			}
			return CommonOuterResponse.success();
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("订单转账支付设置错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@GetMapping("/queryPaySetting")
	@Logable(businessTag = "rcArchive.queryPaySetting")
	@ApiOperation(value ="获取订单转账支付设置",notes = "获取订单转账支付设置")
	public CommonOuterResponse<PaySettingResponse> queryPaySetting(@RequestParam Long archiveId,
																   @RequestHeader("x-userid") Long userId) {
		try {
			return CommonOuterResponse.success(rcArchiveService.getPaySetting(archiveId));
		} catch (AppException e) {
			return CommonOuterResponse.fail(e.getErrorCode(),  e.getErrorMsg());
		} catch (Exception e) {
			logger.printMessage("获取订单转账支付设置错误：" + e.getMessage());
			logger.printLog(e);
			return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
		}
	}

	@PostMapping("/testExportWebsiteEffectivenessJob")
	@Logable(businessTag = "testExportWebsiteEffectivenessJob")
	public CommonOuterResponse testExportWebsiteEffectivenessJob() throws Exception {
		exportWebsiteEffectivenessJob.execute(null);
		return CommonOuterResponse.success();
	}
}

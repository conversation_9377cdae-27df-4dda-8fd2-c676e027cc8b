package com.epaylinks.efps.rc.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LimitSwitchStatusResponse {
    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @ApiModelProperty(value = "单日总限额",dataType = "Long")
    private Long daliyValue;

    @ApiModelProperty(value = "单日剩余限额",dataType = "Long")
    private Long remainValue;

    @ApiModelProperty(value = "单笔限额",dataType = "Long")
    private Long singleValue;

    @ApiModelProperty(value = "日限额状态(1:打开；2：关闭)",dataType = "String")
    private String daliyStatus;

    @ApiModelProperty(value = "单笔限额状态(1:打开；2：关闭)",dataType = "String")
    private String singleStatus;
}

package com.epaylinks.efps.rc.controller;


import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.service.BlackListService;
import com.epaylinks.efps.rc.service.BwListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/batchTask")
@Api(value = "RcBatchTaskController", description = "风控操作批量设置")
public class RcBatchTaskController {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private BlackListService blackListService;

    @Autowired
    private BwListService bwListService;

    /**
     * 批量任务列表
     */
    @PostMapping(value = "/batchRcStatus")
    @Logable(businessTag = "RcBatchTaskController.batchRcStatus",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "批量风控状态设置", notes = "批量风控状态设置", httpMethod = "POST")
    public BatchResponse batchRcStatus(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ){
        BatchResponse response = new BatchResponse();
        try {
            logger.printMessage("准备进行批量风控状态设置..");
            response = batchTaskService.batchSaveByFile(file, (short) 26, null, userId);
            if(response!=null){
                response.setDetailMessage(null);
            }
            if(response!=null &&
                    (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                response.setReturnCode("0000");
                response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.printMessage("批量风控状态设置，异常："+e.getLocalizedMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                if(response!=null &&
                        (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                    response.setReturnCode("0000");
                    response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
                }else {
                    response.setReturnCode(((AppException) e).getErrorCode());
                    response.setReturnMsg(((AppException) e).getErrorMsg());
                }
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    /**
     * 批量任务列表
     */
    @PostMapping(value = "/batchAccStatus")
    @Logable(businessTag = "RcBatchTaskController.batchAccStatus",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "批量账户状态设置", notes = "批量账户状态设置", httpMethod = "POST")
    public BatchResponse batchAccStatus(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ){
        BatchResponse response = new BatchResponse();
        try {
            logger.printMessage("准备进行批量账户状态设置..");
            response = batchTaskService.batchSaveByFile(file, (short) 27, null, userId);
            if(response!=null){
                response.setDetailMessage(null);
            }
            if(response!=null &&
                    (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                response.setReturnCode("0000");
                response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.printMessage("批量账户状态设置，异常："+e.getLocalizedMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                if(response!=null &&
                        (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                    response.setReturnCode("0000");
                    response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
                }else {
                    response.setReturnCode(((AppException) e).getErrorCode());
                    response.setReturnMsg(((AppException) e).getErrorMsg());
                }
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @PostMapping(value = "/batchBlackList")
    @Logable(businessTag = "RcBatchTaskController.batchBlackList")
    @ApiOperation(value = "黑名单库批量", notes = "黑名单库批量", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "CARDHOLDER：持卡人；MERCHANT：商户", required = false, dataType = "Short", paramType = "query")
    })
    public BatchResponse batchBlackList(@RequestPart(value = "file") MultipartFile file,
                                        @RequestParam() String type,
                                        @RequestHeader(value = "x-userid",defaultValue = "0") Long userId) {
        BatchResponse response = new BatchResponse();
        try {
            response = blackListService.getBlackList(file,type,userId);
        } catch (Exception e) {
            logger.printMessage("黑名单库批量异常：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }
}

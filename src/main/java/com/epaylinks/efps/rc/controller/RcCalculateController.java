package com.epaylinks.efps.rc.controller;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.BwList.BwType;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcCalculateLog;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.job.RcIndexResetJob;
import com.epaylinks.efps.rc.kafka.KafkaProducer;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.service.export.ExportRcCalculateService;
import com.epaylinks.efps.rc.service.rccalculate.ApplyMerchantRc;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.CardUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class RcCalculateController {
    @Autowired
    private BwListService bwListService;
    @Autowired
    private RcLimitService rcLimitService;
    @Autowired
    private RcArchiveService rcArchiveService;
    @Autowired
    private RcCalculteBasic basicService;
    @Autowired
    private LogService logService;
    @Autowired
    private EarlyWarningService earlyWarningService;
    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;
    @Autowired
    private KafkaProducer kafkaProducer;
    @Autowired
    private RcIndexResetJob taskJob;
    @Autowired
    @Lazy
    RcCalculateController self;
    @Autowired
    private GreyRcService greyRcService;
    @Autowired
    private ApplyMerchantRc applyMerchantRc;
    @Autowired
    private TempLimitService tempLimitService;
    @Autowired
    private CustomerManageLimitService customerManageLimitService;
    @Autowired
    private Transfer259LimitService transfer259LimitService;
    @Autowired
    private ExportFileService exportFileService;
    @Autowired
    private ExportRcCalculateService exportRcCalculateService;

    @Autowired
    private HessianService hessianService;

    private static final String RC_HAND_UP_TERMINAL = "rcHandUpTerminal"; // 风控系统停用终端
    @Autowired
    private OtherMapper otherMapper;


    /**
     * 返回false代表风控计算结果不通过
     * 返回true代表风控结果通过
     *
     * @param
     * @return
     */
    @PostMapping("/calculate")
    @Exceptionable
    @Logable(businessTag = "calculate")
    public boolean calculate(@RequestBody List<RcCalculateRequest> rcCalculateRequests) {
        CommonOuterResponse<?> result = self.calculateResponse(rcCalculateRequests);
        return result.isSuccess();
    }

    private boolean isCustomerWhite(String customerCode) {
        Map<String, Boolean> whiteMap = bwListService.queryValueInBw(BwType.WHITE.code, ImmutableList.of(customerCode));
        return whiteMap.get(customerCode);
    }

    /**
     * 返回false代表风控计算结果不通过
     * 返回true代表风控结果通过
     *
     * @param
     * @return
     */
    @PostMapping("/calculateResponse")
    @Exceptionable
    @Logable(businessTag = "calculateResponse")
    public CommonOuterResponse<?> calculateResponse(@RequestBody List<RcCalculateRequest> rcCalculateRequests) {

        CommonOuterResponse<?> response = CommonOuterResponse.success("通过风控");

        Boolean wResult = null;
        List<Long> successRcLimitIds = new ArrayList<>();
        Map<String, Map<String, String>> successParams = new HashMap<>();
        Map<Long, String> successTargetTypeMap = new HashMap<>();
        Map<Long, String> successTargetIdMap = new HashMap<>();
        for (int i = 0; i < rcCalculateRequests.size(); i++) {

            RcCalculateRequest rcCalculateRequest = rcCalculateRequests.get(i);
            if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(rcCalculateRequest.getBusinessType())) {
                tempLimitService.calculate(RcCalculateRequestWrapper.wrap(rcCalculateRequest));
            }

            //进件风控
            Optional<CommonOuterResponse<?>> applyMerchantResponse = applyMerchantRc.getApplyMerchantCalcResponse(RcCalculateRequestWrapper.wrap(rcCalculateRequest));
            if (applyMerchantResponse.isPresent()) {
                return applyMerchantResponse.get();
            }

            //灰名单风控
            Optional<CommonOuterResponse<Object>> checkGreyResponse = greyRcService.getCheckGreyResponse(rcCalculateRequest);
            if (checkGreyResponse.isPresent()) {
                return checkGreyResponse.get();
            }

            //内部黑名单风控
            Optional<CommonOuterResponse<Object>> checkInnerBlackList = greyRcService.checkInnerBlackList(rcCalculateRequest);
            if (checkInnerBlackList.isPresent()) {
                return checkInnerBlackList.get();
            }

            Map<String, String> businessTargetIds = rcCalculateRequest.getBusinessTargetIds();
            String customerCode = businessTargetIds.get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);

            //增加商户名称风控
            businessTargetIds.put(RcConstants.BusinessTagerType.CUSTOMER_NAME.code, customerInfo.getName());

            //商管的限额
            customerManageLimitService.calculate(customerInfo, RcCalculateRequestWrapper.wrap(rcCalculateRequest));

            //个人账户转换为个人风控
            if (Constants.customerCategory.EFPS_PERSON.code.equals(customerInfo.getCustomerCategory())) {
                businessTargetIds.remove(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
                businessTargetIds.put(RcConstants.BusinessTagerType.PERSON.code, customerCode);
            } else if (customerInfo.getClientNo() != null) { //非个人，增加客户号风控
                businessTargetIds.put(RcConstants.BusinessTagerType.CLIENT_NO.code, customerInfo.getClientNo());

                //259 代付交易管控
                transfer259LimitService.calculate(RcCalculateRequestWrapper.wrap(rcCalculateRequest));
            }

            String amount = basicService.getRcIndex(rcCalculateRequest.getIndexs(), RcConstants.RcIndex.AMOUNT);

            boolean isCustomerWhite = isCustomerWhite(customerCode);

            // 预警判断 2020.11.26
            try {
                if (Constants.rcBusinessType.LOG_IN.code.equals(rcCalculateRequest.getBusinessType())) {
                    earlyWarningService.checkLoginCountWarning(customerCode, rcCalculateRequest.getBusinessType());
                } else if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(rcCalculateRequest.getBusinessType())) {
                    if (amount != null && !isCustomerWhite) { //商户编号白名单不预警
                        earlyWarningService.checkSingleAmountWarning(customerCode,
                                rcCalculateRequest.getBusinessType(), rcCalculateRequest.getBusinessCode(), Long.parseLong(amount));
                    }
                }
            } catch (Exception e) {
                logService.printLog(e);
            }

            List<String> list = new ArrayList<>();
            List<String> listForWhiteCustomer = new ArrayList<>();
            Map<String, String> targetsTypeMap = new HashMap<>(); // 保存风控对象类型，如：56xxx，商户号
            Set<String> businessTargetKeys = businessTargetIds.keySet();
            for (String key : businessTargetKeys) {
                //如果入参的参数是需要检验的业务对象类型
                if (RcConstants.BusinessTagerType.contains(key)) {
                    list.add(businessTargetIds.get(key));
                    targetsTypeMap.put(businessTargetIds.get(key), key);
                    //处于白名单的商户编号，交易时不参与相关证件限额、相关终端限额的控制
                    if (isCustomerWhite && !(RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(key) ||
                            RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(key) ||
                            RcConstants.BusinessTagerType.TERM.code.equals(key))) {
                        listForWhiteCustomer.add(businessTargetIds.get(key));
                    }
                }
            }

            // 黑白名单列表增加商户证件号  2021.2.4
            if (customerInfo != null && customerInfo.getBusinessLicenseNo() != null  // 小微不需要校验营业执照号
                    && !Constants.CustomerType.MICRO.code.toString().equals(String.valueOf(customerInfo.getCustomerType()))) {
                list.add(customerInfo.getBusinessLicenseNo());
                targetsTypeMap.put(customerInfo.getBusinessLicenseNo(), RcConstants.BusinessTagerType.BUSINESS_LICENSE.code);
            }
            if (customerInfo != null && customerInfo.getLeaPersoniDentificationNo() != null
                    && Constants.CertificateType.ID.code.toString().equals(String.valueOf(customerInfo.getLeaPersoniDentificationType()))) {
                list.add(customerInfo.getLeaPersoniDentificationNo());
                targetsTypeMap.put(customerInfo.getLeaPersoniDentificationNo(), RcConstants.BusinessTagerType.IDENTITY_CARD.code);
            }

            // 查询风控对象是否命中黑名单
            Map<String, Boolean> blackMap = bwListService.queryValueInBw(BwType.BLACK.code, list);
            Set<String> blackMapKey = blackMap.keySet();
            for (String key : blackMapKey) {
                if (blackMap.get(key)) {
                    RcArchive rcArchive = rcArchiveService.selectByCodeOrName(key, null);
                    if (rcArchive == null) {
                        //如果为空说明这个黑名单命中的值不是一个单独的维度，应该就是商户维度
                        rcArchive = rcArchiveService.selectByCodeOrName(customerCode, null);
                        if (rcArchive == null) {
                            //如果商户维度也为空，基本上就说明参数有问题，但是还是要插入一笔订单
                            rcArchive = new RcArchive();
                            rcArchive.setArchiveCode("未知");
                            rcArchive.setArchiveType("未知");
                            rcArchive.setArchiveName("未知");
                        }
                    }
                    // 根据黑名单返回黑名单类型
                    String targetType = targetsTypeMap.get(key);
                    RcConstants.BusinessTagerType businessTagerType = RcConstants.BusinessTagerType.getBusinessTagerTypeByCode(targetType);

                    String riskTagsDesc = null;
                    List<String> bwRiskTags = bwListService.getBwRiskTags(targetType, key);
                    if (bwRiskTags != null && !bwRiskTags.isEmpty()) {
                        riskTagsDesc = "(" + Joiner.on(",").join(bwRiskTags) + ")";
                    }
                    logService.printLog("print:" + key + "-" + businessTagerType.code);
                    String keyHidden = key;
                    String keyEncrypt = key;
                    if ("001".equals(businessTagerType.code)) {
                        keyHidden = CardUtils.getHiddenCreditCardNo(key);
                    } else if ("002".equals(businessTagerType.code)) {
                        keyHidden = CardUtils.getHiddenMobilePhone(key);
                    } else if ("004".equals(businessTagerType.code)) {
                        keyHidden = CardUtils.getHiddenBankCardNo(key);
                    }

                    String message = "RC" + businessTagerType.message + "存在风险";
                    String remark = "RC" + businessTagerType.message + keyHidden + "存在风险";
                    if (riskTagsDesc != null) {
                        message += riskTagsDesc;
                        remark += riskTagsDesc;
                    }
                    try {
                        keyEncrypt = hessianService.symmetricEncryptData(key);
                    } catch (Exception e ) {
                        e.printStackTrace();
                    }
                    basicService.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(), rcArchive.getArchiveName(), businessTagerType.blackType.defineCode, "是", "/", amount, false, remark, rcArchive.getArchiveType(),keyEncrypt);

                    //只要某个指标存在于黑名单，直接风控不过

                    response.setReturnCode(RcCode.RC_CACLCUELATE_ERROR.code);
                    response.setReturnMsg(message);
                    return response;
                }
            }

            //进行白名单校验
            List<String> notExistWhiteBusinessTargetId = new ArrayList<>();
            Map<String, Boolean> whiteMap = bwListService.queryValueInBw(BwType.WHITE.code, isCustomerWhite ? listForWhiteCustomer : list);
            Set<String> whiteMapKey = whiteMap.keySet();
            for (String key : whiteMapKey) {
                if (!whiteMap.get(key) || (isCustomerWhite && key.equals(customerCode))) { //账户状态和风控状态不受白名单控制
                    wResult = false;
                    notExistWhiteBusinessTargetId.add(key);
                }
            }
            if ((i == rcCalculateRequests.size() - 1) && wResult == null) {
                //集合中的所有业务对象id都校验过了，都在白名单中
                wResult = true;
            }
            if (wResult != null && wResult == true) {
                //如果全部参数都在白名单校验结果中
                return response;
            }

            for (String buginessTargetId : notExistWhiteBusinessTargetId) {
                // 非小微不对身份证对象校验 ，但前面需要校验黑白名单
                if (!Constants.CustomerType.MICRO.code.toString().equals(String.valueOf(customerInfo.getCustomerType()))
                        && Constants.CertificateType.ID.code.toString().equals(targetsTypeMap.get(buginessTargetId))) {
                    continue;
                }
                //获取所有不在黑白名单中的业务对象的指标，进行指标的计算
                //不存在于黑白名单,获取计算的所有指标
                List<RcLimit> rcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(rcCalculateRequest.getBusinessType(), targetsTypeMap.get(buginessTargetId), buginessTargetId);
                if (rcLimits == null || rcLimits.isEmpty()) {
                    continue;
                }
                if (isCustomerWhite && buginessTargetId.equals(customerCode)) { //白名单商户只检查账户状态和风控状态
                    rcLimits = rcLimits.stream().filter(rcLimit -> "ACCOUNT-STATUS".equals(rcLimit.getDefineCode()) || "RC-STATUS".equals(rcLimit.getDefineCode())).collect(Collectors.toList());
                }
              /*// 前面黑白名已添加证件号到notExistWhiteBusinessTargetId，以下代码注释，否则会重复计算。
                 RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, buginessTargetId);
				if (rcArchive != null && rcArchive.getCertificateNo() != null) {
                    List<RcLimit> certLimits = rcLimitService.queryRcLimit(RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcArchive.getCertificateNo(), "WD");
                    List<RcLimit> licenseLimits = rcLimitService.queryRcLimit(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code, rcArchive.getCertificateNo(), "WD");
                    if (certLimits != null) {
                        rcLimits.addAll(certLimits);
                    }
                    if (licenseLimits != null) {
                        rcLimits.addAll(licenseLimits);
                    }
                }*/

                if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetsTypeMap.get(buginessTargetId))) {
                    if (customerInfo.getCustomerType() != null) {
                        int ct = customerInfo.getCustomerType().intValue();

                        List<RcLimit> industryRcLimits = null;

                        if (ct == Constants.CustomerType.MICRO.code ||
                                ct == Constants.CustomerType.BUSINESSMAN.code) {
                            //小微和个体工商户取MCC风控限额
                            Customer customer = otherMapper.queryCustomerByCustomerNo(buginessTargetId);
                            if (customer != null && customer.getMcc() != null) {
                                industryRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(rcCalculateRequest.getBusinessType(),
                                        RcConstants.BusinessTagerType.INDUSTRY.code, customerInfo.getCustomerType() + "_" + customer.getMcc());
                            }
                        } else if (ct == Constants.CustomerType.ENTERPRISE.code ||
                                ct == Constants.CustomerType.ABROAD.code ||
                                ct == Constants.CustomerType.GOVERNMENT.code ||
                                ct == Constants.CustomerType.OTHERS.code) {
                            //取行业类别风控限额
                            String industry = otherMapper.selectInnerBizType(buginessTargetId);
                            if (industry != null) {
                                industryRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(rcCalculateRequest.getBusinessType(),
                                        RcConstants.BusinessTagerType.INDUSTRY.code, customerInfo.getCustomerType() + "_" + industry);
                            }
                        }

                        if (!CollectionUtils.isEmpty(industryRcLimits)) {
                            for (RcLimit rcLimit : rcLimits) {
                                for (RcLimit industryRcLimit : industryRcLimits) {
                                    if (industryRcLimit.getDefineCode().equals(rcLimit.getDefineCode())) {
                                        try {
                                            if (Long.parseLong(industryRcLimit.getLimitValue()) < Long.parseLong(rcLimit.getLimitValue())) {
                                                rcLimit.setLimitValue(industryRcLimit.getLimitValue());
                                            }
                                        } catch (Exception ignore) {
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (!Constants.BusinessCode.FZ.code.equals(rcCalculateRequest.getBusinessCode())) {
                        //商户本身是平台商
                        if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) {
                            List<RcLimit> platRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(rcCalculateRequest.getBusinessType(),
                                    RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, buginessTargetId);
                            rcLimits.addAll(platRcLimits);
                        } else { //商户有平台商
                            if (customerInfo.getPlatCustomerCode() != null) { //商户的平台商加入风控
                                List<RcLimit> platRcLimits = rcLimitService.queryByBusinessTypeAndTaregetId(rcCalculateRequest.getBusinessType(),
                                        RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, customerInfo.getPlatCustomerCode());
                                rcLimits.addAll(platRcLimits);
                            }
                        }
                    }
                }


                Map<String, String> paramsMap = rcCalculateRequest.getIndexs();
                if (paramsMap != null) {
                    paramsMap.put("businessCode", rcCalculateRequest.getBusinessCode());
                    paramsMap.put("payMethod", rcCalculateRequest.getPayMethod());
                    paramsMap.put("customerCode", buginessTargetId);
                    Map<String, String> map = rcCalculateRequest.getBusinessTargetIds();
                    Set<String> mapSet = map.keySet();
                    for (String key : mapSet) {
                        if (RcConstants.BusinessTagerType.BANK_CARD.code.equals(key)) {// 迭代商户的情况增加银行卡号，同卡代付限额处理
                            paramsMap.put("bankCardNo", businessTargetIds.get(RcConstants.BusinessTagerType.BANK_CARD.code));
                        } else if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(key)) {// 迭代商户的情况增加银行卡号，同卡代付限额处理
                            paramsMap.put("identityCard", businessTargetIds.get(RcConstants.BusinessTagerType.IDENTITY_CARD.code));
                        }
                    }
                }

                boolean resultFlag = true;
                String result = null;
                for (RcLimit rcLimit : rcLimits) {
                    //根据指标找到对应的指标定义
                    RcCalculate rcCalculate = (RcCalculate) SpringContextUtils.getBean(rcLimit.getDefineCode());
                    result = rcCalculate.calculate(rcLimit, rcCalculateRequest);
                    if (CommonOuterResponse.SUCCEE.equals(result)) {
                        successRcLimitIds.add(rcLimit.getDefineId());
                        successTargetIdMap.put(rcLimit.getDefineId(), rcLimit.getBusinessTagerId());
                        successTargetTypeMap.put(rcLimit.getDefineId(), rcLimit.getBusinessTagerType());
                        successParams.put(rcLimit.getDefineId() + ":" + rcLimit.getBusinessTagerType() + ":" + rcLimit.getBusinessTagerId(), paramsMap);
                    } else {
                        resultFlag = false;
                        break;
                    }
                }

                if (!resultFlag) {
                    // 触发风控，增加对终端的禁用处理 2021.4.21
                    if (RcConstants.BusinessTagerType.TERM.code.equals(targetsTypeMap.get(buginessTargetId))) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("terminalCode", buginessTargetId);
                        kafkaProducer.send("CUS_CustomerChange", RC_HAND_UP_TERMINAL, jsonObject);
                    }

                    for (Long definId : successRcLimitIds) {
                        Map<String, String> p = successParams.get(definId + ":" + successTargetTypeMap.get(definId) + ":" + successTargetIdMap.get(definId));
                        if (p != null) {
                            paramsMap = p;
                        }
                        RcLimit codeLimit = rcLimitService.queryByTargerIdIfNullByLevel(definId, successTargetTypeMap.get(definId), successTargetIdMap.get(definId));
                        RcIndexAddValue rcIndexAddValue = (RcIndexAddValue) SpringContextUtils.getBean(codeLimit.getDefineCode());
                        paramsMap.put("payState", "01");
                        rcIndexAddValue.calculateValue(codeLimit, paramsMap);
                    }

                    response.setReturnCode(RcCode.RC_CACLCUELATE_ERROR.code);
                    response.setReturnMsg(result);
                    return response;
                }
            }
        }
        return response;
    }


    @GetMapping("/rcCalculate/queryLog")
    @Exceptionable
    @Logable(businessTag = "queryLog", outputResult = false)
    @Validatable
    @ApiOperation(value = "分页查询风控拦截日志")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "targetTypeCode", value = "风控对象类型", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "businessTargetId", value = "对象编号", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "transactionNo", value = "订单号", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "limitTypeCode", value = "触发类型", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "startTime", value = "开始时间", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "pageNum", value = "开始页面", required = true, dataType = "int", paramType = "query"),
                    @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),

            })
    public PageResult<RcCalculateLog> queryLog(String targetTypeCode, String businessTargetId, String transactionNo, String limitTypeCode, String startTime, String endTime, int pageNum, int pageSize) {
        Map<String, String> map = new HashMap<>();
        map.put("targetTypeCode", targetTypeCode);
        map.put("businessTargetId", businessTargetId);
        map.put("transactionNo", transactionNo);
        map.put("limitTypeCode", limitTypeCode);
        map.put("startTime", startTime);
        map.put("endTime", endTime);

        //当前页面记录
        int endNum = pageSize * pageNum;
        int startNum = endNum - pageSize + 1;

        PageResult<RcCalculateLog> page = rcArchiveService.queryCalculateLog(startNum, endNum, map);
        return page;
    }


    @GetMapping("/rcCalculate/exportLog")
    @Exceptionable
    @Logable(businessTag = "exportLog")
    @Validatable
//    @DownloadAble
    @ApiOperation(value = "导出风控拦截日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "targetTypeCode", value = "风控对象类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "businessTargetId", value = "对象编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "transactionNo", value = "订单号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "limitTypeCode", value = "触发类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称(带文件类型后缀，例如.csv)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如csv", required = false, dataType = "String", paramType = "query")})
    public PageResult<RcCalculateLog> exportLog(
            String targetTypeCode, String businessTargetId, String transactionNo,
            String limitTypeCode, String startTime, String endTime,
            @RequestParam(required = false) boolean download,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String type,
            @RequestHeader(value = "x-userid", required = true) Long userId,
            HttpServletRequest httpServletRequest) {

        Map<String, String> map = new HashMap<>();
        map.put("targetTypeCode", targetTypeCode);
        map.put("businessTargetId", businessTargetId);
        map.put("transactionNo", transactionNo);
        map.put("limitTypeCode", limitTypeCode);
        map.put("startTime", startTime);
        map.put("endTime", endTime);

        if (download) {
            return exportFileService.download(map,fileName,httpServletRequest,exportRcCalculateService);
        }
        PageResult<RcCalculateLog> page = rcArchiveService.exportCalculateLog(map);
        if (!CommonOuterResponse.SUCCEE.equals(page.getCode())) {
            throw new AppException(page.getMessage(), page.getCode()); // 异常往抛，否则下载无提示错误
        }
        return page;
    }

    @PostMapping("/rcCalculate/saveLockRcLog")
    @Exceptionable
    @Logable(businessTag = "saveLockRcLog")
    @Validatable
    @ApiOperation(value = "新增风控锁定日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessTargetId", value = "对象编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "targetName", value = "对象名称", dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse saveLockRcLog(String businessTargetId, String targetName) {

        try {
            basicService.insertCalculateLog("/", businessTargetId,
                    targetName, RcConstants.RcLimitDefineType.ACCOUNT_STATUS.defineCode, "锁定", "/", "/", false, "账户锁定", RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        return CommonOuterResponse.success();
    }

    @PostMapping("/rcCalculate/testResetIndex")
    @Exceptionable
    @Logable(businessTag = "testResetIndex")
    @Validatable
    @ApiOperation(value = "重置指标累计值(测试)")
    @ApiImplicitParams({})
    public CommonOuterResponse testResetIndex() {

        try {
            TaskRequest request = new TaskRequest();
            taskJob.execute(request);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        return CommonOuterResponse.success();
    }

}

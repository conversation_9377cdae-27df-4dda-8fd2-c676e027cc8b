package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.domain.RiskEventRecord;
import com.epaylinks.efps.rc.domain.RiskEventSummary;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.service.DataAuthService;
import com.epaylinks.efps.rc.service.EarlyWarningService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.export.ExportRiskEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/riskEventData")
@Api(value = "RiskEventRuleController", description = "风控管理-可疑事件监控记录控制类")
public class RiskEventRecordController {

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private OtherService otherService;

    @Autowired
    private LogService logService;

    @Autowired
    private EarlyWarningService earlyWarningService;

    @Autowired
    private ExportFileService exportFileService;

    @Autowired
    private ExportRiskEventService exportRiskEventService;

    @Autowired
    private DataAuthService dataAuthService;
    

    @GetMapping("/pageQuery")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRecordController.pageQuery")
    @ApiOperation(value ="分页查询可疑事件监控记录")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "pageNum", value = "开始页面", required = true, dataType = "int", paramType = "query"),
                    @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
                    @ApiImplicitParam(name = "ruleType",value = "规则分类",dataType = "String",paramType = "query"),
                    @ApiImplicitParam(name = "triggerAction", value = "措施",dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "searchKey", value = "触发商户", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "startTime", value = "开始时间，格式：yyyyMMddhhmmss", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "endTime", value = "结束时间，格式：yyyyMMddhhmmss", dataType = "String", paramType = "query"),
            })
    public PageResult<RiskEventRecord> pageQuery(int pageNum, int pageSize, String ruleType, String triggerAction, String searchKey,
                                                String startTime, String endTime, @RequestHeader(value = "x-userid")Long userId){
        PageResult<RiskEventRecord> pageResult = new PageResult<>();
        //校验开始时间格式
        if(StringUtils.isNotBlank(startTime) && !StringUtils.isNumeric(startTime)){
            pageResult.setCode(RcCode.PARAM_ERROR.code);
            pageResult.setMessage(RcCode.PARAM_ERROR.message);
            return pageResult;
        }
        //校验结束时间格式
        if(StringUtils.isNotBlank(endTime) && !StringUtils.isNumeric(endTime)){
            pageResult.setCode(RcCode.PARAM_ERROR.code);
            pageResult.setMessage(RcCode.PARAM_ERROR.message);
            return pageResult;
        }
        if(userId==null){
            userId = 1L;
        }
        //根据userId查询user
        User user = otherService.selectUserById(userId);
        if(user == null){
            pageResult.setCode(RcCode.USER_NOT_EXIXT.code);
            pageResult.setMessage(RcCode.USER_NOT_EXIXT.message);
            return pageResult;
        }
        try {
            //当前页面记录
            int endNum = pageSize * pageNum;
            int startNum = endNum - pageSize + 1;
            //分页查询可疑事件监控记录
            PageResult<RiskEventRecord> page = riskEventRecordService.pageQueryEventRecord(startNum,endNum,
                    ruleType, triggerAction, searchKey, startTime, endTime, user.getName(),userId);
            return page;
        } catch (AppException e) {
            pageResult.setCode(e.getErrorCode());
            pageResult.setMessage(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            pageResult.setCode(RcCode.SYSTEM_EXCEPTION.code);
            pageResult.setMessage(RcCode.SYSTEM_EXCEPTION.message+":"+e.getMessage());
        }
        return pageResult;
    }

    @PostMapping("/addEventRecord")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRecordController.addEventRecord")
    @ApiOperation(value ="新增可疑事件监控记录")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleCode",value = "规则代码", dataType = "String",required = true,paramType = "query"),
                    @ApiImplicitParam(name = "triggerCustCode",value = "触发商户编号", dataType = "String",required = false,paramType = "query"),
                    @ApiImplicitParam(name = "triggerCustName",value = "触发商户名称", dataType = "String",required = false,paramType = "query"),
                    @ApiImplicitParam(name = "triggerValue",value = "触发值", dataType = "String",required = false,paramType = "query"),
            })
    public CommonOuterResponse addEventRecord(String ruleCode, String triggerCustCode, String triggerCustName, String triggerValue){
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            int res = riskEventRecordService.addEventRecord(ruleCode,triggerCustCode,triggerCustName,triggerValue);
            response.setData("新增可疑事件监控记录条数："+res);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message+":"+e.getMessage());
        }
        return response;
    }
    
    @PostMapping("/batchModify")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRecordController.batchModify")
    @ApiOperation(value ="批量修改可疑事件记录", notes = "批量修改可疑事件记录（暂处理结果）", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids",value = "批量ID，多个使用英文逗号隔开", dataType = "String",required = true,paramType = "query"),
        @ApiImplicitParam(name = "dealResult",value = "处理结果", dataType = "String",required = false,paramType = "query"),
    })
    public CommonOuterResponse batchModify(
            @RequestParam( value = "ids", required = true) String ids,
            @RequestParam( value = "dealResult", required = false) String dealResult,
            @RequestHeader(value = "x-userid", required = true) Long userId){
        
        try {
            int res = riskEventRecordService.batchModify(ids, dealResult, userId);
            return CommonOuterResponse.success();
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return  CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }
    
    
    
    @GetMapping("/pageSummary")
    @Validatable
    @Exceptionable
//    @DownloadAble
    @Logable(businessTag = "RiskEventRecordController.pageSummary")
    @ApiOperation(value ="分页查询可疑事件监控汇总")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "pageNum", value = "开始页面", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "ruleCode",value = "规则编码", dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "ruleType",value = "规则分类", dataType = "String",paramType = "query"),
        @ApiImplicitParam(name = "searchKey", value = "触发商户", dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "startTime", value = "开始时间，格式：yyyyMMddhhmmss", dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "endTime", value = "结束时间，格式：yyyyMMddhhmmss", dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
        @ApiImplicitParam(name = "fileName", value = "导出文件名称(带文件类型后缀，例如.csv)", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "type", value = "导出文件类型，例如csv", required = false, dataType = "String", paramType = "query")
    })
    public PageResult<RiskEventSummary> pageSummary(
            int pageNum, int pageSize,
            String ruleCode,
            String ruleType,
            String searchKey,
            String startTime, 
            String endTime,
            @RequestParam(required = false) boolean download,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String type,
            @RequestHeader(value = "x-userid") Long userId,
            HttpServletRequest httpServletRequest){

        PageResult<RiskEventSummary> pageResult = new PageResult<>();
        try {
            //当前页面记录
            int endNum = pageSize * pageNum;
            int startNum = endNum - pageSize + 1;
            
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("endNum", endNum);
            paramMap.put("startNum", startNum);
            paramMap.put("ruleCode", ruleCode);
            paramMap.put("ruleType", ruleType);
            paramMap.put("searchKey", searchKey);
            paramMap.put("startTime", startTime);
            paramMap.put("endTime", endTime);

            dataAuthService.setMapParam(paramMap,userId);

            if (download) {
                return exportFileService.download(paramMap,fileName,httpServletRequest,exportRiskEventService);
            }
            //分页查询可疑事件监控记录
            PageResult<RiskEventSummary> page = riskEventRecordService.pageRiskEventSummary(paramMap, download);
            return page;
        } catch (AppException e) {
            pageResult.setCode(e.getErrorCode());
            pageResult.setMessage(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            pageResult.setCode(RcCode.SYSTEM_EXCEPTION.code);
            pageResult.setMessage(RcCode.SYSTEM_EXCEPTION.message+":"+e.getMessage());
        }
        return pageResult;
    }
    
    
    @PostMapping("/testCheckTxsOrderAmountWarning")
    @Logable(businessTag = "RiskEventRecordController.testCheckTxsOrderAmountWarning")
    @ApiOperation(value ="测试交易控制预警可疑事件监控记录")
    @ApiImplicitParams(
    {
            @ApiImplicitParam(name = "triggerCustCode",value = "触发商户编号", dataType = "String",required = true, paramType = "query"),
            @ApiImplicitParam(name = "businessType",value = "业务类型", dataType = "String",required = true,paramType = "query"),
            @ApiImplicitParam(name = "businessCode",value = "业务编码", dataType = "String",required = true,paramType = "query"),
            @ApiImplicitParam(name = "amount",value = "交易金额", dataType = "Long",required = true,paramType = "query"),
    })
    public CommonOuterResponse testCheckTxsOrderAmountWarning(
            String triggerCustCode, String businessType, String businessCode, Long amount){
        
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            
            earlyWarningService.checkTxsOrderAmountWarning(triggerCustCode, businessType, businessCode, amount);
            return CommonOuterResponse.success();
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message+":"+e.getMessage());
        }
        return response;
    }

}

package com.epaylinks.efps.rc.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class PaySettingResponse {
    @ApiModelProperty(value = "校验设置(0：不校验；1：需校验（非同名卡）；2：需校验所有卡；3：只允许对公账户)",dataType = "String")
    private String paySetting;

    @ApiModelProperty(value = "原因",dataType = "String")
    private String reason;

    private String uniqueId;

    private String url;

    private String fileName;
}

package com.epaylinks.efps.rc.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TemporaryLimitResponse {
    private String customerNo;

    @ApiModelProperty(value = "临时入金状态【1：正常；2：限制临时入金】",dataType = "String")
    private String temporaryStatus;

    @ApiModelProperty(value = "支付宝单日入金最高限额",dataType = "Long")
    private Long alipaySingleMaxLimit;

    @ApiModelProperty(value = "微信单日入金最高限额",dataType = "Long")
    private Long wechatSingleMaxLimit;

    @ApiModelProperty(value = "银联二维码单日入金最高限额",dataType = "Long")
    private Long unionCodeSingleMaxLimit;

    @ApiModelProperty(value = "银联在线单日入金最高限额",dataType = "Long")
    private Long unionOnlineSingleMaxLimit;

    @ApiModelProperty(value = "快捷支付单日入金最高限额",dataType = "Long")
    private Long quickPaySingleMaxLimit;

    @ApiModelProperty(value = "网银支付单日入金最高限额",dataType = "Long")
    private Long bankPaySingleMaxLimit;
}

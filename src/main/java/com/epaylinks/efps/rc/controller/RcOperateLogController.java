package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcOperateLogService;
import com.epaylinks.efps.rc.vo.OperateLogVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志控制器
 *
 * <AUTHOR>
 * @date 2020-06-10 17:38
 */
@RestController
@RequestMapping("/operateLog")
public class RcOperateLogController {

    @Autowired
    private OtherService ortherService;

    @Autowired
    private RcOperateLogService rcOperateLogService;

    @Autowired
    private CommonLogger logger;

//    @GetMapping("/pageQuery")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "operateLog.pageQuery", outputResult = false)
    @ApiOperation(value = "分页查询")
    @DownloadAble
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "pageNum", value = "当前页面", dataType = "int", required = true, paramType = "query"),
                    @ApiImplicitParam(name = "pageSize", value = "页面大小", dataType = "int", required = true, paramType = "query"),
                    @ApiImplicitParam(name = "startTime", value = "开始时间yyyy-MM-dd HH:mm:ss", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "endTime", value = "结束时间yyyy-MM-dd HH:mm:ss", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "permId", value = "菜单ID【80205：风险核验】", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "code", value = "商户编号/其他号码", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "name", value = "商户名称/其他名称", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "download", value = "是否导出，true/false", dataType = "Boolean", paramType = "query"),
                    @ApiImplicitParam(name = "fileName", value = "导出文件名称",  dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "type", value = "导出文件类型，例如.csv",  dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "fileSource", value = "文件来源",  dataType = "String", paramType = "query")
            })
    public PageResult<OperateLogVo> pageQuery(@RequestHeader("x-userid") Long userId, Integer pageNum, Integer pageSize, String startTime, String endTime,
                                              String permId, String code, String name, boolean download, String fileName, String type, String fileSource) {
        PageResult<OperateLogVo> response = new PageResult();
        //根据userId查询user
        User user = ortherService.selectUserById(userId);
        if (user == null) {
            response.setCode(RcCode.USER_NOT_EXIXT.code);
            response.setErrorMsg(RcCode.USER_NOT_EXIXT.message);
            return response;
        }
        try {
            Map<String, String> params = new HashMap(7);
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            int endRowNo = pageNum * pageSize;
            params.put("beginRowNo", String.valueOf(beginRowNo));
            params.put("endRowNo", String.valueOf(endRowNo));
            if (StringUtils.isNotBlank(startTime)) {
                params.put("startTime", startTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                params.put("endTime", endTime);
            }
            if (StringUtils.isNotBlank(permId)) {
                params.put("permId", permId);
            }
            if (StringUtils.isNotBlank(code)) {
                params.put("code", code);
            }
            if (StringUtils.isNotBlank(name)) {
                params.put("name", name);
            }
            response.setTotal(rcOperateLogService.totalOperateLog(params));
            if(download) {
                params.put("endRowNo", String.valueOf(response.getTotal()));
            }
            response.setRows(rcOperateLogService.getOperateLogVoList(params));
            response.setCode(CommonOuterResponse.SUCCEE);
            response.setMessage("查询成功!");
        } catch (AppException e) {
            response.setCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("查询风控操作日志错误：" + e.getMessage());
            logger.printLog(e);
            response.setCode(RcCode.QUERY_OPERATELOG_EXCEPTION.code);
            response.setErrorMsg(RcCode.QUERY_OPERATELOG_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/transLogList")
    public CommonOuterResponse<List<OperateLogVo>> getOperateLogVoList(@RequestBody List<OperateLogVo> operateLogVos) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            List<OperateLogVo> result = rcOperateLogService.getOperateLogVoList(operateLogVos);
            return CommonOuterResponse.success(result);
        } catch (Exception e) {
            logger.printMessage("风控操作日志转换error：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException)e).getErrorCode());
                response.setReturnMsg(((AppException)e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

}

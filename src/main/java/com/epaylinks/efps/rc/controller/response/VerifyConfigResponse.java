package com.epaylinks.efps.rc.controller.response;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.rc.vo.VerifyConfigVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class VerifyConfigResponse {
    @ApiModelProperty(value = "记录ID",dataType = "Long")
    private Long id;

    @FieldAnnotation(fieldName = "商户号")
    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @FieldAnnotation(fieldName = "商户名称")
    @ApiModelProperty(value = "商户名称",dataType = "String")
    private String customerName;

    @ApiModelProperty(value = "核验业务",dataType = "String")
    private String checkBusiness;

    /*
    6个业务名称字段用于列表导出
     */
    @FieldAnnotation(fieldName = "信用卡快捷协议支付")
    private String protocolPayCredit;
    @FieldAnnotation(fieldName = "储蓄卡快捷协议支付")
    private String protocolPay;
    @FieldAnnotation(fieldName = "信用卡快捷直接支付")
    private String quickPayCredit;
    @FieldAnnotation(fieldName = "储蓄卡快捷直接支付")
    private String quickPay;
    @FieldAnnotation(fieldName = "代付到信用卡")
    private String withdrawCreditCard;
    @FieldAnnotation(fieldName = "代付到储蓄卡")
    private String withdraw;

    @FieldAnnotation(fieldName = "首次核验通道",dictionaries = "0:系统默认,5:联润-涉赌涉传销B,7:信联,8:羽山,87:羽山+信联,85:羽山+联润涉赌涉传销B,10:联润-涉赌涉欺诈G")
    @ApiModelProperty(value = "首次核验通道",dataType = "String")
    private String firstVerifyChannel;

    @FieldAnnotation(fieldName = "内部备注")
    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;

    @FieldAnnotation(fieldName = "使用状态",dictionaries = "0:未启用,1:已启用")
    private String useStatus;

    @FieldAnnotation(fieldName = "审核状态",dictionaries = "0:待审核,1:审核通过,2:审核不通过")
    @ApiModelProperty(value = "审核状态",dataType = "String")
    private String auditStatus;

    private Long userId;

    @FieldAnnotation(fieldName = "操作人")
    @ApiModelProperty(value = "操作人",dataType = "String")
    private String userName;

    @FieldAnnotation(fieldName = "操作时间")
    @ApiModelProperty(value = "操作时间",dataType = "String")
    private String opeTime;

    @ApiModelProperty(value = "操作类型",dataType = "String")
    private String opeType;

    private VerifyConfigVo oldValue;

    private VerifyConfigVo newValue;
}

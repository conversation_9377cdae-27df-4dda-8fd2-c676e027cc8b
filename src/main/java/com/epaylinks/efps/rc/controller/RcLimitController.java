package com.epaylinks.efps.rc.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018.09.28
 */
@RestController
@RequestMapping("/rcLimit")
@Api(value = "RcLimitController", description = "风控指标控制类")
public class RcLimitController {

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private RcLimitService rcLimitService;

	@Autowired
	private OtherService ortherService;

	@Autowired
	private RcDefineService rcDefineService;

	@Autowired
	private RcArchiveService rcArchiveService;

	static final String PICTURE_TYPE = "limit_pic";

	@PostMapping("/save")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcLimit.save")
	@ApiOperation(value = "添加风控指标")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "defineCode", value = "风控指标定义编码", dataType = "String", required = true, paramType = "query"),
					@ApiImplicitParam(name = "defineId", value = "风控指标定义Id", dataType = "Long", required = true, paramType = "query"),
					@ApiImplicitParam(name = "businessTagerType", value = "业务对象类型,005:h", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTagerId", value = "业务对象ID", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "limitValue", value = "String", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "limitType", value = "限制值的JAVA类型", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "limitLevel", value = "优先级,数值越小优先级越高", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "limitVersion", value = "指标版本", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "startTime", value = "开始时间 yyyyMMdd HHmmss", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "endTime", value = "结束时间 yyyyMMdd HHmmss", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "effectValue", value = "效果值 -1表示一票否决", required = false, dataType = "String", paramType = "query")
			})
	public CommonOuterResponse save(RcLimit rcLimit, @RequestHeader("x-userid") Long userId) {
		CommonOuterResponse response = new CommonOuterResponse();
		rcLimit.setCreateTime(new Date());

		//先判断当前风控定义指标是否已存在
		RcLimit rcLimit1 = rcLimitService.queryLimit(rcLimit.getDefineId(), rcLimit.getBusinessTagerId());
		if (rcLimit1 != null) {
			response.setReturnCode(RcCode.LIMIT_ADD_EXCEPTION.code);
			response.setReturnMsg(RcCode.LIMIT_ADD_EXCEPTION.message);
			return response;
		}

		//根据userId查询user
		User user = ortherService.selectUserById(userId);
		if (user == null) {
			response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
			response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
			return response;
		}

		//先判断指标编码是否存在
		RcDefine rcDefine = rcDefineService.selectByCode(rcLimit.getDefineCode());
		if (rcDefine == null) {
			response.setReturnCode(RcCode.ADD_RCLIMT_EXCEPTION.code);
			response.setReturnMsg(RcCode.ADD_RCLIMT_EXCEPTION.message);
			return response;
		}

		rcLimit.setUserId(user.getUid());
		rcLimit.setUserName(user.getName());
		rcLimit.setLimitId(sequenceService.nextValue("rc"));
		rcLimit.setBusinnesType(rcDefine.getBusinessType());
		boolean flag = rcLimitService.save(rcLimit);

		if (!flag) {
			response.setReturnCode(RcCode.ADD_RCLIMT_EXCEPTION.code);
			response.setReturnMsg(RcCode.ADD_RCLIMT_EXCEPTION.message);
		}

		return response;
	}


	@GetMapping("/query")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcLimit.query")
	@ApiOperation(value = "查看商户风控指标")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "customerCode", value = "商户号", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "pageNum", value = "当前页面", required = true, dataType = "Integer", paramType = "query"),
					@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "Integer", paramType = "query")
			})
	public PageResult<Map<String,Object>> limitQuery(String customerCode, String customerName,Integer pageNum,Integer pageSize) {
		PageResult<Map<String,Object>> result = new PageResult<>();
		if (StringUtils.isBlank(customerCode) && StringUtils.isBlank(customerName)) {
			result.setCode(RcCode.ARCHIVE_QUERY_EXCEPTION.code);
			result.setMessage(RcCode.ARCHIVE_QUERY_EXCEPTION.message);
			return result;
		}

		RcArchive rcArchive = rcArchiveService.selectByCodeOrName(customerCode, customerName);
		if(rcArchive == null){
			result.setCode(RcCode.USER_NOT_EXIXT.code);
			result.setMessage(RcCode.USER_NOT_EXIXT.message);
			return result;
		}
		customerCode = rcArchive.getArchiveCode();
		result =rcLimitService.queryAllLimit(customerCode,pageNum,pageSize);
		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功!");

		return result;
	}

	@GetMapping("/test")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "customerCode", value = "商户号", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTargetType", value = "对象类型：005：商户号；", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessType", value = "业务类型 insidePayIn insidePayOut withDraw refund gateWayPay logIn ", required = true, dataType = "String", paramType = "query")
			})
	public List<RcLimit> test(String businessType, String businessTargetType, String customerCode){

	    return rcLimitService.queryByBusinessTypeAndTaregetId(businessType, businessTargetType, customerCode);
	}
	
    @GetMapping("/queryRedisLimit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "targetId", value = "指标对象ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "datetime", value = "日期(yyyyMMdd)", required = true, dataType = "String", paramType = "query")

    })
    public Map<String,Object> queryRedisLimit(String targetId, String datetime) {
        
        return rcLimitService.queryRedisLimit(targetId, datetime);
    }

}

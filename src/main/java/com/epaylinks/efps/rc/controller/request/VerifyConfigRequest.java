package com.epaylinks.efps.rc.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel
public class VerifyConfigRequest {
    @ApiModelProperty(value = "记录ID（修改时需传）",dataType = "Long")
    private Long id;

    @ApiModelProperty(value = "商户编号",dataType = "String")
    @NotBlank(message = "商户编号不能为空")
    private String customerNo;

    @ApiModelProperty(value = "商户名称",dataType = "String")
    @NotBlank(message = "商户名称不能为空")
    private String customerName;

    @ApiModelProperty(value = "核验业务",dataType = "String")
    @NotBlank(message = "核验业务不能为空")
    private String checkBusiness;

    @ApiModelProperty(value = "首选核验通道【0：系统默认；7：信联；8：羽山；5：联润；87：羽山+信联；85：羽山+联润】",dataType = "String")
    @NotBlank(message = "首次核验通道不能为空")
    private String firstVerifyChannel;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;
}

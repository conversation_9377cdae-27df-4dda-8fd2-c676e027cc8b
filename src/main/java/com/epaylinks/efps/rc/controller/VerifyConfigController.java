package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.controller.request.VerifyCheckBusinessRequest;
import com.epaylinks.efps.rc.controller.request.VerifyConfigRequest;
import com.epaylinks.efps.rc.controller.response.VerifyConfigResponse;
import com.epaylinks.efps.rc.service.VerifyConfigService;
import com.epaylinks.efps.rc.service.export.ExportVerifyConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "风险核验配置")
@RestController
@RequestMapping("/verifyConfig")
public class VerifyConfigController {
    @Autowired
    private VerifyConfigService verifyConfigService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private ExportVerifyConfigService exportVerifyConfigService;

    @Autowired
    private ExportFileService exportFileService;

    @PostMapping("/add")
    @ApiOperation(value = "新增核验配置",notes = "新增核验配置")
    @Logable(businessTag = "VerifyConfigController.add")
    public CommonOuterResponse add(@Valid @RequestBody VerifyConfigRequest request,
                                   @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.add(request,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("新增核验配置error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/edit")
    @ApiOperation(value = "修改核验配置",notes = "修改核验配置")
    @Logable(businessTag = "VerifyConfigController.edit")
    public CommonOuterResponse edit(@Valid @RequestBody VerifyConfigRequest request,
                                    @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.edit(request,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("更新核验配置error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除",notes = "删除")
    @Logable(businessTag = "VerifyConfigController.delete")
    public CommonOuterResponse delete(@RequestParam(value = "ids") String ids,
                                      @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.delete(ids,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("删除核验配置error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/audit")
    @ApiOperation(value = "审核",notes = "审核")
    @Logable(businessTag = "VerifyConfigController.audit")
    public CommonOuterResponse audit(@RequestParam(value = "ids") String ids,
                                     @RequestParam(value = "auditResult") String auditResult,
                                     @RequestParam(value = "opinion") String opinion,
                                     @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.audit(ids,auditResult,opinion,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("审核核验配置error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

//    @DownloadAble
    @GetMapping("/pageQuery")
    @ApiOperation(value = "分页列表",notes = "分页列表")
    @Logable(businessTag = "VerifyConfigController.pageQuery",outputResult = false)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "useStatus", value = "使用状态【0：未启用；1：已启用】", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "firstVerifyChannel", value = "首选核验通道【0：系统默认；7：信联；8：羽山；5：联润】", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "checkBusiness", value = "核验业务", required = false, dataType = "String", paramType = "query")
    })
    public PageResult<List<VerifyConfigResponse>> pageQuery(@RequestParam(value = "customerNo",required = false) String customerNo,
                                                            @RequestParam(value = "auditStatus",required = false) String auditStatus,
                                                            @RequestParam(value = "useStatus",required = false) String useStatus,
                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                            @RequestParam(value = "endTime", required = false) String endTime,
                                                            @RequestParam(value = "firstVerifyChannel", required = false) String firstVerifyChannel,
                                                            @RequestParam(value = "checkBusiness", required = false) String checkBusiness,
                                                            @RequestParam(value = "pageNum") int pageNum,
                                                            @RequestParam(value = "pageSize") int pageSize,
                                                            @RequestParam(required = false, defaultValue = "false") boolean download,
                                                            @RequestParam(required = false) String fileName,
                                                            @RequestParam(required = false, defaultValue = "csv") String type,
                                                            @RequestParam(required = false) String fileSource,
                                                            @RequestHeader(value = "x-userid") Long userId,
                                                            HttpServletRequest httpServletRequest) {
        PageResult page = new PageResult();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("startTime",startTime);
            paramsMap.put("endTime",endTime);
            paramsMap.put("firstVerifyChannel",firstVerifyChannel);
            paramsMap.put("checkBusiness",checkBusiness);
            paramsMap.put("customerNo",customerNo);
            paramsMap.put("auditStatus",auditStatus);
            paramsMap.put("useStatus",useStatus);
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            if (download) {
                return exportFileService.download(paramsMap,fileName,httpServletRequest,exportVerifyConfigService);
            }
            page = verifyConfigService.pageQuery(paramsMap,userId,download);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logger.printMessage("分页查询核验配置错误：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(RcCode.SYSTEM_EXCEPTION.code);
                page.setMessage(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @PostMapping("check")
    @ApiOperation(value = "业务管理添加服务费提示",notes = "业务管理添加服务费提示")
    @Logable(businessTag = "VerifyConfigController.checkBusiness")
    public CommonOuterResponse checkBusiness(@Valid @RequestBody VerifyCheckBusinessRequest request,
                                             @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.checkBusiness(request,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("校验是否配置支付服务费error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/batchSave")
    @ApiOperation(value = "核验配置批量导入",notes = "核验配置批量导入")
    public BatchResponse batchSave(@RequestPart(value = "file") MultipartFile file,
                                   @RequestHeader(value = "x-userid") Long userId) {
        BatchResponse response = new BatchResponse();
        try {
            response = batchTaskService.batchSaveByFileThread(file, (short) 29, null, userId);
        } catch (Exception e) {
            e.printStackTrace();
            logger.printMessage("核验配置模板导入error：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @GetMapping("/getFirstVerifyChannel")
    public CommonOuterResponse getFirstVerifyChannel(@RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return verifyConfigService.getFirstVerifyChannel();
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("查询首选核验通道error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }
}

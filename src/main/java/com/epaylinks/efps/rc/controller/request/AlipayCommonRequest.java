package com.epaylinks.efps.rc.controller.request;

import lombok.Data;

/**
 * 阿里风险推送请求公共参数
 * <AUTHOR>
 * @date 2020-07-07
 *
 */
@Data
public class AlipayCommonRequest {
    
    private String service; // 服务出口网关名 alipay.riskgo.risk.push
    
    private String sign_type;   // 签名方式（RSA或RSA2）  RSA2
    
    private String sign;   // 签名值
    
    private String charset ; // 请求的编码格式，返回的响应   GBK

    

}

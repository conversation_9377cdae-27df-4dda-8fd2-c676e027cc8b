package com.epaylinks.efps.rc.controller;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.controller.request.AlipayPushRiskRequest;
import com.epaylinks.efps.rc.service.ChannelRiskService;
import com.epaylinks.efps.rc.vo.ChannelRiskRecordVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/risk")
@Api(value = "RiskRecordController", description = "风险记录控制类")
public class RiskRecordController {
    
    @Autowired
    private ChannelRiskService channelRiskService;
    
     /**
     * 商户信息列表
     */
    @RequestMapping(value = "/pageQuery", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "RiskRecordController.pageQuery", outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息列表", notes = "商户信息列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "beginCreateTime", value = "创建开始时间,yyyyMMddhhmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "创建结束时间,yyyyMMddhhmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "platCustomerCode", value = "所属平台商户", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "serviceCustomerCode", value = "代理商商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channelId", value = "渠道商PID", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channelMchtNo", value = "上游商户号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskType", value = "风险类型：1:欺诈; 2:赌博; 3:套现; 4:套费率", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    public PageResult<ChannelRiskRecordVo> pageQuery(
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "beginCreateTime", required = false) String beginCreateTime,
            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
            @RequestParam(value = "platCustomerCode", required = false) String platCustomerCode,
            @RequestParam(value = "serviceCustomerCode", required = false) String serviceCustomerCode,
            @RequestParam(value = "channelId", required = false) String channelId,
            @RequestParam(value = "channelMchtNo", required = false) String channelMchtNo,
            @RequestParam(value = "riskType", required = false) String riskType,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader("x-userid") Long userId
    ){
        
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("customerCode", customerCode);
            map.put("beginCreateTime", beginCreateTime);
            map.put("endCreateTime", endCreateTime);
            map.put("platCustomerCode", platCustomerCode);
            map.put("serviceCustomerCode", serviceCustomerCode);
            map.put("channelId", channelId);
            map.put("channelMchtNo", channelMchtNo);
            map.put("riskType", riskType);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            
            return channelRiskService.pageQuery(map,userId);
            
        } catch (AppException e) {
            return PageResult.fail(e.getErrorCode(), e.getErrorMsg());
            
        }  catch (Exception e) {
            return PageResult.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        
    }
    
    /**
     * 接收支付宝推送通知
     * 成功则返回success七个字符
     */
    @RequestMapping(value = "/push", method = RequestMethod.POST)
    @Logable(businessTag = "RiskRecordController.push")
    @ApiOperation(value = "支付宝推送风险通知接口", notes = "支付宝推送风险通知接口", httpMethod = "POST")
    @ApiImplicitParams({
    })
    public String push(AlipayPushRiskRequest alipayPushRiskRequest) {
        
        try {
            System.out.println(alipayPushRiskRequest.getRisklevel().getBytes());
            
            System.out.println("service:" + alipayPushRiskRequest.getService());
            System.out.println("encode:"+ getEncoding(alipayPushRiskRequest.getRisklevel()));

            System.out.println( "1:" + new String(alipayPushRiskRequest.getRisklevel().getBytes("UTF-8"), "UTF-8"));
            System.out.println( "2:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("GBK"), "GBK"));
            System.out.println("3:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("GBK"),"UTF-8"));
            System.out.println("4:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("ISO-8859-1"),"UTF-8"));
            System.out.println("5:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("ISO-8859-1"),"GBK"));
            System.out.println("6:" + URLEncoder.encode(alipayPushRiskRequest.getRisklevel(),"GBK"));
            System.out.println("7:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("ISO-8859-1"),"gb2312"));
            System.out.println("8:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("gb2312"),"utf-8"));
            System.out.println("9:" +new String(alipayPushRiskRequest.getRisklevel().getBytes("gb2312"),"gbk"));


            
            if (!"alipay.riskgo.risk.push".equals(alipayPushRiskRequest.getService())) {
                return "fail";
            }
         
            channelRiskService.saveAlipayRisk(alipayPushRiskRequest);
           
            return "success";
            
        } catch (Exception e) {
            e.printStackTrace();
            return "fail";
        }
    }

    public static String getEncoding(String str) {
        String encode = "GB2312";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) { // 判断是不是GB2312
                String s = encode;
                return s; // 是的话，返回“GB2312“，以下代码同理
            }
        } catch (Exception exception) {
        }
        encode = "ISO-8859-1";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) { // 判断是不是ISO-8859-1
                String s1 = encode;
                return s1;
            }
        } catch (Exception exception1) {
        }
        encode = "UTF-8";
        try {
            byte[] by = str.getBytes(encode);
            if (str.equals(new String(str.getBytes(encode), encode))) { // 判断是不是UTF-8
                String s2 = encode;
                return s2;
            }
        } catch (Exception exception2) {
        }
        encode = "GBK";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) { // 判断是不是GBK
                String s3 = encode;
                return s3;
            }
        } catch (Exception exception3) {
        }
        return "not known encode";
    }

}

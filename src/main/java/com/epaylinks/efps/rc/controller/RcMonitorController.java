package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import com.epaylinks.efps.rc.service.monitor.NoTxnMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25 10:37
 */
@RestController
@RequestMapping("/monitor")
public class RcMonitorController {
    @Autowired
    private NoTxnMonitor noTxnMonitor;

    @Autowired
    private RcMonitorService rcMonitorService;

    @PostMapping("noTxnMonitor")
    public void noTxnMonitor() {
        noTxnMonitor.monitor();
    }

    @PostMapping("changeAccountStatus")
    public void changeAccountStatus(String customerCode, RcMonitorService.AccountStatus accountStatus, String reason) {
        rcMonitorService.changeAccountStatus(customerCode, accountStatus, reason);
    }
}

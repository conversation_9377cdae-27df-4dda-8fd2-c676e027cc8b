package com.epaylinks.efps.rc.controller.response;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
@ApiModel
public class RiskInfoExportResponse {
    @ApiModelProperty(value = "序号",dataType = "Integer")
    @FieldAnnotation(fieldName = "序号")
    private Integer rownum;

    @ApiModelProperty(value = "商户编号",dataType = "String")
    @FieldAnnotation(fieldName = "商户编号")
    private String customerNo;

    @ApiModelProperty(value = "商户名称",dataType = "String")
    @FieldAnnotation(fieldName = "商户名称")
    private String customerName;

    @ApiModelProperty(value = "商户性质",dataType = "String")
    @FieldAnnotation(fieldName = "商户性质",dictionaries = "10:个体工商户,20:企业商户,30:境外商户,40:内部商户,50:小微商户,60:个人,70:政府/事业单位,99:其他组织,1:Ⅰ类,2:Ⅱ类,3:Ⅲ类")
    private String type;

    @ApiModelProperty(value = "所属平台商",dataType = "String")
    @FieldAnnotation(fieldName = "所属平台商")
    private String platCustomerNo;

    @ApiModelProperty(value = "所属服务商",dataType = "String")
    @FieldAnnotation(fieldName = "所属服务商")
    private String serviceCustomerNo;

    @ApiModelProperty(value = "风险等级",dataType = "String")
    @FieldAnnotation(fieldName = "风险等级",dictionaries = "LOW_LEVEL:低风险,MIDDLE_LEVEL:中风险,HIGHT_LEVEL:高风险")
    private String riskLevel;

    @ApiModelProperty(value = "出金报备状态",dataType = "String")
    @FieldAnnotation(fieldName = "出金报备状态",dictionaries = "0:不需要报备,1:需要报备,2:需要报备")
    private String bindCardStatus;

    private Long salesId;

    @ApiModelProperty(value = "业务员",dataType = "String")
    @FieldAnnotation(fieldName = "业务员")
    private String salesName;

    private Long companyId;

    @ApiModelProperty(value = "所属分公司",dataType = "String")
    @FieldAnnotation(fieldName = "所属分公司")
    private String companyName;

    @ApiModelProperty(value = "注册时间",dataType = "String")
    @FieldAnnotation(fieldName = "注册时间")
    private String regTime;

    @ApiModelProperty(value = "风控状态",dataType = "String")
    @FieldAnnotation(fieldName = "风控状态",dictionaries = "0:正常,1:冻结")
    private String riskControlStatus;

    @ApiModelProperty(value = "冻结最近日期",dataType = "String")
    @FieldAnnotation(fieldName = "冻结最近日期")
    private String lastFrozenTime;

    @ApiModelProperty(value = "冻结最近原因",dataType = "String")
    @FieldAnnotation(fieldName = "冻结最近原因")
    private String lastFrozenReason;

    private Long riskCtrlChangeOperator;

    @ApiModelProperty(value = "风控变更操作人",dataType = "String")
    @FieldAnnotation(fieldName = "风控变更操作人")
    private String riskCtrlChangeOperatorName;

    @ApiModelProperty(value = "账户状态",dataType = "String")
    @FieldAnnotation(fieldName = "账户状态",dictionaries = "0:正常,1:冻结,2:止付,3:禁止入金")
    private String accountStatus;

    @ApiModelProperty(value = "账户异常最近日期",dataType = "String")
    @FieldAnnotation(fieldName = "账户异常最近日期")
    private String lastAbnormalTime;

    @ApiModelProperty(value = "账户异常最近原因",dataType = "String")
    @FieldAnnotation(fieldName = "账户异常最近原因")
    private String lastAbnormalReason;

    private Long accountChangeOperator;

    @ApiModelProperty(value = "账户变更操作人",dataType = "String")
    @FieldAnnotation(fieldName = "账户变更操作人")
    private String accountChangeOperatorName;


    @ApiModelProperty(value = "风控冻结金额")
    @FieldAnnotation(fieldName = "风控冻结金额",yuanHandler = true)
    private Long frozenAmount;

    @ApiModelProperty(value = "实际账户冻结金额")
    @FieldAnnotation(fieldName = "实际账户冻结金额",yuanHandler = true)
    private Long actualFrozenAmount;

    @ApiModelProperty(value = "临时入金限制")
    @FieldAnnotation(fieldName = "临时入金限制",dictionaries = "1:正常,2:限制临时入金")
    private String temporaryStatus;

    @ApiModelProperty(value = "入金限制最近日期",dataType = "String")
    @FieldAnnotation(fieldName = "入金限制最近日期")
    private String temporaryLimitTime;

    @ApiModelProperty(value = "入金限制项目",dataType = "String")
    @FieldAnnotation(fieldName = "入金限制项目")
    private String temporaryLimitItem;

    @ApiModelProperty(value = "入金限制原因",dataType = "String")
    @FieldAnnotation(fieldName = "入金限制原因")
    private String temporaryLimitReason;

    @ApiModelProperty(value = "入金限制操作人",dataType = "String")
    @FieldAnnotation(fieldName = "入金限制操作人")
    private String temporaryLimitOperatorName;

    private String riskNewValue;

    private String accountNewValue;
}

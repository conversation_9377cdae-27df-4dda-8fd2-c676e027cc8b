package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.service.PhoneBwListService;
import com.epaylinks.efps.rc.vo.AuditRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 手机白名单管理审核相关接口
 */
@Api(value = "PhoneBwListController",description = "手机白名单管理审核相关接口")
@RestController
@RequestMapping("/phoneBwList")
public class PhoneBwListController {
    @Autowired
    private PhoneBwListService phoneBwListService;
    @Autowired
    private LogService logService;

    @PostMapping("/saveAuditRecord")
    @Logable(businessTag = "PhoneBwListController.saveAuditRecord")
    @ApiOperation(value = "新增审核记录(手机白名单、cust调用)",notes = "新增审核记录(手机白名单、cust调用)")
    public CommonOuterResponse saveAuditRecord(@RequestParam("id") Long id,
                                               @RequestParam(value = "oldVlue",required = false) String oldVlue,
                                               @RequestParam(value = "newValue") String newValue,
                                               @RequestParam(value = "code") String code,
                                               @RequestHeader("x-userid") Long userId) {
        try {
            RcAuditRecord record = phoneBwListService.saveAuditRecord(id,oldVlue,newValue,userId,code);
            return CommonOuterResponse.success("success",record);
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

    @GetMapping("/queryRecord")
    @Logable(businessTag = "PhoneBwListController.queryRecord")
    @ApiOperation(value = "查询手机白名单记录",notes = "查询手机白名单记录")
    public CommonOuterResponse<RcAuditRecord> queryRecord(@RequestParam("bwId") Long bwId,
                                           @RequestParam("type") String type,
                                           @RequestHeader("x-userid") Long userId) {
        try {
            RcAuditRecord record = phoneBwListService.queryRecord(bwId,type,userId);
            return CommonOuterResponse.success("success",record);
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

    @PostMapping("/auditRecord")
    @Logable(businessTag = "PhoneBwListController.auditRecord")
    @ApiOperation(value = "批量审核手机白名单",notes = "批量审核手机白名单")
    public CommonOuterResponse auditRecord(@RequestParam("rIds") String rIds,
                                           @RequestParam("auditResult") Short auditResult,
                                           @RequestParam(value = "comments",required = false) String comments,
                                           @RequestHeader("x-userid") Long userId) {
        try {
            return CommonOuterResponse.success("success",phoneBwListService.auditRecord(rIds,auditResult,comments,userId,"1"));
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }


    @PostMapping("/saveAuditRecordCurrency")
    @Logable(businessTag = "saveAuditRecordCurrency")
    @ApiOperation(value = "新增审核记录",notes = "新增审核记录")
    public CommonOuterResponse saveAuditRecordCurrency(@RequestParam("id") Long id,
                                               @RequestParam(value = "oldVlue",required = false) String oldVlue,
                                               @RequestParam(value = "newValue") String newValue,
                                               @RequestParam(value = "code") String code,
                                               @RequestParam(value = "auditTargetType") String auditTargetType,
                                               @RequestHeader("x-userid") Long userId) {
        try {
            RcAuditRecord record = phoneBwListService.saveAuditRecord(id,oldVlue,newValue,userId,code,auditTargetType);
            return CommonOuterResponse.success("success",record);
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            System.out.println("错误信息：" + e.getMessage());
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

    @PostMapping("/saveAuditRecordList")
    @Logable(businessTag = "saveAuditRecordList")
    public CommonOuterResponse saveAuditRecordList(@RequestBody List<AuditRecordVo> auditRecordVos,
                                                   @RequestHeader("x-userid") Long userId) {
        try {
            return CommonOuterResponse.success("success",phoneBwListService.saveAuditRecordList(auditRecordVos));
        } catch (AppException e) {
            return  CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }
}

package com.epaylinks.efps.rc.controller.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阿里风险推送请求报文
 * 
 * <AUTHOR>
 * @date 2020-07-07
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AlipayPushRiskRequest extends AlipayCommonRequest {

    private String smid; // 涉嫌风险子商户在支付宝被分配的商户ID 2.08826E+15

    private String pid; // 申请业务合作伙伴ID 2.08812E+15

    private String externalId; // 否 涉嫌风险子商户在银行被分配的商户ID 1.0153E+11

    private String sourceId; // 是 40 风险子商户对应的服务商ID 2.08812E+15

    private String tradeNos; // 否 128 风险交易号样例（由于风险识别策略差异，个别情况，由于无风险交易号样例） 201705090000000001wd2

    private String alipayTradeNos; // 否 40 支付宝流水号 2.01911E+20

    private String bank_card_no; // 否 40 银行卡号 6.22848E+18

    private String risktype; // 是 40 风险类型 欺诈、赌博、套现、套费率

    private String risklevel; // 是 40 风险情况描述 欺诈风险（欺诈风险极高，且有投诉）

    private String riskDesc; //  否  1024 风险定位原因说明 套费率：例如：买卖家关系异常

}

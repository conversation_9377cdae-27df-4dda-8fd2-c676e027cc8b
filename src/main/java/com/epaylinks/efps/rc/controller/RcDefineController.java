package com.epaylinks.efps.rc.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.RcDefine;
import com.epaylinks.efps.rc.service.RcDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date  2018.09.28
 */
@RestController
@RequestMapping("/rcDefine")
@Api(value = "RcDefineController", description = "风控指标定义类")
public class RcDefineController {

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private RcDefineService rcDefineService;


	@PostMapping("/save")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcDefine.save")
	@ApiOperation(value ="添加风控指标定义")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "groupId",value = "风控指标定义分组ID", dataType = "Long",required = true,paramType = "query"),
					@ApiImplicitParam(name = "levelCode", value = "风控指标定义编码", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "type", value = "风控类型 0:事前 1:事中 2:事后", required = true,dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "serviceId", value = "服务ID", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessType", value = "适用业务类型", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "remark", value = "说明", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "effectValue", value = "默认风控值", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "rule", value = "风控规则(预留字段)", required = false, dataType = "String", paramType = "query"),
			})
	public CommonResponse save(RcDefine rcDefine, @RequestHeader("x-userid")Long userId){
		CommonResponse response = new CommonResponse();
		rcDefine.setUserId(userId);
		rcDefine.setDefineId(sequenceService.nextValue("rc"));
		rcDefine.setCreateTime(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
		rcDefineService.insertSelective(rcDefine);

		response.setCode(CommonResponse.SUCCEE);
		response.setMessage("新增风控指标定义成功!");
		return response;
	}


	@GetMapping("/query")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcDefine.query")
	@ApiOperation(value ="查询风控指标定义")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "identifier",value = "风控指标定义编码", dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "bigId", value = "大类Id", required = false, dataType = "Long", paramType = "query"),
					@ApiImplicitParam(name = "smallId", value = "小类Id", required = false,dataType = "Long", paramType = "query"),
					@ApiImplicitParam(name = "startTime", value = "开始时间(yyyyMMddHHmmss)", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "endTime", value = "结束时间(yyyyMMddHHmmss)", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "pageNum", value = "当前页面", required = true, dataType = "Integer", paramType = "query"),
					@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "Integer", paramType = "query"),
			})
	public PageResult<Map<String,Object>> query(String identifier, Long bigId, Long smallId, String startTime, String endTime, Integer pageNum, Integer pageSize){


		PageResult<Map<String,Object>> result = rcDefineService.pageDefineQuery(identifier,bigId,smallId,startTime,endTime,pageNum,pageSize);


		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功");
		return result;
	}


}

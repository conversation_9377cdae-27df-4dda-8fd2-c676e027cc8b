package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.error.exception.EpException;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.RcTxsOrderMapper;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.drools.DroolsSceneService;
import com.epaylinks.efps.rc.drools.model.AddOrUpdateRuleRequest;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RiskEventMonitorService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import com.epaylinks.efps.rc.service.monitor.UnionQrTransactionMonitor;
import com.epaylinks.efps.rc.vo.AuditRiskRuleQueryResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.DataInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/riskEvent")
@Api(value = "RiskEventRuleController", description = "风控管理-可疑事件监控规则控制类")
public class RiskEventRuleController {

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private OtherService otherService;

    @Autowired
    private LogService logService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private DroolsSceneService droolsSceneService;

    @GetMapping("/isCustomerExclude")
    public boolean isCustomerExclude(String customerCode, String ruleCode) {
        return riskEventRuleService.isCustomerExclude(customerCode, ruleCode);
    }

    @GetMapping("/getRule")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.getRule")
    @ApiOperation(value = "查询单个可疑事件规则")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleCode", value = "规则代码，例如R001等", dataType = "String", required = true, paramType = "query"),
            })
    public CommonOuterResponse getRule(String ruleCode) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            RiskEventRule rule = riskEventRuleService.getRule(ruleCode);
            response.setData(rule);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }

    @GetMapping("/ruleList")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.ruleList")
    @ApiOperation(value = "查询可疑事件规则列表")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleStatus", value = "规则状态，01启用，02停用", dataType = "String", required = false, paramType = "query"),
                    @ApiImplicitParam(name = "auditStatus", value = "审核状态：0待审核，1审核通过，2审核不通过", dataType = "int", required = false, paramType = "query"),
            })
    public PageResult<RiskEventRule> ruleList(String ruleStatus, Short auditStatus,
                                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                              @RequestHeader(value = "x-userid") Long userId) {
        PageResult page = new PageResult();
        try {
            if (userId == null) {
                userId = 1L;
            }
            //根据userId查询user
            User user = otherService.selectUserById(userId);
            if (user == null) {
                throw new AppException(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            paramsMap.put("ruleStatus", ruleStatus);
            paramsMap.put("auditStatus", auditStatus);
            Integer count = riskEventRuleService.countRulePage(paramsMap);
            List<RiskEventRule> list = riskEventRuleService.selectRuleList(ruleStatus, auditStatus, user.getName(),paramsMap);
            page.setTotal(count);
            page.setRows(list);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logService.printLog("可疑事件规则查询错误：" + e.getMessage());
            logService.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(RcCode.SYSTEM_EXCEPTION.code);
                page.setMessage(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    /*
    @GetMapping("/ruleList")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.ruleList")
    @ApiOperation(value = "查询可疑事件规则列表")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleStatus", value = "规则状态，01启用，02停用", dataType = "String", required = false, paramType = "query"),
                    @ApiImplicitParam(name = "auditStatus", value = "审核状态：0待审核，1审核通过，2审核不通过", dataType = "int", required = false, paramType = "query"),
            })
    public CommonOuterResponse<List<RiskEventRule>> ruleList(String ruleStatus, Short auditStatus, @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse<List<RiskEventRule>> response = new CommonOuterResponse<List<RiskEventRule>>();
        if (userId == null) {
            userId = 1L;
        }
        //根据userId查询user
        User user = otherService.selectUserById(userId);
        if (user == null) {
            response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
            response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
            return response;
        }
        try {
            List<RiskEventRule> list = riskEventRuleService.selectRuleList(ruleStatus, auditStatus, user.getName());
            response.setData(list);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }
     */

    @PostMapping("/updateRule")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.updateRule")
    @ApiOperation(value = "修改可疑事件规则")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleId", value = "规则ID", dataType = "int", required = true, paramType = "query"),
                    @ApiImplicitParam(name = "ruleParam1", value = "参数一", dataType = "String", required = false, paramType = "query"),
                    @ApiImplicitParam(name = "ruleParam2", value = "参数二", dataType = "String", required = false, paramType = "query"),
                    @ApiImplicitParam(name = "triggerAction", value = "措施", dataType = "String", required = false, paramType = "query"),
            })
    public CommonOuterResponse updateRule(Long ruleId, String ruleParam1, String ruleParam2, String triggerAction, String uniqueId, String fileName,
                                          @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        if (userId == null) {
            userId = 0L;
        }

        try {
            if (droolsSceneService.isDroolsRule(ruleId)) {
                EpAssert.notBlank(ruleParam1, RcCode.PARAM_ERROR.msg("ruleParam1不能为空"));
                AddOrUpdateRuleRequest addOrUpdateRuleRequest = EpAssert.getOrThrow(() -> JsonUtils.jsonToObj(ruleParam1, AddOrUpdateRuleRequest.class),
                        RcCode.PARAM_ERROR.msg("ruleParam1格式错误"));
                addOrUpdateRuleRequest.setRuleId(ruleId);
                droolsSceneService.addOrUpdateRule(addOrUpdateRuleRequest, userId);
            } else {
                int res = riskEventRuleService.updateRule(ruleId, ruleParam1, ruleParam2, triggerAction, userId, uniqueId, fileName);
                String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
                response.setData("1".equals(audFlag) ? "提交成功" : "更新规则条数：" + res);
            }
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (EpException e) {
            throw e;
        } catch (Exception e) {
            logService.printLog("修改可疑事件规则error：" + e.getMessage());
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }

    @PostMapping("/setRuleON")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.setRuleON")
    @ApiOperation(value = "启用可疑事件规则")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleIds", value = "规则ID，不允许多个", dataType = "String", required = true, paramType = "query"),
            })
    public CommonOuterResponse setRuleON(String ruleIds, @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        //校验参数是否为空
        if (ruleIds == null) {
            response.setReturnCode(RcCode.PARAM_ERROR.code);
            response.setReturnMsg(RcCode.PARAM_ERROR.message);
            return response;
        }
        //校验参数分隔符、是否为数字
        String[] ids = ruleIds.split(",");
        if (!riskEventRuleService.checkRuleIds(ids)) {
            response.setReturnCode(RcCode.PARAM_ERROR.code);
            response.setReturnMsg(RcCode.PARAM_ERROR.message);
            return response;
        }
        if (userId == null) {
            userId = 0L;
        }
        //根据userId查询user
        User user = otherService.selectUserById(userId);
        if (user == null) {
            response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
            response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
            return response;
        }
        try {
            if (droolsSceneService.isDroolsRule(Long.parseLong(ruleIds))) {
                droolsSceneService.on(Long.parseLong(ruleIds), userId);
            } else {
                int res = 0;
                for (String id : ids) {
                    if (riskEventRuleService.setRuleON(Long.parseLong(id), user) > 0) {
                        res++;
                    }
                }
                String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
                response.setData("1".equals(audFlag) ? "提交成功" : "更新规则条数：" + res);
            }
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (EpException e) {
            throw e;
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }

    @PostMapping("/setRuleOFF")
    @Validatable
    @Exceptionable
    @Logable(businessTag = "RiskEventRuleController.setRuleOFF")
    @ApiOperation(value = "停用可疑事件规则")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleIds", value = "规则ID，不允许多个", dataType = "String", required = true, paramType = "query"),
            })
    public CommonOuterResponse setRuleOFF(String ruleIds, @RequestHeader(value = "x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        //校验参数是否为空
        if (ruleIds == null) {
            response.setReturnCode(RcCode.PARAM_ERROR.code);
            response.setReturnMsg(RcCode.PARAM_ERROR.message);
            return response;
        }
        //校验参数分隔符、是否为数字
        String[] ids = ruleIds.split(",");
        if (!riskEventRuleService.checkRuleIds(ids)) {
            response.setReturnCode(RcCode.PARAM_ERROR.code);
            response.setReturnMsg(RcCode.PARAM_ERROR.message);
            return response;
        }
        if (userId == null) {
            userId = 0L;
        }
        //根据userId查询user
        User user = otherService.selectUserById(userId);
        if (user == null) {
            response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
            response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
            return response;
        }
        try {
            if (droolsSceneService.isDroolsRule(Long.parseLong(ruleIds))) {
                droolsSceneService.off(Long.parseLong(ruleIds), userId);
            } else {
                int res = 0;
                for (String id : ids) {
                    if (riskEventRuleService.setRuleOFF(Long.parseLong(id), user) > 0) {
                        res++;
                    }
                }
                String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
                response.setData("1".equals(audFlag) ? "提交成功" : "更新规则条数：" + res);
            }
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (EpException e) {
            throw e;
        } catch (Exception e) {
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }


    @GetMapping("/queryAuditRiskRule")
    @Logable(businessTag = "RiskEventRuleController.queryAuditRiskRule")
    @ApiOperation(value = "查询可疑事件规则审核信息", notes = "查询可疑事件规则审核信息", httpMethod = "GET")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleId", value = "规则ID", required = true, dataType = "Long", paramType = "query"),
            })
    public CommonOuterResponse<AuditRiskRuleQueryResponse> queryAuditRiskRule(
            @RequestParam(value = "ruleId") Long ruleId,
            @RequestHeader(value = "x-userid") Long userId) {

        try {
            if (droolsSceneService.isDroolsRule(ruleId)) {
                return CommonOuterResponse.success(droolsSceneService.getAuditDetail(ruleId));
            } else {
                return CommonOuterResponse.success(riskEventRuleService.queryAuditBwList(ruleId));
            }
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (EpException e) {
            throw e;
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }


    @PostMapping("/auditRiskRule")
    @Logable(businessTag = "RiskEventRuleController.auditRiskRule")
    @ApiOperation(value = "审核可疑事件规则", notes = "审核可疑事件规则", httpMethod = "POST")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "ruleId", value = "规则ID", required = true, dataType = "Long", paramType = "query"),
                    @ApiImplicitParam(name = "auditResult", value = "审核状态：1审核通过，2驳回", valueRange = "{1,2}", required = true, dataType = "int", paramType = "query"),
                    @ApiImplicitParam(name = "remarks", value = "审核意见", required = true, dataType = "String", paramType = "query"),
            })
    public CommonOuterResponse auditRiskRule(
            @RequestParam(value = "ruleId", required = true) Long ruleId,
            @RequestParam(value = "auditResult", required = true) Short auditResult,
            @RequestParam(value = "remarks", required = true) String remarks,
            @RequestHeader(value = "x-userid", required = true) Long userId) {

        try {
            if (droolsSceneService.isDroolsRule(ruleId)) {
                droolsSceneService.audit(ruleId, auditResult, remarks, userId);
            } else {
                riskEventRuleService.auditRiskRule(ruleId, auditResult, remarks, userId);
            }
            return CommonOuterResponse.success();
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (EpException e) {
            throw e;
        } catch (Exception e) {
            logger.printMessage("审核可疑事件规则error：" + e.getMessage());
            logger.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

    @Autowired
    private RcTxsOrderMapper rcTxsOrderMapper;

    @Autowired
    private RiskEventMonitorService riskEventMonitorService;

    @Autowired
    private UnionQrTransactionMonitor unionQrTransactionMonitor;

    @Autowired
    private RcMonitorService rcMonitorService;

    @PostMapping("/monitor")
    public void monitor(String transactionNo) {
        RcTxsOrder order = rcTxsOrderMapper.findOneByTransactionNo(transactionNo);
        riskEventMonitorService.asyncMonitor(order);
    }

    @PostMapping("/addQrMonitorPlatCustomer")
    public void addQrMonitorPlatCustomer(String customerCode) {
        unionQrTransactionMonitor.addQrMonitorPlatCustomer(customerCode);
    }

    @PostMapping("/delQrMonitorPlatCustomer")
    public void delQrMonitorPlatCustomer(String customerCode) {
        unionQrTransactionMonitor.delQrMonitorPlatCustomer(customerCode);
    }

    @ApiOperation(value = "启动停用的业务", notes = "启动停用的业务", httpMethod = "POST")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "reopenTime", value = "启动日期，格式yyyyMMdd", required = true, dataType = "String"),
            })
    @PostMapping("/reopenBusiness")
    public void reopenBusiness(String reopenTime) {
        unionQrTransactionMonitor.autoReopenBusiness(Timex.of(reopenTime, Timex.Format.yyyyMMdd).toDate());
    }

    @ApiOperation(value = "启动停用的终端", notes = "启动停用的终端", httpMethod = "POST")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "reopenTime", value = "启动日期，格式yyyyMMdd", required = true, dataType = "String"),
            })
    @PostMapping("/reopenTerm")
    public void reopenTerm(String reopenTime) {
        rcMonitorService.autoReopenTerm(Timex.of(reopenTime, Timex.Format.yyyyMMdd).toDate());
    }

    @PostMapping("/uploadExcelAttachment")
    @ApiOperation(value = "上传Excel附件", notes = "上传Excel附件", httpMethod = "POST")
    public CommonOuterResponse uploadExcelAttachment(@RequestParam("file") MultipartFile multipartFile,
                                                     @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return riskEventRuleService.uploadExcel(multipartFile);
        } catch (AppException e) {
            logService.printLog("上传Excel附件error：" + e.getErrorMsg());
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog("上传Excel附件error：" + e.getMessage());
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message + ":" + e.getMessage());
        }
        return response;
    }

    @GetMapping("/test")
    public Boolean test(String customerNo, String ruleCode) {
        try {
            return riskEventRuleService.isCustomerExclude(customerNo, ruleCode);
        } catch (Exception e) {
            logger.printLog("测试错误：" + e.getMessage());
            logger.printLog(e);
        }
        return null;
    }

    @GetMapping("/testUrl")
    public void testUrl(String site) {
        URL url;
        DataInputStream dataInputStream = null;
        FileOutputStream fileOutputStream = null;
        try {
            String filePath = "tmp/batch/merchant/" + System.currentTimeMillis() + "免触发商户配置R024-20231211.xlsx";
            File destfile = new File(filePath);
            url = new URL(site);
            dataInputStream = new DataInputStream(url.openStream());
            fileOutputStream = new FileOutputStream(destfile);
            FileCopyUtils.copy(dataInputStream, fileOutputStream);
        } catch (MalformedURLException e) {
            logService.printLog(e);
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code, CustReturnCode.SYSTEM_EXCEPTION.message);
        } catch (IOException e) {
            logService.printLog("出现IO异常：" + e.getMessage() + "-" + e.getCause());
            logService.printLog(e);
        } finally {
            try {
                if (dataInputStream != null) {
                    dataInputStream.close();
                }
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @PostMapping("/testGetCustomerInfo")
    @Logable(businessTag = "testGetCustomerInfo")
    public void testGetCustomerInfo(String fileName,String uniqueId) {
        try {
            riskEventRuleService.getCustomerInfo(null,fileName,uniqueId);
        } catch (Exception e) {
            logger.printMessage("测试获取Excel信息错误:" + e.getMessage());
            logger.printLog(e);
        }
    }
}

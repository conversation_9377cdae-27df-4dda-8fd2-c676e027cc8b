package com.epaylinks.efps.rc.controller.response;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@Data
@ApiModel
public class GamblingPyramidResponse implements Serializable {

    private Long id;

    @FieldAnnotation(fieldName="类型名称",dictionaries = "001:身份证号,002:手机号,003:社会统一信用代码,004:银行账号,011:网站")
    @ApiModelProperty(value = "类型名称：001身份证号；002手机号；003社会统一信用代码；004银行账号；011网站",dataType = "String")
    private String cardAccountType;

    @FieldAnnotation(fieldName="类型值")
    @ApiModelProperty(value = "类型值脱敏",dataType = "String")
    private String cardAccount;

    private String cardEncrypt;

    @FieldAnnotation(fieldName="是否加入黑名单",dictionaries = "1:是,0:否")
    @ApiModelProperty(value = "是否加入黑名单[1：是；0：否]",dataType = "String")
    private String joinBlacklist;

    @FieldAnnotation(fieldName="核验结果",dictionaries = "1:成功,0:失败,2:未核验")
    @ApiModelProperty(value = "核验结果[1：成功；0：失败]",dataType = "String")
    private String verificationResults;

    @ApiModelProperty(value = "风险标签：涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation，无：none",dataType = "String")
    private String riskTag;

    @FieldAnnotation(fieldName="风险标签")
    private String riskTagShow;

    @ApiModelProperty(value = "核验类型：涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation，无：none",dataType = "String")
    private String checkType;

    @FieldAnnotation(fieldName="核验类型")
    private String checkTypeShow;

    @FieldAnnotation(fieldName="核验结果备注")
    @ApiModelProperty(value = "备注",dataType = "String")
    private String remarks;

    @FieldAnnotation(fieldName="易票联订单号")
    @ApiModelProperty(value = "原请求流水号",dataType = "String")
    private String reqSerialNo;

    @FieldAnnotation(fieldName="商户号")
    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @FieldAnnotation(fieldName="商户全称")
    @ApiModelProperty(value = "商户名称",dataType = "String")
    private String customerName;

    @FieldAnnotation(fieldName="商户原交易商户订单号")
    @ApiModelProperty(value = "商户原交易商户订单号",dataType = "String")
    private String transOrderNo;

//    @FieldAnnotation(fieldName="核验时间")
//    @ApiModelProperty(value = "核验时间",dataType = "String")
//    private String checkTime;

    @FieldAnnotation(fieldName="上游名称")
    @ApiModelProperty(value = "上游名称",dataType = "String")
    private String channelName;

    @ApiModelProperty(value = "操作人ID",dataType = "Long")
    private Long operId;

    @FieldAnnotation(fieldName="操作人")
    @ApiModelProperty(value = "操作人",dataType = "String")
    private String operName;

    @FieldAnnotation(fieldName="批次号")
    @ApiModelProperty(value = "批次号",dataType = "String")
    private String batchNo;

    @FieldAnnotation(fieldName="创建时间")
    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String createTime;

    @FieldAnnotation(fieldName="来源",dictionaries = "0:商户API,1:内部使用,2:定期核验,3:交易中核验,4:风控手动核验,5:报备转账卡时核验,6:商户入网时核验")
    @ApiModelProperty(value = "来源：0:商户API,1:内部使用,2:定期核验,3:交易中核验,4:风控手动核验,5:报备转账卡时核验,6:商户入网时核验",dataType = "String")
    private String source;

}

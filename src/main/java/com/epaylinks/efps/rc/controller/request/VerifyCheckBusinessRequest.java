package com.epaylinks.efps.rc.controller.request;

import com.epaylinks.efps.rc.vo.CheckBusinessVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel
public class VerifyCheckBusinessRequest {
    @ApiModelProperty(value = "商户编号",dataType = "String")
    @NotBlank(message = "商户编号不能为空")
    private String customerNo;

    @ApiModelProperty(value = "来源【verifyConfig：核验配置；businessManage：业务管理】",dataType = "String")
    @NotBlank(message = "来源不为空")
    private String source;

    private List<CheckBusinessVo> businessVoList;
}

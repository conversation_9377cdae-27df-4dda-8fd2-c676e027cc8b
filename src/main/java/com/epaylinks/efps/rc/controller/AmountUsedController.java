package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.domain.BasicAmountLimitUsedQueryResponse;
import com.epaylinks.efps.rc.domain.CustomerAmountDetail;
import com.epaylinks.efps.rc.service.RcAmountUsedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;

@RestController
@RequestMapping("/amountUsed")
@Api(value = "AmountUsedController", description = "额度已用简易controller")
public class AmountUsedController {

    @Autowired
    private RcAmountUsedService rcAmountUsedService;
    @Validatable
    @Exceptionable
    //@Logable(businessTag = "getCurrentAmountUsed")
    @ApiImplicitParams({
    })
    @PostMapping("/getCurrentAmountUsed")
    public CommonOuterResponse<List<CustomerAmountDetail>> getCurrentAmountUsed(@RequestParam(name = "customerCodeList", required = true) List<String> customerCodeList) {
        try{
            return rcAmountUsedService.process(customerCodeList);
        }catch (Exception e){
            e.printStackTrace();
            return CommonOuterResponse.fail("0001", "异常");
        }

    }
}

package com.epaylinks.efps.rc.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ProportionResponse {
    @ApiModelProperty(value = "是否开通",dataType = "Boolean")
    private Boolean whetherOpenWithdraw;

    @ApiModelProperty(value = "代付占比",dataType = "Integer")
    private Integer proportion;

    @ApiModelProperty(value = "最低提现出金比例设置",dataType = "Integer")
    private Integer minWithdrawRatio;

    @ApiModelProperty(value = "开始时间",dataType = "String")
    private String withdrawStartTime;

    @ApiModelProperty(value = "结束时间",dataType = "String")
    private String withdrawEndTime;

    private String uniqueId;

    private String fileName;

    private String fileUrl;
}

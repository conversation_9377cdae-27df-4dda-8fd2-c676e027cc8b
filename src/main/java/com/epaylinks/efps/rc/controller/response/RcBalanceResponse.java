package com.epaylinks.efps.rc.controller.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 10:42
 */
@Data
public class RcBalanceResponse {
    private Long availableBalance;
    private Long floatBalance;
    private Long frozenBalance;
    private Long splitBalance;
    private Long withdrawBalance;
    private Long actualFrozenBalance;
    private Long rcBalance;
}

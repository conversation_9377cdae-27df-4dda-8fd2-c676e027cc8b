package com.epaylinks.efps.rc.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.BwListMapper;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.dao.RcLimitMapper;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.thread.InitBwListRedisThread;
import com.epaylinks.efps.rc.thread.InitRcLimitDataRedisThread;
import com.epaylinks.efps.rc.thread.InitRcLimitRedisThread;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/redis")
@Api(value = "RedisController", description = "同步redis控制器")
public class RedisController {

	@Autowired
	private BwListMapper bwListMapper;
	@Autowired
	private RcLimitMapper rcLimitMapper;
	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private RedisTemplate redisTemplate;
	@Autowired
	private LogService logService;
	
	@Value("${initRedisPageSize:10000}")
	private static final int PAGE_SIZE = 10000; // 每次处理大小
	
	@Value("${initRedisThreadPoolSize:10}")
    private static final int THREAD_POOL_SIZE = 10; // 线程池大小
	

	@PostMapping("/initBlackList")
	@Logable(businessTag = "initBlackList")
	@ApiOperation(value ="初始化黑名单")
	public CommonOuterResponse<String> initBlackList() {

        ExecutorService threadPool = Executors.newScheduledThreadPool(THREAD_POOL_SIZE);// 初始化线程池
        
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("bwType", BwList.BwType.BLACK.code);
        int total = bwListMapper.queryTotal(paramMap); // 总数
        logService.printLog("===== initBlackList, total: " + total);

        // 删除全部缓存数据
        redisTemplate.expire(BwList.BwType.BLACK.message, 0, TimeUnit.MILLISECONDS);
        
        int startNum = 0;
        int endNum = 0;
        while (total - endNum > 0) {
            endNum = total > PAGE_SIZE ? endNum + PAGE_SIZE : total;
            logService.printLog("===== initBlackList, startNum: " + (startNum + 1) + " , endNum: " + endNum);
            InitBwListRedisThread thread = new InitBwListRedisThread((startNum + 1), endNum, BwList.BwType.BLACK);
            threadPool.execute(thread);
            startNum = startNum + PAGE_SIZE ;
        }
        
        threadPool.shutdown();
        
        return CommonOuterResponse.success();
	}
	
	
	@PostMapping("/initWhiteList")
	@Logable(businessTag = "initWhiteList")
	@ApiOperation(value ="初始化白名单")
	public CommonOuterResponse<String> initWhiteList() {
	    
        ExecutorService threadPool = Executors.newScheduledThreadPool(THREAD_POOL_SIZE);// 初始化线程池
        
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("bwType", BwList.BwType.WHITE.code);
        int total = bwListMapper.queryTotal(paramMap); // 总数
        logService.printLog("===== initWhiteList, total: " + total);

        // 删除全部缓存数据
        redisTemplate.expire(BwList.BwType.WHITE.message, 0, TimeUnit.MILLISECONDS);
        
        int startNum = 0;
        int endNum = 0;
        while (total - endNum > 0) {
            endNum = total > PAGE_SIZE ? endNum + PAGE_SIZE : total;
            logService.printLog("===== initWhiteList, startNum: " + (startNum + 1) + " , endNum: " + endNum);
            InitBwListRedisThread thread = new InitBwListRedisThread((startNum + 1), endNum, BwList.BwType.WHITE);
            threadPool.execute(thread);
            startNum = startNum + PAGE_SIZE ;
        }
        
        threadPool.shutdown();
        
        return CommonOuterResponse.success();
	}
	
	
	@PostMapping("/initRcLimit")
    @Logable(businessTag = "initRcLimit")
    @ApiOperation(value ="初始化风控指标")
	public CommonOuterResponse<String> initRcLimit() {
	    
	    ExecutorService threadPool = Executors.newScheduledThreadPool(THREAD_POOL_SIZE);// 初始化线程池
        
        int total = rcLimitMapper.totalLimit(); // 风控指标总数
        logService.printLog("===== initRcLimit, total: " + total);

        int startNum = 0;
        int endNum = 0;
        while (total - endNum > 0) {
            endNum = total > PAGE_SIZE ? endNum + PAGE_SIZE : total;
            logService.printLog("===== initRcLimit, startNum: " + (startNum + 1) + " , endNum: " + endNum);
            InitRcLimitRedisThread thread = new InitRcLimitRedisThread((startNum + 1), endNum);
            threadPool.execute(thread);
            startNum = startNum + PAGE_SIZE ;
        }
        
        threadPool.shutdown();
        
        return CommonOuterResponse.success();
	}
	
	
	
	@PostMapping("/initRcLimitData")
	@Logable(businessTag = "initRcLimitData")
	@ApiOperation(value ="初始化风控限额累计值（仅当月、当年累计值）")
	public CommonOuterResponse<String> initRcLimitData() {
	    
	    ExecutorService threadPool = Executors.newScheduledThreadPool(THREAD_POOL_SIZE);// 初始化线程池
        
        int total = rcLimitDataMapper.totalInitRedis(); // 风控指标总数
        logService.printLog("===== initRcLimitData, total: " + total);
        
        int startNum = 0;
        int endNum = 0;
        while (total - endNum > 0) {
            endNum = total > PAGE_SIZE ? endNum + PAGE_SIZE : total;
            logService.printLog("===== initRcLimitData, startNum: " + (startNum + 1) + " , endNum: " + endNum);
            InitRcLimitDataRedisThread thread = new InitRcLimitDataRedisThread((startNum + 1), endNum);
            threadPool.execute(thread);
            startNum = startNum + PAGE_SIZE ;
        }
        
        threadPool.shutdown();
        
        return CommonOuterResponse.success();
	}
	
	
	@Deprecated
	@PostMapping("/synchronization")
	@Logable(businessTag = "synchronization")
	public void synchronization(){

		//查询黑名单
		List<BwList> balckList = bwListMapper.selectAll(BwList.BwType.BLACK.code);

		if(balckList != null && !balckList.isEmpty()){
	        // 设置过期时间，删除缓存所有数据
			redisTemplate.expire(BwList.BwType.BLACK.message,-1,TimeUnit.SECONDS);
			for (BwList bwList : balckList){
			     //存到redis中去
                redisTemplate.opsForHash().put(BwList.BwType.BLACK.message,bwList.getBusinessTagerId(),bwList);
			}
		}

		//查询白名单
		List<BwList> whiteList = bwListMapper.selectAll(BwList.BwType.WHITE.code);

		if(whiteList != null && !whiteList.isEmpty()){
            // 设置过期时间，删除缓存所有数据
			redisTemplate.expire(BwList.BwType.WHITE.message,-1, TimeUnit.SECONDS);
			for (BwList bwList : whiteList){
			     //存到redis中去
                 redisTemplate.opsForHash().put(BwList.BwType.WHITE.message,bwList.getBusinessTagerId(),bwList);
			}
		}

		//风控指标
		List<RcLimit> rcLimitList = rcLimitMapper.selectALL();

		for (RcLimit rcLimit : rcLimitList){
		    String key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型/终端维度扩展
                key = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            }
			Map<String,RcLimit> map =(Map<String, RcLimit>) redisTemplate.opsForHash().get(key,rcLimit.getBusinnesType());
			if(map == null){
				//说明这个商户已经存在某个风控指标
				redisTemplate.expire(key,-1,TimeUnit.SECONDS);
				map = new HashedMap();
			}
			map.put(rcLimit.getDefineCode(),rcLimit);

			redisTemplate.opsForHash().put(key,rcLimit.getBusinnesType(),map);
		}
	}


}

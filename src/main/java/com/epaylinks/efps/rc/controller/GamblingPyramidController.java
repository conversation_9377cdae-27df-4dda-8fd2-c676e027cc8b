package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.response.EpResponse;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.controller.request.BankCardRiskCheckRequest;
import com.epaylinks.efps.rc.controller.response.CrossBorderResponse;
import com.epaylinks.efps.rc.controller.response.GamblingPyramidResponse;
import com.epaylinks.efps.rc.domain.GamblingPyramidRecord;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.service.BwListService;
import com.epaylinks.efps.rc.service.GamblingPyramidService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.export.ExportGamblingPyramidService;
import com.epaylinks.efps.rc.service.impl.ExportWebsiteEffectivenessService;
import com.epaylinks.efps.rc.util.CardUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/gamblingPyramid")
@Api(value = "GamblingPyramidController", description = "涉赌传销核验")
public class GamblingPyramidController {
    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private LogService logService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private GamblingPyramidService gamblingPyramidService;

    @Autowired
    private BwListService bwListService;

    @Autowired
    private ExportWebsiteEffectivenessService exportWebsiteEffectivenessService;

    @Autowired
    private ExportFileService exportFileService;

    @Autowired
    private ExportGamblingPyramidService exportGamblingPyramidService;

    @Autowired
    private OtherService ortherService;

    @Autowired
    private RedisDataTransService redisDataTransService;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @PostMapping(value = "/batchSave")
    @Exceptionable
    @ApiOperation(value = "涉赌传销核验模板导入", notes = "涉赌传销核验模板导入", httpMethod = "POST")
    @Logable(businessTag = "GamblingPyramidController.batchSave")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "模板类型【22:银行卡核验；30:网站核验；42：风控临时入金限制】", required = false, dataType = "Short", paramType = "query")
    })
    public BatchResponse batchSave(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "type",required = false,defaultValue = "22") Short type,
            @RequestHeader(value = "x-userid") Long userId
    ) {
        BatchResponse response = new BatchResponse();
        logger.printMessage("开始导入：" + sdf.format(new Date()));
        try {
            response = batchTaskService.batchSaveByFileThread(file, type, null, userId);
        } catch (Exception e) {
            e.printStackTrace();
            logger.printMessage(type + "-" + userId + "-模板导入错误：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        logger.printMessage("导入结束：" + sdf.format(new Date()));
        return response;
    }

    @GetMapping("/pageQuery")
    @Logable(businessTag = "GamblingPyramidController.pageQuery", outputResult = false)
    @ApiOperation(value = "涉赌传销核验分页查询", notes = "涉赌传销核验分页查询", httpMethod = "GET")
//    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginCreateTime", value = "开始创建日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "结束创建日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerNo", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "batchNo", value = "批次号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardAccount", value = "类型值", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardAccountType", value = "类型名称：001身份证号；002手机号；003社会统一信用代码；004银行账号；011：网站", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskTag", value = "风险标签：全部：不传值，涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation，无：none", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "verificationResults", value = "核验结果[1：成功；0：失败；2：未核验；全部：不传值]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "joinBlacklist", value = "是否加入黑名单[1：是；0：否；全部：不传值]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channelName", value = "上游名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "操作人", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源：0-商户API 1-内部使用 2-定期核验", required = false, dataType = "String", paramType = "query"),
    })
    public PageResult<GamblingPyramidResponse> pageResult(@RequestParam(value = "beginCreateTime", required = false) String beginCreateTime,
                                                          @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                          @RequestParam(value = "customerNo", required = false) String customerNo,
                                                          @RequestParam(value = "batchNo", required = false) String batchNo,
                                                          @RequestParam(value = "cardAccount", required = false) String cardAccount,
                                                          @RequestParam(value = "cardAccountType", required = false) String cardAccountType,
                                                          @RequestParam(value = "riskTag", required = false) String riskTag,
                                                          @RequestParam(value = "joinBlacklist", required = false) String joinBlacklist,
                                                          @RequestParam(value = "verificationResults", required = false) String verificationResults,
                                                          @RequestParam(value = "channelName", required = false) String channelName,
                                                          @RequestParam(value = "realName", required = false) String realName,
                                                          @RequestParam(value = "source", required = false) String source,
                                                          @RequestParam(value = "display", defaultValue = "0", required = false) String display,
                                                          @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                          @RequestParam(required = false, defaultValue = "false") boolean download,
                                                          @RequestParam(required = false) String fileName,
                                                          @RequestParam(required = false, defaultValue = "csv") String type,
                                                          @RequestParam(required = false) String fileSource,
                                                          @RequestHeader(value = "x-userid") Long userId,
                                                          HttpServletRequest httpServletRequest) {
        PageResult<GamblingPyramidResponse> page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            paramsMap.put("beginCreateTime", beginCreateTime);
            paramsMap.put("endCreateTime", endCreateTime);
            paramsMap.put("customerNo", customerNo);
            paramsMap.put("batchNo", batchNo);
            paramsMap.put("cardAccountType", cardAccountType);
            paramsMap.put("opeSource",source);
            if (!StringUtils.isEmpty(cardAccount)) {
                paramsMap.put("cardAccountHash", CardUtils.getCardNoHash(cardAccount));
            }
            if (!StringUtils.isEmpty(riskTag)) {
                if ("none".equals(riskTag)) {
                    paramsMap.put("riskTag", riskTag);
                } else if ("gambling".equals(riskTag)) {
                    paramsMap.put("gamblingOrNot", "1");
                } else if ("pyramid".equals(riskTag)) {
                    paramsMap.put("pyramidOrNot", "1");
                } else if ("fraud".equals(riskTag)) {
                    paramsMap.put("fraudOrNot", "1");
                } else if ("cashout".equals(riskTag)) {
                    paramsMap.put("cashoutOrNot", "1");
                } else if ("violation".equals(riskTag)) {
                    paramsMap.put("violationOrNot", "1");
                } else {
                    paramsMap.put("riskTag",riskTag);
                }
            }
            paramsMap.put("joinBlacklist", joinBlacklist);
            paramsMap.put("verificationResults", verificationResults);
            paramsMap.put("realName", realName);
            paramsMap.put("channelName", channelName);
            paramsMap.put("display",display);

            User operUser = ortherService.selectUserById(userId);
            if (operUser != null) {
                if (Constants.PasUserType.COMPANY.code.equals(operUser.getUserType())){ // 分公司管理员能查询分公司下所有商户
                    paramsMap.put("userCompanyId", operUser.getCompanyId());
                } else if (Constants.PasUserType.SALES.code.equals(operUser.getUserType())){ // 分公司管理员能查询业务员的商户
                    paramsMap.put("businessManId", userId);
                } else {
                    List<Long> companyIds = redisDataTransService.getCompanyIdList(userId);
                    if (!companyIds.isEmpty()) {
                        paramsMap.put("companyIds", companyIds);
                    }
                }
            }

            if (download) {
                return exportFileService.download(paramsMap,fileName,httpServletRequest,exportGamblingPyramidService);
            }
            page = gamblingPyramidService.pageQuery(paramsMap, userId, display, download);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logService.printLog("涉赌分页查询错误：" + e.getMessage());
            logService.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(RcCode.SYSTEM_EXCEPTION.code);
                page.setMessage(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @PostMapping("/test")
    public BatchResponse test(String customerNo, String transOrderNo, String cardAccount, String batchNo, Long userId) {
        BatchResponse response = new BatchResponse();
        try {
            gamblingPyramidService.gamblingPyramidCheck(customerNo, transOrderNo, cardAccount, batchNo, userId,new GamblingPyramidService.CheckExtraParam());
        } catch (Exception e) {
            e.printStackTrace();
            logService.printLog("涉赌传销核验测试error");
            logService.printLog(e.getMessage());
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @PostMapping("/singleCheck")
    @Logable(businessTag = "GamblingPyramidController.singleCheck")
    @ApiOperation(value = "单个核验", notes = "单个核验", httpMethod = "POST")
    public CommonOuterResponse singleCheck(@RequestParam("customerNo") String customerNo,
                                           @RequestParam("cardAccount") String cardAccount,
                                           @RequestParam(value = "source",required = false) String source,
                                           @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return CommonOuterResponse.success(gamblingPyramidService.singleCheck(customerNo, cardAccount,source, userId));
        } catch (Exception e) {
            logger.printMessage("涉赌传销单个核验error:" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @PostMapping("/bankCardRiskCheck")
    public EpResponse<List<String>> bankCardRiskCheck(@RequestBody BankCardRiskCheckRequest request) {
        GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
        checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.CUSTOMER_API);
        checkExtraParam.setTransactionNo(request.getTransactionNo());
        CommonOuterResponse response = EpAssert.getOrThrow(() ->
                gamblingPyramidService.commonRiskCheck(request.getCustomerCode(),
                        null,
                        RcConstants.BusinessTagerType.BANK_CARD.code,
                        request.getCardNo(),
                        null,
                        0L,
                        checkExtraParam), RcCode.CHECK_CARD_FAIL);
        EpAssert.state(response.isSuccess() ||
                        RcCode.ADD_BWLIST_EXIS.code.equals(response.getReturnCode()) ||
                        RcCode.VERIFICATION_PERIOD.code.equals(response.getReturnCode()),
                RcCode.CHECK_CARD_FAIL.msg(response.getReturnMsg()));

        return EpResponse.success(bwListService.getBwRiskTagCode(RcConstants.BusinessTagerType.BANK_CARD.code, request.getCardNo()));
    }

    @PostMapping("/testExport")
    public void test(@RequestParam(value = "num",required = false) Integer num) {
        try {
            exportWebsiteEffectivenessService.handBuild(num);
        } catch (Exception e) {
            logger.printMessage("错误信息：" + e.getMessage());
            logger.printMessage(e);
        }
    }

    @GetMapping("/exportByRedis")
    public void exportByRedis() {
        try {
            exportWebsiteEffectivenessService.exportByRedis();
        } catch (Exception e) {
            logger.printMessage("通过redis获取数据导出错误信息：" + e.getMessage());
            logger.printMessage(e);
        }
    }

    @GetMapping("/getUpstreamList")
    public CommonOuterResponse getUpstreamList() {
        return CommonOuterResponse.success(RcConstants.UpstreamName.toList());
    }

    @PostMapping("/crossBorder")
    @ApiOperation(value = "风险核验（跨境调用）", notes = "风险核验（跨境调用）", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "checkValue", value = "核验值", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "checkType", value = "核验对象【IDENTITY_CARD：身份证；BUSINESS_LICENSE：营业执照；】", required = true, dataType = "String", paramType = "query")
    })
    @Logable(businessTag = "GamblingPyramidController.crossBorder")
    public CrossBorderResponse crossBorder(@RequestParam(value = "customerCode",required = false) String customerCode,
                                           @RequestParam("checkType") String checkType,
                                           @RequestParam("checkValue") String checkValue) {
        CrossBorderResponse response = new CrossBorderResponse();
        try {
            String targetType;
            if ("IDENTITY_CARD".equals(checkType)) {
                targetType = RcConstants.BusinessTagerType.IDENTITY_CARD.code;
            } else if ("BUSINESS_LICENSE".equals(checkType)) {
                targetType = RcConstants.BusinessTagerType.BUSINESS_LICENSE.code;
            } else if ("BANK_CARD".equals(checkType)) {
                targetType = RcConstants.BusinessTagerType.BANK_CARD.code;
            } else {
                throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
            }
            Boolean isBlack = bwListService.isBlackList(targetType,checkValue);
            if (isBlack) {
                response.setRisk("1");
                return response;
            }
            GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
            checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.NETWORK_ACCESS);
            CommonOuterResponse checkResponse =
                    gamblingPyramidService.commonRiskCheck(customerCode,
                    null,
                    targetType,
                    checkValue,
                    null,
                    0L, checkExtraParam);
            if (checkResponse.getData() != null) {
                GamblingPyramidRecord gamblingPyramidRecord = (GamblingPyramidRecord)checkResponse.getData();
                if ((org.apache.commons.lang3.StringUtils.isNotBlank(gamblingPyramidRecord.getRemarks()) && gamblingPyramidRecord.getRemarks().contains("已在黑名单库"))
                        || org.apache.commons.lang3.StringUtils.isNotBlank(gamblingPyramidRecord.getRiskTag())) {
                    response.setRisk("1");
                } else {
                    response.setRisk("0");
                }
            }
        } catch (Exception e) {
            logger.printMessage("crossBorder风控核验错误:" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @GetMapping("/getRiskTagList")
    @ApiOperation(value = "获取风险标签查询列表", notes = "获取风险标签查询列表", httpMethod = "GET")
    public CommonOuterResponse getRiskTagList(@RequestHeader(value = "x-userid",required = false) Long userId) {
        List<Map<String, String>> riskTagList = RcConstants.RiskTag.riskTagList();
        Map<String,String> noneMap = new HashMap<>();
        noneMap.put("code","none");
        noneMap.put("comment","无");
        riskTagList.add(noneMap);
        return CommonOuterResponse.success(riskTagList);
    }

    @PostMapping("/testUnionBlack")
    public void testUnionBlack(@RequestParam String checkValue,
                               @RequestParam String customerCode) {
        try {
            GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
            checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.NETWORK_ACCESS);
            CommonOuterResponse checkResponse =
                    gamblingPyramidService.commonRiskCheck(customerCode,
                            null,
                            RcConstants.BusinessTagerType.IDENTITY_CARD.code,
                            checkValue,
                            null,
                            0L, checkExtraParam);
        } catch (Exception e) {
            logger.printMessage("测试错误：" + e.getMessage());
            logger.printLog(e);
        }
    }
}

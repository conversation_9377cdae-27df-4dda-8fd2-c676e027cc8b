package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.dao.RcDefineMapper;
import com.epaylinks.efps.rc.domain.RcDefineGroup;
import com.epaylinks.efps.rc.service.RcDefineGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018.09.29
 */
@RestController
@RequestMapping("/defineGroup")
@Api(value = "RcDefineGroupController",description = "风控指标定义分组")
public class RcDefineGroupController {

	@Autowired
	private RcDefineGroupService rcDefineGroupService;


	@GetMapping("/group")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.group")
	@ApiOperation(value ="指标大小类查询")
	public Map<String,Object> group(){
		Map<String,Object> map = new HashedMap();
		List<Map<String,Object>> list = rcDefineGroupService.groupQuery();
		map.put("returnCode",CommonOuterResponse.SUCCEE);
		map.put("returnMessage","查询成功");
		map.put("data",list);
		return map;
	}





	@GetMapping("/bigGroupQuery")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.bigGroupQuery")
	@ApiOperation(value ="指标大类查询")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "groupIdentifier",value = "大类编号", dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "pageNum",value = "当前页面", dataType = "Integer",required = true,paramType = "query"),
					@ApiImplicitParam(name = "pageSize",value = "页面大小", dataType = "Integer",required = true,paramType = "query")
			}
	)
	public PageResult<RcDefineGroup> bigGroupQuery(String groupIdentifier,Integer pageNum,Integer pageSize){
		if(StringUtils.isBlank(groupIdentifier)){
			groupIdentifier = "";
		}
		PageResult<RcDefineGroup> result = rcDefineGroupService.pageBigQuery(groupIdentifier,pageNum,pageSize);
		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功！");
		return result;
	}




	@GetMapping("/smallGroupQuery")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcArchive.limitQuery")
	@ApiOperation(value ="指标小类查询")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "bigId",value = "大类Id", dataType = "Long",required = false,paramType = "query"),
					@ApiImplicitParam(name = "identifier",value = "小类编号", dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "pageNum",value = "当前页面", dataType = "Integer",required = true,paramType = "query"),
					@ApiImplicitParam(name = "pageSize",value = "页面大小", dataType = "Integer",required = true,paramType = "query")
			}
	)
	public PageResult<Map<String,Object>> smallGroupQuery(Long bigId,String identifier,Integer pageNum,Integer pageSize){
		PageResult<Map<String,Object>> result = rcDefineGroupService.pageSmallQuery(bigId,identifier,pageNum,pageSize);
		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功！");
		return result;
	}


}

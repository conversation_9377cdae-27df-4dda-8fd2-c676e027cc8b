package com.epaylinks.efps.rc.controller;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.service.ComplainTradeImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/complain")
@Api(value = "ComplainTradeImportController", description = "投诉交易记录导入管理")
public class ComplainTradeImportController {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private ComplainTradeImportService importService;

    /**
     * 投诉交易记录批量导入
     */
    @PostMapping(value = "/import")
    @Exceptionable
    @ApiOperation(value = "投诉交易记录批量导入", notes = "投诉交易记录批量导入", httpMethod = "POST")
    public CommonResponse batchSave(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ){
        CommonResponse response = new CommonResponse();
        try {
            logger.printMessage("进入投诉交易记录批量导入，操作用户ID："+userId);
            response = importService.importByFile(file,userId);
        } catch (AppException e) {
            logger.printMessage("投诉交易记录批量导入错误："+e.getLocalizedMessage());
            logger.printLog(e);
            response.setCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("投诉交易记录批量导入异常："+e.getLocalizedMessage());
            logger.printLog(e);
            response.setCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setErrorMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
		return response;
    }
}

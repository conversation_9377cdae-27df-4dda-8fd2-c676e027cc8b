package com.epaylinks.efps.rc.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.oplog.OpService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.domain.cust.AttachmentVO;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/amountLimit")
@Api(value = "AmountLimitController", description = "额度指标控制类")
public class AmountLimitController {

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private RcLimitService rcLimitService;

	@Autowired
	private OtherService ortherService;

	@Autowired
	private RcArchiveService rcArchiveService;

	@Autowired
	private AmountLimitAudService amountLimitAudService;
	
	@Autowired
	private FsService fsService;

	@Autowired
	private RcOperateLogService rcOperateLogService;

    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;
	
	@Autowired
	private LogService logService;

	@Autowired
    private LimitSwitchService limitSwitchService;

    @Autowired
    private LimitAttachmentService limitAttachmentService;

    @Autowired
    private OpService opService;

    @Autowired
    private CommonLogger logger;
	
    static final String PICTURE_TYPE = "limit_pic";

    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID

    @Autowired
    private RedisTemplate redisTemplate;
    
	@Validatable
	@Exceptionable
	@Logable(businessTag = "AmountLimitController.amountLimitAudSh")
	@ApiOperation(value ="商户额度审核")
	@OpLog(opModule = "风控管理-业务管理-商户限额审核",opMethod = "审核")
	@ApiImplicitParams({
//		@ApiImplicitParam(name = "audId",value = "审核ID", dataType = "Long",required = true,paramType = "query"),
		@ApiImplicitParam(name = "audIds",value = "审核ID，用英文逗号隔开", dataType = "String",required = true,paramType = "query"),
	    @ApiImplicitParam(name = "result",value = "审核结果：true：通过；false: 不通过", dataType = "Boolean",required = true,paramType = "query"),
		@ApiImplicitParam(name = "audOption",value = "审核备注", dataType = "String",required = true,paramType = "query")
	})
	@PostMapping("/amountLimitAudSh")
	public CommonOuterResponse amountLimitAudSh(
	        @RequestHeader("x-userid")Long userId,
            @RequestParam String audIds,
            @RequestParam String audOption,
            @RequestParam Boolean result){

		CommonOuterResponse response = new CommonOuterResponse();
		try {
            response = ((AmountLimitController)AopContext.currentProxy()).auditMethod(userId,audIds,audOption,result);
//            response = auditMethod(userId,audIds,audOption,result);
        } catch (AppException e) {
            logger.printMessage("商户额度审核error:" + e.getMessage());
            logger.printLog(e);
		    response.setReturnCode(e.getErrorCode());
		    response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("商户额度审核error:" + e.getMessage());
            logger.printLog(e);
		    response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
		    response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }

		return response;
	}

	@Transactional(rollbackFor = {RuntimeException.class,AppException.class,Exception.class})
	public CommonOuterResponse auditMethod(Long userId,String audIds,String audOption,Boolean result) {
	    logService.printLog("进入额度审核调用");
        CommonOuterResponse response = new CommonOuterResponse();
        String jg= "";
        Date currentTime = new Date();
        User user = ortherService.selectUserById(userId);
        if(user == null){
            response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
            response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
            return response;
        }

        // 修改为批量审核
        String[] strs = audIds.split(",");
        List<Long> bwIdList = new ArrayList<>();
        try{
            for (int i = 0; i < strs.length; i++) {
                Long temp = Long.parseLong(strs[i]);
                bwIdList.add(temp);
            }
        } catch (Exception e) {
            response.setReturnCode(RcCode.RC_ARG_ERROR.code);
            response.setReturnMsg(RcCode.RC_ARG_ERROR.message);
            return response;
        }
        for (int i = 0; i < bwIdList.size(); i++) {
            Long audId = bwIdList.get(i);
            // 原审核代码===============start
            RcLimitAud rcLimitAud = amountLimitAudService.selectLimitAudById(audId);
            if (rcLimitAud == null) {
                response.setReturnCode(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code);
                response.setReturnMsg(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
                throw new AppException(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
//                return response;
            }

            //判断是否是待审核状态
            if (!RcConstants.AudStatus.WAITING.code.equals(rcLimitAud.getAudStatus())) {
                response.setReturnCode(RcCode.AUD_EXCEPTION.code);
                response.setReturnMsg(RcCode.AUD_EXCEPTION.message);
                throw new AppException(RcCode.AUD_EXCEPTION.code,RcCode.AUD_EXCEPTION.message);
//                return response;
            }

            // 查询风控档案
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimitAud.getTargetType(), rcLimitAud.getAudCode());
            if (rcArchive == null) {
                return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
            }

            //通过审核
            if (result != null && result) {
                if (StringUtils.isBlank(audOption)) {
                    response.setReturnCode(RcCode.AUD_OPTION_EXCEPTION.code);
                    response.setReturnMsg(RcCode.AUD_OPTION_EXCEPTION.message);
                    throw new AppException(RcCode.AUD_OPTION_EXCEPTION.code,RcCode.AUD_OPTION_EXCEPTION.message);
//                    return response;
                }
                //审核通过（只保留一次审核）
                jg = "通过复审";
                rcLimitAud.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
                rcLimitAud.setLastUser(userId);
                rcLimitAud.setLastName(user.getName());
                rcLimitAud.setLastTime(currentTime);
                rcLimitAud.setLastOpinion(audOption);

                // 组装数据
                AmountLimitObject amountLimitObject = new AmountLimitObject();
                if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimitAud.getTargetType())) {
                    amountLimitObject.setMainAmount(amountLimitAudService.queryAudLimitValueToObj(audId, MainAmountLimit.class));
                    amountLimitObject.setOutAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId,OutAmountLimit.class));
                    amountLimitObject.setInstAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId,InstAmountLimit.class));
                    amountLimitObject.setWithdrawAmount(amountLimitAudService.queryAudLimitValueToObj(audId, WithdrawAmountLimit.class));
                    amountLimitObject.setCardAmount(amountLimitAudService.queryAudLimitValueToObj(audId, CardAmountLimit.class));
                    amountLimitObject.setMerchantAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, MerchantAmountLimit.class));
                    amountLimitObject.setUserAmount(amountLimitAudService.queryAudLimitValueToObj(audId, UserAmountLimit.class));
                    CustomerInfo customerInfo = null;
                    try {
                        customerInfo = cumCacheServiceImpl.getCustomerInfo(rcLimitAud.getAudCode(), rcLimitAud.getAudCode(), UserType.PPS_USER.code);
                    } catch (Exception e) {
                        logService.printLog(e);
                        throw new AppException(RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.code,RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.message + ":cum");
                    }
                    //平台商维度限额
                    if (customerInfo != null && Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) {
                        amountLimitObject.setPlatCustomerAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, PlatCustomerAmountLimit.class));
                    }
                } else if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                        || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType())) {
                    amountLimitObject.setCertificateAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, CertificateAmountLimit.class));
                    amountLimitObject.setInstAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId,InstAmountLimit.class));
                } else if (RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())) {
                    amountLimitObject.setTermAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, TermAmountLimit.class));

                } else if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())) {
                    amountLimitObject.setPersonAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, PersonAmountLimit.class));
                } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(rcArchive.getArchiveType())) {
                    amountLimitObject.setMainAmount(amountLimitAudService.queryAudLimitValueToObj(audId, MainAmountLimit.class));
                    amountLimitObject.setOutAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId,OutAmountLimit.class));
                    amountLimitObject.setWithdrawAmount(amountLimitAudService.queryAudLimitValueToObj(audId, WithdrawAmountLimit.class));
                    amountLimitObject.setCardAmount(amountLimitAudService.queryAudLimitValueToObj(audId, CardAmountLimit.class));
                    amountLimitObject.setMerchantAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, MerchantAmountLimit.class));
                    amountLimitObject.setUserAmount(amountLimitAudService.queryAudLimitValueToObj(audId, UserAmountLimit.class));
                } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(rcArchive.getArchiveType())) {
                    amountLimitObject.setMainAmount(amountLimitAudService.queryAudLimitValueToObj(audId, MainAmountLimit.class));
                    amountLimitObject.setWithdrawAmount(amountLimitAudService.queryAudLimitValueToObj(audId, WithdrawAmountLimit.class));
                    amountLimitObject.setCardAmount(amountLimitAudService.queryAudLimitValueToObj(audId, CardAmountLimit.class));
                    amountLimitObject.setMerchantAmountLimit(amountLimitAudService.queryAudLimitValueToObj(audId, MerchantAmountLimit.class));
                    amountLimitObject.setUserAmount(amountLimitAudService.queryAudLimitValueToObj(audId, UserAmountLimit.class));
                }

                // 更新限额值
                saveAmountLimit(rcArchive, amountLimitObject, user,null);

                rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
            } else if (result != null && !result) {
                if (StringUtils.isBlank(audOption)) {
                    response.setReturnCode(RcCode.AUD_OPTION_EXCEPTION.code);
                    response.setReturnMsg(RcCode.AUD_OPTION_EXCEPTION.message);
                    throw new AppException(RcCode.AUD_OPTION_EXCEPTION.code,RcCode.AUD_OPTION_EXCEPTION.message);
//                    return response;
                }
                jg = "审核不通过";
                rcLimitAud.setAudStatus(RcConstants.AudStatus.FAIL.code);
                rcLimitAud.setLastUser(userId);
                rcLimitAud.setLastName(user.getName());
                rcLimitAud.setLastTime(currentTime);
                rcLimitAud.setLastOpinion(audOption);

                rcArchive.setAudStatus(RcConstants.AudStatus.FAIL.code);
            }
            // 更新审核记录状态
            amountLimitAudService.updateByPrimaryKey(rcLimitAud);
            // 更新商户附件信息和状态
            limitAttachmentService.updateAttachmentStatus(rcArchive,result);

            // 更新档案审核状态
            rcArchive.setUpdateTime(new Date());
            rcArchiveService.updateRcArchiveSelective(rcArchive);
            response.setReturnCode(CommonOuterResponse.SUCCEE);
            response.setReturnMsg("操作成功!");
            // 记录日志
            if (RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())) {
                OpLogHandle.setOpModule("风控管理-业务管理-终端限额审核");
            } else if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())) {
                OpLogHandle.setOpModule("风控管理-业务管理-个人限额审核");
            }
            // 原审核代码===============end
        }
        if (!response.getReturnCode().equals(CommonOuterResponse.SUCCEE)) {
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }

        OpLogHandle.setOpContent("用户:" + user.getName() + "把申请单号为:" + audIds + ",的审核状态设置为:" + jg);
        return response;
    }

	
	@Validatable
	@Exceptionable
	@Logable(businessTag = "amountLimitAudDetail")
	@ApiOperation(value ="商户额度审核详情")
	@ApiImplicitParams({
	    @ApiImplicitParam(name = "audId",value = "审核ID", dataType = "Long",required = true,paramType = "query"),
	})
	@PostMapping("/amountLimitAudDetail")
	public RcLimitAud amountLimitAudDetail(Long audId){
		RcLimitAud rcLimitAud = amountLimitAudService.selectAudByKey(audId);

		if(rcLimitAud == null){
	        rcLimitAud = new RcLimitAud();
			rcLimitAud.setReturnCode(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code);
			rcLimitAud.setReturnMsg(RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
			return rcLimitAud;
		}

		//获取图片文件
		//文件路径
		if(StringUtils.isNotBlank(rcLimitAud.getUrl())){
			Map<String,String> map = fsService.filePath(rcLimitAud.getUrl(),30,100,"show");
			rcLimitAud.setUrl(map.get("filePath"));
		}
		rcLimitAud.setReturnCode(CommonOuterResponse.SUCCEE);
		rcLimitAud.setReturnMsg("查询成功!");
		return rcLimitAud;
	}
	

	@Validatable
	@Exceptionable
	@Logable(businessTag = "amountLimitAudQuery")
	@ApiOperation(value ="商户额度审核列表")
	@ApiImplicitParams({
    	@ApiImplicitParam(name = "customerCode",value = "商户编号", dataType = "String",required = false,paramType = "query"),
    	@ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String", paramType = "query"),
    	@ApiImplicitParam(name = "pageNum", value = "当前页面号", required = true, dataType = "Integer", paramType = "query"),
    	@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "Integer", paramType = "query"),
    	@ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
    	@ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
	})
	@PostMapping("/amountLimitAudQuery")
	public PageResult<RcLimitAud> amountLimitAudQuery(
			String customerCode,
			String status,
			Integer pageNum,
			Integer pageSize,
			String startTime,
			String endTime
	){
		PageResult<RcLimitAud> result = null;

		result = amountLimitAudService.queryAudListPage(customerCode,status,pageNum,pageSize,startTime,endTime);

		result.setCode(CommonOuterResponse.SUCCEE);
		result.setMessage("查询成功");

		return result;
	}

	
	@Validatable
	@Exceptionable
	@Logable(businessTag = "uploadFile")
	@ApiOperation(value ="上传审核文件")
	@PostMapping("/uploadFile")
	public Map<String,String> uploadFile(
			@RequestPart("file")MultipartFile file,@RequestParam("customerCode")String customerCode
	){
		//获取上传token
		String uploadToken = fsService.uploadToken(PICTURE_TYPE,"RC","0","商户额度限制图片");
		//上传文件
		FileUploadResponse resp = fsService.uploadFile(file,uploadToken,customerCode);
		//文件路径
		Map<String,String> map = fsService.filePath(resp.getUniqueId(),30,100,"show");

		Map<String,String> returnMap = new HashedMap();

		if(!"0000".equals(map.get("resultCode"))){
			returnMap.put("returnCode",map.get("resultCode"));
			returnMap.put("returnMsg",map.get("resultMsg"));
			return returnMap;
		}
		returnMap.put("returnCode","0000");
		returnMap.put("returnMsg","查询成功!");
		returnMap.put("url",map.get("filePath"));
		returnMap.put("uniqueId",resp.getUniqueId());
		return returnMap;
	}

	
	@Exceptionable
	@Transactional
	@Logable(businessTag = "amountLimitSet")
	@ApiOperation(value ="商户限额管理-限额修改（或提交审核）")
	@OpLog(opModule = "风控管理-业务管理-商户限额设置",opMethod="设置商户限额")
	@ApiImplicitParams(
	{
	})
	@PostMapping("/amountLimitSet")
	public CommonOuterResponse amountLimitSet(
			@RequestHeader("x-userid") Long userId,
			@RequestBody() AmountLimitObject amountLimitObject){

        System.out.println("进入方法：========================");
        // 档案类型：001：身份证；003：统一社会信用代码；005:商户编号（兼容原没类型情况）
        String type = StringUtils.isNotBlank(amountLimitObject.getType()) 
                ? amountLimitObject.getType() : RcConstants.BusinessTagerType.CUSTOMER_CODE.code;
        // 商户限额管理添加附件判断
        if ((RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(type)
                || RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(type)
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(type))
                && !org.springframework.util.StringUtils.isEmpty(amountLimitObject.getSource())
                && "1".equals(amountLimitObject.getSource())) {
            if (amountLimitObject.getAttachments() == null || amountLimitObject.getAttachments().size() == 0) {
                throw new AppException(RcCode.MISSING_ATTACHMENT.code,RcCode.MISSING_ATTACHMENT.message);
            }
        }

        String customerCode = amountLimitObject.getCustomerCode(); // 商户编号/证件号码
        //查询风控档案
        RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(type, customerCode);
        // 行业默认限额列表来源于批量设置，无档案时插入
        if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(type) && rcArchive == null) {
            Integer nature = amountLimitObject.getNature();
            rcArchive = new RcArchive();
            rcArchive.setArchiveId(sequenceService.nextValue("rc"));
            rcArchive.setUserId(userId);
            rcArchive.setUserName(null);
            rcArchive.setAccountStatus("0");
            rcArchive.setRcStatus("0");
            rcArchive.setCusStatus("1");
            rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
            rcArchive.setArchiveType(RcConstants.BusinessTagerType.INDUSTRY.code);
            rcArchive.setArchiveCode(customerCode);
            rcArchive.setArchiveName(RcConstants.BusinessTagerType.INDUSTRY.message);
            rcArchive.setRcLevel("LOW_LEVEL");
            rcArchive.setRegTime(new Date());
            rcArchive.setType(nature);
            String[] mccIndustry = customerCode.split("_");
            if (mccIndustry.length < 2) {
                throw new AppException(RcCode.PARAM_ERROR.code,"设置行业默认限额参数错误：" + customerCode);
            }
            if (Constants.CustomerType.BUSINESSMAN.code == nature || Constants.CustomerType.MICRO.code == nature) {
                rcArchive.setMcc(mccIndustry[1]);
            } else if (Constants.CustomerType.ENTERPRISE.code == nature || Constants.CustomerType.ABROAD.code == nature
                    || Constants.CustomerType.GOVERNMENT.code == nature || Constants.CustomerType.OTHERS.code == nature) {
                rcArchive.setIndustry(mccIndustry[1]);
            }
            rcArchiveService.insert(rcArchive);
        }
        if (rcArchive == null) {
            return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message); 
        }
        // 商户注销不允许修改
        if (RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
            return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message); 
        }
        // 审核中不允许修改
        if ("01".equals(rcArchive.getAudStatus())) {
            return CommonOuterResponse.fail(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code, RcCode.AUD_STATUS_UPDATE_EXCEPTION.message); 
        }
        
        //根据userId查找userName
        User user = ortherService.selectUserById(userId);
        if(user == null){
            return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
        }
        
	    // 判断是否需要审核，不需审核走原逻辑  2020.8.10
	    String audFlag = ortherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        // 商户限额管理-新增和更新附件列表
        List<AttachmentVO> attachmentVOS = amountLimitObject.getAttachments();
        logService.printLog("打印附件信息：" + attachmentVOS);
        if ((RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType()))
                && attachmentVOS != null && attachmentVOS.size() > 0) {
            LimitAttachmentWithBLOBs limitAttachment = new LimitAttachmentWithBLOBs();
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("archiveType",rcArchive.getArchiveType());
            paramMap.put("archiveCode",rcArchive.getArchiveCode());
            List<LimitAttachmentWithBLOBs> limitAttachments = limitAttachmentService.selectByArchiveTypeAndCode(paramMap);
            logService.printLog("打印数据库附件信息：" + limitAttachments);
            if (limitAttachments == null || limitAttachments.size() == 0) { // 空值时插入
                logService.printLog("-----------进入插入");
                limitAttachment.setAttachmentId(limitAttachmentService.querySeq());
                limitAttachment.setArchiveType(rcArchive.getArchiveType());
                limitAttachment.setArchiveCode(rcArchive.getArchiveCode());
                limitAttachment.setAuditStatus("1".equals(audFlag) ? "0" : "1");
                limitAttachment.setAttachmentData(JSON.toJSONString(attachmentVOS));
                limitAttachment.setCreateTime(new Date());
                limitAttachment.setUpdateTime(new Date());
                limitAttachmentService.save(limitAttachment);
            } else {
                logService.printLog("----进入更新");
                limitAttachment = limitAttachments.get(0);
                limitAttachment.setAuditStatus("1".equals(audFlag) ? "0" : "1");
                limitAttachment.setOldData(limitAttachment.getAttachmentData());
                limitAttachment.setAttachmentData(JSON.toJSONString(attachmentVOS));
                limitAttachment.setUpdateTime(new Date());
                limitAttachmentService.updateById(limitAttachment);
            }
        }
        if ("1".equals(audFlag)) {
	        // 提交审核
	        try {
    	        amountLimitAudService.submitLimitAud(rcArchive, amountLimitObject, userId);
                if ("batch".equals(amountLimitObject.getSource())) { // 批量页面导入自动审核通过
                    RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(rcArchive.getArchiveCode(), rcArchive.getArchiveType());
                    if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
                        ((AmountLimitController)AopContext.currentProxy()).auditMethod(defaultAuditUserId,String.valueOf(limitAudRecord.getAudId()),"批量导入自动审核通过",true);
                    }
                }
	        } catch (AppException e) {
	            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
	        } catch (Exception e) {
                logger.printMessage("修改限额提交审核错误:" + e.getMessage());
                logger.printLog(e);
	            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
	        }
            OpLogHandle.setOpContent("用户:"+user.getName()+",提交风控档案:"+customerCode+"更新限额值审核");
	        return CommonOuterResponse.success("提交成功");
	    }
	    
        // 不需审核走原逻辑 2020.8.10
	    try {
	        // 更新风控限额值
	        saveAmountLimit(rcArchive, amountLimitObject, user,null);
	        
	        // 更新档案审核状态 ,原来审核不通过改为自动审核后，应该改为审核通过状态 20220119
	        if (!RcConstants.AudStatus.SUCCESS.code.equals(rcArchive.getAudStatus())) {
	            rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
	            rcArchive.setUpdateTime(new Date());
	            rcArchiveService.updateRcArchiveSelective(rcArchive);
	        }
	        
	        // 记录日志
	        if (RcConstants.BusinessTagerType.TERM.code.equals(type)) {
	            OpLogHandle.setOpModule("风控管理-业务管理-终端限额设置");
	            OpLogHandle.setOpMethod("设置终端限额");
	        } else if (RcConstants.BusinessTagerType.PERSON.code.equals(type)) {
	            OpLogHandle.setOpModule("风控管理-业务管理-个人限额设置");
                OpLogHandle.setOpMethod("设置个人限额");
	        }
	        OpLogHandle.setOpContent("用户:"+user.getName()+",给档案编号:"+  customerCode +"设置了新的限额");
	    } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
	    } catch (Exception e) {
            logger.printMessage("更新风控限额值:" + e.getMessage());
            logger.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        
		return CommonOuterResponse.success("提交成功");
	}

    private void savePlatAmountLimit(RcArchive rcArchive , AmountLimitObject amountLimitObject, User user) {
        PlatCustomerAmountLimit platCustomerAmountLimitNew = amountLimitObject.getPlatCustomerAmountLimit();

        if (platCustomerAmountLimitNew == null) {
            return;
        }

        RcArchive platArchive = new RcArchive();
        org.springframework.beans.BeanUtils.copyProperties(rcArchive, platArchive);
        platArchive.setArchiveType(RcConstants.BusinessTagerType.PLAT_CUSTOMER.code);

        Map<String, Long> platCustomerMap = rcLimitService.queryAmountLimit(platArchive.getArchiveCode(),
                RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, platArchive.getRcLevel());

        PlatCustomerAmountLimit platCustomerAmountLimitDefault = PlatCustomerAmountLimit.fromMap(platCustomerMap);

        RcLimit paltDayInLimit = rcLimitService.queryLimit(DefineCode.DAY_IN.defineId, platArchive.getArchiveType(), platArchive.getArchiveCode());
        if (paltDayInLimit == null) {
            paltDayInLimit = createRcLimt(DefineCode.DAY_IN,
                    platArchive, platCustomerAmountLimitNew.getPlatInAmountDay().toString(),"Long",user);
            rcLimitService.save(paltDayInLimit);
            insertOperateLog(platArchive, user.getUid(), new Date(), DefineCode.DAY_IN, String.valueOf(platCustomerAmountLimitDefault.getPlatInAmountDay()),
                    platCustomerAmountLimitNew.getPlatInAmountDay().toString());
        } else {
            String origValue = paltDayInLimit.getLimitValue();
            paltDayInLimit.setLimitValue(platCustomerAmountLimitNew.getPlatInAmountDay().toString());
            rcLimitService.updateLimit(paltDayInLimit);
            insertOperateLog(platArchive, user.getUid(), new Date(), DefineCode.DAY_IN, origValue,  platCustomerAmountLimitNew.getPlatInAmountDay().toString());
        }

        RcLimit paltMonthInLimit = rcLimitService.queryLimit(DefineCode.MONTH_IN.defineId, platArchive.getArchiveType(), platArchive.getArchiveCode());
        if (paltMonthInLimit == null) {
            paltMonthInLimit = createRcLimt(DefineCode.MONTH_IN,
                    platArchive, platCustomerAmountLimitNew.getPlatInAmountMonth().toString(),"Long",user);
            rcLimitService.save(paltMonthInLimit);
            insertOperateLog(platArchive, user.getUid(), new Date(), DefineCode.MONTH_IN, String.valueOf(platCustomerAmountLimitDefault.getPlatInAmountMonth()),
                    platCustomerAmountLimitNew.getPlatInAmountMonth().toString());
        } else {
            String origValue = paltMonthInLimit.getLimitValue();
            paltMonthInLimit.setLimitValue(platCustomerAmountLimitNew.getPlatInAmountMonth().toString());
            rcLimitService.updateLimit(paltMonthInLimit);
            insertOperateLog(platArchive, user.getUid(), new Date(), DefineCode.MONTH_IN, origValue,  platCustomerAmountLimitNew.getPlatInAmountMonth().toString());
        }
    }
	
	/**
	 * 更新限额值
	 * @param rcArchive
	 * @param amountLimitObject
	 * @param user
	 */
	@Transactional
	void saveAmountLimit(RcArchive rcArchive , AmountLimitObject amountLimitObject, User user,String source) {
        logService.printLog("source:" + source);
	    logService.printLog("进入更新限额:" + JSON.toJSONString(amountLimitObject));
	    Long userId = user.getUid();
        MainAmountLimit amountLimit = amountLimitObject.getMainAmount();
        OutAmountLimit outAmountLimit = amountLimitObject.getOutAmountLimit();
        InstAmountLimit instAmountLimit = amountLimitObject.getInstAmountLimit() != null ? amountLimitObject.getInstAmountLimit() : new InstAmountLimit();
        WithdrawAmountLimit withdrawAmount = amountLimitObject.getWithdrawAmount();
        CardAmountLimit cardAmount = amountLimitObject.getCardAmount();
        UserAmountLimit userAmount = amountLimitObject.getUserAmount();
        MerchantAmountLimit merchantAmountLimit = amountLimitObject.getMerchantAmountLimit();
        CertificateAmountLimit certificateAmountLimit = amountLimitObject.getCertificateAmountLimit();


        if (amountLimitObject.getRcLevel() != null && StringUtils.isNotBlank(RcConstants.RcLevel.getMessageByCode(amountLimitObject.getRcLevel()))
                && !"firstLimit".equals(source)) {
            // 修改风控等级（批量设置），批量设置等级与限额 同一事务处理，避免改等级后设置限额失败。
            rcArchive.setUserId(user.getUid());
            rcArchive.setUserName(user.getName());
            rcArchive.setRcLevel(amountLimitObject.getRcLevel());
            rcArchive.setRcLevelReason(amountLimitObject.getRcLevelReason());
            rcArchiveService.updateRcLevel(rcArchive, amountLimitObject.getRcLevel());

            OpLogHandle.setOpContent("用户：" + user.getName() + ",修改风控档案：" + rcArchive.getArchiveCode() + "的风控等级为："
                    + amountLimitObject.getRcLevel() + ",修改原因是:" + amountLimitObject.getRcLevelReason());
        } else {
            rcArchive.setUpdateTime(new Date());
            rcArchiveService.updateRcArchiveSelective(rcArchive); // 更新时间
        }
        if ("firstLimit".equals(source)) { // 初审设置限额
            rcArchive.setSource("firstLimit");
        }


        // 先查询该商户（或证件）是否有自己的限额
        boolean exist = false;
        String targetType = rcArchive.getArchiveType();
        if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)) { 
            exist = rcLimitService.queryLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId, targetType, rcArchive.getArchiveCode()) == null ? false : true ;
        } else if(RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType)) {
            targetType = RcConstants.BusinessTagerType.CUSTOMER_CODE.code; // 原默认为商户类型
            exist = rcLimitService.queryLimit(DefineCode.SINGLT_IN.defineId, targetType, rcArchive.getArchiveCode()) == null ? false : true ;
        } else if(RcConstants.BusinessTagerType.TERM.code.equals(targetType)) {
            exist = rcLimitService.queryLimit(DefineCode.SINGLT_IN.defineId, targetType, rcArchive.getArchiveCode()) == null ? false : true ;
        } else if(RcConstants.BusinessTagerType.PERSON.code.equals(targetType)) {
            exist = rcLimitService.queryLimit(DefineCode.ACCOUNT_NAME_LIMIT.defineId, targetType, rcArchive.getArchiveCode()) == null ? false : true ;
        } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
            exist = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId, targetType, rcArchive.getArchiveCode()) == null ? false : true;
        } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(targetType)) {
            exist = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId, targetType, rcArchive.getArchiveCode() ) == null ? false : true;
        }

        // ******** 商户门户新增指标
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType)) {
            String daliyValue = org.springframework.util.StringUtils.isEmpty(outAmountLimit.getDaliyOutLimit()) ? null : outAmountLimit.getDaliyOutLimit().toString();
            String singleValue = org.springframework.util.StringUtils.isEmpty(outAmountLimit.getSingleOutLimit()) ? null : outAmountLimit.getSingleOutLimit().toString();
            Date modifyDay = new Date();
            boolean outDaliyExist = false;
            boolean outSingleExist = false;
            // 查询该商户门户出金是否有设置限额 rc_limit_switch
            LimitSwitch daliy = limitSwitchService.getLimitInfo(rcArchive.getArchiveCode(),"Daliy-Out-Limit");
            LimitSwitch single = limitSwitchService.getLimitInfo(rcArchive.getArchiveCode(),"Single-Out-Limit");
            if (!org.springframework.util.StringUtils.isEmpty(daliy)) {
                outDaliyExist = true;
            }
            if (!org.springframework.util.StringUtils.isEmpty(single)) {
                outSingleExist = true;
            }
            Map<String,Long> map = rcLimitService.queryCustomerAmountLimit(rcArchive.getRcLevel(), rcArchive.getRcLevel(), rcArchive.getRcLevel());
            // 新增/更新出金设置指标
            if (!outDaliyExist && !org.springframework.util.StringUtils.isEmpty(outAmountLimit.getDaliyOutLimit())) {
                LimitSwitch newDaliy = new LimitSwitch();
                newDaliy.setId(limitSwitchService.selectPriKey());
                newDaliy.setCustomerNo(rcArchive.getArchiveCode());
                newDaliy.setLimitIndex("Daliy-Out-Limit");
                newDaliy.setLimitValue(outAmountLimit.getDaliyOutLimit());
                newDaliy.setLimitStatus("2");
                newDaliy.setCreateTime(new Date());
                newDaliy.setUpdateTime(new Date());
                limitSwitchService.insert(newDaliy);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DALIY_OUT_LIMIT, String.valueOf(map.get(DefineCode.DALIY_OUT_LIMIT.defineCode)), daliyValue);

            }
            if (outDaliyExist) {
                String oldValue = org.springframework.util.StringUtils.isEmpty(daliy.getLimitValue()) ? "" : daliy.getLimitValue() + "";
                daliy.setLimitValue(outAmountLimit.getDaliyOutLimit());
                if (org.springframework.util.StringUtils.isEmpty(outAmountLimit.getDaliyOutLimit()) || outAmountLimit.getDaliyOutLimit() == 0L) {
                    daliy.setLimitStatus("2");
                }
                daliy.setUpdateTime(new Date());
                limitSwitchService.updateLimitSwitch(daliy);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DALIY_OUT_LIMIT, oldValue, daliyValue);


            }
            if (!outSingleExist && !org.springframework.util.StringUtils.isEmpty(outAmountLimit.getSingleOutLimit())) {
                LimitSwitch newSingle = new LimitSwitch();
                newSingle.setId(limitSwitchService.selectPriKey());
                newSingle.setCustomerNo(rcArchive.getArchiveCode());
                newSingle.setLimitIndex("Single-Out-Limit");
                newSingle.setLimitValue(outAmountLimit.getSingleOutLimit());
                newSingle.setLimitStatus("2");
                newSingle.setCreateTime(new Date());
                newSingle.setUpdateTime(new Date());
                limitSwitchService.insert(newSingle);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_OUT_LIMIT, String.valueOf(map.get(DefineCode.SINGLE_OUT_LIMIT.defineCode)), singleValue);

            }
            if (outSingleExist) {
                String oldValue = org.springframework.util.StringUtils.isEmpty(single.getLimitValue()) ? "" : single.getLimitValue() + "";
                single.setLimitValue(outAmountLimit.getSingleOutLimit());
                if (org.springframework.util.StringUtils.isEmpty(outAmountLimit.getSingleOutLimit()) || outAmountLimit.getSingleOutLimit() == 0L) {
                    single.setLimitStatus("2");
                }
                single.setUpdateTime(new Date());
                limitSwitchService.updateLimitSwitch(single);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_OUT_LIMIT, oldValue, singleValue);

            }
        }
        
        //新增
        if (!exist){
            Date modifyDay = new Date();
            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType)
                    || RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {

                // 获取默认值，记录日志旧值使用
                //Map<String,Long> map = rcLimitService.queryCustomerAmountLimit(rcArchive.getArchiveCode(), rcArchive.getParentCode(), rcArchive.getRcLevel());
                // “风险等级限额+行业默认限额”同指标取最低值，不同指标按设定值配置
                RcArchive tmpArchive = new RcArchive();
                org.springframework.beans.BeanUtils.copyProperties(rcArchive,tmpArchive);
                rcLimitService.setRcArchive(tmpArchive);
                logger.printMessage("tmpArchive-print:" + JSON.toJSONString(tmpArchive));
                Map<String,Long> map = rcLimitService.queryMinAmountLimit(targetType,tmpArchive.getRcLevel(), tmpArchive.getRcLevel(), tmpArchive);

                logger.printMessage(targetType + "打印默认值：" + JSON.toJSONString(map));
                if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
                    // 商户门户单日出金限额
                    RcLimit daliyOutLimit = createRcLimt(DefineCode.DALIY_OUT_LIMIT,rcArchive,outAmountLimit.getDaliyOutLimit().toString(),"Long",user);
                    rcLimitService.save(daliyOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DALIY_OUT_LIMIT, String.valueOf(map.get(DefineCode.DALIY_OUT_LIMIT.defineCode)), outAmountLimit.getDaliyOutLimit().toString());

                    // 商户门户单笔出金限额
                    RcLimit singleOutLimit = createRcLimt(DefineCode.SINGLE_OUT_LIMIT,rcArchive,outAmountLimit.getSingleOutLimit().toString(),"Long",user);
                    rcLimitService.save(singleOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_OUT_LIMIT, String.valueOf(map.get(DefineCode.SINGLE_OUT_LIMIT.defineCode)), outAmountLimit.getSingleOutLimit().toString());
                }

                //单笔入金
                RcLimit singleInLimit = createRcLimt(DefineCode.SINGLT_IN,rcArchive,amountLimit.getInAmountSingle().toString(),"Long",user);
                rcLimitService.save(singleInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, String.valueOf(map.get(DefineCode.SINGLT_IN.defineCode)), amountLimit.getInAmountSingle().toString());
    
                //单日入金
                RcLimit dayInLimit = createRcLimt(DefineCode.DAY_IN,rcArchive,amountLimit.getInAmountDay().toString(),"Long",user);
                rcLimitService.save(dayInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, String.valueOf(map.get(DefineCode.DAY_IN.defineCode)), amountLimit.getInAmountDay().toString());
    
                //单月入金
                RcLimit monthInLimit = createRcLimt(DefineCode.MONTH_IN,rcArchive,amountLimit.getInAmountMonth().toString(),"Long",user);
                rcLimitService.save(monthInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, String.valueOf(map.get(DefineCode.MONTH_IN.defineCode)), amountLimit.getInAmountMonth().toString());
    
                //单年入金
                RcLimit yearInLimit = createRcLimt(DefineCode.YEAR_IN,rcArchive,amountLimit.getInAmountYear().toString(),"Long",user);
                rcLimitService.save(yearInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, String.valueOf(map.get(DefineCode.YEAR_IN.defineCode)), amountLimit.getInAmountYear().toString());
    
                //单笔提现。20210730改为提现
                RcLimit singleOutLimit = createRcLimt(DefineCode.SINGLT_OUT,rcArchive,amountLimit.getOutAmountSingle().toString(),"Long",user);
                rcLimitService.save(singleOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_OUT, String.valueOf(map.get(DefineCode.SINGLT_OUT.defineCode)), amountLimit.getOutAmountSingle().toString());
    
                //单日提现。 20210730改为提现
                RcLimit dayOutLimit = createRcLimt(DefineCode.DAY_OUT,rcArchive,amountLimit.getOutAmountDay().toString(),"Long",user);
                rcLimitService.save(dayOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_OUT, String.valueOf(map.get(DefineCode.DAY_OUT.defineCode)), amountLimit.getOutAmountDay().toString());
    
                //单月提现。 20210730改为提现
                RcLimit monthOutLimit = createRcLimt(DefineCode.MONTH_OUT,rcArchive,amountLimit.getOutAmountMonth().toString(),"Long",user);
                rcLimitService.save(monthOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_OUT, String.valueOf(map.get(DefineCode.MONTH_OUT.defineCode)), amountLimit.getOutAmountMonth().toString());
    
                //单年提现。 20210730改为提现
                RcLimit yearOutLimit = createRcLimt(DefineCode.YEAR_OUT,rcArchive,amountLimit.getOutAmountYear().toString(),"Long",user);
                rcLimitService.save(yearOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_OUT, String.valueOf(map.get(DefineCode.YEAR_OUT.defineCode)), amountLimit.getOutAmountYear().toString());
    
                // 新增单笔代付 20210730
                RcLimit singleWithdrawLimit = createRcLimt(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountSingle().toString(), "Long", user);
                rcLimitService.save(singleWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, String.valueOf(map.get(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineCode)), amountLimit.getWithdrawAmountSingle().toString());
    
                // 新增单日代付 20210730
                RcLimit dayWithdrawLimit = createRcLimt(DefineCode.DAY_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountDay().toString(), "Long", user);
                rcLimitService.save(dayWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_WITHDRAW_AMOUNT, String.valueOf(map.get(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineCode)), amountLimit.getWithdrawAmountDay().toString());
                
                // 新增单月代付 20210730
                RcLimit monthWithdrawLimit = createRcLimt(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountMonth().toString(), "Long", user);
                rcLimitService.save(monthWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, String.valueOf(map.get(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineCode)), amountLimit.getWithdrawAmountMonth().toString());
                 
                // 新增单年代付 20210730
                RcLimit yearWithdrawLimit = createRcLimt(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountYear().toString(), "Long", user);
                rcLimitService.save(yearWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, String.valueOf(map.get(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineCode)), amountLimit.getWithdrawAmountYear().toString());
                
                //垫资代付单日限额
                RcLimit withdrawAmountDayLimit = createRcLimt(DefineCode.DAY_WITHDRAW,rcArchive,withdrawAmount.getWithdrawAmountDay().toString(),"Long",user);
                rcLimitService.save(withdrawAmountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_WITHDRAW, String.valueOf(map.get(DefineCode.DAY_WITHDRAW.defineCode)), withdrawAmount.getWithdrawAmountDay().toString());
    
                // 同卡代付单日限额
                RcLimit cardOutAmountDayLimit = createRcLimt(DefineCode.CARD_DAY_OUT_AMOUNT,rcArchive,cardAmount.getCardOutAmountDay().toString(),"Long",user);
                rcLimitService.save(cardOutAmountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_AMOUNT, String.valueOf(map.get(DefineCode.CARD_DAY_OUT_AMOUNT.defineCode)), cardAmount.getCardOutAmountDay().toString());
    
                // 同卡代付单日限笔数
                RcLimit cardOutCountDayLimit = createRcLimt(DefineCode.CARD_DAY_OUT_COUNT,rcArchive,cardAmount.getCardOutCountDay().toString(),"Long",user);
                rcLimitService.save(cardOutCountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_COUNT, String.valueOf(map.get(DefineCode.CARD_DAY_OUT_COUNT.defineCode)), cardAmount.getCardOutCountDay().toString());
    
                // 同卡代付单月限额
                RcLimit cardOutAmountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_OUT_AMOUNT,rcArchive,cardAmount.getCardOutAmountMonth().toString(),"Long",user);
                rcLimitService.save(cardOutAmountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_AMOUNT, String.valueOf(map.get(DefineCode.CARD_MONTH_OUT_AMOUNT.defineCode)), cardAmount.getCardOutAmountMonth().toString());
    
                // 同卡代付单月限笔数
                RcLimit cardOutCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_OUT_COUNT,rcArchive,cardAmount.getCardOutCountMonth().toString(),"Long",user);
                rcLimitService.save(cardOutCountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_COUNT, String.valueOf(map.get(DefineCode.CARD_MONTH_OUT_COUNT.defineCode)), cardAmount.getCardOutCountMonth().toString());
    
                // 同卡入金单日限额
                RcLimit cardInAmountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_AMOUNT,rcArchive,cardAmount.getCardInAmountDay().toString(),"Long",user);
                rcLimitService.save(cardInAmountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_AMOUNT, String.valueOf(map.get(DefineCode.CARD_DAY_IN_AMOUNT.defineCode)), cardAmount.getCardInAmountDay().toString());
    
                // 同卡入金单日限笔数
                RcLimit cardInCountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_COUNT,rcArchive,cardAmount.getCardInCountDay().toString(),"Long",user);
                rcLimitService.save(cardInCountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_COUNT, String.valueOf(map.get(DefineCode.CARD_DAY_IN_COUNT.defineCode)), cardAmount.getCardInCountDay().toString());
    
                // 同卡入金单月限额
                RcLimit cardInAmountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_AMOUNT,rcArchive,cardAmount.getCardInAmountMonth().toString(),"Long",user);
                rcLimitService.save(cardInAmountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_AMOUNT, String.valueOf(map.get(DefineCode.CARD_MONTH_IN_AMOUNT.defineCode)), cardAmount.getCardInAmountMonth().toString());
    
                // 同卡入金单月限笔数
                RcLimit cardInCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_COUNT,rcArchive,cardAmount.getCardInCountMonth().toString(),"Long",user);
                rcLimitService.save(cardInCountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_COUNT, String.valueOf(map.get(DefineCode.CARD_MONTH_IN_COUNT.defineCode)), cardAmount.getCardInCountMonth().toString());

                // 同卡入金单日限总笔数（失败+成功）  20210730
                RcLimit cardInTotalCountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_TOTAL_COUNT, rcArchive, cardAmount.getCardInTotalCountDay().toString(),"Long",user);
                rcLimitService.save(cardInTotalCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_TOTAL_COUNT, String.valueOf(map.get(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineCode)), cardAmount.getCardInTotalCountDay().toString());
                
                // 同卡入金单月限总笔数（失败+成功）  20210730
                RcLimit cardInTotalCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_TOTAL_COUNT,rcArchive,cardAmount.getCardInTotalCountMonth().toString(),"Long",user);
                rcLimitService.save(cardInTotalCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_TOTAL_COUNT, String.valueOf(map.get(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineCode)), cardAmount.getCardInTotalCountMonth().toString());

                // 20240116
                // 信用卡入金单笔最高限额
                RcLimit creditCardInAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getCreditCardInAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_AMOUNT, String.valueOf(map.get(DefineCode.CREDIT_CARD_IN_AMOUNT.defineCode)), cardAmount.getCreditCardInAmount().toString());
                // 信用卡入金单日最高限额
                RcLimit creditCardInDayAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getCreditCardInDayAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_DAY_AMOUNT, String.valueOf(map.get(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineCode)), cardAmount.getCreditCardInDayAmount().toString());
                // 信用卡入金单日最高限额
                RcLimit creditCardInMonAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getCreditCardInMonAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_MON_AMOUNT, String.valueOf(map.get(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineCode)), cardAmount.getCreditCardInMonAmount().toString());
                // 储蓄卡入金单笔最高限额
                RcLimit debitCardInAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getDebitCardInAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_AMOUNT, String.valueOf(map.get(DefineCode.DEBIT_CARD_IN_AMOUNT.defineCode)), cardAmount.getDebitCardInAmount().toString());
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInDayAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getDebitCardInDayAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_DAY_AMOUNT, String.valueOf(map.get(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineCode)), cardAmount.getDebitCardInDayAmount().toString());
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInMonAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getDebitCardInMonAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_MON_AMOUNT, String.valueOf(map.get(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineCode)), cardAmount.getDebitCardInMonAmount().toString());

                // 机构
                if (instAmountLimit.getCardDayInAmount() != null) {
                    RcLimit instCardDayInAmount = createRcLimt(DefineCode.INST_CARD_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getCardDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, String.valueOf(map.get(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode)), instAmountLimit.getCardDayInAmount().toString());
                }
                if (instAmountLimit.getUserDayInAmount() != null) {
                    RcLimit instUserDayInAmount = createRcLimt(DefineCode.INST_USER_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getUserDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, String.valueOf(map.get(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode)), instAmountLimit.getUserDayInAmount().toString());
                }
                if (instAmountLimit.getCardTotalInAmount() != null) {
                    RcLimit instCardTotalInAmount = createRcLimt(DefineCode.INST_CARD_TOTAL_IN_AMOUNT,rcArchive,instAmountLimit.getCardTotalInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardTotalInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_TOTAL_IN_AMOUNT, String.valueOf(map.get(DefineCode.INST_CARD_TOTAL_IN_AMOUNT.defineCode)), instAmountLimit.getCardTotalInAmount().toString());
                }
                if (instAmountLimit.getUserTotalInAmount() != null) {
                    RcLimit instUserTotalInAmount = createRcLimt(DefineCode.INST_USER_TOTAL_IN_AMOUNT,rcArchive,instAmountLimit.getUserTotalInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserTotalInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_TOTAL_IN_AMOUNT, String.valueOf(map.get(DefineCode.INST_USER_TOTAL_IN_AMOUNT.defineCode)), instAmountLimit.getUserTotalInAmount().toString());
                }

                // 用户入金单日限额
                RcLimit userInAmountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_AMOUNT,rcArchive,userAmount.getUserInAmountDay().toString(),"Long",user);
                rcLimitService.save(userInAmountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_AMOUNT, String.valueOf(map.get(DefineCode.USER_DAY_IN_AMOUNT.defineCode)), userAmount.getUserInAmountDay().toString());
    
                // 用户入金单日限笔数
                RcLimit userInCountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_COUNT,rcArchive,userAmount.getUserInCountDay().toString(),"Long",user);
                rcLimitService.save(userInCountDayLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_COUNT, String.valueOf(map.get(DefineCode.USER_DAY_IN_COUNT.defineCode)), userAmount.getUserInCountDay().toString());
    
                // 用户入金单月限额
                RcLimit userInAmountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_AMOUNT,rcArchive,userAmount.getUserInAmountMonth().toString(),"Long",user);
                rcLimitService.save(userInAmountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_AMOUNT, String.valueOf(map.get(DefineCode.USER_MONTH_IN_AMOUNT.defineCode)), userAmount.getUserInAmountMonth().toString());
    
                // 用户入金单月限笔数
                RcLimit userInCountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_COUNT,rcArchive,userAmount.getUserInCountMonth().toString(),"Long",user);
                rcLimitService.save(userInCountMonthLimit);
                // 插入操作日志
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_COUNT, String.valueOf(map.get(DefineCode.USER_MONTH_IN_COUNT.defineCode)), userAmount.getUserInCountMonth().toString());

                // 用户入金单日限总笔数（成功+失败）  20210730
                RcLimit userInTotalCountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_TOTAL_COUNT,rcArchive,userAmount.getUserInTotalCountDay().toString(),"Long",user);
                rcLimitService.save(userInTotalCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_TOTAL_COUNT, String.valueOf(map.get(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineCode)), userAmount.getUserInTotalCountDay().toString());
                
                // 用户入金单月限总笔数（成功+失败）  20210730
                RcLimit userInTotalCountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_TOTAL_COUNT,rcArchive,userAmount.getUserInTotalCountMonth().toString(),"Long",user);
                rcLimitService.save(userInTotalCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_TOTAL_COUNT, String.valueOf(map.get(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineCode)), userAmount.getUserInTotalCountMonth().toString());

                // 商户维度日转账次数限额 2020.6.22
                RcLimit transferCountDayLimit = createRcLimt(DefineCode.WITHDRAW_DAY_MAX_COUNT, rcArchive, merchantAmountLimit.getTransCountLimitDay().toString(), "Long", user);
                rcLimitService.save(transferCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, String.valueOf(map.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode)), merchantAmountLimit.getTransCountLimitDay().toString());
                
                // 商户日提现次数限额 2022.1.13
                RcLimit datMaxOuntCountLimit = createRcLimt(DefineCode.DAY_MAX_OUT_COUNT, rcArchive, merchantAmountLimit.getDayMaxOutCount().toString(), "Long", user);
                rcLimitService.save(datMaxOuntCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, String.valueOf(map.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode)), merchantAmountLimit.getDayMaxOutCount().toString());

                savePlatAmountLimit(rcArchive, amountLimitObject, user);
            } else if(RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)
                   || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)){
               
               // 获取默认值，记录日志旧值使用
               Map<String,Long> certMap = rcLimitService.queryCertAmountLimit(rcArchive.getArchiveCode(), RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcArchive.getRcLevel());

                // 证件维度日转账次数限额
                RcLimit transferCountDayLimit = createRcLimt(DefineCode.WITHDRAW_DAY_MAX_COUNT, rcArchive, certificateAmountLimit.getTransCountLimitDay().toString(), "Long", user);
                rcLimitService.save(transferCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, String.valueOf(certMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode)), certificateAmountLimit.getTransCountLimitDay().toString());
                
                // 证件维度日提现次数限额 20220113
                RcLimit DayMaxOutLimit = createRcLimt(DefineCode.DAY_MAX_OUT_COUNT, rcArchive, certificateAmountLimit.getDayMaxOutCount().toString(), "Long", user);
                rcLimitService.save(DayMaxOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, String.valueOf(certMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode)), certificateAmountLimit.getDayMaxOutCount().toString());
                
                // 日交易累计限额  20210810
                RcLimit dayMaxInLimit = createRcLimt(DefineCode.CERT_DAY_MAX_IN_AMOUNT, rcArchive, certificateAmountLimit.getDayMaxInAmount().toString(), "Long", user);
                rcLimitService.save(dayMaxInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_DAY_MAX_IN_AMOUNT, String.valueOf(certMap.get(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineCode)), certificateAmountLimit.getDayMaxInAmount().toString());
                
                // 月交易累计限额  20210810
                RcLimit monthMaxInLimit = createRcLimt(DefineCode.CERT_MONTH_MAX_IN_AMOUNT, rcArchive, certificateAmountLimit.getMonthMaxInAmount().toString(), "Long", user);
                rcLimitService.save(monthMaxInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_MONTH_MAX_IN_AMOUNT, String.valueOf(certMap.get(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineCode)), certificateAmountLimit.getMonthMaxInAmount().toString());
            
                // 单日信用卡交易累计限额 20210810
                RcLimit dayMaxCreditLimit = createRcLimt(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT, rcArchive, certificateAmountLimit.getDayMaxCreditAmount().toString(), "Long", user);
                rcLimitService.save(dayMaxCreditLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT, String.valueOf(certMap.get(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineCode)), certificateAmountLimit.getDayMaxCreditAmount().toString());
            
                // 月交易累计限额  20210810
                RcLimit monthMaxCreditLimit = createRcLimt(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT, rcArchive, certificateAmountLimit.getMonthMaxCreditAmount().toString(), "Long", user);
                rcLimitService.save(monthMaxCreditLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT, String.valueOf(certMap.get(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineCode)), certificateAmountLimit.getMonthMaxCreditAmount().toString());

                if (instAmountLimit.getCardDayInAmount() != null) {
                    RcLimit instCardDayInAmount = createRcLimt(DefineCode.INST_CARD_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getCardDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, String.valueOf(certMap.get(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode)), instAmountLimit.getCardDayInAmount().toString());
                }
                if (instAmountLimit.getUserDayInAmount() != null) {
                    RcLimit instUserDayInAmount = createRcLimt(DefineCode.INST_USER_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getUserDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, String.valueOf(certMap.get(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode)), instAmountLimit.getUserDayInAmount().toString());
                }
           } else if(RcConstants.BusinessTagerType.TERM.code.equals(targetType)){
               
               TermAmountLimit termAmountLimit = amountLimitObject.getTermAmountLimit();
               // 获取终端维度默认值，记录日志旧值使用
               Map<String,Long> termMap = rcLimitService.queryAmountLimit(rcArchive.getArchiveCode(), RcConstants.BusinessTagerType.TERM.code, rcArchive.getRcLevel());

               //单笔入金
               RcLimit singleInLimit = createRcLimt(DefineCode.SINGLT_IN, rcArchive, termAmountLimit.getAmountSingle().toString(),"Long", user);
               rcLimitService.save(singleInLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, String.valueOf(termMap.get(DefineCode.SINGLT_IN.defineCode)), termAmountLimit.getAmountSingle().toString());
   
               //单日入金
               RcLimit dayInLimit = createRcLimt(DefineCode.DAY_IN, rcArchive, termAmountLimit.getAmountDay().toString(),"Long", user);
               rcLimitService.save(dayInLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, String.valueOf(termMap.get(DefineCode.DAY_IN.defineCode)), termAmountLimit.getAmountDay().toString());
   
               //单月入金
               RcLimit monthInLimit = createRcLimt(DefineCode.MONTH_IN,rcArchive, termAmountLimit.getAmountMonth().toString(), "Long", user);
               rcLimitService.save(monthInLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, String.valueOf(termMap.get(DefineCode.MONTH_IN.defineCode)), termAmountLimit.getAmountMonth().toString());
   
               //单年入金
               RcLimit yearInLimit = createRcLimt(DefineCode.YEAR_IN,rcArchive, termAmountLimit.getAmountYear().toString(),"Long",user);
               rcLimitService.save(yearInLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, String.valueOf(termMap.get(DefineCode.YEAR_IN.defineCode)), termAmountLimit.getAmountYear().toString());
   
               //单日笔数
               RcLimit dayCountLimit = createRcLimt(DefineCode.DAY_IN_COUNT, rcArchive, termAmountLimit.getCountDay().toString(),"Long", user);
               rcLimitService.save(dayCountLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN_COUNT, String.valueOf(termMap.get(DefineCode.DAY_IN_COUNT.defineCode)), termAmountLimit.getAmountDay().toString());
   
               //单月笔数
               RcLimit monthCountLimit = createRcLimt(DefineCode.MONTH_IN_COUNT,rcArchive, termAmountLimit.getCountMonth().toString(), "Long", user);
               rcLimitService.save(monthCountLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN_COUNT, String.valueOf(termMap.get(DefineCode.MONTH_IN_COUNT.defineCode)), termAmountLimit.getAmountMonth().toString());
   
               //单年笔数
               RcLimit yearCountLimit = createRcLimt(DefineCode.YEAR_IN_COUNT,rcArchive, termAmountLimit.getCountYear().toString(),"Long",user);
               rcLimitService.save(yearCountLimit);
               insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN_COUNT, String.valueOf(termMap.get(DefineCode.YEAR_IN_COUNT.defineCode)), termAmountLimit.getAmountYear().toString());
           
           }  else if(RcConstants.BusinessTagerType.PERSON.code.equals(targetType)){
               // 个人维度  ********
                PersonAmountLimit personAmountLimit = amountLimitObject.getPersonAmountLimit();
                Map<String,Long> personMap = rcLimitService.queryAmountLimit(rcArchive.getArchiveCode(), RcConstants.BusinessTagerType.PERSON.code, rcArchive.getRcLevel());
                // 同名卡转账
                RcLimit accountNameLimit = createRcLimt(DefineCode.ACCOUNT_NAME_LIMIT, rcArchive, personAmountLimit.getAccountNameLimit().toString(), "Long", user);
                rcLimitService.save(accountNameLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.ACCOUNT_NAME_LIMIT, String.valueOf(personMap.get(DefineCode.ACCOUNT_NAME_LIMIT.defineCode)), personAmountLimit.getAccountNameLimit().toString());
                // 向单位支付账户转账
                RcLimit personTransCompany = createRcLimt(DefineCode.PERSON_TRANS_COMPANY, rcArchive, personAmountLimit.getPersonTransCompany().toString(), "Long", user);
                rcLimitService.save(personTransCompany);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_COMPANY, String.valueOf(personMap.get(DefineCode.PERSON_TRANS_COMPANY.defineCode)), personAmountLimit.getPersonTransCompany().toString());
                // 向个人支付账户转账
                RcLimit personTransPerson = createRcLimt(DefineCode.PERSON_TRANS_PERSON, rcArchive, personAmountLimit.getPersonTransPerson().toString(), "Long", user);
                rcLimitService.save(personTransPerson);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_PERSON, String.valueOf(personMap.get(DefineCode.PERSON_TRANS_PERSON.defineCode)), personAmountLimit.getPersonTransPerson().toString());
    
                // 终身出金(I类)
                if (personAmountLimit.getTotalMaxOutAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.TOTAL_MAX_OUT_AMOUNT, rcArchive, personAmountLimit.getTotalMaxOutAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.TOTAL_MAX_OUT_AMOUNT, String.valueOf(personMap.get(DefineCode.TOTAL_MAX_OUT_AMOUNT.defineCode)), personAmountLimit.getTotalMaxOutAmount().toString());
                }
                
                // 个人单日出金（II类、III类）
                if (personAmountLimit.getUserDayMaxOutAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.USER_DAY_MAX_OUT_AMOUNT, rcArchive, personAmountLimit.getUserDayMaxOutAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_MAX_OUT_AMOUNT, String.valueOf(personMap.get(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineCode)), personAmountLimit.getUserDayMaxOutAmount().toString());
                }
                
                // 个人单年出金（II类、III类）
                if (personAmountLimit.getUserYearMaxOutAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.USER_YEAR_MAX_OUT_AMOUNT, rcArchive, personAmountLimit.getUserYearMaxOutAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_YEAR_MAX_OUT_AMOUNT, String.valueOf(personMap.get(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineCode)), personAmountLimit.getUserYearMaxOutAmount().toString());
                }

                // 个人余额转账单笔最高限额（II类、III类）
                if (personAmountLimit.getBalanceTransAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_TRANS_AMOUNT, rcArchive, personAmountLimit.getBalanceTransAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_TRANS_AMOUNT.defineCode)), personAmountLimit.getBalanceTransAmount().toString());
                }
                // 余额转账单日累计限额（II类、III类）
                if (personAmountLimit.getBalanceTransDayAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_TRANS_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceTransDayAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_DAY_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_TRANS_DAY_AMOUNT.defineCode)), personAmountLimit.getBalanceTransDayAmount().toString());
                }
                // 单笔提现最高限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_WITHDRAW_AMOUNT, rcArchive, personAmountLimit.getBalanceWithdrawAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_WITHDRAW_AMOUNT.defineCode)), personAmountLimit.getBalanceWithdrawAmount().toString());
                }
                // 单日提现累计限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawDayAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceWithdrawDayAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT.defineCode)), personAmountLimit.getBalanceWithdrawDayAmount().toString());
                }
                // 单笔余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_CONSUME_AMOUNT, rcArchive, personAmountLimit.getBalanceConsumeAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_CONSUME_AMOUNT.defineCode)), personAmountLimit.getBalanceConsumeAmount().toString());
                }
                // 单日余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeDayAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_CONSUME_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceConsumeDayAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_DAY_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_CONSUME_DAY_AMOUNT.defineCode)), personAmountLimit.getBalanceConsumeDayAmount().toString());
                }
                // 单笔入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_IN_AMOUNT, rcArchive, personAmountLimit.getBalanceInAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_IN_AMOUNT.defineCode)), personAmountLimit.getBalanceInAmount().toString());
                }
                // 单日入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInDayAmount() != null) {
                    RcLimit limit = createRcLimt(DefineCode.BALANCE_IN_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceInDayAmount().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_DAY_AMOUNT, String.valueOf(personMap.get(DefineCode.BALANCE_IN_DAY_AMOUNT.defineCode)), personAmountLimit.getBalanceInDayAmount().toString());
                }
             } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(targetType)) {
                //单笔入金
                RcLimit singleInLimit = createRcLimt(DefineCode.SINGLT_IN,rcArchive,amountLimit.getInAmountSingle().toString(),"Long",user);
                rcLimitService.save(singleInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, null, amountLimit.getInAmountSingle().toString());

                //单日入金
                RcLimit dayInLimit = createRcLimt(DefineCode.DAY_IN,rcArchive,amountLimit.getInAmountDay().toString(),"Long",user);
                rcLimitService.save(dayInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, null, amountLimit.getInAmountDay().toString());

                //单月入金
                RcLimit monthInLimit = createRcLimt(DefineCode.MONTH_IN,rcArchive,amountLimit.getInAmountMonth().toString(),"Long",user);
                rcLimitService.save(monthInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, null, amountLimit.getInAmountMonth().toString());

                // 20240116
                // 信用卡入金单笔最高限额
                RcLimit creditCardInAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getCreditCardInAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_AMOUNT, null, cardAmount.getCreditCardInAmount().toString());
                // 信用卡入金单日最高限额
                RcLimit creditCardInDayAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getCreditCardInDayAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_DAY_AMOUNT, null, cardAmount.getCreditCardInDayAmount().toString());
                // 信用卡入金单日最高限额
                RcLimit creditCardInMonAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getCreditCardInMonAmount().toString(),"Long",user);
                rcLimitService.save(creditCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_MON_AMOUNT, null, cardAmount.getCreditCardInMonAmount().toString());
                // 储蓄卡入金单笔最高限额
                RcLimit debitCardInAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getDebitCardInAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_AMOUNT, null, cardAmount.getDebitCardInAmount().toString());
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInDayAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getDebitCardInDayAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_DAY_AMOUNT, null, cardAmount.getDebitCardInDayAmount().toString());
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInMonAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getDebitCardInMonAmount().toString(),"Long",user);
                rcLimitService.save(debitCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_MON_AMOUNT, null, cardAmount.getDebitCardInMonAmount().toString());

                // 小微/个体工商户无以下限额
                if (Constants.CustomerType.BUSINESSMAN.code != rcArchive.getType() && Constants.CustomerType.MICRO.code != rcArchive.getType()) {
                    //单年入金
                    RcLimit yearInLimit = createRcLimt(DefineCode.YEAR_IN,rcArchive,amountLimit.getInAmountYear().toString(),"Long",user);
                    rcLimitService.save(yearInLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, null, amountLimit.getInAmountYear().toString());

                    //单笔提现。20210730改为提现
                    RcLimit singleOutLimit = createRcLimt(DefineCode.SINGLT_OUT,rcArchive,amountLimit.getOutAmountSingle().toString(),"Long",user);
                    rcLimitService.save(singleOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_OUT, null, amountLimit.getOutAmountSingle().toString());

                    //单日提现。 20210730改为提现
                    RcLimit dayOutLimit = createRcLimt(DefineCode.DAY_OUT,rcArchive,amountLimit.getOutAmountDay().toString(),"Long",user);
                    rcLimitService.save(dayOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_OUT, null, amountLimit.getOutAmountDay().toString());

                    //单月提现。 20210730改为提现
                    RcLimit monthOutLimit = createRcLimt(DefineCode.MONTH_OUT,rcArchive,amountLimit.getOutAmountMonth().toString(),"Long",user);
                    rcLimitService.save(monthOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_OUT, null, amountLimit.getOutAmountMonth().toString());

                    //单年提现。 20210730改为提现
                    RcLimit yearOutLimit = createRcLimt(DefineCode.YEAR_OUT,rcArchive,amountLimit.getOutAmountYear().toString(),"Long",user);
                    rcLimitService.save(yearOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_OUT, null, amountLimit.getOutAmountYear().toString());

                    // 新增单笔代付 20210730
                    RcLimit singleWithdrawLimit = createRcLimt(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountSingle().toString(), "Long", user);
                    rcLimitService.save(singleWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, null, amountLimit.getWithdrawAmountSingle().toString());

                    // 新增单日代付 20210730
                    RcLimit dayWithdrawLimit = createRcLimt(DefineCode.DAY_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountDay().toString(), "Long", user);
                    rcLimitService.save(dayWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_WITHDRAW_AMOUNT, null, amountLimit.getWithdrawAmountDay().toString());

                    // 新增单月代付 20210730
                    RcLimit monthWithdrawLimit = createRcLimt(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountMonth().toString(), "Long", user);
                    rcLimitService.save(monthWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, null, amountLimit.getWithdrawAmountMonth().toString());

                    // 新增单年代付 20210730
                    RcLimit yearWithdrawLimit = createRcLimt(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, rcArchive, amountLimit.getWithdrawAmountYear().toString(), "Long", user);
                    rcLimitService.save(yearWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, null, amountLimit.getWithdrawAmountYear().toString());

                    //垫资代付单日限额
                    RcLimit withdrawAmountDayLimit = createRcLimt(DefineCode.DAY_WITHDRAW,rcArchive,withdrawAmount.getWithdrawAmountDay().toString(),"Long",user);
                    rcLimitService.save(withdrawAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_WITHDRAW, null, withdrawAmount.getWithdrawAmountDay().toString());

                    // 同卡代付单日限额
                    RcLimit cardOutAmountDayLimit = createRcLimt(DefineCode.CARD_DAY_OUT_AMOUNT,rcArchive,cardAmount.getCardOutAmountDay().toString(),"Long",user);
                    rcLimitService.save(cardOutAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_AMOUNT, null, cardAmount.getCardOutAmountDay().toString());

                    // 同卡代付单日限笔数
                    RcLimit cardOutCountDayLimit = createRcLimt(DefineCode.CARD_DAY_OUT_COUNT,rcArchive,cardAmount.getCardOutCountDay().toString(),"Long",user);
                    rcLimitService.save(cardOutCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_COUNT, null, cardAmount.getCardOutCountDay().toString());

                    // 同卡代付单月限额
                    RcLimit cardOutAmountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_OUT_AMOUNT,rcArchive,cardAmount.getCardOutAmountMonth().toString(),"Long",user);
                    rcLimitService.save(cardOutAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_AMOUNT, null, cardAmount.getCardOutAmountMonth().toString());

                    // 同卡代付单月限笔数
                    RcLimit cardOutCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_OUT_COUNT,rcArchive,cardAmount.getCardOutCountMonth().toString(),"Long",user);
                    rcLimitService.save(cardOutCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_COUNT, null, cardAmount.getCardOutCountMonth().toString());

                    // 同卡入金单日限额
                    RcLimit cardInAmountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_AMOUNT,rcArchive,cardAmount.getCardInAmountDay().toString(),"Long",user);
                    rcLimitService.save(cardInAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_AMOUNT, null, cardAmount.getCardInAmountDay().toString());

                    // 同卡入金单日限笔数
                    RcLimit cardInCountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_COUNT,rcArchive,cardAmount.getCardInCountDay().toString(),"Long",user);
                    rcLimitService.save(cardInCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_COUNT, null, cardAmount.getCardInCountDay().toString());

                    // 同卡入金单月限额
                    RcLimit cardInAmountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_AMOUNT,rcArchive,cardAmount.getCardInAmountMonth().toString(),"Long",user);
                    rcLimitService.save(cardInAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_AMOUNT, null, cardAmount.getCardInAmountMonth().toString());

                    // 同卡入金单月限笔数
                    RcLimit cardInCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_COUNT,rcArchive,cardAmount.getCardInCountMonth().toString(),"Long",user);
                    rcLimitService.save(cardInCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_COUNT, null, cardAmount.getCardInCountMonth().toString());

                    // 同卡入金单日限总笔数（失败+成功）  20210730
                    RcLimit cardInTotalCountDayLimit = createRcLimt(DefineCode.CARD_DAY_IN_TOTAL_COUNT, rcArchive, cardAmount.getCardInTotalCountDay().toString(),"Long",user);
                    rcLimitService.save(cardInTotalCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_TOTAL_COUNT, null, cardAmount.getCardInTotalCountDay().toString());

                    // 同卡入金单月限总笔数（失败+成功）  20210730
                    RcLimit cardInTotalCountMonthLimit = createRcLimt(DefineCode.CARD_MONTH_IN_TOTAL_COUNT,rcArchive,cardAmount.getCardInTotalCountMonth().toString(),"Long",user);
                    rcLimitService.save(cardInTotalCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_TOTAL_COUNT, null, cardAmount.getCardInTotalCountMonth().toString());

                    // 用户入金单日限额
                    RcLimit userInAmountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_AMOUNT,rcArchive,userAmount.getUserInAmountDay().toString(),"Long",user);
                    rcLimitService.save(userInAmountDayLimit);
                    // 插入操作日志
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_AMOUNT, null, userAmount.getUserInAmountDay().toString());

                    // 用户入金单日限笔数
                    RcLimit userInCountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_COUNT,rcArchive,userAmount.getUserInCountDay().toString(),"Long",user);
                    rcLimitService.save(userInCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_COUNT, null, userAmount.getUserInCountDay().toString());

                    // 用户入金单月限额
                    RcLimit userInAmountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_AMOUNT,rcArchive,userAmount.getUserInAmountMonth().toString(),"Long",user);
                    rcLimitService.save(userInAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_AMOUNT, null, userAmount.getUserInAmountMonth().toString());

                    // 用户入金单月限笔数
                    RcLimit userInCountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_COUNT,rcArchive,userAmount.getUserInCountMonth().toString(),"Long",user);
                    rcLimitService.save(userInCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_COUNT, null, userAmount.getUserInCountMonth().toString());

                    // 用户入金单日限总笔数（成功+失败）  20210730
                    RcLimit userInTotalCountDayLimit = createRcLimt(DefineCode.USER_DAY_IN_TOTAL_COUNT,rcArchive,userAmount.getUserInTotalCountDay().toString(),"Long",user);
                    rcLimitService.save(userInTotalCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_TOTAL_COUNT, null, userAmount.getUserInTotalCountDay().toString());

                    // 用户入金单月限总笔数（成功+失败）  20210730
                    RcLimit userInTotalCountMonthLimit = createRcLimt(DefineCode.USER_MONTH_IN_TOTAL_COUNT,rcArchive,userAmount.getUserInTotalCountMonth().toString(),"Long",user);
                    rcLimitService.save(userInTotalCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_TOTAL_COUNT, null, userAmount.getUserInTotalCountMonth().toString());

                    // 商户维度日转账次数限额 2020.6.22
                    RcLimit transferCountDayLimit = createRcLimt(DefineCode.WITHDRAW_DAY_MAX_COUNT, rcArchive, merchantAmountLimit.getTransCountLimitDay().toString(), "Long", user);
                    rcLimitService.save(transferCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, null, merchantAmountLimit.getTransCountLimitDay().toString());

                    // 商户日提现次数限额 2022.1.13
                    RcLimit datMaxOuntCountLimit = createRcLimt(DefineCode.DAY_MAX_OUT_COUNT, rcArchive, merchantAmountLimit.getDayMaxOutCount().toString(), "Long", user);
                    rcLimitService.save(datMaxOuntCountLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, null, merchantAmountLimit.getDayMaxOutCount().toString());
                }

            }
            
        } else { //更新
        
            Date modifyDay = new Date();
            String origValue;

            logService.printLog("update打印rcarchive:" + rcArchive);
            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType) || RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
                if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
                    //商户门户单日出金限额
                    RcLimit daliyOutLimit = rcLimitService.queryLimit(DefineCode.DALIY_OUT_LIMIT.defineId, targetType,rcArchive.getArchiveCode());
                    origValue = daliyOutLimit.getLimitValue();
                    daliyOutLimit.setLimitValue(outAmountLimit.getDaliyOutLimit().toString());
                    rcLimitService.updateLimit(daliyOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DALIY_OUT_LIMIT, origValue, outAmountLimit.getDaliyOutLimit().toString());
                    //商户门户单笔出金限额
                    RcLimit singleOutLimit = rcLimitService.queryLimit(DefineCode.SINGLE_OUT_LIMIT.defineId, targetType, rcArchive.getArchiveCode());
                    origValue = singleOutLimit.getLimitValue();
                    singleOutLimit.setLimitValue(outAmountLimit.getSingleOutLimit().toString());
                    rcLimitService.updateLimit(singleOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_OUT_LIMIT, origValue, outAmountLimit.getSingleOutLimit().toString());
                }

                //单笔入金
                RcLimit singleInLimit = rcLimitService.queryLimit(DefineCode.SINGLT_IN.defineId,targetType, rcArchive.getArchiveCode());
                logService.printLog("update单笔入金：" + singleInLimit);
                origValue = singleInLimit.getLimitValue();
                singleInLimit.setLimitValue(amountLimit.getInAmountSingle().toString());
                rcLimitService.updateLimit(singleInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, origValue, amountLimit.getInAmountSingle().toString());
    
                //单日入金
                RcLimit dayInLimit = rcLimitService.queryLimit(DefineCode.DAY_IN.defineId,targetType,rcArchive.getArchiveCode());
                origValue = dayInLimit.getLimitValue();
                dayInLimit.setLimitValue(amountLimit.getInAmountDay().toString());
                rcLimitService.updateLimit(dayInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, origValue, amountLimit.getInAmountDay().toString());
    
                //单月入金
                RcLimit monthInLimit = rcLimitService.queryLimit(DefineCode.MONTH_IN.defineId,targetType,rcArchive.getArchiveCode());
                origValue = monthInLimit.getLimitValue();
                monthInLimit.setLimitValue(amountLimit.getInAmountMonth().toString());
                rcLimitService.updateLimit(monthInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, origValue, amountLimit.getInAmountMonth().toString());
    
                //单年入金
                RcLimit yearInLimit = rcLimitService.queryLimit(DefineCode.YEAR_IN.defineId,targetType,rcArchive.getArchiveCode());
                origValue = yearInLimit.getLimitValue();
                yearInLimit.setLimitValue(amountLimit.getInAmountYear().toString());
                rcLimitService.updateLimit(yearInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, origValue, amountLimit.getInAmountYear().toString());
    
                //单笔提现，20210730改为提现
                RcLimit singleOutLimit = rcLimitService.queryLimit(DefineCode.SINGLT_OUT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = singleOutLimit.getLimitValue();
                singleOutLimit.setLimitValue(amountLimit.getOutAmountSingle().toString());
                rcLimitService.updateLimit(singleOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_OUT, origValue, amountLimit.getOutAmountSingle().toString());
    
                //单日提现，20210730改为提现
                RcLimit dayOutLimit = rcLimitService.queryLimit(DefineCode.DAY_OUT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = dayOutLimit.getLimitValue();
                dayOutLimit.setLimitValue(amountLimit.getOutAmountDay().toString());
                rcLimitService.updateLimit(dayOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_OUT, origValue, amountLimit.getOutAmountDay().toString());
    
                //单月提现，20210730改为提现
                RcLimit monthOutLimit = rcLimitService.queryLimit(DefineCode.MONTH_OUT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = monthOutLimit.getLimitValue();
                monthOutLimit.setLimitValue(amountLimit.getOutAmountMonth().toString());
                rcLimitService.updateLimit(monthOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_OUT, origValue, amountLimit.getOutAmountMonth().toString());
    
                //单年提现，20210730改为提现
                RcLimit yearOutLimit = rcLimitService.queryLimit(DefineCode.YEAR_OUT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = yearOutLimit.getLimitValue();
                yearOutLimit.setLimitValue(amountLimit.getOutAmountYear().toString());
                rcLimitService.updateLimit(yearOutLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_OUT, origValue, amountLimit.getOutAmountYear().toString());
    
                // 单笔代付，20210730
                RcLimit singleWithdrawLimit = rcLimitService.queryLimit(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineId,targetType, rcArchive.getArchiveCode());
                origValue = singleWithdrawLimit.getLimitValue();
                singleWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountSingle().toString());
                rcLimitService.updateLimit(singleWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountSingle().toString());
    
                // 单日代付，20210730
                RcLimit dayWithdrawLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineId,targetType, rcArchive.getArchiveCode());
                origValue = dayWithdrawLimit.getLimitValue();
                dayWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountDay().toString());
                rcLimitService.updateLimit(dayWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountDay().toString());
                
                // 单月代付，20210730
                RcLimit monthWithdrawLimit = rcLimitService.queryLimit(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineId, targetType,rcArchive.getArchiveCode());
                origValue = monthWithdrawLimit.getLimitValue();
                monthWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountMonth().toString());
                rcLimitService.updateLimit(monthWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountMonth().toString());
                
                // 单年代付，20210730
                RcLimit yearWithdrawLimit = rcLimitService.queryLimit(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineId, targetType,rcArchive.getArchiveCode());
                origValue = yearWithdrawLimit.getLimitValue();
                yearWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountYear().toString());
                rcLimitService.updateLimit(yearWithdrawLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountYear().toString());
                
                //垫资代付单日限额
                RcLimit withdrawAmountDayLimit = rcLimitService.queryLimit(DefineCode.DAY_WITHDRAW.defineId,targetType,rcArchive.getArchiveCode());
                origValue = withdrawAmountDayLimit.getLimitValue();
                withdrawAmountDayLimit.setLimitValue(withdrawAmount.getWithdrawAmountDay().toString());
                rcLimitService.updateLimit(withdrawAmountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_WITHDRAW, origValue, withdrawAmount.getWithdrawAmountDay().toString());
    
                // 同卡代付单日限额
                RcLimit cardOutAmountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_OUT_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardOutAmountDayLimit.getLimitValue();
                cardOutAmountDayLimit.setLimitValue(cardAmount.getCardOutAmountDay().toString());
                rcLimitService.updateLimit(cardOutAmountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_AMOUNT, origValue, cardAmount.getCardOutAmountDay().toString());
    
                // 同卡代付单日限笔数
                RcLimit cardOutCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_OUT_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardOutCountDayLimit.getLimitValue();
                cardOutCountDayLimit.setLimitValue(cardAmount.getCardOutCountDay().toString());
                rcLimitService.updateLimit(cardOutCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_COUNT, origValue, cardAmount.getCardOutCountDay().toString());
    
                // 同卡代付单月限额
                RcLimit cardOutAmountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_OUT_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardOutAmountMonthLimit.getLimitValue();
                cardOutAmountMonthLimit.setLimitValue(cardAmount.getCardOutAmountMonth().toString());
                rcLimitService.updateLimit(cardOutAmountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_AMOUNT, origValue, cardAmount.getCardOutAmountMonth().toString());
    
                // 同卡代付单月限笔数
                RcLimit cardOutCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_OUT_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardOutCountMonthLimit.getLimitValue();
                cardOutCountMonthLimit.setLimitValue(cardAmount.getCardOutCountMonth().toString());
                rcLimitService.updateLimit(cardOutCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_COUNT, origValue, cardAmount.getCardOutCountMonth().toString());
    
                // 同卡入金单日限额
                RcLimit cardInAmountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInAmountDayLimit.getLimitValue();
                cardInAmountDayLimit.setLimitValue(cardAmount.getCardInAmountDay().toString());
                rcLimitService.updateLimit(cardInAmountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_AMOUNT, origValue, cardAmount.getCardInAmountDay().toString());
    
                // 同卡入金单日限笔数
                RcLimit cardInCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInCountDayLimit.getLimitValue();
                cardInCountDayLimit.setLimitValue(cardAmount.getCardInCountDay().toString());
                rcLimitService.updateLimit(cardInCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_COUNT, origValue, cardAmount.getCardInCountDay().toString());
    
                // 同卡入金单月限额
                RcLimit cardInAmountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInAmountMonthLimit.getLimitValue();
                cardInAmountMonthLimit.setLimitValue(cardAmount.getCardInAmountMonth().toString());
                rcLimitService.updateLimit(cardInAmountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_AMOUNT, origValue, cardAmount.getCardInAmountMonth().toString());
    
                // 同卡入金单月限笔数
                RcLimit cardInCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInCountMonthLimit.getLimitValue();
                cardInCountMonthLimit.setLimitValue(cardAmount.getCardInCountMonth().toString());
                rcLimitService.updateLimit(cardInCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_COUNT, origValue, cardAmount.getCardInCountMonth().toString());
    
                // 同卡入金单日限总笔数（成功+失败）  20210730 
                RcLimit cardInTotalCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInTotalCountDayLimit.getLimitValue();
                cardInTotalCountDayLimit.setLimitValue(cardAmount.getCardInTotalCountDay().toString());
                rcLimitService.updateLimit(cardInTotalCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_TOTAL_COUNT, origValue, cardAmount.getCardInTotalCountDay().toString());
                
                // 同卡入金单月限总笔数（成功+失败）  20210730 
                RcLimit cardInTotalCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = cardInTotalCountMonthLimit.getLimitValue();
                cardInTotalCountMonthLimit.setLimitValue(cardAmount.getCardInTotalCountMonth().toString());
                rcLimitService.updateLimit(cardInTotalCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_TOTAL_COUNT, origValue, cardAmount.getCardInTotalCountMonth().toString());

                // 20240118
                // 信用卡入金单笔最高限额
                RcLimit creditCardInAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (creditCardInAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId,rcArchive);
                    creditCardInAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getCreditCardInAmount().toString(),"Long",user);
                    rcLimitService.save(creditCardInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_AMOUNT, origValue, cardAmount.getCreditCardInAmount().toString());
                } else {
                    origValue = creditCardInAmount.getLimitValue();
                    creditCardInAmount.setLimitValue(cardAmount.getCreditCardInAmount().toString());
                    rcLimitService.updateLimit(creditCardInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_AMOUNT, origValue, cardAmount.getCreditCardInAmount().toString());
                }

                // 信用卡入金单日最高限额
                RcLimit creditCardInDayAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (creditCardInDayAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineId,rcArchive);
                    creditCardInDayAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getCreditCardInDayAmount().toString(),"Long",user);
                    rcLimitService.save(creditCardInDayAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getCreditCardInDayAmount().toString());
                } else {
                    origValue = creditCardInDayAmount.getLimitValue();
                    creditCardInDayAmount.setLimitValue(cardAmount.getCreditCardInDayAmount().toString());
                    rcLimitService.updateLimit(creditCardInDayAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getCreditCardInDayAmount().toString());
                }
                // 信用卡入金单日最高限额
                RcLimit creditCardInMonAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (creditCardInMonAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineId,rcArchive);
                    creditCardInMonAmount = createRcLimt(DefineCode.CREDIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getCreditCardInMonAmount().toString(),"Long",user);
                    rcLimitService.save(creditCardInMonAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getCreditCardInMonAmount().toString());
                } else {
                    origValue = creditCardInMonAmount.getLimitValue();
                    creditCardInMonAmount.setLimitValue(cardAmount.getCreditCardInMonAmount().toString());
                    rcLimitService.updateLimit(creditCardInMonAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getCreditCardInMonAmount().toString());
                }
                // 储蓄卡入金单笔最高限额
                RcLimit debitCardInAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (debitCardInAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.DEBIT_CARD_IN_AMOUNT.defineId,rcArchive);
                    debitCardInAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_AMOUNT,rcArchive,cardAmount.getDebitCardInAmount().toString(),"Long",user);
                    rcLimitService.save(debitCardInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_AMOUNT, origValue, cardAmount.getDebitCardInAmount().toString());
                } else {
                    origValue = debitCardInAmount.getLimitValue();
                    debitCardInAmount.setLimitValue(cardAmount.getDebitCardInAmount().toString());
                    rcLimitService.updateLimit(debitCardInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_AMOUNT, origValue, cardAmount.getDebitCardInAmount().toString());
                }
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInDayAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (debitCardInDayAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineId,rcArchive);
                    debitCardInDayAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT,rcArchive,cardAmount.getDebitCardInDayAmount().toString(),"Long",user);
                    rcLimitService.save(debitCardInDayAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getDebitCardInDayAmount().toString());
                } else {
                    origValue = debitCardInDayAmount.getLimitValue();
                    debitCardInDayAmount.setLimitValue(cardAmount.getDebitCardInDayAmount().toString());
                    rcLimitService.updateLimit(debitCardInDayAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getDebitCardInDayAmount().toString());
                }
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInMonAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (debitCardInMonAmount == null) {
                    origValue = queryMinLimitValue(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineId,rcArchive);
                    debitCardInMonAmount = createRcLimt(DefineCode.DEBIT_CARD_IN_MON_AMOUNT,rcArchive,cardAmount.getDebitCardInMonAmount().toString(),"Long",user);
                    rcLimitService.save(debitCardInMonAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getDebitCardInMonAmount().toString());
                } else {
                    origValue = debitCardInMonAmount.getLimitValue();
                    debitCardInMonAmount.setLimitValue(cardAmount.getDebitCardInMonAmount().toString());
                    rcLimitService.updateLimit(debitCardInMonAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getDebitCardInMonAmount().toString());
                }

                // 机构：无默认值；页面新增修改可为null
                // 同卡入金单日最高限额
                RcLimit instCardDayInAmount = rcLimitService.queryLimit(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instCardDayInAmount == null && instAmountLimit.getCardDayInAmount() != null) {
                    instCardDayInAmount = createRcLimt(DefineCode.INST_CARD_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getCardDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, "", instAmountLimit.getCardDayInAmount().toString());
                }
                if (instCardDayInAmount != null) {
                    if (instAmountLimit.getCardDayInAmount() != null) {
                        origValue = instCardDayInAmount.getLimitValue();
                        instCardDayInAmount.setLimitValue(instAmountLimit.getCardDayInAmount().toString());
                        rcLimitService.updateLimit(instCardDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, origValue, instAmountLimit.getCardDayInAmount().toString());
                    } else {
                        origValue = instCardDayInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instCardDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, origValue, "");
                    }
                }
                // 同人入金单日最高限额
                RcLimit instUserDayInAmount = rcLimitService.queryLimit(DefineCode.INST_USER_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instUserDayInAmount == null && instAmountLimit.getUserDayInAmount() != null) {
                    instUserDayInAmount = createRcLimt(DefineCode.INST_USER_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getUserDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, "", instAmountLimit.getUserDayInAmount().toString());
                }
                if (instUserDayInAmount != null) {
                    if (instAmountLimit.getUserDayInAmount() != null) {
                        origValue = instUserDayInAmount.getLimitValue();
                        instUserDayInAmount.setLimitValue(instAmountLimit.getUserDayInAmount().toString());
                        rcLimitService.updateLimit(instUserDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, origValue, instAmountLimit.getUserDayInAmount().toString());
                    } else {
                        origValue = instUserDayInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instUserDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, origValue, "");
                    }
                }
                // 近180天同卡累计入金最高限额
                RcLimit instCardTotalInAmount = rcLimitService.queryLimit(DefineCode.INST_CARD_TOTAL_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instCardTotalInAmount == null && instAmountLimit.getCardTotalInAmount() != null) {
                    instCardTotalInAmount = createRcLimt(DefineCode.INST_CARD_TOTAL_IN_AMOUNT,rcArchive,instAmountLimit.getCardTotalInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardTotalInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_TOTAL_IN_AMOUNT, "", instAmountLimit.getCardTotalInAmount().toString());
                }
                if (instCardTotalInAmount != null) {
                    if (instAmountLimit.getCardTotalInAmount() != null) {
                        origValue = instCardTotalInAmount.getLimitValue();
                        instCardTotalInAmount.setLimitValue(instAmountLimit.getCardTotalInAmount().toString());
                        rcLimitService.updateLimit(instCardTotalInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_TOTAL_IN_AMOUNT, origValue, instAmountLimit.getCardTotalInAmount().toString());
                    } else {
                        origValue = instCardTotalInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instCardTotalInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_TOTAL_IN_AMOUNT, origValue, "");
                    }
                }
                // 近180天同人累计入金最高限额
                RcLimit instUserTotalInAmount = rcLimitService.queryLimit(DefineCode.INST_USER_TOTAL_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instUserTotalInAmount == null && instAmountLimit.getUserTotalInAmount() != null) {
                    instUserTotalInAmount = createRcLimt(DefineCode.INST_USER_TOTAL_IN_AMOUNT,rcArchive,instAmountLimit.getUserTotalInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserTotalInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_TOTAL_IN_AMOUNT, "", instAmountLimit.getUserTotalInAmount().toString());
                }
                if (instUserTotalInAmount != null) {
                    if (instAmountLimit.getUserTotalInAmount() != null) {
                        origValue = instUserTotalInAmount.getLimitValue();
                        instUserTotalInAmount.setLimitValue(instAmountLimit.getUserTotalInAmount().toString());
                        rcLimitService.updateLimit(instUserTotalInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_TOTAL_IN_AMOUNT, origValue, instAmountLimit.getUserTotalInAmount().toString());
                    } else {
                        origValue = instUserTotalInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instUserTotalInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_TOTAL_IN_AMOUNT, origValue, "");
                    }
                }

                // 用户入金单日限额
                RcLimit userInAmountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInAmountDayLimit.getLimitValue();
                userInAmountDayLimit.setLimitValue(userAmount.getUserInAmountDay().toString());
                rcLimitService.updateLimit(userInAmountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_AMOUNT, origValue, userAmount.getUserInAmountDay().toString());
    
                // 用户入金单日限笔数
                RcLimit userInCountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInCountDayLimit.getLimitValue();
                userInCountDayLimit.setLimitValue(userAmount.getUserInCountDay().toString());
                rcLimitService.updateLimit(userInCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_COUNT, origValue, userAmount.getUserInCountDay().toString());
    
                // 用户入金单月限额
                RcLimit userInAmountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInAmountMonthLimit.getLimitValue();
                userInAmountMonthLimit.setLimitValue(userAmount.getUserInAmountMonth().toString());
                rcLimitService.updateLimit(userInAmountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_AMOUNT, origValue, userAmount.getUserInAmountMonth().toString());
    
                // 用户入金单月限笔数
                RcLimit userInCountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInCountMonthLimit.getLimitValue();
                userInCountMonthLimit.setLimitValue(userAmount.getUserInCountMonth().toString());
                rcLimitService.updateLimit(userInCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_COUNT, origValue, userAmount.getUserInCountMonth().toString());
                
                // 用户入金单日限总笔数（成功+失败）  20210730 
                RcLimit userInTotalCountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInTotalCountDayLimit.getLimitValue();
                userInTotalCountDayLimit.setLimitValue(userAmount.getUserInTotalCountDay().toString());
                rcLimitService.updateLimit(userInTotalCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_TOTAL_COUNT, origValue, userAmount.getUserInTotalCountDay().toString());
                
                // 用户入金单月限总笔数（成功+失败）  20210730 
                RcLimit userInTotalCountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineId,targetType,rcArchive.getArchiveCode());
                origValue = userInTotalCountMonthLimit.getLimitValue();
                userInTotalCountMonthLimit.setLimitValue(userAmount.getUserInTotalCountMonth().toString());
                rcLimitService.updateLimit(userInTotalCountMonthLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_TOTAL_COUNT, origValue, userAmount.getUserInTotalCountMonth().toString());
                
                // 商户维度日转账次数限额 2020.6.22
                RcLimit transferCountDayLimit = rcLimitService.queryLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId,targetType, rcArchive.getArchiveCode());
                origValue = transferCountDayLimit.getLimitValue();
                transferCountDayLimit.setLimitValue(merchantAmountLimit.getTransCountLimitDay().toString());
                rcLimitService.updateLimit(transferCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, origValue, merchantAmountLimit.getTransCountLimitDay().toString());
                
                // 商户日提现次数限额 2022.1.13
                RcLimit dayMaxOutCountLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_OUT_COUNT.defineId,targetType, rcArchive.getArchiveCode());
                origValue = dayMaxOutCountLimit.getLimitValue();
                dayMaxOutCountLimit.setLimitValue(merchantAmountLimit.getDayMaxOutCount().toString());
                rcLimitService.updateLimit(dayMaxOutCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, origValue, merchantAmountLimit.getDayMaxOutCount().toString());

                savePlatAmountLimit(rcArchive, amountLimitObject, user);
                
            } else if(RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType) 
                    || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)){
                
                // 证件维度日转账次数限额 2020.6.22
                RcLimit transferCountDayLimit = rcLimitService.queryLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = transferCountDayLimit.getLimitValue();
                transferCountDayLimit.setLimitValue(certificateAmountLimit.getTransCountLimitDay().toString());
                rcLimitService.updateLimit(transferCountDayLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, origValue, certificateAmountLimit.getTransCountLimitDay().toString());
                
                // 证件日提现次数限额 2022.1.13
                RcLimit dayMaxOutCountLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_OUT_COUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = dayMaxOutCountLimit.getLimitValue();
                dayMaxOutCountLimit.setLimitValue(certificateAmountLimit.getDayMaxOutCount().toString());
                rcLimitService.updateLimit(dayMaxOutCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, origValue, certificateAmountLimit.getDayMaxOutCount().toString());
                
                // 日交易累计限额 20210810
                RcLimit dayMaxInAmountLimit = rcLimitService.queryLimit(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = dayMaxInAmountLimit.getLimitValue();
                dayMaxInAmountLimit.setLimitValue(certificateAmountLimit.getDayMaxInAmount().toString());
                rcLimitService.updateLimit(dayMaxInAmountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_DAY_MAX_IN_AMOUNT, origValue, certificateAmountLimit.getDayMaxInAmount().toString());
             
                // 月交易累计限额 20210810
                RcLimit monthMaxInAmountLimit = rcLimitService.queryLimit(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = monthMaxInAmountLimit.getLimitValue();
                monthMaxInAmountLimit.setLimitValue(certificateAmountLimit.getMonthMaxInAmount().toString());
                rcLimitService.updateLimit(monthMaxInAmountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_MONTH_MAX_IN_AMOUNT, origValue, certificateAmountLimit.getMonthMaxInAmount().toString());
             
                // 单日信用卡交易累计限额 20210810
                RcLimit dayMaxCreditAmountLimit = rcLimitService.queryLimit(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = dayMaxCreditAmountLimit.getLimitValue();
                dayMaxCreditAmountLimit.setLimitValue(certificateAmountLimit.getDayMaxCreditAmount().toString());
                rcLimitService.updateLimit(dayMaxCreditAmountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT, origValue, certificateAmountLimit.getDayMaxCreditAmount().toString());
                
                // 单月信用卡交易累计限额 20210810
                RcLimit monthMaxCreditAmountLimit = rcLimitService.queryLimit(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineId, targetType, rcArchive.getArchiveCode());
                origValue = monthMaxCreditAmountLimit.getLimitValue();
                monthMaxCreditAmountLimit.setLimitValue(certificateAmountLimit.getMonthMaxCreditAmount().toString());
                rcLimitService.updateLimit(monthMaxCreditAmountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT, origValue, certificateAmountLimit.getMonthMaxCreditAmount().toString());

                // 同卡入金单日最高限额
                RcLimit instCardDayInAmount = rcLimitService.queryLimit(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instCardDayInAmount == null && instAmountLimit.getCardDayInAmount() != null) {
                    instCardDayInAmount = createRcLimt(DefineCode.INST_CARD_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getCardDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instCardDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, "", instAmountLimit.getCardDayInAmount().toString());
                }
                if (instCardDayInAmount != null) {
                    if (instAmountLimit.getCardDayInAmount() != null) {
                        origValue = instCardDayInAmount.getLimitValue();
                        instCardDayInAmount.setLimitValue(instAmountLimit.getCardDayInAmount().toString());
                        rcLimitService.updateLimit(instCardDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, origValue, instAmountLimit.getCardDayInAmount().toString());
                    } else {
                        origValue = instCardDayInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instCardDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_CARD_DAY_IN_AMOUNT, origValue, "");
                    }
                }
                // 同人入金单日最高限额
                RcLimit instUserDayInAmount = rcLimitService.queryLimit(DefineCode.INST_USER_DAY_IN_AMOUNT.defineId,targetType,rcArchive.getArchiveCode());
                if (instUserDayInAmount == null && instAmountLimit.getUserDayInAmount() != null) {
                    instUserDayInAmount = createRcLimt(DefineCode.INST_USER_DAY_IN_AMOUNT,rcArchive,instAmountLimit.getUserDayInAmount().toString(),"Long",user);
                    rcLimitService.save(instUserDayInAmount);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, "", instAmountLimit.getUserDayInAmount().toString());
                }
                if (instUserDayInAmount != null) {
                    if (instAmountLimit.getUserDayInAmount() != null) {
                        origValue = instUserDayInAmount.getLimitValue();
                        instUserDayInAmount.setLimitValue(instAmountLimit.getUserDayInAmount().toString());
                        rcLimitService.updateLimit(instUserDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, origValue, instAmountLimit.getUserDayInAmount().toString());
                    } else {
                        origValue = instUserDayInAmount.getLimitValue();
                        rcLimitService.deleteRcLimit(instUserDayInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.INST_USER_DAY_IN_AMOUNT, origValue, "");
                    }
                }
            } else if (RcConstants.BusinessTagerType.TERM.code.equals(targetType)) {
                // 终端维度
                TermAmountLimit termAmountLimit = amountLimitObject.getTermAmountLimit();
                //单笔入金
                RcLimit singleInLimit = rcLimitService.queryLimit(DefineCode.SINGLT_IN.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = singleInLimit.getLimitValue();
                singleInLimit.setLimitValue(termAmountLimit.getAmountSingle().toString());
                rcLimitService.updateLimit(singleInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, origValue, termAmountLimit.getAmountSingle().toString());
    
                //单日入金
                RcLimit dayInLimit = rcLimitService.queryLimit(DefineCode.DAY_IN.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = dayInLimit.getLimitValue();
                dayInLimit.setLimitValue(termAmountLimit.getAmountDay().toString());
                rcLimitService.updateLimit(dayInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, origValue, termAmountLimit.getAmountDay().toString());
    
                //单月入金
                RcLimit monthInLimit = rcLimitService.queryLimit(DefineCode.MONTH_IN.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = monthInLimit.getLimitValue();
                monthInLimit.setLimitValue(termAmountLimit.getAmountMonth().toString());
                rcLimitService.updateLimit(monthInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, origValue, termAmountLimit.getAmountMonth().toString());
    
                //单年入金
                RcLimit yearInLimit = rcLimitService.queryLimit(DefineCode.YEAR_IN.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = yearInLimit.getLimitValue();
                yearInLimit.setLimitValue(termAmountLimit.getAmountYear().toString());
                rcLimitService.updateLimit(yearInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, origValue, termAmountLimit.getAmountYear().toString());
                
                // 单日笔数
                RcLimit dayCountLimit = rcLimitService.queryLimit(DefineCode.DAY_IN_COUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = dayCountLimit.getLimitValue();
                dayCountLimit.setLimitValue(termAmountLimit.getCountDay().toString());
                rcLimitService.updateLimit(dayCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN_COUNT, origValue, termAmountLimit.getCountDay().toString());
    
                //单月笔数
                RcLimit monthCountLimit = rcLimitService.queryLimit(DefineCode.MONTH_IN_COUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = monthCountLimit.getLimitValue();
                monthCountLimit.setLimitValue(termAmountLimit.getCountMonth().toString());
                rcLimitService.updateLimit(monthCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN_COUNT, origValue, termAmountLimit.getCountMonth().toString());
    
                //单年笔数
                RcLimit yearCountLimit = rcLimitService.queryLimit(DefineCode.YEAR_IN_COUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                origValue = yearCountLimit.getLimitValue();
                yearCountLimit.setLimitValue(termAmountLimit.getCountYear().toString());
                rcLimitService.updateLimit(yearCountLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN_COUNT, origValue, termAmountLimit.getCountYear().toString());
                 
             } else if (RcConstants.BusinessTagerType.PERSON.code.equals(targetType)) {
                 // 个人维度  ********
                 PersonAmountLimit personAmountLimit = amountLimitObject.getPersonAmountLimit();
                 //单笔入金
                 RcLimit accountNameLimit = rcLimitService.queryLimit(DefineCode.ACCOUNT_NAME_LIMIT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                 origValue = accountNameLimit.getLimitValue();
                 accountNameLimit.setLimitValue(personAmountLimit.getAccountNameLimit().toString());
                 rcLimitService.updateLimit(accountNameLimit);
                 insertOperateLog(rcArchive, userId, modifyDay, DefineCode.ACCOUNT_NAME_LIMIT, origValue, personAmountLimit.getAccountNameLimit().toString());//
                // 向单位支付账户转账 (新增的限额在更新时可能查不到商户对应的限额，取默认值)
                RcLimit personTransCompany = rcLimitService.queryLimit(DefineCode.PERSON_TRANS_COMPANY.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                if (personTransCompany == null) {
                    personTransCompany = rcLimitService.queryLimit(DefineCode.PERSON_TRANS_COMPANY.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                    RcLimit limit = createRcLimt(DefineCode.PERSON_TRANS_COMPANY, rcArchive, personAmountLimit.getPersonTransCompany().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_COMPANY, personTransCompany.getLimitValue(), personAmountLimit.getPersonTransCompany().toString());
                } else {
                    origValue = personTransCompany.getLimitValue();
                    personTransCompany.setLimitValue(personAmountLimit.getPersonTransCompany().toString());
                    rcLimitService.updateLimit(personTransCompany);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_COMPANY, origValue, personAmountLimit.getPersonTransCompany().toString());
                }
                // 向个人支付账户转账
                RcLimit personTransPerson = rcLimitService.queryLimit(DefineCode.PERSON_TRANS_PERSON.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                if (personTransPerson == null) {
                    personTransPerson = rcLimitService.queryLimit(DefineCode.PERSON_TRANS_PERSON.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                    RcLimit limit = createRcLimt(DefineCode.PERSON_TRANS_PERSON, rcArchive, personAmountLimit.getPersonTransPerson().toString(), "Long", user);
                    rcLimitService.save(limit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_PERSON, personTransPerson.getLimitValue(), personAmountLimit.getPersonTransPerson().toString());
                } else {
                    origValue = personTransPerson.getLimitValue();
                    personTransPerson.setLimitValue(personAmountLimit.getPersonTransPerson().toString());
                    rcLimitService.updateLimit(personTransPerson);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.PERSON_TRANS_PERSON, origValue, personAmountLimit.getPersonTransPerson().toString());
                }

                 // 终身出金(I类)
                 if (personAmountLimit.getTotalMaxOutAmount() != null) {
                     RcLimit limit =  rcLimitService.queryLimit(DefineCode.TOTAL_MAX_OUT_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode()); 
                     origValue = limit.getLimitValue();
                     limit.setLimitValue(personAmountLimit.getTotalMaxOutAmount().toString());
                     rcLimitService.updateLimit(limit);
                     insertOperateLog(rcArchive, userId, modifyDay, DefineCode.TOTAL_MAX_OUT_AMOUNT, origValue, personAmountLimit.getTotalMaxOutAmount().toString());
                 }
                 
                 // 个人单日出金（II类、III类）
                 if (personAmountLimit.getUserDayMaxOutAmount() != null) {
                     RcLimit limit =  rcLimitService.queryLimit(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode()); 
                     origValue = limit.getLimitValue();
                     limit.setLimitValue(personAmountLimit.getUserDayMaxOutAmount().toString());
                     rcLimitService.updateLimit(limit);
                     insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_MAX_OUT_AMOUNT, origValue, personAmountLimit.getUserDayMaxOutAmount().toString());
                 }
                 
                 // 个人单年出金（II类、III类）
                 if (personAmountLimit.getUserYearMaxOutAmount() != null) {
                     RcLimit limit =  rcLimitService.queryLimit(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode()); 
                     origValue = limit.getLimitValue();
                     limit.setLimitValue(personAmountLimit.getUserYearMaxOutAmount().toString());
                     rcLimitService.updateLimit(limit);
                     insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_YEAR_MAX_OUT_AMOUNT, origValue, personAmountLimit.getUserYearMaxOutAmount().toString());
                 }
                // 个人余额转账单笔最高限额（II类、III类）
                if (personAmountLimit.getBalanceTransAmount() != null) {
                    RcLimit balanceTransAmount = rcLimitService.queryLimit(DefineCode.BALANCE_TRANS_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceTransAmount == null) {
                        balanceTransAmount = rcLimitService.queryLimit(DefineCode.BALANCE_TRANS_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_TRANS_AMOUNT, rcArchive, personAmountLimit.getBalanceTransAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_AMOUNT, balanceTransAmount.getLimitValue(), personAmountLimit.getBalanceTransAmount().toString());
                    } else {
                        origValue = balanceTransAmount.getLimitValue();
                        balanceTransAmount.setLimitValue(personAmountLimit.getBalanceTransAmount().toString());
                        rcLimitService.updateLimit(balanceTransAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_AMOUNT, origValue, personAmountLimit.getBalanceTransAmount().toString());
                    }
                }
                // 余额转账单日累计限额（II类、III类）
                if (personAmountLimit.getBalanceTransDayAmount() != null) {
                    RcLimit balanceTransDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_TRANS_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceTransDayAmount == null) {
                        balanceTransDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_TRANS_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_TRANS_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceTransDayAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_DAY_AMOUNT, balanceTransDayAmount.getLimitValue(), personAmountLimit.getBalanceTransDayAmount().toString());
                    } else {
                        origValue = balanceTransDayAmount.getLimitValue();
                        balanceTransDayAmount.setLimitValue(personAmountLimit.getBalanceTransDayAmount().toString());
                        rcLimitService.updateLimit(balanceTransDayAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_TRANS_DAY_AMOUNT, origValue, personAmountLimit.getBalanceTransDayAmount().toString());
                    }
                }
                // 单笔提现最高限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawAmount() != null) {
                    RcLimit balanceWithdrawAmount = rcLimitService.queryLimit(DefineCode.BALANCE_WITHDRAW_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceWithdrawAmount == null) {
                        balanceWithdrawAmount = rcLimitService.queryLimit(DefineCode.BALANCE_WITHDRAW_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_WITHDRAW_AMOUNT, rcArchive, personAmountLimit.getBalanceWithdrawAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_AMOUNT, balanceWithdrawAmount.getLimitValue(), personAmountLimit.getBalanceWithdrawAmount().toString());
                    } else {
                        origValue = balanceWithdrawAmount.getLimitValue();
                        balanceWithdrawAmount.setLimitValue(personAmountLimit.getBalanceWithdrawAmount().toString());
                        rcLimitService.updateLimit(balanceWithdrawAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_AMOUNT, origValue, personAmountLimit.getBalanceWithdrawAmount().toString());
                    }
                }
                // 单日提现累计限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawDayAmount() != null) {
                    RcLimit balanceWithdrawDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceWithdrawDayAmount == null) {
                        balanceWithdrawDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceWithdrawDayAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT, balanceWithdrawDayAmount.getLimitValue(), personAmountLimit.getBalanceWithdrawDayAmount().toString());
                    } else {
                        origValue = balanceWithdrawDayAmount.getLimitValue();
                        balanceWithdrawDayAmount.setLimitValue(personAmountLimit.getBalanceWithdrawDayAmount().toString());
                        rcLimitService.updateLimit(balanceWithdrawDayAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT, origValue, personAmountLimit.getBalanceWithdrawDayAmount().toString());
                    }
                }
                // 单笔余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeAmount() != null) {
                    RcLimit balanceConsumeAmount = rcLimitService.queryLimit(DefineCode.BALANCE_CONSUME_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceConsumeAmount == null) {
                        balanceConsumeAmount = rcLimitService.queryLimit(DefineCode.BALANCE_CONSUME_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_CONSUME_AMOUNT, rcArchive, personAmountLimit.getBalanceConsumeAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_AMOUNT, balanceConsumeAmount.getLimitValue(), personAmountLimit.getBalanceConsumeAmount().toString());
                    } else {
                        origValue = balanceConsumeAmount.getLimitValue();
                        balanceConsumeAmount.setLimitValue(personAmountLimit.getBalanceConsumeAmount().toString());
                        rcLimitService.updateLimit(balanceConsumeAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_AMOUNT, origValue, personAmountLimit.getBalanceConsumeAmount().toString());
                    }
                }
                // 单日余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeDayAmount() != null) {
                    RcLimit balanceConsumeDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_CONSUME_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceConsumeDayAmount == null) {
                        balanceConsumeDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_CONSUME_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_CONSUME_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceConsumeDayAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_DAY_AMOUNT, balanceConsumeDayAmount.getLimitValue(), personAmountLimit.getBalanceConsumeDayAmount().toString());
                    } else {
                        origValue = balanceConsumeDayAmount.getLimitValue();
                        balanceConsumeDayAmount.setLimitValue(personAmountLimit.getBalanceConsumeDayAmount().toString());
                        rcLimitService.updateLimit(balanceConsumeDayAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_CONSUME_DAY_AMOUNT, origValue, personAmountLimit.getBalanceConsumeDayAmount().toString());
                    }
                }
                // 单笔入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInAmount() != null) {
                    RcLimit balanceInAmount = rcLimitService.queryLimit(DefineCode.BALANCE_IN_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceInAmount == null) {
                        balanceInAmount = rcLimitService.queryLimit(DefineCode.BALANCE_IN_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_IN_AMOUNT, rcArchive, personAmountLimit.getBalanceInAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_AMOUNT, balanceInAmount.getLimitValue(), personAmountLimit.getBalanceInAmount().toString());
                    } else {
                        origValue = balanceInAmount.getLimitValue();
                        balanceInAmount.setLimitValue(personAmountLimit.getBalanceInAmount().toString());
                        rcLimitService.updateLimit(balanceInAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_AMOUNT, origValue, personAmountLimit.getBalanceInAmount().toString());
                    }
                }
                // 单日入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInDayAmount() != null) {
                    RcLimit balanceInDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_IN_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
                    if (balanceInDayAmount == null) {
                        balanceInDayAmount = rcLimitService.queryLimit(DefineCode.BALANCE_IN_DAY_AMOUNT.defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
                        RcLimit limit = createRcLimt(DefineCode.BALANCE_IN_DAY_AMOUNT, rcArchive, personAmountLimit.getBalanceInDayAmount().toString(), "Long", user);
                        rcLimitService.save(limit);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_DAY_AMOUNT, balanceInDayAmount.getLimitValue(), personAmountLimit.getBalanceInDayAmount().toString());
                    } else {
                        origValue = balanceInDayAmount.getLimitValue();
                        balanceInDayAmount.setLimitValue(personAmountLimit.getBalanceInDayAmount().toString());
                        rcLimitService.updateLimit(balanceInDayAmount);
                        insertOperateLog(rcArchive, userId, modifyDay, DefineCode.BALANCE_IN_DAY_AMOUNT, origValue, personAmountLimit.getBalanceInDayAmount().toString());
                    }
                }
             } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(targetType)) {
                //单笔入金
                RcLimit singleInLimit = rcLimitService.queryLimit(DefineCode.SINGLT_IN.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                logService.printLog("update单笔入金：" + singleInLimit);
                origValue = singleInLimit.getLimitValue();
                singleInLimit.setLimitValue(amountLimit.getInAmountSingle().toString());
                rcLimitService.updateLimit(singleInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_IN, origValue, amountLimit.getInAmountSingle().toString());

                //单日入金
                RcLimit dayInLimit = rcLimitService.queryLimit(DefineCode.DAY_IN.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = dayInLimit.getLimitValue();
                dayInLimit.setLimitValue(amountLimit.getInAmountDay().toString());
                rcLimitService.updateLimit(dayInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_IN, origValue, amountLimit.getInAmountDay().toString());

                //单月入金
                RcLimit monthInLimit = rcLimitService.queryLimit(DefineCode.MONTH_IN.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = monthInLimit.getLimitValue();
                monthInLimit.setLimitValue(amountLimit.getInAmountMonth().toString());
                rcLimitService.updateLimit(monthInLimit);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_IN, origValue, amountLimit.getInAmountMonth().toString());

                // 20240117
                // 信用卡入金单笔最高限额
                RcLimit creditCardInAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = creditCardInAmount.getLimitValue();
                creditCardInAmount.setLimitValue(cardAmount.getCreditCardInAmount().toString());
                rcLimitService.updateLimit(creditCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_AMOUNT, origValue, cardAmount.getCreditCardInAmount().toString());
                // 信用卡入金单日最高限额
                RcLimit creditCardInDayAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = creditCardInDayAmount.getLimitValue();
                creditCardInDayAmount.setLimitValue(cardAmount.getCreditCardInDayAmount().toString());
                rcLimitService.updateLimit(creditCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getCreditCardInDayAmount().toString());
                // 信用卡入金单月最高限额
                RcLimit creditCardInMonAmount = rcLimitService.queryLimit(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = creditCardInMonAmount.getLimitValue();
                creditCardInMonAmount.setLimitValue(cardAmount.getCreditCardInMonAmount().toString());
                rcLimitService.updateLimit(creditCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CREDIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getCreditCardInMonAmount().toString());
                // 储蓄卡入金单笔最高限额
                RcLimit debitCardInAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = debitCardInAmount.getLimitValue();
                debitCardInAmount.setLimitValue(cardAmount.getDebitCardInAmount().toString());
                rcLimitService.updateLimit(debitCardInAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_AMOUNT, origValue, cardAmount.getDebitCardInAmount().toString());
                // 储蓄卡入金单日最高限额
                RcLimit debitCardInDayAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = debitCardInDayAmount.getLimitValue();
                debitCardInDayAmount.setLimitValue(cardAmount.getDebitCardInDayAmount().toString());
                rcLimitService.updateLimit(debitCardInDayAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_DAY_AMOUNT, origValue, cardAmount.getDebitCardInDayAmount().toString());
                // 储蓄卡入金单月最高限额
                RcLimit debitCardInMonAmount = rcLimitService.queryLimit(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                origValue = debitCardInMonAmount.getLimitValue();
                debitCardInMonAmount.setLimitValue(cardAmount.getDebitCardInMonAmount().toString());
                rcLimitService.updateLimit(debitCardInMonAmount);
                insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DEBIT_CARD_IN_MON_AMOUNT, origValue, cardAmount.getDebitCardInMonAmount().toString());

                // 小微/个体工商户无以下限额
                if (Constants.CustomerType.BUSINESSMAN.code != rcArchive.getType() && Constants.CustomerType.MICRO.code != rcArchive.getType()) {
                    //单年入金
                    RcLimit yearInLimit = rcLimitService.queryLimit(DefineCode.YEAR_IN.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = yearInLimit.getLimitValue();
                    yearInLimit.setLimitValue(amountLimit.getInAmountYear().toString());
                    rcLimitService.updateLimit(yearInLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_IN, origValue, amountLimit.getInAmountYear().toString());

                    //单笔提现，20210730改为提现
                    RcLimit singleOutLimit = rcLimitService.queryLimit(DefineCode.SINGLT_OUT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = singleOutLimit.getLimitValue();
                    singleOutLimit.setLimitValue(amountLimit.getOutAmountSingle().toString());
                    rcLimitService.updateLimit(singleOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLT_OUT, origValue, amountLimit.getOutAmountSingle().toString());

                    //单日提现，20210730改为提现
                    RcLimit dayOutLimit = rcLimitService.queryLimit(DefineCode.DAY_OUT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = dayOutLimit.getLimitValue();
                    dayOutLimit.setLimitValue(amountLimit.getOutAmountDay().toString());
                    rcLimitService.updateLimit(dayOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_OUT, origValue, amountLimit.getOutAmountDay().toString());

                    //单月提现，20210730改为提现
                    RcLimit monthOutLimit = rcLimitService.queryLimit(DefineCode.MONTH_OUT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = monthOutLimit.getLimitValue();
                    monthOutLimit.setLimitValue(amountLimit.getOutAmountMonth().toString());
                    rcLimitService.updateLimit(monthOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_OUT, origValue, amountLimit.getOutAmountMonth().toString());

                    //单年提现，20210730改为提现
                    RcLimit yearOutLimit = rcLimitService.queryLimit(DefineCode.YEAR_OUT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = yearOutLimit.getLimitValue();
                    yearOutLimit.setLimitValue(amountLimit.getOutAmountYear().toString());
                    rcLimitService.updateLimit(yearOutLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_OUT, origValue, amountLimit.getOutAmountYear().toString());

                    // 单笔代付，20210730
                    RcLimit singleWithdrawLimit = rcLimitService.queryLimit(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                    origValue = singleWithdrawLimit.getLimitValue();
                    singleWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountSingle().toString());
                    rcLimitService.updateLimit(singleWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountSingle().toString());

                    // 单日代付，20210730
                    RcLimit dayWithdrawLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                    origValue = dayWithdrawLimit.getLimitValue();
                    dayWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountDay().toString());
                    rcLimitService.updateLimit(dayWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountDay().toString());

                    // 单月代付，20210730
                    RcLimit monthWithdrawLimit = rcLimitService.queryLimit(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                    origValue = monthWithdrawLimit.getLimitValue();
                    monthWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountMonth().toString());
                    rcLimitService.updateLimit(monthWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.MONTH_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountMonth().toString());

                    // 单年代付，20210730
                    RcLimit yearWithdrawLimit = rcLimitService.queryLimit(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineId, RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = yearWithdrawLimit.getLimitValue();
                    yearWithdrawLimit.setLimitValue(amountLimit.getWithdrawAmountYear().toString());
                    rcLimitService.updateLimit(yearWithdrawLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.YEAR_MAX_WITHDRAW_AMOUNT, origValue, amountLimit.getWithdrawAmountYear().toString());

                    //垫资代付单日限额
                    RcLimit withdrawAmountDayLimit = rcLimitService.queryLimit(DefineCode.DAY_WITHDRAW.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = withdrawAmountDayLimit.getLimitValue();
                    withdrawAmountDayLimit.setLimitValue(withdrawAmount.getWithdrawAmountDay().toString());
                    rcLimitService.updateLimit(withdrawAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_WITHDRAW, origValue, withdrawAmount.getWithdrawAmountDay().toString());

                    // 同卡代付单日限额
                    RcLimit cardOutAmountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_OUT_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardOutAmountDayLimit.getLimitValue();
                    cardOutAmountDayLimit.setLimitValue(cardAmount.getCardOutAmountDay().toString());
                    rcLimitService.updateLimit(cardOutAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_AMOUNT, origValue, cardAmount.getCardOutAmountDay().toString());

                    // 同卡代付单日限笔数
                    RcLimit cardOutCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_OUT_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardOutCountDayLimit.getLimitValue();
                    cardOutCountDayLimit.setLimitValue(cardAmount.getCardOutCountDay().toString());
                    rcLimitService.updateLimit(cardOutCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_OUT_COUNT, origValue, cardAmount.getCardOutCountDay().toString());

                    // 同卡代付单月限额
                    RcLimit cardOutAmountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_OUT_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardOutAmountMonthLimit.getLimitValue();
                    cardOutAmountMonthLimit.setLimitValue(cardAmount.getCardOutAmountMonth().toString());
                    rcLimitService.updateLimit(cardOutAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_AMOUNT, origValue, cardAmount.getCardOutAmountMonth().toString());

                    // 同卡代付单月限笔数
                    RcLimit cardOutCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_OUT_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardOutCountMonthLimit.getLimitValue();
                    cardOutCountMonthLimit.setLimitValue(cardAmount.getCardOutCountMonth().toString());
                    rcLimitService.updateLimit(cardOutCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_OUT_COUNT, origValue, cardAmount.getCardOutCountMonth().toString());

                    // 同卡入金单日限额
                    RcLimit cardInAmountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInAmountDayLimit.getLimitValue();
                    cardInAmountDayLimit.setLimitValue(cardAmount.getCardInAmountDay().toString());
                    rcLimitService.updateLimit(cardInAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_AMOUNT, origValue, cardAmount.getCardInAmountDay().toString());

                    // 同卡入金单日限笔数
                    RcLimit cardInCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInCountDayLimit.getLimitValue();
                    cardInCountDayLimit.setLimitValue(cardAmount.getCardInCountDay().toString());
                    rcLimitService.updateLimit(cardInCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_COUNT, origValue, cardAmount.getCardInCountDay().toString());

                    // 同卡入金单月限额
                    RcLimit cardInAmountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInAmountMonthLimit.getLimitValue();
                    cardInAmountMonthLimit.setLimitValue(cardAmount.getCardInAmountMonth().toString());
                    rcLimitService.updateLimit(cardInAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_AMOUNT, origValue, cardAmount.getCardInAmountMonth().toString());

                    // 同卡入金单月限笔数
                    RcLimit cardInCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInCountMonthLimit.getLimitValue();
                    cardInCountMonthLimit.setLimitValue(cardAmount.getCardInCountMonth().toString());
                    rcLimitService.updateLimit(cardInCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_COUNT, origValue, cardAmount.getCardInCountMonth().toString());

                    // 同卡入金单日限总笔数（成功+失败）  20210730
                    RcLimit cardInTotalCountDayLimit = rcLimitService.queryLimit(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInTotalCountDayLimit.getLimitValue();
                    cardInTotalCountDayLimit.setLimitValue(cardAmount.getCardInTotalCountDay().toString());
                    rcLimitService.updateLimit(cardInTotalCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_DAY_IN_TOTAL_COUNT, origValue, cardAmount.getCardInTotalCountDay().toString());

                    // 同卡入金单月限总笔数（成功+失败）  20210730
                    RcLimit cardInTotalCountMonthLimit = rcLimitService.queryLimit(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = cardInTotalCountMonthLimit.getLimitValue();
                    cardInTotalCountMonthLimit.setLimitValue(cardAmount.getCardInTotalCountMonth().toString());
                    rcLimitService.updateLimit(cardInTotalCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.CARD_MONTH_IN_TOTAL_COUNT, origValue, cardAmount.getCardInTotalCountMonth().toString());

                    // 用户入金单日限额
                    RcLimit userInAmountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInAmountDayLimit.getLimitValue();
                    userInAmountDayLimit.setLimitValue(userAmount.getUserInAmountDay().toString());
                    rcLimitService.updateLimit(userInAmountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_AMOUNT, origValue, userAmount.getUserInAmountDay().toString());

                    // 用户入金单日限笔数
                    RcLimit userInCountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInCountDayLimit.getLimitValue();
                    userInCountDayLimit.setLimitValue(userAmount.getUserInCountDay().toString());
                    rcLimitService.updateLimit(userInCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_COUNT, origValue, userAmount.getUserInCountDay().toString());

                    // 用户入金单月限额
                    RcLimit userInAmountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_AMOUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInAmountMonthLimit.getLimitValue();
                    userInAmountMonthLimit.setLimitValue(userAmount.getUserInAmountMonth().toString());
                    rcLimitService.updateLimit(userInAmountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_AMOUNT, origValue, userAmount.getUserInAmountMonth().toString());

                    // 用户入金单月限笔数
                    RcLimit userInCountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInCountMonthLimit.getLimitValue();
                    userInCountMonthLimit.setLimitValue(userAmount.getUserInCountMonth().toString());
                    rcLimitService.updateLimit(userInCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_COUNT, origValue, userAmount.getUserInCountMonth().toString());

                    // 用户入金单日限总笔数（成功+失败）  20210730
                    RcLimit userInTotalCountDayLimit = rcLimitService.queryLimit(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInTotalCountDayLimit.getLimitValue();
                    userInTotalCountDayLimit.setLimitValue(userAmount.getUserInTotalCountDay().toString());
                    rcLimitService.updateLimit(userInTotalCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_DAY_IN_TOTAL_COUNT, origValue, userAmount.getUserInTotalCountDay().toString());

                    // 用户入金单月限总笔数（成功+失败）  20210730
                    RcLimit userInTotalCountMonthLimit = rcLimitService.queryLimit(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code,rcArchive.getArchiveCode());
                    origValue = userInTotalCountMonthLimit.getLimitValue();
                    userInTotalCountMonthLimit.setLimitValue(userAmount.getUserInTotalCountMonth().toString());
                    rcLimitService.updateLimit(userInTotalCountMonthLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.USER_MONTH_IN_TOTAL_COUNT, origValue, userAmount.getUserInTotalCountMonth().toString());

                    // 商户维度日转账次数限额 2020.6.22
                    RcLimit transferCountDayLimit = rcLimitService.queryLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                    origValue = transferCountDayLimit.getLimitValue();
                    transferCountDayLimit.setLimitValue(merchantAmountLimit.getTransCountLimitDay().toString());
                    rcLimitService.updateLimit(transferCountDayLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.WITHDRAW_DAY_MAX_COUNT, origValue, merchantAmountLimit.getTransCountLimitDay().toString());

                    // 商户日提现次数限额 2022.1.13
                    RcLimit dayMaxOutCountLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_OUT_COUNT.defineId,RcConstants.BusinessTagerType.INDUSTRY.code, rcArchive.getArchiveCode());
                    origValue = dayMaxOutCountLimit.getLimitValue();
                    dayMaxOutCountLimit.setLimitValue(merchantAmountLimit.getDayMaxOutCount().toString());
                    rcLimitService.updateLimit(dayMaxOutCountLimit);
                    insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_OUT_COUNT, origValue, merchantAmountLimit.getDayMaxOutCount().toString());
                }
            }
        }
	}
	
	
	@Validatable
	@Exceptionable
	@Logable(businessTag = "amountLimitQuery")
	@ApiOperation(value ="商户限额管理-限额查询")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "type",value = "类型：001：身份证；003：统一社会信用代码；005:商户编号；008：终端号码；009：个人限额", dataType = "String",required = true,paramType = "query"),
        @ApiImplicitParam(name = "customerCode",value = "商户编号/证件号码/终端号", dataType = "String",required = true,paramType = "query"),
    })
    @GetMapping("/amountLimitQuery")
    public AmountLimitQueryResponse amountLimitQuery(
            @RequestParam("type") String type,
            @RequestParam("customerCode") String customerCode){

	    AmountLimitQueryResponse resp = new AmountLimitQueryResponse();
		//根据商户号查询商户
        RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(type, customerCode);
        if (rcArchive == null){
            resp.setReturnCode(RcCode.USER_NOT_EXIXT.code);
            resp.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
			return resp;
		}
        
        try {
            BeanUtils.copyProperties(resp, rcArchive);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }

        MainAmountLimit mainAmountLimit = new MainAmountLimit();
        OutAmountLimit outAmountLimit = new OutAmountLimit();
        InstAmountLimit instAmountLimit = new InstAmountLimit();
        WithdrawAmountLimit withdrawAmount = new WithdrawAmountLimit();
        CardAmountLimit cardLimit = new CardAmountLimit();
        MerchantAmountLimit merchantAmountLimit = new MerchantAmountLimit();
        UserAmountLimit userAmount = new UserAmountLimit();
        CertificateAmountLimit certificateAmount = new CertificateAmountLimit();
        TermAmountLimit termAmountLimit = new TermAmountLimit();
        PersonAmountLimit personAmountLimit = new PersonAmountLimit();
        PlatCustomerAmountLimit platCustomerAmountLimit = null;

        // 新增附件列表信息返回
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType())) {
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("archiveType",rcArchive.getArchiveType());
            paramMap.put("archiveCode",rcArchive.getArchiveCode());
            List<LimitAttachmentWithBLOBs> attachments = limitAttachmentService.selectByArchiveTypeAndCode(paramMap);
            if (attachments != null && attachments.size() > 0) {
                LimitAttachmentWithBLOBs limitAttachment = attachments.get(0);
                // 新值
                List<AttachmentVO> attachmentVOS = JSONArray.parseArray(limitAttachment.getAttachmentData(),AttachmentVO.class);
                if (attachmentVOS != null && attachmentVOS.size() > 0) {
                    for (int i = 0; i < attachmentVOS.size(); i++) {
                        AttachmentVO attachmentVO = attachmentVOS.get(i);
                        Map<String,String> urlMap = fsService.filePath(attachmentVO.getUniqueId(),30,100,"download");
                        attachmentVO.setUrl(urlMap.get("filePath"));
                        attachmentVOS.set(i,attachmentVO);
                    }
                }
                resp.setAttachments(attachmentVOS);
            }
        }

        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {
    		//根据商户号查询限额
    		//Map<String,Long> amountLimitMap = rcLimitService.queryCustomerAmountLimit(customerCode, rcArchive.getParentCode(), rcArchive.getRcLevel());
            Map<String,Long> amountLimitMap = rcLimitService.queryAmountLimit(rcArchive.getArchiveType(),customerCode,rcArchive.getRcLevel(),rcArchive);
            mainAmountLimit = transMainAmountLimit(amountLimitMap);
    		resp.setMainAmount(mainAmountLimit);

            LimitSwitch daliy = limitSwitchService.getLimitInfo(customerCode,"Daliy-Out-Limit");
            if (!org.springframework.util.StringUtils.isEmpty(daliy)) {
                outAmountLimit.setDaliyOutLimit(daliy.getLimitValue());
            }
            LimitSwitch single = limitSwitchService.getLimitInfo(customerCode,"Single-Out-Limit");
            if (!org.springframework.util.StringUtils.isEmpty(single)) {
                outAmountLimit.setSingleOutLimit(single.getLimitValue());
            }
            // rc_limit_switch未配置时查询默认限额
            if (daliy == null && single == null) {
                Map map = rcLimitService.queryCustomerAmountLimit(rcArchive.getRcLevel(),rcArchive.getRcLevel(),rcArchive.getRcLevel());
                outAmountLimit = transOutAmountLimit(map);
            }
            resp.setOutAmountLimit(outAmountLimit);

    		
    	    withdrawAmount.setWithdrawAmountDay(amountLimitMap.get(DefineCode.DAY_WITHDRAW.defineCode));
    	    resp.setWithdrawAmount(withdrawAmount);

    	    cardLimit = transCardAmountLimit(amountLimitMap);
    		resp.setCardAmount(cardLimit);

    		userAmount = transUserAmountLimit(amountLimitMap);
    		resp.setUserAmount(userAmount);

            instAmountLimit = transInstAmountLimit(amountLimitMap);
            resp.setInstAmountLimit(instAmountLimit);
    
            merchantAmountLimit.setTransCountLimitDay(amountLimitMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            merchantAmountLimit.setDayMaxOutCount(amountLimitMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
            resp.setMerchantAmountLimit(merchantAmountLimit);

            CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
            //平台商返回平台商维度限额
            if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) {
                Map<String, Long> platCustomerDefaultMap = rcLimitService.queryAmountLimit(customerCode, RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, rcArchive.getRcLevel());
                platCustomerAmountLimit = PlatCustomerAmountLimit.fromMap(platCustomerDefaultMap);
                resp.setPlatCustomerAmountLimit(platCustomerAmountLimit);
                resp.setAudPlatCustomerAmountLimit(new PlatCustomerAmountLimit());
            }

        } else  if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType())) {
            // 根据证件号查询限额
            Map<String,Long> certLimitMap = rcLimitService.queryCertAmountLimit(customerCode, rcArchive.getArchiveType(), rcArchive.getRcLevel());
            certificateAmount.setTransCountLimitDay(certLimitMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            certificateAmount.setDayMaxInAmount(certLimitMap.get(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineCode));
            certificateAmount.setMonthMaxInAmount(certLimitMap.get(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineCode));
            certificateAmount.setDayMaxCreditAmount(certLimitMap.get(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineCode));
            certificateAmount.setMonthMaxCreditAmount(certLimitMap.get(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineCode));
            certificateAmount.setDayMaxOutCount(certLimitMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
            
            resp.setCertificateAmountLimit(certificateAmount);

            instAmountLimit.setCardDayInAmount(certLimitMap.get(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode));
            instAmountLimit.setUserDayInAmount(certLimitMap.get(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode));
            resp.setInstAmountLimit(instAmountLimit);
       
        } else if (RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())){
            // 根据终端号查询限额
            Map<String,Long> termLimitMap = rcLimitService.queryAmountLimit(customerCode, rcArchive.getArchiveType(), rcArchive.getRcLevel());
            termAmountLimit = transTermAmountLimit(termLimitMap);
            resp.setTermAmountLimit(termAmountLimit); 
        
        } else if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())){
            // 根据风控等级查询个人限额 ********
            Map<String, Long> personDefaultMap = rcLimitService.queryAmountLimit(customerCode, rcArchive.getArchiveType(), rcArchive.getRcLevel());
            Map<String, Long> addDefaultMap = rcLimitService.queryAddAmountLimitMap(customerCode, rcArchive.getArchiveType(), rcArchive.getRcLevel());
            if (addDefaultMap != null) {
                personDefaultMap.putAll(addDefaultMap);
            }
            personAmountLimit = transPersonAmountLimit(personDefaultMap);
            resp.setPersonAmountLimit(personAmountLimit);
        } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(rcArchive.getArchiveType())) {
            Map<String,Long> clientNoMap = rcLimitService.queryAmountLimit(rcArchive.getArchiveType(),customerCode,rcArchive.getRcLevel(),rcArchive);
            mainAmountLimit = transMainAmountLimit(clientNoMap);
            withdrawAmount.setWithdrawAmountDay(clientNoMap.get(DefineCode.DAY_WITHDRAW.defineCode));
            cardLimit = transCardAmountLimit(clientNoMap);
            userAmount = transUserAmountLimit(clientNoMap);
            merchantAmountLimit.setTransCountLimitDay(clientNoMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            merchantAmountLimit.setDayMaxOutCount(clientNoMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
            outAmountLimit = transOutAmountLimit(clientNoMap);

            resp.setMainAmount(mainAmountLimit);
            resp.setWithdrawAmount(withdrawAmount);
            resp.setCardAmount(cardLimit);
            resp.setUserAmount(userAmount);
            resp.setMerchantAmountLimit(merchantAmountLimit);
            resp.setOutAmountLimit(outAmountLimit);
        } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(rcArchive.getArchiveType())) {
            Map<String, Long> industryMap = rcLimitService.queryIndustryAmountLimit(customerCode, rcArchive.getArchiveType());
            mainAmountLimit = transMainAmountLimit(industryMap);
            withdrawAmount.setWithdrawAmountDay(industryMap.get(DefineCode.DAY_WITHDRAW.defineCode));
            cardLimit = transCardAmountLimit(industryMap);
            userAmount = transUserAmountLimit(industryMap);
            merchantAmountLimit.setTransCountLimitDay(industryMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            merchantAmountLimit.setDayMaxOutCount(industryMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
            outAmountLimit = transOutAmountLimit(industryMap);

            resp.setMainAmount(mainAmountLimit);
            resp.setWithdrawAmount(withdrawAmount);
            resp.setCardAmount(cardLimit);
            resp.setUserAmount(userAmount);
            resp.setMerchantAmountLimit(merchantAmountLimit);
            resp.setOutAmountLimit(outAmountLimit);

        }
        
        // 应前端要求，初始化返回空对象。。。
        resp.setAudMainAmount(new MainAmountLimit());
        resp.setAudOutAmountLimit(new OutAmountLimit());
        resp.setAudInstAmountLimit(new InstAmountLimit());
        resp.setAudWithdrawAmount(new WithdrawAmountLimit());
        resp.setAudCardAmount(new CardAmountLimit());
        resp.setAudMerchantAmountLimit(new MerchantAmountLimit());
        resp.setAudCertificateAmountLimit(new CertificateAmountLimit());
        resp.setAudUserAmount(new UserAmountLimit());
        resp.setAudTermAmountLimit(new TermAmountLimit());
        resp.setAudPersonAmountLimit(new PersonAmountLimit());

        // 审核记录值查询 2020.8.10
        RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(rcArchive.getArchiveCode(), rcArchive.getArchiveType());
        if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
            resp.setAudId(limitAudRecord.getAudId());
            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
                    || RcConstants.BusinessTagerType.CLIENT_NO.code.equals(rcArchive.getArchiveType())
                    || RcConstants.BusinessTagerType.INDUSTRY.code.equals(rcArchive.getArchiveType())) {
                resp.setAudMainAmount(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), MainAmountLimit.class));
                resp.setAudOutAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(),OutAmountLimit.class));
                if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {
                    resp.setAudInstAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(),InstAmountLimit.class));
                }
                resp.setAudWithdrawAmount(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), WithdrawAmountLimit.class));
                resp.setAudCardAmount(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), CardAmountLimit.class));
                resp.setAudMerchantAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), MerchantAmountLimit.class));
                resp.setAudUserAmount(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), UserAmountLimit.class));

                // 相同值设置为空，前端展示不同值
                setSameValueAsNull(mainAmountLimit, resp.getAudMainAmount());
                setSameValueAsNull(outAmountLimit,resp.getAudOutAmountLimit());
                setSameValueAsNull(withdrawAmount, resp.getAudWithdrawAmount());
                setSameValueAsNull(cardLimit, resp.getAudCardAmount());
                setSameValueAsNull(merchantAmountLimit, resp.getAudMerchantAmountLimit());
                setSameValueAsNull(userAmount, resp.getAudUserAmount());
                //平台商返回平台商维度限额
                if (platCustomerAmountLimit != null) {
                    resp.setAudPlatCustomerAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), PlatCustomerAmountLimit.class));
                    setSameValueAsNull(platCustomerAmountLimit, resp.getAudPlatCustomerAmountLimit());
                }

             } else  if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                     || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType())) {
                resp.setAudCertificateAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), CertificateAmountLimit.class));
                resp.setAudInstAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), InstAmountLimit.class));
                setSameValueAsNull(certificateAmount, resp.getAudCertificateAmountLimit());
             
             } else if (RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())){
                 resp.setAudTermAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), TermAmountLimit.class));
                 setSameValueAsNull(termAmountLimit, resp.getAudTermAmountLimit());
             
             } else if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())){
                 resp.setAudPersonAmountLimit(amountLimitAudService.queryAudLimitValueToObj(limitAudRecord.getAudId(), PersonAmountLimit.class));
                 setSameValueAsNull(personAmountLimit, resp.getAudPersonAmountLimit());
             }
            
        }
        
        resp.setReturnCode(CommonOuterResponse.SUCCEE);
		resp.setReturnMsg("查询成功!");
		return resp;
	}

	

    /**
	 * 对比两对象，属性值相同的设置dest值为null
	 * @param src
	 * @param dest
	 */
	private void setSameValueAsNull(Object src, Object dest) {

	    if (src == null || dest == null) {
	        return ;
	    }
	    if (!src.getClass().getName().equals(dest.getClass().getName())) { // 类不同返回
	        return ;
	    }
	    
	    BeanWrapper source = new BeanWrapperImpl(src);
	    BeanWrapper target = new BeanWrapperImpl(dest);
	    java.beans.PropertyDescriptor[] pds = source.getPropertyDescriptors();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = source.getPropertyValue(pd.getName());
            Object targetValue = target.getPropertyValue(pd.getName());
            if ("class".equals(pd.getName())) {
                continue;
            }
            if (targetValue != null && targetValue.equals(srcValue)) {
                target.setPropertyValue(pd.getName(), null);
            }
        }
	}
	
	
	@Validatable
	@Exceptionable
	@Logable(businessTag = "defaultQuery")
	@ApiOperation(value ="查询默认限额")
	@ApiImplicitParams(
	{
        @ApiImplicitParam(name = "type",value = "类型：001：身份证；003：统一社会信用代码；005:商户编号；008：终端号码；009：个人限额", dataType = "String",required = false, paramType = "query"),
		@ApiImplicitParam(name = "customerCode",value = "商户编号", dataType = "String",required = true,paramType = "query"),
	})
	@GetMapping("/defaultQuery")
	public Map<String,Object> defaultQuery(
            @RequestParam(value = "type", required = false) String type,
            @RequestParam("customerCode") String customerCode
       ){
		Map<String,Object>  respons = new HashedMap();

		type =  StringUtils.isBlank(type) ? RcConstants.BusinessTagerType.CUSTOMER_CODE.code : type;
		//根据商户号查询商户
		RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(type, customerCode);
		if ( rcArchive == null) {
		    respons.put("returnCode",RcCode.RC_ARCHIVE_NOT_EXISTS.code);
	        respons.put("returnMsg",RcCode.RC_ARCHIVE_NOT_EXISTS.message);
	        return respons;
		}

	    LevelAmountLimit levelAmountLimit = new LevelAmountLimit();

        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {

    		//根据风控等级查询限额
    		Map<String,Long> defaultMap = rcLimitService.queryCustomerAmountLimit(rcArchive.getRcLevel(), rcArchive.getRcLevel(), rcArchive.getRcLevel());
    
    	    WithdrawAmountLimit defaultWithdrawLimit = new WithdrawAmountLimit();
    	    defaultWithdrawLimit.setWithdrawAmountDay(defaultMap.get(DefineCode.DAY_WITHDRAW.defineCode));

            OutAmountLimit defaultdefaultOutAmount = new OutAmountLimit();
            defaultdefaultOutAmount.setDaliyOutLimit(defaultMap.get(DefineCode.DALIY_OUT_LIMIT.defineCode));
            defaultdefaultOutAmount.setSingleOutLimit(defaultMap.get(DefineCode.SINGLE_OUT_LIMIT.defineCode));

    		MerchantAmountLimit merchantAmountLimit = new MerchantAmountLimit();
            merchantAmountLimit.setTransCountLimitDay(defaultMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            merchantAmountLimit.setDayMaxOutCount(defaultMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));

            levelAmountLimit.setDefaultMainAmount(transMainAmountLimit(defaultMap));
            levelAmountLimit.setDefaultWithdrawAmount(defaultWithdrawLimit);
            levelAmountLimit.setDefaultOutAmount(defaultdefaultOutAmount);
            levelAmountLimit.setDefaultCardAmount(transCardAmountLimit(defaultMap));
            levelAmountLimit.setDefaultUserAmount(transUserAmountLimit(defaultMap));
            levelAmountLimit.setDefaultMerchantAmount(merchantAmountLimit);

            CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
            if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) {
                Map<String, Long> platCustomerDefaultMap = rcLimitService.queryAmountLimit(rcArchive.getRcLevel(),
                        RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, rcArchive.getRcLevel());
                levelAmountLimit.setDefaultPlatCustomerAmountLimit(PlatCustomerAmountLimit.fromMap(platCustomerDefaultMap));
            }

        } else if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(rcArchive.getArchiveType())
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(rcArchive.getArchiveType())) {        
            //根据风控等级查询限额
            Map<String,Long> defaultMap = rcLimitService.queryCertAmountLimit(rcArchive.getRcLevel(), RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcArchive.getRcLevel());
            
            CertificateAmountLimit certificateAmount = new CertificateAmountLimit();
            certificateAmount.setTransCountLimitDay(defaultMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
            certificateAmount.setDayMaxInAmount(defaultMap.get(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineCode));
            certificateAmount.setMonthMaxInAmount(defaultMap.get(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineCode));
            certificateAmount.setDayMaxCreditAmount(defaultMap.get(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineCode));
            certificateAmount.setMonthMaxCreditAmount(defaultMap.get(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineCode));
            certificateAmount.setDayMaxOutCount(defaultMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));

            levelAmountLimit.setDefaultCertificateAmount(certificateAmount);
        
        } else if ( RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())) {
            //根据风控等级查询限额
            Map<String,Long> defaultMap = rcLimitService.queryAmountLimit(rcArchive.getRcLevel(), RcConstants.BusinessTagerType.TERM.code, rcArchive.getRcLevel());
            levelAmountLimit.setDefaultTermAmount(transTermAmountLimit(defaultMap));
        
        } else if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())){
            // 根据风控等级查询个人限额 ********
            Map<String, Long> personDefaultMap = rcLimitService.queryAmountLimit(rcArchive.getRcLevel(), RcConstants.BusinessTagerType.PERSON.code, rcArchive.getRcLevel());
            levelAmountLimit.setDefaultPersonAmount(transPersonAmountLimit(personDefaultMap));
        }
        
		respons.put("returnCode","0000");
		respons.put("returnMsg","查询成功");
		respons.put("data",levelAmountLimit);
        System.out.println("打印值：" + JSON.toJSONString(levelAmountLimit));
		return respons;
	}

	/**
	 * 风险等级额度查询
	 * @return
	 */
	@GetMapping("/levelAmountQuery")
	@Exceptionable
	@Logable(businessTag = "levelAmount.query")
	@ApiOperation(value ="风险等级额度查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "targetType",value = "类型【005：商户默认限额管理；012：客户号默认限额管理】", dataType = "String",required = true,paramType = "query"),
            @ApiImplicitParam(name = "rcLevel",value = "风险等级【LOW_LEVEL,MIDDLE_LEVEL,HIGHT_LEVEL】", dataType = "String",required = true,paramType = "query")
    })
	public LevelAmountLimit queryLevelAmountLimit(String targetType, String rcLevel){
		LevelAmountLimit  levelAmountLimit = new LevelAmountLimit();

		boolean flag = true;

		for (RcConstants.RcLevel level : RcConstants.RcLevel.values()){
		    if(level.code.equals(rcLevel)){
		        flag = false;
		        break;
            }
        }

        if (flag){
		    throw new AppException(RcCode.RC_LEVLE_ERROR.code,RcCode.RC_LEVLE_ERROR.message);
        }
        Map<String,Long> map;
        if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
            map = rcLimitService.queryAmountLimit(rcLevel,RcConstants.BusinessTagerType.CLIENT_NO.code,rcLevel);
        } else {
            map = rcLimitService.queryCustomerAmountLimit(rcLevel, rcLevel, rcLevel);
        }

	    levelAmountLimit.setDefaultMainAmount(transMainAmountLimit(map));

	    // 20221129
        levelAmountLimit.setDefaultOutAmount(transOutAmountLimit(map));

	    WithdrawAmountLimit withdrawAmount = new WithdrawAmountLimit();
	    withdrawAmount.setWithdrawAmountDay(map.get(DefineCode.DAY_WITHDRAW.defineCode));
        levelAmountLimit.setDefaultWithdrawAmount(withdrawAmount);
        
        levelAmountLimit.setDefaultCardAmount(transCardAmountLimit(map));
		levelAmountLimit.setDefaultUserAmount(transUserAmountLimit(map));

        MerchantAmountLimit merchantAmountLimit = new MerchantAmountLimit();
        merchantAmountLimit.setTransCountLimitDay(map.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
        merchantAmountLimit.setDayMaxOutCount(map.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
        levelAmountLimit.setDefaultMerchantAmount(merchantAmountLimit);

        Map<String,Long> certMap = rcLimitService.queryCertAmountLimit(rcLevel, RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel);
        CertificateAmountLimit certificateAmount = new CertificateAmountLimit();
        certificateAmount.setTransCountLimitDay(certMap.get(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode));
        certificateAmount.setDayMaxInAmount(certMap.get(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineCode));
        certificateAmount.setMonthMaxInAmount(certMap.get(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineCode));
        certificateAmount.setDayMaxCreditAmount(certMap.get(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineCode));
        certificateAmount.setMonthMaxCreditAmount(certMap.get(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineCode));
        certificateAmount.setDayMaxOutCount(certMap.get(DefineCode.DAY_MAX_OUT_COUNT.defineCode));
        levelAmountLimit.setDefaultCertificateAmount(certificateAmount);

        //根据风控等级查询终端限额
        Map<String,Long> defaultMap = rcLimitService.queryAmountLimit(rcLevel, RcConstants.BusinessTagerType.TERM.code, rcLevel);
        levelAmountLimit.setDefaultTermAmount(transTermAmountLimit(defaultMap));
        
        //根据风控等级查询个人限额
        Map<String, Long> personDefaultMap = rcLimitService.queryAmountLimit(rcLevel, RcConstants.BusinessTagerType.PERSON.code, rcLevel);
        levelAmountLimit.setDefaultPersonAmount(transPersonAmountLimit(personDefaultMap));

        Map<String, Long> platCustomerDefaultMap = rcLimitService.queryAmountLimit(rcLevel, RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, rcLevel);
        levelAmountLimit.setDefaultPlatCustomerAmountLimit(PlatCustomerAmountLimit.fromMap(platCustomerDefaultMap));

		levelAmountLimit.setReturnCode(CommonOuterResponse.SUCCEE);
		levelAmountLimit.setReturnMsg("查询成功");
		levelAmountLimit.setRcLevel(rcLevel);
		
		return levelAmountLimit;
	}
	
	
	
	private MainAmountLimit transMainAmountLimit(Map<String, Long> limitMap) {
	    
	    MainAmountLimit mainLimit = new MainAmountLimit();
	    mainLimit.setInAmountSingle(limitMap.get(DefineCode.SINGLT_IN.defineCode));
	    mainLimit.setInAmountDay(limitMap.get(DefineCode.DAY_IN.defineCode));
	    mainLimit.setInAmountMonth(limitMap.get(DefineCode.MONTH_IN.defineCode));
	    mainLimit.setInAmountYear(limitMap.get(DefineCode.YEAR_IN.defineCode));
	    mainLimit.setOutAmountSingle(limitMap.get(DefineCode.SINGLT_OUT.defineCode));
	    mainLimit.setOutAmountDay(limitMap.get(DefineCode.DAY_OUT.defineCode));
	    mainLimit.setOutAmountMonth(limitMap.get(DefineCode.MONTH_OUT.defineCode));
	    mainLimit.setOutAmountYear(limitMap.get(DefineCode.YEAR_OUT.defineCode));
	    // 20210730
	    mainLimit.setWithdrawAmountSingle(limitMap.get(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineCode));
	    mainLimit.setWithdrawAmountDay(limitMap.get(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineCode));
	    mainLimit.setWithdrawAmountMonth(limitMap.get(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineCode));
	    mainLimit.setWithdrawAmountYear(limitMap.get(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineCode));
        return mainLimit;
	}

	private OutAmountLimit transOutAmountLimit(Map<String, Long> amountLimitMap) {
        OutAmountLimit outAmountLimit = new OutAmountLimit();
        outAmountLimit.setDaliyOutLimit(org.springframework.util.StringUtils.isEmpty(amountLimitMap.get(DefineCode.DALIY_OUT_LIMIT.defineCode)) ? 500000L : amountLimitMap.get(DefineCode.DALIY_OUT_LIMIT.defineCode));
        outAmountLimit.setSingleOutLimit(org.springframework.util.StringUtils.isEmpty(amountLimitMap.get(DefineCode.SINGLE_OUT_LIMIT.defineCode)) ? 500000L : amountLimitMap.get(DefineCode.SINGLE_OUT_LIMIT.defineCode));
        return outAmountLimit;
    }

    private InstAmountLimit transInstAmountLimit(Map<String, Long> amountLimitMap) {
        InstAmountLimit instAmountLimit = new InstAmountLimit();
        instAmountLimit.setCardDayInAmount(amountLimitMap.get(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode));
        instAmountLimit.setUserDayInAmount(amountLimitMap.get(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode));
        instAmountLimit.setCardTotalInAmount(amountLimitMap.get(DefineCode.INST_CARD_TOTAL_IN_AMOUNT.defineCode));
        instAmountLimit.setUserTotalInAmount(amountLimitMap.get(DefineCode.INST_USER_TOTAL_IN_AMOUNT.defineCode));
        return instAmountLimit;
    }
	
	private CardAmountLimit transCardAmountLimit(Map<String, Long> amountLimitMap) {

	    CardAmountLimit cardLimit = new CardAmountLimit();
        cardLimit.setCardOutAmountDay(amountLimitMap.get(DefineCode.CARD_DAY_OUT_AMOUNT.defineCode));
        cardLimit.setCardOutCountDay(amountLimitMap.get(DefineCode.CARD_DAY_OUT_COUNT.defineCode));
        cardLimit.setCardOutAmountMonth(amountLimitMap.get(DefineCode.CARD_MONTH_OUT_AMOUNT.defineCode));
        cardLimit.setCardOutCountMonth(amountLimitMap.get(DefineCode.CARD_MONTH_OUT_COUNT.defineCode));
        cardLimit.setCardInAmountDay(amountLimitMap.get(DefineCode.CARD_DAY_IN_AMOUNT.defineCode));
        cardLimit.setCardInCountDay(amountLimitMap.get(DefineCode.CARD_DAY_IN_COUNT.defineCode));
        cardLimit.setCardInAmountMonth(amountLimitMap.get(DefineCode.CARD_MONTH_IN_AMOUNT.defineCode));
        cardLimit.setCardInCountMonth(amountLimitMap.get(DefineCode.CARD_MONTH_IN_COUNT.defineCode));
        // 20210730
        cardLimit.setCardInTotalCountDay(amountLimitMap.get(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineCode));
        cardLimit.setCardInTotalCountMonth(amountLimitMap.get(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineCode));
        // 20240119
        cardLimit.setCreditCardInAmount(amountLimitMap.get(DefineCode.CREDIT_CARD_IN_AMOUNT.defineCode));
        cardLimit.setCreditCardInDayAmount(amountLimitMap.get(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineCode));
        cardLimit.setCreditCardInMonAmount(amountLimitMap.get(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineCode));
        cardLimit.setDebitCardInAmount(amountLimitMap.get(DefineCode.DEBIT_CARD_IN_AMOUNT.defineCode));
        cardLimit.setDebitCardInDayAmount(amountLimitMap.get(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineCode));
        cardLimit.setDebitCardInMonAmount(amountLimitMap.get(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineCode));
        return cardLimit;
    }
	
    private UserAmountLimit transUserAmountLimit(Map<String, Long> amountLimitMap) {

        UserAmountLimit userAmount = new UserAmountLimit();
        userAmount.setUserInAmountDay(amountLimitMap.get(DefineCode.USER_DAY_IN_AMOUNT.defineCode));
        userAmount.setUserInCountDay(amountLimitMap.get(DefineCode.USER_DAY_IN_COUNT.defineCode));
        userAmount.setUserInAmountMonth(amountLimitMap.get(DefineCode.USER_MONTH_IN_AMOUNT.defineCode));
        userAmount.setUserInCountMonth(amountLimitMap.get(DefineCode.USER_MONTH_IN_COUNT.defineCode));
        // 20210730
        userAmount.setUserInTotalCountDay(amountLimitMap.get(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineCode));
        userAmount.setUserInTotalCountMonth(amountLimitMap.get(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineCode));
        return userAmount;
    }
	
	private TermAmountLimit transTermAmountLimit(Map<String, Long> limitMap) {

    	TermAmountLimit termAmount = new TermAmountLimit();
        termAmount.setAmountSingle(limitMap.get(DefineCode.SINGLT_IN.defineCode));
        termAmount.setAmountDay(limitMap.get(DefineCode.DAY_IN.defineCode));
        termAmount.setAmountMonth(limitMap.get(DefineCode.MONTH_IN.defineCode));
        termAmount.setAmountYear(limitMap.get(DefineCode.YEAR_IN.defineCode));
        termAmount.setCountDay(limitMap.get(DefineCode.DAY_IN_COUNT.defineCode));
        termAmount.setCountMonth(limitMap.get(DefineCode.MONTH_IN_COUNT.defineCode));
        termAmount.setCountYear(limitMap.get(DefineCode.YEAR_IN_COUNT.defineCode));
        return termAmount;
	}
    
	private PersonAmountLimit transPersonAmountLimit(Map<String, Long> limitMap) {
	    
	    PersonAmountLimit personAmountLimit = new PersonAmountLimit();
        personAmountLimit.setAccountNameLimit(limitMap.get(DefineCode.ACCOUNT_NAME_LIMIT.defineCode));
        personAmountLimit.setTotalMaxOutAmount(limitMap.get(DefineCode.TOTAL_MAX_OUT_AMOUNT.defineCode));
        personAmountLimit.setUserDayMaxOutAmount(limitMap.get(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineCode));
        personAmountLimit.setUserYearMaxOutAmount(limitMap.get(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineCode));
        personAmountLimit.setPersonTransCompany(limitMap.get(DefineCode.PERSON_TRANS_COMPANY.defineCode));
        personAmountLimit.setPersonTransPerson(limitMap.get(DefineCode.PERSON_TRANS_PERSON.defineCode));
        personAmountLimit.setBalanceTransAmount(limitMap.get(DefineCode.BALANCE_TRANS_AMOUNT.defineCode));
        personAmountLimit.setBalanceTransDayAmount(limitMap.get(DefineCode.BALANCE_TRANS_DAY_AMOUNT.defineCode));
        personAmountLimit.setBalanceWithdrawAmount(limitMap.get(DefineCode.BALANCE_WITHDRAW_AMOUNT.defineCode));
        personAmountLimit.setBalanceWithdrawDayAmount(limitMap.get(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT.defineCode));
        personAmountLimit.setBalanceConsumeAmount(limitMap.get(DefineCode.BALANCE_CONSUME_AMOUNT.defineCode));
        personAmountLimit.setBalanceConsumeDayAmount(limitMap.get(DefineCode.BALANCE_CONSUME_DAY_AMOUNT.defineCode));
        personAmountLimit.setBalanceInAmount(limitMap.get(DefineCode.BALANCE_IN_AMOUNT.defineCode));
        personAmountLimit.setBalanceInDayAmount(limitMap.get(DefineCode.BALANCE_IN_DAY_AMOUNT.defineCode));
        return personAmountLimit;
	}

	
	/**
	 *  设置风险等级额度
	 * @param userId
	 * @return
	 */
	@Validatable
	@Exceptionable
	@Logable(businessTag = "rcLimit.levelAmountUpdate")
	@ApiOperation(value ="风险等级限额更新(默认限额)")
	@PostMapping("/levelAmountUpdate")
	public CommonOuterResponse saveDefaultLevel(
			@RequestHeader("x-userid")Long userId,
		    @RequestBody() LevelAmountLimit levelAmountLimit
	){
		CommonOuterResponse  response = new CommonOuterResponse();

		//根据userId查询user
		User user = ortherService.selectUserById(userId);
		if(user == null){
			response.setReturnCode(RcCode.USER_NOT_EXIXT.code);
			response.setReturnMsg(RcCode.USER_NOT_EXIXT.message);
			return response;
		}
		try {
            String rcLevelName = "";
            String rcLevel = levelAmountLimit.getRcLevel();
            MainAmountLimit amountLimit = levelAmountLimit.getDefaultMainAmount();
            OutAmountLimit outAmountLimit = levelAmountLimit.getDefaultOutAmount();
            WithdrawAmountLimit withdrawAmountLimit = levelAmountLimit.getDefaultWithdrawAmount();
            CardAmountLimit cardAmountLimit = levelAmountLimit.getDefaultCardAmount();
    		UserAmountLimit userAmountLimit = levelAmountLimit.getDefaultUserAmount();
    	    MerchantAmountLimit merchantAmountLimit = levelAmountLimit.getDefaultMerchantAmount();
            CertificateAmountLimit certificateAmount = levelAmountLimit.getDefaultCertificateAmount();
            TermAmountLimit termAmountLimit = levelAmountLimit.getDefaultTermAmount();
            PersonAmountLimit personAmountLimit = levelAmountLimit.getDefaultPersonAmount();
            PlatCustomerAmountLimit platCustomerAmountLimit = levelAmountLimit.getDefaultPlatCustomerAmountLimit();
            String targetType = levelAmountLimit.getTargetType();
            if (StringUtils.isBlank(targetType)) {
                targetType = RcConstants.BusinessTagerType.CUSTOMER_CODE.code;
            }

            //参数校验
    		for (RcConstants.RcLevel  rcLevel1: RcConstants.RcLevel.values()){
            	if (rcLevel1.code.equals(rcLevel)){
                    rcLevelName = rcLevel1.message;
                    break;
    			}
    		}
    
    		if (StringUtils.isBlank(rcLevelName)){
                response.setReturnCode(RcCode.RC_LEVLE_ERROR.code);
                response.setReturnMsg(RcCode.RC_LEVLE_ERROR.message);
                return response;
            }
    
    		if (amountLimit != null) {
        		//入金单笔最低
        		rcLimitService.updateAmountLimit(DefineCode.SINGLT_IN.defineId,targetType,rcLevel,
        		        amountLimit.getInAmountSingle().toString(), userId, user.getName());
        		//入金单日限额
        	    rcLimitService.updateAmountLimit(DefineCode.DAY_IN.defineId,targetType,rcLevel,
        	                amountLimit.getInAmountDay().toString(), userId, user.getName());
        		//入金单月限额
        		rcLimitService.updateAmountLimit(DefineCode.MONTH_IN.defineId,targetType,rcLevel,
                        amountLimit.getInAmountMonth().toString(), userId, user.getName());
        		//入金单年限额
                rcLimitService.updateAmountLimit(DefineCode.YEAR_IN.defineId,targetType,rcLevel,
                        amountLimit.getInAmountYear().toString(), userId, user.getName());
        		// 提现单笔限额， 20210730 与代付分离，保留为提现
                rcLimitService.updateAmountLimit(DefineCode.SINGLT_OUT.defineId,targetType,rcLevel,
                        amountLimit.getOutAmountSingle().toString(), userId, user.getName());
        		// 提现单日限额， 20210730 与代付分离，保留为提现
                rcLimitService.updateAmountLimit(DefineCode.DAY_OUT.defineId,targetType,rcLevel,
                        amountLimit.getOutAmountDay().toString(), userId, user.getName());
        		// 提现单月限额， 20210730 与代付分离，保留为提现
        		rcLimitService.updateAmountLimit(DefineCode.MONTH_OUT.defineId,targetType,rcLevel,
                        amountLimit.getOutAmountMonth().toString(), userId, user.getName());
        		// 提现单年限额， 20210730 与代付分离，保留为提现
                rcLimitService.updateAmountLimit(DefineCode.YEAR_OUT.defineId,targetType,rcLevel,
                        amountLimit.getOutAmountYear().toString(), userId, user.getName());
                
                // 代付单笔限额， 20210730
                rcLimitService.updateAmountLimit(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineId,targetType,rcLevel,
                        amountLimit.getWithdrawAmountSingle().toString(), userId, user.getName());
                // 代付单日限额， 20210730
                rcLimitService.updateAmountLimit(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineId,targetType,rcLevel,
                        amountLimit.getWithdrawAmountDay().toString(), userId, user.getName());
                // 代付单月限额， 20210730
                rcLimitService.updateAmountLimit(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineId,targetType,rcLevel,
                        amountLimit.getWithdrawAmountMonth().toString(), userId, user.getName());
                // 代付单年限额， 20210730
                rcLimitService.updateAmountLimit(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineId,targetType,rcLevel,
                        amountLimit.getWithdrawAmountYear().toString(), userId, user.getName());
    		}

            if (outAmountLimit != null) {
                // 商户门户单日出金限额
                rcLimitService.updateAmountLimit(DefineCode.DALIY_OUT_LIMIT.defineId,targetType,rcLevel,
                        outAmountLimit.getDaliyOutLimit().toString(), userId, user.getName());
                // 商户门户单笔出金限额
                rcLimitService.updateAmountLimit(DefineCode.SINGLE_OUT_LIMIT.defineId,targetType,rcLevel,
                        outAmountLimit.getSingleOutLimit().toString(), userId, user.getName());
            }
    		
    		if (withdrawAmountLimit != null) {
        	    //垫资代付单日限额
                rcLimitService.updateAmountLimit(DefineCode.DAY_WITHDRAW.defineId,targetType,rcLevel,
                        withdrawAmountLimit.getWithdrawAmountDay().toString(), userId, user.getName());
    		}
    		
    		if (cardAmountLimit != null) {
                // 同卡单日限额
                rcLimitService.updateAmountLimit(DefineCode.CARD_DAY_OUT_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardOutAmountDay().toString(), userId, user.getName());
                // 同卡单日限笔数
                rcLimitService.updateAmountLimit(DefineCode.CARD_DAY_OUT_COUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardOutCountDay().toString(), userId, user.getName());
                // 同卡单月限额
                rcLimitService.updateAmountLimit(DefineCode.CARD_MONTH_OUT_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardOutAmountMonth().toString(), userId, user.getName());
                // 同卡单月限笔额
                rcLimitService.updateAmountLimit(DefineCode.CARD_MONTH_OUT_COUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardOutCountMonth().toString(), userId, user.getName());
        		// 同卡入金单日限额
        		rcLimitService.updateAmountLimit(DefineCode.CARD_DAY_IN_AMOUNT.defineId,targetType,rcLevel,
        				cardAmountLimit.getCardInAmountDay().toString(), userId, user.getName());
        		// 同卡入金单日限笔数
        		rcLimitService.updateAmountLimit(DefineCode.CARD_DAY_IN_COUNT.defineId,targetType,rcLevel,
        				cardAmountLimit.getCardInCountDay().toString(), userId, user.getName());
        		// 同卡入金单月限额
        		rcLimitService.updateAmountLimit(DefineCode.CARD_MONTH_IN_AMOUNT.defineId,targetType,rcLevel,
        				cardAmountLimit.getCardInAmountMonth().toString(), userId, user.getName());
        		// 同卡入金单月限笔额
        		rcLimitService.updateAmountLimit(DefineCode.CARD_MONTH_IN_COUNT.defineId,targetType,rcLevel,
        				cardAmountLimit.getCardInCountMonth().toString(), userId, user.getName());
        		// 同卡入金单日总笔数（成功+失败） 20210730
        		rcLimitService.updateAmountLimit(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardInTotalCountDay().toString(), userId, user.getName());
                // 同卡入金单月总笔数（成功+失败） 20210730
        		rcLimitService.updateAmountLimit(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCardInTotalCountMonth().toString(), userId, user.getName());

                // 信用卡单笔入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.CREDIT_CARD_IN_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCreditCardInAmount().toString(), userId, user.getName());
                // 信用卡单日入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.CREDIT_CARD_IN_DAY_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCreditCardInDayAmount().toString(), userId, user.getName());
                // 信用卡单月入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.CREDIT_CARD_IN_MON_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getCreditCardInMonAmount().toString(), userId, user.getName());
                // 储蓄卡卡单笔入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.DEBIT_CARD_IN_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getDebitCardInAmount().toString(), userId, user.getName());
                // 储蓄卡卡单日入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.DEBIT_CARD_IN_DAY_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getDebitCardInDayAmount().toString(), userId, user.getName());
                // 储蓄卡卡单月入金最高限额 20240119
                rcLimitService.updateAmountLimit(DefineCode.DEBIT_CARD_IN_MON_AMOUNT.defineId,targetType,rcLevel,
                        cardAmountLimit.getDebitCardInMonAmount().toString(), userId, user.getName());
    		}
    		
    		if (userAmountLimit != null) {
        		// 用户入金单日限额
        		rcLimitService.updateAmountLimit(DefineCode.USER_DAY_IN_AMOUNT.defineId,targetType,rcLevel,
        				userAmountLimit.getUserInAmountDay().toString(), userId, user.getName());
        		// 用户入金单日限笔数
        		rcLimitService.updateAmountLimit(DefineCode.USER_DAY_IN_COUNT.defineId,targetType,rcLevel,
        				userAmountLimit.getUserInCountDay().toString(), userId, user.getName());
        		// 用户入金单月限额
        		rcLimitService.updateAmountLimit(DefineCode.USER_MONTH_IN_AMOUNT.defineId,targetType,rcLevel,
        				userAmountLimit.getUserInAmountMonth().toString(), userId, user.getName());
        		// 用户入金单月限笔额
        		rcLimitService.updateAmountLimit(DefineCode.USER_MONTH_IN_COUNT.defineId,targetType,rcLevel,
        				userAmountLimit.getUserInCountMonth().toString(), userId, user.getName());
        		// 用户入金单日总笔数（成功+失败） 20210730
                rcLimitService.updateAmountLimit(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineId,targetType,rcLevel,
                        userAmountLimit.getUserInTotalCountDay().toString(), userId, user.getName());
                // 用户入金单月总笔数（成功+失败） 20210730
                rcLimitService.updateAmountLimit(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineId,targetType,rcLevel,
                        userAmountLimit.getUserInTotalCountMonth().toString(), userId, user.getName());
    		}
    		
    		if (merchantAmountLimit != null) {
                // 商户转账日次数
                rcLimitService.updateAmountLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId,targetType,rcLevel,
                        merchantAmountLimit.getTransCountLimitDay().toString(), userId, user.getName());
                // 商户日提现次数 20220113
                rcLimitService.updateAmountLimit(DefineCode.DAY_MAX_OUT_COUNT.defineId,targetType,rcLevel,
                        merchantAmountLimit.getDayMaxOutCount().toString(), userId, user.getName());
    		}
    		
    		if (certificateAmount != null) {
    		    // 证件转账日次数
                rcLimitService.updateAmountLimit(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getTransCountLimitDay().toString(), userId, user.getName());
                // 证件日提现次数 20220113
                rcLimitService.updateAmountLimit(DefineCode.DAY_MAX_OUT_COUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getDayMaxOutCount().toString(), userId, user.getName());
                // 20210810
                rcLimitService.updateAmountLimit(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getDayMaxInAmount().toString(), userId, user.getName());
                
                rcLimitService.updateAmountLimit(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getMonthMaxInAmount().toString(), userId, user.getName());
                
                rcLimitService.updateAmountLimit(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getDayMaxCreditAmount().toString(), userId, user.getName());
                
                rcLimitService.updateAmountLimit(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineId,
                        RcConstants.BusinessTagerType.IDENTITY_CARD.code, rcLevel, 
                        certificateAmount.getMonthMaxCreditAmount().toString(), userId, user.getName());                
    		}
    		
            // 终端默认指标
            if (termAmountLimit != null) {
                //入金单笔最低
                rcLimitService.updateAmountLimit(DefineCode.SINGLT_IN.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getAmountSingle().toString(), userId, user.getName());
                //入金单日限额
                rcLimitService.updateAmountLimit(DefineCode.DAY_IN.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getAmountDay().toString(), userId, user.getName());
                //入金单月限额
                rcLimitService.updateAmountLimit(DefineCode.MONTH_IN.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getAmountMonth().toString(), userId, user.getName());
                //入金单年限额
                rcLimitService.updateAmountLimit(DefineCode.YEAR_IN.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getAmountYear().toString(), userId, user.getName());
                
                //入金单日交易笔数
                rcLimitService.updateAmountLimit(DefineCode.DAY_IN_COUNT.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getCountDay().toString(), userId, user.getName());
                //入金单月交易笔数
                rcLimitService.updateAmountLimit(DefineCode.MONTH_IN_COUNT.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getCountMonth().toString(), userId, user.getName());
                //入金单年交易笔数
                rcLimitService.updateAmountLimit(DefineCode.YEAR_IN_COUNT.defineId, RcConstants.BusinessTagerType.TERM.code, rcLevel,
                        termAmountLimit.getCountYear().toString(), userId, user.getName());
            }
            
            // 个人维度  ********
            if (personAmountLimit != null) {
                // 非同名卡转账限制
                rcLimitService.updateAmountLimit(DefineCode.ACCOUNT_NAME_LIMIT.defineId, RcConstants.BusinessTagerType.PERSON.code, rcLevel,
                        personAmountLimit.getAccountNameLimit().toString(), userId, user.getName());
                // 向单位支付账户转账
                rcLimitService.updateAmountLimit(DefineCode.PERSON_TRANS_COMPANY.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                        personAmountLimit.getPersonTransCompany().toString(),userId, user.getName());
                // 向个人支付账户转账
                rcLimitService.updateAmountLimit(DefineCode.PERSON_TRANS_PERSON.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                        personAmountLimit.getPersonTransPerson().toString(),userId, user.getName());
                
                // 终身出金(I类)
                if (personAmountLimit.getTotalMaxOutAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.TOTAL_MAX_OUT_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code, rcLevel,
                            personAmountLimit.getTotalMaxOutAmount().toString(), userId, user.getName());
                }
                
                // 个人单日出金（II类、III类）
                if (personAmountLimit.getUserDayMaxOutAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code, rcLevel,
                            personAmountLimit.getUserDayMaxOutAmount().toString(), userId, user.getName());
                }
                
                // 个人单年出金（II类、III类）
                if (personAmountLimit.getUserYearMaxOutAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code, rcLevel,
                            personAmountLimit.getUserYearMaxOutAmount().toString(), userId, user.getName());
                }

                // 个人余额转账单笔最高限额（II类、III类）
                if (personAmountLimit.getBalanceTransAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_TRANS_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceTransAmount().toString(),userId,user.getName());
                }
                // 余额转账单日累计限额（II类、III类）
                if (personAmountLimit.getBalanceTransDayAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_TRANS_DAY_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceTransDayAmount().toString(),userId,user.getName());
                }
                // 单笔提现最高限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_WITHDRAW_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceWithdrawAmount().toString(),userId,user.getName());
                }
                // 单日提现累计限额（II类、III类）
                if (personAmountLimit.getBalanceWithdrawDayAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_WITHDRAW_DAY_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceWithdrawDayAmount().toString(),userId,user.getName());
                }
                // 单笔余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_CONSUME_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceConsumeAmount().toString(),userId,user.getName());
                }
                // 单日余额消费最高限额（II类、III类）
                if (personAmountLimit.getBalanceConsumeDayAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_CONSUME_DAY_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceConsumeDayAmount().toString(),userId,user.getName());
                }
                // 单笔入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_IN_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceInAmount().toString(),userId, user.getName());
                }
                // 单日入金最高限额（II类、III类）
                if (personAmountLimit.getBalanceInDayAmount() != null) {
                    rcLimitService.updateAmountLimit(DefineCode.BALANCE_IN_DAY_AMOUNT.defineId, RcConstants.BusinessTagerType.PERSON.code,rcLevel,
                            personAmountLimit.getBalanceInDayAmount().toString(),userId, user.getName());
                }
            }

            // 平台商维度
            if (platCustomerAmountLimit != null) {
                //入金单日限额
                rcLimitService.updateAmountLimit(DefineCode.DAY_IN.defineId, RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, rcLevel,
                        platCustomerAmountLimit.getPlatInAmountDay().toString(), userId, user.getName());
                //入金单月限额
                rcLimitService.updateAmountLimit(DefineCode.MONTH_IN.defineId, RcConstants.BusinessTagerType.PLAT_CUSTOMER.code, rcLevel,
                        platCustomerAmountLimit.getPlatInAmountMonth().toString(), userId, user.getName());
            }

            // 客户/商户维度默认限额-风控日志前置加参数名称
            if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType) || RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
                String content = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType) ? "商户默认限额管理" : "客户号默认限额管理";
                RcOperateLog log = new RcOperateLog();
                log.setPermId("80402");
                log.setCode("/");
                log.setName("/");
                log.setType("2");
                log.setOperator(String.valueOf(userId));
                log.setOperateTime(new Date());
                log.setOperateContent(content);
                log.setOrigValue(null);
                log.setNewValue(null);
                rcOperateLogService.insert(log);
            }
    
    		OpLogHandle.setOpContent("用户:"+user.getName()+"修改了"+rcLevelName+"默认限额");
    		response.setReturnCode(CommonOuterResponse.SUCCEE);
    		response.setReturnMsg("更新成功！！");
    		
    	} catch (AppException e) {
    	    e.printStackTrace();
    	    response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            
    	} catch (Exception e) {
            logger.printMessage("更新默认限额管理错误：" + e.getMessage());
            logger.printLog(e);
            e.printStackTrace();
    	    response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
            return response;
    	}
		
		return response;
	}


    RcLimit createRcLimt(DefineCode defineCode,RcArchive rcArchive,String limitValue,String limitType,User user){
        RcLimit rcLimit = new RcLimit();
        rcLimit.setLimitId(sequenceService.nextValue("rc"));
        rcLimit.setDefineCode(defineCode.defineCode);
        rcLimit.setBusinnesType(defineCode.businessType);
        rcLimit.setBusinessTagerType(rcArchive.getArchiveType());
        rcLimit.setBusinessTagerId(rcArchive.getArchiveCode());
        rcLimit.setLimitValue(limitValue);
        rcLimit.setLimitType(limitType);
        rcLimit.setUserId(user.getUid());
        rcLimit.setUserName(user.getName());
        rcLimit.setCreateTime(new Date());
        rcLimit.setDefineId(defineCode.defineId);
        rcLimit.setUnit(defineCode.unit);
        return rcLimit;
    }

	/**
	 * 插入操作日志
	 * @param rcArchive
	 * @param userId
	 * @param modifyDate
	 * @param defineCode
	 * @param origValue
	 * @param newValue
	 */
	private void insertOperateLog(RcArchive rcArchive, long userId, Date modifyDate, DefineCode defineCode, String origValue, String newValue) {
	    if (origValue != null && origValue.equals(newValue)) { // 无论新增限额，还是修改限额，新旧值相同时不记录 20211028
	        return ;
	    }
		RcOperateLog log = new RcOperateLog();
		// 在商户限额管理菜单里
		log.setPermId("80401"); // 商户限额管理
        if (RcConstants.BusinessTagerType.PERSON.code.equals(rcArchive.getArchiveType())) {
            log.setPermId("80406"); // 个人限额管理
        } else if(RcConstants.BusinessTagerType.TERM.code.equals(rcArchive.getArchiveType())) {
            log.setPermId("80404"); // 终端限额管理
        } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(rcArchive.getArchiveType())) {
            log.setPermId("80409"); // 客户号限额管理
        } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(rcArchive.getArchiveType())) {
            log.setPermId("80410"); // 行业默认限额管理
        }
        if ("firstLimit".equals(rcArchive.getSource())) {
            log.setPermId("100103");
        }
		log.setCode(rcArchive.getArchiveCode());
		log.setName(rcArchive.getArchiveName());
		log.setType("2");
		log.setOperator(String.valueOf(userId));
		log.setOperateTime(modifyDate);
		log.setOrigValue(origValue);
		log.setNewValue(newValue);
        if (DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode.equals(defineCode.defineCode)) { // 特殊处理
            log.setOperateContent(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType()) ?
                    "商户当日累计转账限次" : "证件当日累计转账限次");
        } if (DefineCode.DAY_MAX_OUT_COUNT.defineCode.equals(defineCode.defineCode)) { // 特殊处理
            log.setOperateContent(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType()) ?
                    "商户当日累计提现限次" : "证件当日累计提现限次");
        } else {
            log.setOperateContent(RcConstants.BusinessTagerType.PLAT_CUSTOMER.code.equals(rcArchive.getArchiveType()) ? ("平台商" + defineCode.name) : defineCode.name);
        }

		rcOperateLogService.insert(log);
	}

	@GetMapping("/getLimitStatus")
    @Logable(businessTag = "AmountLimitController.getLimitStatus")
    @ApiOperation(value ="查询商户门户出金单笔/单日限额指标",notes = "查询商户门户出金单笔/单日限额指标",httpMethod = "GET")
    public CommonOuterResponse getLimitStatus(@RequestParam(value = "customerNo") String customerNo) {
	    CommonOuterResponse response = new CommonOuterResponse();
	    try {
            return CommonOuterResponse.success("success",limitSwitchService.getLimitStatus(customerNo));
        } catch (AppException e) {
	        logService.printLog(e);
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog("查询商户门户出金单笔/单日限额指标error:" + e.getMessage());
            logService.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
	    return response;
    }

    @PostMapping("/updateDaliyWithdrawal")
    @Transactional
//    @OpLog(opModule = "统一进件-商户管理-商户信息管理",opMethod="商户信息管理")
//    @ApiOperation(value = "修改单日提现/代付限额",notes = "修改单日提现/代付限额")
//    @Logable(businessTag = "AmountLimitController.updateDaliyWithdrawal")
    public CommonOuterResponse updateDaliyWithdrawal(String customerNo, Long userId,String source) {
        logService.printLog("进入修改单日提现/代付限额:" + source);
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            Thread.sleep(3000);
            // 获取商户限额信息
            AmountLimitQueryResponse amountLimitQueryResponse = amountLimitQuery(RcConstants.BusinessTagerType.CUSTOMER_CODE.code,customerNo);
            if (!"0000".equals(amountLimitQueryResponse.getReturnCode())) {
                logService.printLog("修改提现/代付限额-查询限额信息失败：" + amountLimitQueryResponse.getReturnMsg());
                return CommonOuterResponse.fail(amountLimitQueryResponse.getReturnCode(),amountLimitQueryResponse.getReturnMsg());
            }
            // 查询风控档案
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(amountLimitQueryResponse.getArchiveType(), customerNo);
            if (rcArchive == null) {
                return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
            }
            // 商户注销不允许修改
            if (RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
                return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
            }
            // 待审核调用审核通过 by wangshaobin
            if (RcConstants.AudStatus.WAITING.code.equals(rcArchive.getAudStatus())) {
                RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(rcArchive.getArchiveCode(), rcArchive.getArchiveType());
                if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
                    ((AmountLimitController)AopContext.currentProxy()).auditMethod(defaultAuditUserId,String.valueOf(limitAudRecord.getAudId()),"设置出金100限额前审核",true);
                }
                amountLimitQueryResponse = new AmountLimitQueryResponse();
                amountLimitQueryResponse = amountLimitQuery(RcConstants.BusinessTagerType.CUSTOMER_CODE.code,customerNo);
            }
            AmountLimitObject amountLimitObject = new AmountLimitObject();
            BeanUtils.copyProperties(amountLimitObject,amountLimitQueryResponse);
            amountLimitObject.setType(amountLimitQueryResponse.getArchiveType());
            amountLimitObject.setCustomerCode(amountLimitQueryResponse.getArchiveCode());
            amountLimitObject.setCustomerName(amountLimitQueryResponse.getArchiveName());
            // 修改单日提现/代付限额
            MainAmountLimit mainAmountLimit = amountLimitObject.getMainAmount();
            mainAmountLimit.setOutAmountDay(10000L);
            mainAmountLimit.setWithdrawAmountDay(10000L);
            amountLimitObject.setMainAmount(mainAmountLimit);
            logService.printLog("商户限额信息-修改后：" + JSON.toJSONString(amountLimitObject));
            // 根据userId查找userName
            User user = ortherService.selectUserById(userId);
            if(user == null){
                return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
            }
            // 更新风控限额值
            ((AmountLimitController)AopContext.currentProxy()).saveAmountLimit(rcArchive, amountLimitObject, user,"firstLimit");
            // 更新档案审核状态
            if (!RcConstants.AudStatus.SUCCESS.code.equals(rcArchive.getAudStatus())) {
                rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
                rcArchive.setUpdateTime(new Date());
                rcArchiveService.updateRcArchiveSelective(rcArchive);
            }
            // 记录日志
            String content = "用户:" + user.getName() + ",给档案编号:" + customerNo + "设置临时限额";
            String module = "统一进件-商户管理-商户信息管理";
            String method = "商户信息管理";
            String opTime = DateUtils.formatDate(new Date(), "yyyyMMdd HHmmss");
            opService.logInsert(userId, module, method, content, opTime);

        } catch (AppException e) {
            logger.printMessage("修改单日提现/代付限额信息error:" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("修改单日提现/代付限额信息error:" + e.getMessage() + "," + e.getLocalizedMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/cancelDaliyWithdrawal")
    @ApiOperation(value = "取消单日提现/代付100限额",notes = "取消单日提现/代付100限额")
//    @OpLog(opModule = "统一进件-商户管理-商户信息管理",opMethod="商户信息管理")
    @Logable(businessTag = "AmountLimitController.cancelDaliyWithdrawal")
    public CommonOuterResponse cancelDaliyWithdrawal(@RequestParam("customerNo") String customerNo,
                                                     @RequestParam("userId") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            Date modifyDay = new Date();
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerNo);
            if (rcArchive == null) {
                return CommonOuterResponse.fail(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
            }
            // 商户注销不允许修改
            if (RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
                return CommonOuterResponse.fail(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
            }
            // 待审核调用审核通过 by wangshaobin
            if (RcConstants.AudStatus.WAITING.code.equals(rcArchive.getAudStatus())) {
                RcLimitAud limitAudRecord = amountLimitAudService.queryLastRecord(rcArchive.getArchiveCode(), rcArchive.getArchiveType());
                if (limitAudRecord != null && RcConstants.AudStatus.WAITING.code.equals(limitAudRecord.getAudStatus())) {
                    ((AmountLimitController)AopContext.currentProxy()).auditMethod(defaultAuditUserId,String.valueOf(limitAudRecord.getAudId()),"设置出金限额前审核",true);
                }
            }
            // 根据userId查找userName
            User user = ortherService.selectUserById(userId);
            if(user == null){
                return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
            }
            rcArchive.setSource("firstLimit");
            // 查询商户默认限额
            Map<String,Long> defaultMap = rcLimitService.queryCustomerAmountLimit(rcArchive.getParentCode(), rcArchive.getParentCode(), rcArchive.getRcLevel());
            MainAmountLimit mainAmountLimit = transMainAmountLimit(defaultMap);
            String origValue = null;
            // 单日提现
            RcLimit dayOutLimit = rcLimitService.queryLimit(DefineCode.DAY_OUT.defineId,rcArchive.getArchiveCode());
            origValue = dayOutLimit.getLimitValue();
            dayOutLimit.setLimitValue(mainAmountLimit.getOutAmountDay().toString());
            rcLimitService.updateLimit(dayOutLimit);
            insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_OUT, origValue, mainAmountLimit.getOutAmountDay().toString());
            // 单日代付
            RcLimit dayWithdrawLimit = rcLimitService.queryLimit(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineId, rcArchive.getArchiveCode());
            origValue = dayWithdrawLimit.getLimitValue();
            dayWithdrawLimit.setLimitValue(mainAmountLimit.getWithdrawAmountDay().toString());
            rcLimitService.updateLimit(dayWithdrawLimit);
            insertOperateLog(rcArchive, userId, modifyDay, DefineCode.DAY_MAX_WITHDRAW_AMOUNT, origValue, mainAmountLimit.getWithdrawAmountDay().toString());

            // 记录日志
            String content = "用户:" + user.getName() + ",给档案编号:" + customerNo + "解除临时限额";
            String module = "统一进件-商户管理-商户信息管理";
            String method = "商户信息管理";
            String opTime = DateUtils.formatDate(new Date(), "yyyyMMdd HHmmss");
            opService.logInsert(userId, module, method, content, opTime);
        } catch (AppException e) {
            logger.printMessage("取消单日提现/代付限额信息error:" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("取消单日提现/代付限额信息error:" + e.getMessage() + "," + e.getLocalizedMessage());
            logger.printLog(e);
            response.setReturnCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    // 获取商户/客户号风险等级默认限额与行业默认限额中的较小值
    private String queryMinLimitValue(Long defineId,RcArchive rcArchive) {
        RcLimit defaultLimit = rcLimitService.queryLimit(defineId,rcArchive.getArchiveType(),rcArchive.getRcLevel());
        RcLimit industryLimit;

        Integer type = rcArchive.getType();
        String mcc = rcArchive.getMcc();
        String industry = rcArchive.getIndustry();
        if (type == null && RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {
            Customer customer = ortherService.queryCustomerByCustomerNo(rcArchive.getArchiveCode());
            if (customer != null && customer.getMcc() != null) {
                type = customer.getType();
                mcc = customer.getMcc();
            }
            if ((Constants.CustomerType.ENTERPRISE.code == type || Constants.CustomerType.ABROAD.code == type
                    || Constants.CustomerType.GOVERNMENT.code == type || Constants.CustomerType.OTHERS.code == type)) {
                industry = ortherService.queryIndustryByCode(customer.getCustomerNo());
            }
        }
        if (Constants.CustomerType.BUSINESSMAN.code == type || Constants.CustomerType.MICRO.code == type) {
            industryLimit = rcLimitService.queryLimit(defineId,RcConstants.BusinessTagerType.INDUSTRY.code,type + "_" + mcc);
        } else if (Constants.CustomerType.ENTERPRISE.code == type || Constants.CustomerType.ABROAD.code == type
                || Constants.CustomerType.GOVERNMENT.code == type || Constants.CustomerType.OTHERS.code == type){
            industryLimit = rcLimitService.queryLimit(defineId,RcConstants.BusinessTagerType.INDUSTRY.code,type + "_" + industry);
        } else {
            industryLimit = null;
        }
        if (industryLimit != null) {
            return Long.parseLong(defaultLimit.getLimitValue()) >= Long.parseLong(industryLimit.getLimitValue()) ? industryLimit.getLimitValue() : defaultLimit.getLimitValue();
        } else {
            return defaultLimit.getLimitValue();
        }
    }

    @GetMapping("/getRedis")
    public CommonOuterResponse getRedis(String key,String businessType) {
        Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(key, businessType);
        return CommonOuterResponse.success(map);
    }

    @PostMapping("/setRedis")
    public void setRedis(String customerNo,String key,String businessType) {
        Map<String, RcLimit> map = new HashMap<>();
        RcLimit rcLimit = rcLimitService.queryLimit(60L,customerNo);
        if (rcLimit != null) {
            key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            businessType = rcLimit.getBusinnesType();
        }
//        map.put(rcLimit.getDefineCode(), rcLimit);
        redisTemplate.opsForHash().put(key, businessType, map);
    }
}

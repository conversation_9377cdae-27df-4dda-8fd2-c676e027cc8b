package com.epaylinks.efps.rc.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.export.ExportFileService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.service.export.ExportBwListService;
import com.epaylinks.efps.rc.util.CheckUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.service.BwListService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.vo.AuditBwQueryResponse;
import com.epaylinks.efps.rc.vo.BwListVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2018.09.27
 */
@RestController
@RequestMapping("/bwList")
@Api(value = "BwListController", description = "黑白名单控制类")
public class BwListController {

    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID 3009001 huangyuekun 黄月坤
    
	@Autowired
	private BwListService bwListService;

	@Autowired
	private OtherService otherService;

	@Autowired
	private LogService logService;

	@Autowired
	private CommonLogger logger;

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private ExportFileService exportFileService;

	@Autowired
	private ExportBwListService exportBwListService;

	private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(10); // 创建一个线程池

	@Transactional
	@PostMapping("/save")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "BwListController.save")
	@ApiOperation(value ="添加黑白名单")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "businessType",value = "适用业务", dataType = "String",required = false,paramType = "query"),
					@ApiImplicitParam(name = "businessTagerType", value = "业务对象类型：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号;014 商户名称", required = true,valueRange = "{001,002,003,004,006,007,005,014}", dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTagerId", value = "业务对象ID", required = true,dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "bwType", value = "黑白名单 0黑名单 1白名单 2灰名单", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "startTime", value = "开始时间yyyyMMdd HHmmss", required = false, dataType = "Date", paramType = "query"),
					@ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMdd HHmmss", required = false, dataType = "Date", paramType = "query"),
					@ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "riskTag", value = "风险标签，多个标签使用','隔开【涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation】", required = false, dataType = "String", paramType = "query")
			})
	public CommonResponse save(BwList bwList, @RequestHeader(value = "x-userid")Long userId){

		CommonResponse response = new CommonResponse();
		
		//根据userId查询user
		User user = otherService.selectUserById(userId);
		if(user == null){
			response.setCode(RcCode.USER_NOT_EXIXT.code);
			response.setMessage(RcCode.USER_NOT_EXIXT.message);
			return response;
		}
		if ("2".equals(bwList.getBwType())) {
			response.setCode(RcCode.ADD_BWLIST_EXCEPTION.code);
			response.setMessage("参数错误");
			return response;
		}
		try {
			CheckUtils.paramCheckLength(bwList.getRiskTag(),300,"风险标签");
			CheckUtils.paramCheckLength(bwList.getRemark(),200,"备注");

    		//先查询该黑白名单是否已存在
    		boolean exis = bwListService.queryExistence(bwList);
    		String type = bwList.getBwType();
    		bwList.setBwType(null);
    		if(exis){
    			response.setCode(RcCode.ADD_BWLIST_EXIS.code);
    			response.setMessage(RcCode.ADD_BWLIST_EXIS.message);
    			return response;
    		}
    		bwList.setBwType(type);
    		bwList.setUserId(userId);
    		bwList.setUserName(user.getName());
    		bwList.setCreateTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));

    		// 判断是否需要审核，不需审核走原逻辑  2020.11.10
            String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
            if ("1".equals(audFlag)) {
				bwList.setBwId(sequenceService.nextValue("rc"));
                bwListService.saveCreateBwAuditRecord(bwList, userId);
                response.setMessage("已提交审核");
            } else {
//                bwListService.saveBwList(bwList);
                // 免审核也需要添加审核记录，单系统自动审核  20220310
				bwList.setBwId(sequenceService.nextValue("rc"));
                bwListService.saveCreateBwAuditRecord(bwList, userId);
                // 自动审核
				fixedThreadPool.execute(new Runnable() {
					@Override
					public void run() {
						try {
							Thread.sleep(20);
							bwListService.auditBwList(bwList.getBwId(), new Short("1"), defaultAuditUserId,null);
						} catch (AppException e) {
							logService.printLog(e.getErrorCode() + "名单管理自动审核失败：" + e.getErrorMsg());
						} catch (Exception e) {
							logService.printLog("名单管理自动审核失败：" + e.getMessage());
						}
					}
				});
                
                response.setMessage("添加成功");
            }
			response.setCode(CommonResponse.SUCCEE);

		} catch (AppException e) {
			response.setCode(RcCode.ADD_BWLIST_EXCEPTION.code);
			response.setMessage(e.getErrorMsg());
			response.setErrorMsg(e.getErrorMsg());
		} catch (Exception e){
			logger.printMessage("更新名单错误：" + e.getMessage());
			logger.printLog(e);
			response.setCode(RcCode.ADD_BWLIST_EXCEPTION.code);
			response.setMessage(RcCode.ADD_BWLIST_EXCEPTION.message);
			response.setErrorMsg(RcCode.ADD_BWLIST_EXCEPTION.message);
		}
		return response;
	}

	@PostMapping("/queryValueInBw")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "BwListController.queryValueInBw")
	@ApiOperation(value ="根据业务类型和ID查找黑白名单")
	@ApiImplicitParams(
	{
			@ApiImplicitParam(name = "ids", value = "业务对象ID列表", required = true,dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "bwType", value = "黑白名单 0黑名单 1白名单", required = true, dataType = "String", paramType = "query"),
	})
	public Map<String, Boolean> queryByBwByBtAndId(
			String bwType,String ids
	){
		List<String> idList = JSON.parseObject(ids,List.class);

		Map<String,Boolean> map = new HashedMap();
			map = bwListService.queryValueInBw(bwType,idList);
		return map;
	}

	@PostMapping("/delete")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "BwListController.delete")
	@ApiOperation(value ="删除黑白名单")
	@ApiImplicitParams(
	{
		@ApiImplicitParam(name = "bwId",value = "黑白名单ID", dataType = "Long",required = true,paramType = "query"),
	})
	public CommonResponse delete(
	        @RequestParam(value =  "bwId") Long bwId,
	        @RequestHeader(value =  "x-userid")Long userId){
		CommonResponse response = new CommonResponse();

		try {
	        // 判断是否需要审核，不需审核走原逻辑  2020.11.10
            String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
            if ("1".equals(audFlag)) {
                bwListService.saveDelBwAuditRecord(bwId, userId);
                response.setMessage("已提交审核");
            } else {
//                bwListService.deleteByPrimaryKey(bwId);
                // 免审核也需要添加审核记录，单系统自动审核  20220310
                bwListService.saveDelBwAuditRecord(bwId, userId);
                // 自动审核
                bwListService.auditBwList(bwId, new Short("1"), defaultAuditUserId,null);
                
                response.setMessage("删除成功");
            }
			response.setCode(CommonResponse.SUCCEE);
		} catch (AppException e) {
		    response.setCode(e.getErrorCode());
		    response.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            response.setCode(RcCode.SYSTEM_EXCEPTION.code);
            response.setErrorMsg(RcCode.SYSTEM_EXCEPTION.message);
        }
		return response;
	}


	@PostMapping("/update")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "BwListController.update")
	@ApiOperation(value ="更新黑白名单")
	@ApiImplicitParams(
	{
			@ApiImplicitParam(name = "bwId",value = "黑白名单ID",dataType = "Long",required = true,paramType = "query"),
			@ApiImplicitParam(name = "businessType",value = "适用业务", dataType = "String",required = false,paramType = "query"),
			@ApiImplicitParam(name = "businessTagerType", value = "业务对象类型：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号", required = true, valueRange = "{001,002,003,004,006,007,005,014}", dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "businessTagerId", value = "业务对象ID", required = true,dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "bwType", value = "黑白名单 0黑名单 1白名单", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "startTime", value = "开始时间yyyyMMdd HHmmss", required = false, dataType = "Date", paramType = "query"),
			@ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMdd HHmmss", required = false, dataType = "Date", paramType = "query"),
			@ApiImplicitParam(name = "riskTag", value = "风险标签，多个标签使用','隔开【涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation】", required = false, dataType = "String", paramType = "query")
	})
	public CommonResponse update(
	        BwList bwList,
	        @RequestHeader(value = "x-userid") Long userId){
		CommonResponse response = new CommonResponse();
		User user = otherService.selectUserById(userId);
		if(user == null){
			response.setCode(RcCode.USER_NOT_EXIXT.code);
			response.setMessage(RcCode.USER_NOT_EXIXT.message);
			return response;
		}
		if ("2".equals(bwList.getBwType())) {
			throw new AppException(RcCode.ADD_BWLIST_EXCEPTION.code,RcCode.ADD_BWLIST_EXCEPTION.message + "：类型不支持设置灰名单");
		}
		bwList.setUserId(userId);
		bwList.setUserName(user.getName());
		//先查询该黑白名单是否已存在
		boolean exis = bwListService.queryExistence(bwList);
		if(exis){
			response.setCode(RcCode.ADD_BWLIST_EXIS.code);
			response.setMessage(RcCode.ADD_BWLIST_EXIS.message);
			return response;
		}

		try {
			CheckUtils.paramCheckLength(bwList.getRiskTag(),300,"风险标签");
			CheckUtils.paramCheckLength(bwList.getRemark(),200,"备注");
		 // 判断是否需要审核，不需审核走原逻辑  2020.11.10
            String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
            if ("1".equals(audFlag)) {
                bwListService.saveUpdateBwAuditRecord(bwList, userId);
                response.setMessage("已提交审核");
            } else {
//                bwListService.updateByPrimaryKeySelective(bwList);
                // 免审核也需要添加审核记录，单系统自动审核  20220310
                bwListService.saveUpdateBwAuditRecord(bwList, userId);
                // 自动审核
                bwListService.auditBwList(bwList.getBwId(), new Short("1"), defaultAuditUserId,null);
                response.setMessage("更新成功");
            }
            response.setCode(CommonResponse.SUCCEE);
            return response;
		} catch (AppException e) {
			response.setCode(RcCode.ADD_BWLIST_EXCEPTION.code);
			response.setMessage(e.getErrorMsg());
			response.setErrorMsg(e.getErrorMsg());
		} catch (Exception e){
			logger.printMessage("更新名单错误：" + e.getMessage());
            logger.printLog(e);
			response.setCode(RcCode.UPDATE_BWLIST_EXCECTION.code);
			response.setMessage(RcCode.UPDATE_BWLIST_EXCECTION.message);
			return response;
		}
		return response;
	}


	@GetMapping("/pageQuery")
	@Validatable
	@Exceptionable
//	@DownloadAble
	@Logable(businessTag = "BwListController.pageQuery")
	@ApiOperation(value ="分页查询")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "pageNum",value = "当前页面",dataType = "int",required = true,paramType = "query"),
					@ApiImplicitParam(name = "pageSize",value = "页面大小", dataType = "int",required = true,paramType = "query"),
					@ApiImplicitParam(name = "startTime", value = "开始时间yyyy-MM-dd HH:mm:ss", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "endTime", value = "结束时间yyyy-MM-dd HH:mm:ss", required = false,dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "bwType", value = "黑白名单 0黑名单 1白名单", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTagerType", value = "业务对象类型：001身份证；002手机号；003社会统一信用代码；004银行卡；006身份证前4位；007经营地址（省-市）；005 商户编号", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTagerId", value = "业务对象Id", required = false, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "status", value = "状态：0待审核，1正常", required = false, dataType = "int", paramType = "query"),
					@ApiImplicitParam(name = "riskTag", value = "风险标签【全部：不传值，涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation，无：none】", required = false, dataType = "String", paramType = "query")
			})
	public PageResult<BwListVo> pageQuery(
			Integer pageNum,
			Integer pageSize,
			String startTime,
			String endTime,
			String bwType,
			String businessTagerType,
			String businessTagerId,
			Short status,
			String riskTag,
			@RequestParam(defaultValue = "false",required = false) boolean download,
			@RequestParam(value = "fileName",required = false) String fileName,
			@RequestParam(value = "type",required = false) String type,
			@RequestParam(value = "fileSource",required = false) String fileSource,
			@RequestHeader("x-userId") Long userId,
			HttpServletRequest httpServletRequest
	){
		//封装数据
		Map<String, Object> map = new HashMap<>();

		map.put("startTime",startTime);
		map.put("endTime",endTime);
		map.put("bwType",bwType);
		map.put("businessTagerType",businessTagerType);
		map.put("businessTagerId",businessTagerId);
		map.put("status", status);
		map.put("riskTag",riskTag);
		PageResult<BwListVo> result = null;
		try {
			if (download) {
				return exportFileService.download(map,fileName,httpServletRequest,exportBwListService);
			}
			 result = bwListService.pageQuery(pageNum,pageSize,map,download);

			if(result != null){
				result.setCode(CommonResponse.SUCCEE);
				result.setMessage("查询成功");
			}
		}catch (Exception e){
			logger.printMessage("名单管理查询异常：" + e.getLocalizedMessage());
			logger.printLog(e);

			result.setCode(RcCode.QUERY_BWLIST_EXCECTION.code);
			result.setMessage(RcCode.QUERY_BWLIST_EXCECTION.message);
		}
		return result;
	}

	@GetMapping("/isBlackList")
	@Validatable
	@Exceptionable
	@Logable(businessTag = "BwListController.isBlackList")
	@ApiOperation(value = "是否黑名单")
	@ApiImplicitParams(
			{
					@ApiImplicitParam(name = "businessTagerType", value = "业务对象类型：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号", required = true, dataType = "String", paramType = "query"),
					@ApiImplicitParam(name = "businessTagerId", value = "业务对象Id", required = true, dataType = "String", paramType = "query"),
			})
	public CommonResponse isBlackList(String businessTagerType, String businessTagerId) {
		CommonResponse response = new CommonResponse();
		try {
			response.setResult(bwListService.isBlackList(businessTagerType, businessTagerId) ? "1" : "0");
			response.setCode(CommonResponse.SUCCEE);
			response.setMessage("查询成功(result值为1，即是黑名单)");
		} catch (AppException e) {
			response.setCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		} catch (Exception e) {
            logService.printLog(e);
			response.setCode(RcCode.IS_BLACKLIST_EXCEPTION.code);
			response.setErrorMsg(RcCode.IS_BLACKLIST_EXCEPTION.message);
		}
		return response;
	}
	

    @GetMapping("/blackOrWhite")
    @Logable(businessTag = "BwListController.blackOrWhite")
    @ApiOperation(value = "判断黑名单或白名单", notes = "判断黑名单或白名单：黑名单<0，白名单>0，其他=0", httpMethod = "GET")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "businessTagerType", value = "业务对象类型：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "businessTagerId", value = "业务对象Id", required = true, dataType = "String", paramType = "query"),
    })
    public Integer blackOrWhite(String businessTagerType, String businessTagerId) {

        try {
            return bwListService.blackOrWhite(businessTagerType, businessTagerId);
        }catch (Exception e) {
            logService.printLog(e);
        }
        return 0;
    }
    
    
    @GetMapping("/queryAuditBwList")
    @Logable(businessTag = "BwListController.queryAuditBwList")
    @ApiOperation(value = "查询黑白名单审核信息", notes = "查询黑名单审核信息", httpMethod = "GET")
    @ApiImplicitParams(
    {
        @ApiImplicitParam(name = "bwId", value = "黑白名单ID", required = true, dataType = "Long", paramType = "query"),
    })
    public CommonOuterResponse<AuditBwQueryResponse> queryAuditBwList(
            @RequestParam(value = "bwId") Long bwId,
            @RequestHeader(value = "x-userid") Long userId ){

        try {
            return CommonOuterResponse.success(bwListService.queryAuditBwList(bwId));
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }
    
    @PostMapping("/auditBwList")
    @Logable(businessTag = "BwListController.auditBwList")
    @ApiOperation(value = "审核黑白名单", notes = "审核黑白名单", httpMethod = "POST")
    @ApiImplicitParams(
    {
		@ApiImplicitParam(name = "bwIds", value = "黑白名单ID列表，用','隔开", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "auditResult", value = "审核状态：1审核通过，2驳回", valueRange = "{1,2}", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "comments", value = "审核意见", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse auditBwList(
//            @RequestParam(value = "bwId") Long bwId,
			@RequestParam(value = "bwIds") String bwIds,
            @RequestParam(value = "auditResult") Integer auditResult,
			@RequestParam(value = "comments",required = false) String comments,
            @RequestHeader(value = "x-userid") Long userId ){

        try {

        	bwListService.batchAuditBwList(bwIds,Short.valueOf(auditResult + ""),userId,comments);
//            bwListService.auditBwList(bwId, auditResult, userId);
            return CommonOuterResponse.success();
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
			logger.printMessage("审核黑白名单错误：" + e.getMessage());
            logger.printLog(e);
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
    }

	/**
	 * 存量名单添加哈希
	 * @return
	 */
	@Logable(businessTag = "BwListController.hashOldInfo")
	@RequestMapping(value = "/hashOldInfo" , method = RequestMethod.POST)
	@ApiOperation(value = "存量名单添加哈希", notes = "存量名单添加哈希", httpMethod = "POST")
	public CommonOuterResponse hashOldInfo () {

		try {
			bwListService.hashOldInfo();
			return CommonOuterResponse.success();
		}catch(Exception e) {
			e.printStackTrace();
			if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
				return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
			}
		}
	}

	@PostMapping("/test")
	public List<String> test(String type,String value) {
		return bwListService.getBwRiskTags(type,value);
	}

	@PostMapping("/testBlackList")
	public Map<String, String> testBlackList(@RequestBody Map<String, String> typeValue) {
		return bwListService.getInnerBlackList(typeValue);
	}

}

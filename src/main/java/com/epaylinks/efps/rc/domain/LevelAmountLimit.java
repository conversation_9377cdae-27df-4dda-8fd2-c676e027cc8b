package com.epaylinks.efps.rc.domain;


import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 默认限额
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class LevelAmountLimit extends CommonOuterResponse {

	private MainAmountLimit defaultMainAmount;
	
	private WithdrawAmountLimit defaultWithdrawAmount;
	
	private CardAmountLimit defaultCardAmount;

	private UserAmountLimit defaultUserAmount;
	
	private MerchantAmountLimit defaultMerchantAmount;
	
	private CertificateAmountLimit defaultCertificateAmount;
	
	private TermAmountLimit defaultTermAmount;
	
	private PersonAmountLimit defaultPersonAmount;

	private PlatCustomerAmountLimit defaultPlatCustomerAmountLimit;

	private String rcLevel;

	private OutAmountLimit defaultOutAmount;

	@ApiModelProperty( value = "005:商户维度；012：客户号维度", dataType = "String")
	private String targetType; // 005:商户维度；012：客户号维度

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}

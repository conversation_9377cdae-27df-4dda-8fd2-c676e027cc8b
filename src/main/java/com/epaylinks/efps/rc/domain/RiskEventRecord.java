package com.epaylinks.efps.rc.domain;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 风控管理-可疑事件监控记录
 */
public class RiskEventRecord {
    private static SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private Long eventId;	//主键ID
    private String ruleCode;	//规则代码
    private Date triggerTime;	//触发时间
    private String triggerTimeShow;	//触发时间
    private String ruleType;	//规则分类
    private String ruleDesc;	//规则描述
    private String triggerCustCode;	//触发商户编号
    private String triggerCustName;	//触发商户名称
    private String triggerValue;	//触发值
    private String triggerAction;	//措施
    private String paramType;	//参数类型，01单参数，02双参数
    private String dealResult; // 处理结果

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public Date getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    public String getTriggerTimeShow() {
        if(triggerTime!=null){
            return formatDate.format(triggerTime);
        }
        return triggerTimeShow;
    }

    public void setTriggerTimeShow(String triggerTimeShow) {
        this.triggerTimeShow = triggerTimeShow;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getTriggerCustCode() {
        return triggerCustCode;
    }

    public void setTriggerCustCode(String triggerCustCode) {
        this.triggerCustCode = triggerCustCode;
    }

    public String getTriggerCustName() {
        return triggerCustName;
    }

    public void setTriggerCustName(String triggerCustName) {
        this.triggerCustName = triggerCustName;
    }

    public String getTriggerValue() {
        return triggerValue;
    }

    public void setTriggerValue(String triggerValue) {
        this.triggerValue = triggerValue;
    }

    public String getTriggerAction() {
        return triggerAction;
    }

    public void setTriggerAction(String triggerAction) {
        this.triggerAction = triggerAction;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getDealResult() {
        return dealResult;
    }

    public void setDealResult(String dealResult) {
        this.dealResult = dealResult;
    }



}

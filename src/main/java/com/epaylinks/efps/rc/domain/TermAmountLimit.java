package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 终端维度限额
 * <AUTHOR>
 * @since 2018-12-10
 *
 */
@Data
@ApiModel(value = "终端维度限额")
public class TermAmountLimit {

    @ApiModelProperty( value = "单笔交易最高限额（分）", dataType = "Long")
    private Long amountSingle;  // 单笔交易最高限额

    @ApiModelProperty( value = "当日交易最高限额（分）", dataType = "Long")
    private Long amountDay; // 当日交易最高限额

    @ApiModelProperty( value = "当月交易最高限额（分）", dataType = "Long")
    private Long amountMonth; // 当月交易最高限额

    @ApiModelProperty( value = "当年交易最高限额（分）", dataType = "Long")
    private Long amountYear; // 当年交易最高限额

    @ApiModelProperty( value = "当日交易最高笔数", dataType = "Long")
    private Long countDay; // 当日交易最高笔数

    @ApiModelProperty( value = "当月交易最高笔数", dataType = "Long")
    private Long countMonth; // 当月交易最高笔数
    
    @ApiModelProperty( value = "当年交易最高笔数", dataType = "Long")
    private Long countYear; // 当年交易最高笔数
    
    
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

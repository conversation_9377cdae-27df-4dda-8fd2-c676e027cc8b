package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.rc.command.DefineCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 个人维度限额
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@Data
@ApiModel(value = "平台商维度限额")
public class PlatCustomerAmountLimit {

    @ApiModelProperty(value = "入金单日最高限额（分）", dataType = "Long")
    private Long platInAmountDay;

    @ApiModelProperty(value = "入金单月最高限额（分）", dataType = "Long")
    private Long platInAmountMonth;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static PlatCustomerAmountLimit fromMap(Map<String, Long> personDefaultMap) {
        PlatCustomerAmountLimit rt = new PlatCustomerAmountLimit();
        rt.setPlatInAmountDay(personDefaultMap.get(DefineCode.DAY_IN.defineCode));
        rt.setPlatInAmountMonth(personDefaultMap.get(DefineCode.MONTH_IN.defineCode));
        return rt;
    }
}

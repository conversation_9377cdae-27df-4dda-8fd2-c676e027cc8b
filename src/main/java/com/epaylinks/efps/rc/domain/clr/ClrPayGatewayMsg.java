package com.epaylinks.efps.rc.domain.clr;

import com.alibaba.fastjson.JSON;

/**
 * 
 * CLR_PayGatewayResult消息 
 * <AUTHOR>
 * @since 2018-12-11
 *
 */
public class ClrPayGatewayMsg {
    /**
     * 外部订单号
     */
    private String outTradeNo;
    /**
     * 易票联订单号
     */
    private String transactionNo;
    /**
     * 业务类型
     */
//    private String businessType;
    /**
     * 支付状态
     */
    private String payState;
    /**
     * 商户编码
     */
    private String customerCode;
    /**
     * 银行卡号
     */
    private String bankCardNo;
    /**
     * 代付数据对象json串, ClrWithdrawRecord
     */
    private String withdrawRecordJson;
    /**
     * 入金支付数据对象json串，ClrPayRecord
     */
    private String clrPayRecordJson;

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getWithdrawRecordJson() {
        return withdrawRecordJson;
    }

    public void setWithdrawRecordJson(String withdrawRecordJson) {
        this.withdrawRecordJson = withdrawRecordJson;
    }

    public String getClrPayRecordJson() {
        return clrPayRecordJson;
    }

    public void setClrPayRecordJson(String clrPayRecordJson) {
        this.clrPayRecordJson = clrPayRecordJson;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}

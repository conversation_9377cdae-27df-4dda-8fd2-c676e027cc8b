package com.epaylinks.efps.rc.domain;


import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel
@Data
@EqualsAndHashCode(callSuper=false)
public class RcLimitAud extends CommonOuterResponse {
	/**
	 * 主键
	 */
	private Long audId;

	/**
	 * 商户号
	 */
	private String audCode;

	/**
	 * 商户名称
	 */
	private String audName;

	/**
	 * 申请用户ID
	 */
	private Long userId;

	/**
	 * 申请用户名称
	 */
	private String userName;

	@ApiModelProperty(value="创建时间", dataType = "Date")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	 * 审核状态（00：待初审，01：待复审，02：已拒绝，03：审核成功，04：已撤销）
	 */
	private String audStatus;

	/**
	 * 附件地址
	 */
	private String url;

	/**
	 * 初审意见
	 */
	private String firstOpinion;

    @ApiModelProperty(value="初审时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date firstTime;

	/**
	 * 初审用户ID
	 */
	private Long firstUser;

	/**
	 * 初审用户名称
	 */
	private String firstName;

	/**
	 * 复审意见
	 */
	private String lastOpinion;

    @ApiModelProperty(value="复审时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date lastTime;

	/**
	 * 复审用户ID
	 */
	private Long lastUser;

	/**
	 * 复审用户名字
	 */
	private String lastName;

	/**
	 * 撤销人员Id
	 */
	private Long revokeUser;

	/**
	 * 撤销人员名称
	 */
	private String revokeName;

    @ApiModelProperty(value="撤销时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date revokeTime;

	private String rcLevel;
	
	/**
	 * 档案类型：001:身份证; 003：统一社会信用代码；005：商户编号
	 */
    private String targetType;

	private AmountLimit defaultLimits;

	private AmountLimit newLimits;

	private AmountLimit oldLimits;


	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
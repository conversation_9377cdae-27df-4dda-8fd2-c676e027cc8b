package com.epaylinks.efps.rc.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date  2022/10/11 14:33
 * @version 1.0
 */
/**
    * 风控可疑事件关闭业务
    */
public class RcCloseBusiness {
    private BigDecimal id;

    private String customerCode;

    private String businessCode;

    private Date closeTime;

    private Date reopenTime;

    private Date createTime;

    private Date updateTime;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    public Date getReopenTime() {
        return reopenTime;
    }

    public void setReopenTime(Date reopenTime) {
        this.reopenTime = reopenTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
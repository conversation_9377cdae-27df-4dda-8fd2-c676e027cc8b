package com.epaylinks.efps.rc.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 17:33
 */
public class RcDroolsWhen {
    private Long id;

    private String leftText;

    private String leftDrl;

    private String operatorText;

    private String operatorDrl;

    private String rightText;

    private String rightDrl;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLeftText() {
        return leftText;
    }

    public void setLeftText(String leftText) {
        this.leftText = leftText;
    }

    public String getLeftDrl() {
        return leftDrl;
    }

    public void setLeftDrl(String leftDrl) {
        this.leftDrl = leftDrl;
    }

    public String getOperatorText() {
        return operatorText;
    }

    public void setOperatorText(String operatorText) {
        this.operatorText = operatorText;
    }

    public String getOperatorDrl() {
        return operatorDrl;
    }

    public void setOperatorDrl(String operatorDrl) {
        this.operatorDrl = operatorDrl;
    }

    public String getRightText() {
        return rightText;
    }

    public void setRightText(String rightText) {
        this.rightText = rightText;
    }

    public String getRightDrl() {
        return rightDrl;
    }

    public void setRightDrl(String rightDrl) {
        this.rightDrl = rightDrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OperatorText {
        private Set<String> textList = new HashSet<>();
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OperatorDrl {
        private Map<String, String> textDrlMap = new HashMap<>();
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RightText {
        public enum Type {
            MULTI_CHOICE,
            SINGLE_CHOICE,
            MULTI_TEXT,
            SINGLE_TEXT,
            TIME_PERIOD
        }

        public enum Format {
            INTEGER,
            STRING,
        }

        private Type type;
        private Format format;
        private List<String> textList = new ArrayList<>();
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RightDrl {
        /**
         * 前缀，拼接drl的前缀
         */
        private String prefix;

        /**
         * 文本对应的drl代码
         */
        private Map<String, String> textDrlMap = new HashMap<>();

        /**
         * 前缀，拼接drl的后缀
         */
        private String suffix;

        /**
         * 拼接多选drl时的分隔符
         */
        private String separator;

        private String sqlParamName;
    }

}
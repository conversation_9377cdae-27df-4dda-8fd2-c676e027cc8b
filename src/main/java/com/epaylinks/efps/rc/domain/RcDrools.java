package com.epaylinks.efps.rc.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/17 15:16
 */
public class RcDrools {
    private Long id;

    private byte[] drlContent;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public byte[] getDrlContent() {
        return drlContent;
    }

    public void setDrlContent(byte[] drlContent) {
        this.drlContent = drlContent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
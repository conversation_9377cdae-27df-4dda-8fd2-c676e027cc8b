package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交易卡限额
 * <AUTHOR>
 * @since 2018-12-10
 *
 */
@Data
@ApiModel
public class CardAmountLimit {

    @ApiModelProperty( value = "同卡代付单日最高限额（分）", dataType = "Long")
	private Long cardOutAmountDay;
	
    @ApiModelProperty( value = "同卡代付单日最高笔数", dataType = "Long")
	private Long cardOutCountDay;
    
    @ApiModelProperty( value = "同卡代付单月最高限额（分）", dataType = "Long")
	private Long cardOutAmountMonth;

    @ApiModelProperty( value = "同卡代付单日最高笔数", dataType = "Long")
	private Long cardOutCountMonth;

    @ApiModelProperty( value = "同卡入金单日最高限额（分）", dataType = "Long")
	private Long cardInAmountDay; 

    @ApiModelProperty( value = "同卡入金单日最高笔数", dataType = "Long")
	private Long cardInCountDay;

    @ApiModelProperty( value = "同卡入金单月最高限额（分）", dataType = "Long")
	private Long cardInAmountMonth; 

    @ApiModelProperty( value = "同卡入金单日最高笔数", dataType = "Long")
	private Long cardInCountMonth;

    // 新增统计成功失败总数指标 20210729
    @ApiModelProperty( value = "同卡入金单日最高总笔数（成功+失败）", dataType = "Long")
    private Long cardInTotalCountDay;
    
    @ApiModelProperty( value = "同卡入金单月最高总笔数（成功+失败）", dataType = "Long")
    private Long cardInTotalCountMonth;

    @ApiModelProperty( value = "信用卡单笔入金最高限额", dataType = "Long")
    private Long creditCardInAmount;

    @ApiModelProperty( value = "信用卡单日入金最高限额", dataType = "Long")
    private Long creditCardInDayAmount;

    @ApiModelProperty( value = "信用卡单月入金最高限额", dataType = "Long")
    private Long creditCardInMonAmount;

    @ApiModelProperty( value = "储蓄卡单笔入金最高限额", dataType = "Long")
    private Long debitCardInAmount;

    @ApiModelProperty( value = "储蓄卡单日入金最高限额", dataType = "Long")
    private Long debitCardInDayAmount;

    @ApiModelProperty( value = "储蓄卡单月入金最高限额", dataType = "Long")
    private Long debitCardInMonAmount;

    
    @Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}

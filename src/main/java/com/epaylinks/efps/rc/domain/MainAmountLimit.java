package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 主账户限额
 * <AUTHOR>
 * @since 2018-12-10
 *
 */
@Data
@ApiModel
public class MainAmountLimit {

    @ApiModelProperty( value = "入金单笔最高限额（分）", dataType = "Long")
	private Long inAmountSingle;

    @ApiModelProperty( value = "入金单日最高限额（分）", dataType = "Long")
	private Long inAmountDay;

    @ApiModelProperty( value = "入金单月最高限额（分）", dataType = "Long")
	private Long inAmountMonth;

    @ApiModelProperty( value = "入金单年最高限额（分）", dataType = "Long")
	private Long inAmountYear;

    // 原出金指标与代付分离，保留做提现  20210730
    @ApiModelProperty( value = "提现单笔最高限额（分）", dataType = "Long")
	private Long outAmountSingle;

    @ApiModelProperty( value = "提现单日最高限额（分）", dataType = "Long")
	private Long outAmountDay;

    @ApiModelProperty( value = "提现单月最高限额（分）", dataType = "Long")
	private Long outAmountMonth;

    @ApiModelProperty( value = "提现单年最高限额（分）", dataType = "Long")
	private Long outAmountYear;

    // 新增代付指标 20210730
    @ApiModelProperty( value = "代付单笔最高限额（分）", dataType = "Long")
    private Long withdrawAmountSingle;

    @ApiModelProperty( value = "代付单日最高限额（分）", dataType = "Long")
    private Long withdrawAmountDay;

    @ApiModelProperty( value = "代付单月最高限额（分）", dataType = "Long")
    private Long withdrawAmountMonth;

    @ApiModelProperty( value = "代付单年最高限额（分）", dataType = "Long")
    private Long withdrawAmountYear;
    
    
    @Override
	public String toString() {
        return JSON.toJSONString(this);
	}
}

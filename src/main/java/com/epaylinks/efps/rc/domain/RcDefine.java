package com.epaylinks.efps.rc.domain;


public class RcDefine{
    /**
     * ID
     */
    private Long defineId;

    /**
     * 风控指标定义分组ID
     */
    private Long groupId;

    /**
     * 风控指标定义编码
     */
    private String code;

    /**
     * 说明
     */
    private String remark;

    /**
     * 风控规则预留字段
     */
    private String rule;

    /**
     * 0:事前，1:事中，2:事后
     */
    private String type;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 默认风控值
     */
    private String effectValue;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * 适用业务
     */
    private String businessType;

    /**
     * 风控指标定义中文名称
     */
    private String alias;

    /**
     * 指标编号
     */
    private String defineIdentifier;

    public Long getDefineId() {
        return defineId;
    }

    public void setDefineId(Long defineId) {
        this.defineId = defineId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEffectValue() {
        return effectValue;
    }

    public void setEffectValue(String effectValue) {
        this.effectValue = effectValue;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getDefineIdentifier() {
        return defineIdentifier;
    }

    public void setDefineIdentifier(String defineIdentifier) {
        this.defineIdentifier = defineIdentifier;
    }

    @Override
    public String toString() {
        return "RcDefine{" +
                "defineId=" + defineId +
                ", groupId=" + groupId +
                ", code='" + code + '\'' +
                ", remark='" + remark + '\'' +
                ", rule='" + rule + '\'' +
                ", type='" + type + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", createTime='" + createTime + '\'' +
                ", effectValue='" + effectValue + '\'' +
                ", serviceId='" + serviceId + '\'' +
                ", businessType='" + businessType + '\'' +
                ", alias='" + alias + '\'' +
                ", defineIdentifier='" + defineIdentifier + '\'' +
                '}';
    }
}
package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商户维度交易限额
 * <AUTHOR>
 * @date 2020-06-22
 *
 */
@Data
public class MerchantAmountLimit {

    @ApiModelProperty( value = "日转账次数", dataType = "Long")
    private Long transCountLimitDay; // 日转账次数
    
    @ApiModelProperty( value = "日提现次数", dataType = "Long")
    private Long dayMaxOutCount; // 日提现次数

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

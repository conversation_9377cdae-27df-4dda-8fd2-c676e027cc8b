package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class ComplainTrade {
    /**
     * 主键ID
     */
    private Long ctId;

    /**
     * 上游机构：微信、支付宝
     */
    @ApiModelProperty(value="上游机构：微信、支付宝", dataType = "String")
    @FieldAnnotation(fieldName="上游机构")
    private String channelType;

    private String inletType;       //渠道类型

    /**
     * 风险识别时间
     */
    @ApiModelProperty(value = "风险识别时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="风险识别时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date riskTime;

    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    /**
     * 订单号
     */
    @ApiModelProperty(value="订单号", dataType = "String")
    @FieldAnnotation(fieldName="订单号")
    private String orderNo;

    /**
     * 金额
     */
    @ApiModelProperty(value="金额", dataType = "String")
    @FieldAnnotation(fieldName="金额")
    private String amount;

    /**
     * 风险描述
     */
    @ApiModelProperty(value="风险描述", dataType = "String")
    @FieldAnnotation(fieldName="风险描述")
    private String riskDesc;

    /**
     * 投诉内容
     */
    @ApiModelProperty(value="投诉内容", dataType = "String")
    @FieldAnnotation(fieldName="投诉内容")
    private String complainContent;

    /**
     * 用户手机
     */
    @ApiModelProperty(value="用户手机", dataType = "String")
    @FieldAnnotation(fieldName="用户手机")
    private String userMobile;

    /**
     * 上游商户号
     */
    @ApiModelProperty(value="上游商户号", dataType = "String")
    @FieldAnnotation(fieldName="上游商户号")
    private String channelMchId;

    /**
     * 商户编号
     */
    @ApiModelProperty(value="商户编号", dataType = "String")
    @FieldAnnotation(fieldName="商户编号")
    private String customerCode;

    /**
     * 商户名称
     */
    @ApiModelProperty(value="商户名称", dataType = "String")
    @FieldAnnotation(fieldName="商户名称")
    private String custName;

    /**
     * 所属代理商
     */
    @ApiModelProperty(value="所属代理商", dataType = "String")
    @FieldAnnotation(fieldName="所属代理商")
    private String agentCustomerCode;

    /**
     * 代理商名称
     */
    @ApiModelProperty(value="代理商名称", dataType = "String")
    @FieldAnnotation(fieldName="代理商名称")
    private String agentCustName;

    /**
     * 所属平台商
     */
    @ApiModelProperty(value="所属平台商", dataType = "String")
    @FieldAnnotation(fieldName="所属平台商")
    private String platCustomerCode;

    /**
     * 平台商名称
     */
    @ApiModelProperty(value="平台商名称", dataType = "String")
    @FieldAnnotation(fieldName="平台商名称")
    private String platCustName;

    /**
     * 业务员
     */
    @ApiModelProperty(value="业务员", dataType = "String")
    @FieldAnnotation(fieldName="业务员")
    private String businessMan;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value="导入批次号", dataType = "String")
    @FieldAnnotation(fieldName="导入批次号")
    private String batchNo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="创建时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String uniRemark;

    public Long getCtId() {
        return ctId;
    }

    public void setCtId(Long ctId) {
        this.ctId = ctId;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getInletType() {
        return inletType;
    }

    public void setInletType(String inletType) {
        this.inletType = inletType;
    }

    public Date getRiskTime() {
        return riskTime;
    }

    public void setRiskTime(Date riskTime) {
        this.riskTime = riskTime;
    }

    public Date getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(Date tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public String getComplainContent() {
        return complainContent;
    }

    public void setComplainContent(String complainContent) {
        this.complainContent = complainContent;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getChannelMchId() {
        if(StringUtils.isBlank(channelMchId)){
            return "";
        }
        return channelMchId;
    }

    public void setChannelMchId(String channelMchId) {
        this.channelMchId = channelMchId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getAgentCustomerCode() {
        return agentCustomerCode;
    }

    public void setAgentCustomerCode(String agentCustomerCode) {
        this.agentCustomerCode = agentCustomerCode;
    }

    public String getAgentCustName() {
        return agentCustName;
    }

    public void setAgentCustName(String agentCustName) {
        this.agentCustName = agentCustName;
    }

    public String getPlatCustomerCode() {
        return platCustomerCode;
    }

    public void setPlatCustomerCode(String platCustomerCode) {
        this.platCustomerCode = platCustomerCode;
    }

    public String getPlatCustName() {
        return platCustName;
    }

    public void setPlatCustName(String platCustName) {
        this.platCustName = platCustName;
    }

    public String getBusinessMan() {
        return businessMan;
    }

    public void setBusinessMan(String businessMan) {
        this.businessMan = businessMan;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUniRemark() {
        return uniRemark;
    }

    public void setUniRemark(String uniRemark) {
        this.uniRemark = uniRemark;
    }

}
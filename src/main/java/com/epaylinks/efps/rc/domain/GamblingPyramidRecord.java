package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.rc.vo.UrlConnVo;
import lombok.Data;

import java.util.Date;

@Data
public class GamblingPyramidRecord {
    /**
     * ID
     */
    private Long id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 银行账号脱敏
     */
    private String cardAccount;

    /**
     * 银行账号HASH值
     */
    private String cardAccountHash;

    /**
     * 银行账号加密
     */
    private String cardAccountEncrypt;

    /**
     * 是否加入黑名单[1：是；0：否]
     */
    private String joinBlacklist;

    /**
     * 核验结果[1：成功；0：失败]
     */
    private String verificationResults;

    /**
     * 是否涉赌[1：是；0：否]
     */
    private String gamblingOrNot;

    /**
     * 是否涉传销[1：是；0：否]
     */
    private String pyramidOrNot;

    /**
     * 原请求流水号
     */
    private String reqSerialNo;

    /**
     * 平台处理流水号
     */
    private String paySerialNo;

    /**
     * 是否调用上游[1：是；0：否]
     */
    private String submitUpstreamOrNot;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 响应码
     */
    private String responseCode;

    /**
     * 响应描述
     */
    private String responseMsg;

    /**
     * 核验时间
     */
    private Date checkTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 接口响应结果集
     */
    private String sourceData;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 商户名称
     */
    private String customerName;

    /**
     * 核验花费
     */
    private String checkCost;

    /**
     * 订单号
     */
    private String transOrderNo;

    /**
     * 是否涉诈[1：是；0：否]
     */
    private String fraudOrNot;

    /**
     * 是否疑似套现[1：是；0：否]
     */
    private String cashoutOrNot;

    /**
     * 是否疑似违规[1：是；0：否]
     */
    private String violationOrNot;

    /**
     * 类型名称：001身份证号；002手机号；003社会统一信用代码；004银行账号；
     */
    private String cardAccountType;

    /**
     * 风险标签：涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation，无：none
     */
    private String riskTag;

    /**
     * 核验类型：涉赌：gambling，涉传销：pyramid，涉诈：fraud，疑似套现：cashout，疑似违规：violation
     */
    private String checkType;

    /**
     * 来源：0-商户API 1-内部使用
     */
    private String source;

    /**
     * 用于网站有效性导出
     */
    private UrlConnVo urlConnVo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getCardAccount() {
        return cardAccount;
    }

    public void setCardAccount(String cardAccount) {
        this.cardAccount = cardAccount;
    }

    public String getCardAccountHash() {
        return cardAccountHash;
    }

    public void setCardAccountHash(String cardAccountHash) {
        this.cardAccountHash = cardAccountHash;
    }

    public String getCardAccountEncrypt() {
        return cardAccountEncrypt;
    }

    public void setCardAccountEncrypt(String cardAccountEncrypt) {
        this.cardAccountEncrypt = cardAccountEncrypt;
    }

    public String getJoinBlacklist() {
        return joinBlacklist;
    }

    public void setJoinBlacklist(String joinBlacklist) {
        this.joinBlacklist = joinBlacklist;
    }

    public String getVerificationResults() {
        return verificationResults;
    }

    public void setVerificationResults(String verificationResults) {
        this.verificationResults = verificationResults;
    }

    public String getGamblingOrNot() {
        return gamblingOrNot;
    }

    public void setGamblingOrNot(String gamblingOrNot) {
        this.gamblingOrNot = gamblingOrNot;
    }

    public String getPyramidOrNot() {
        return pyramidOrNot;
    }

    public void setPyramidOrNot(String pyramidOrNot) {
        this.pyramidOrNot = pyramidOrNot;
    }

    public String getReqSerialNo() {
        return reqSerialNo;
    }

    public void setReqSerialNo(String reqSerialNo) {
        this.reqSerialNo = reqSerialNo;
    }

    public String getPaySerialNo() {
        return paySerialNo;
    }

    public void setPaySerialNo(String paySerialNo) {
        this.paySerialNo = paySerialNo;
    }

    public String getSubmitUpstreamOrNot() {
        return submitUpstreamOrNot;
    }

    public void setSubmitUpstreamOrNot(String submitUpstreamOrNot) {
        this.submitUpstreamOrNot = submitUpstreamOrNot;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMsg() {
        return responseMsg;
    }

    public void setResponseMsg(String responseMsg) {
        this.responseMsg = responseMsg;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSourceData() {
        return sourceData;
    }

    public void setSourceData(String sourceData) {
        this.sourceData = sourceData;
    }
}
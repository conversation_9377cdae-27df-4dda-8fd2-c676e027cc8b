package com.epaylinks.efps.rc.domain.cust;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class Customer {
	@ApiModelProperty(value="商户ID", dataType = "Long")
    private Long customerId;

	@ApiModelProperty(value="父级商户ID", dataType = "Long")
    private Long parentCustomerId;
	
    @ApiModelProperty(value="商户编号", dataType = "String")
    private String customerNo;

    @ApiModelProperty(value="商户名称", dataType = "String")
    private String name;

    @ApiModelProperty(value="商户简称", dataType = "String")
    private String shortName;

    @ApiModelProperty(value="电话号码", dataType = "String")
    private String phone;

    @ApiModelProperty(value="手机号码", dataType = "String")
    private String mobile;

    @ApiModelProperty(value="邮箱", dataType = "String")
    private String email;

	@ApiModelProperty(value="注册时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

	@ApiModelProperty(value="商户来源 1:EPSP平台录入,2:EPSP接口,3:云闪付开放平台,4:旧系统,5:H5自助平台,6:PC自助平台,7:终端自助平台,8:代理商门户,9:代理商APP", dataType = "String")
    private String sourceChannel;

    @ApiModelProperty(value="状态（1：正常；2：冻结；3：注销；4：止付）", dataType = "Integer")
    private Integer status;

    @ApiModelProperty(value="商户类型（性质）：10：个体工商户, 20：企业商户,50：小微商户", dataType = "Integer")
    private Integer type;

    @ApiModelProperty(value="客户级别（0：顶级客户；1：拓展客户）", dataType = "Integer")
    private Integer custLevel;

    @ApiModelProperty(value="是否自有（0：否；1：是）", dataType = "Integer")
    private Integer isOwn;

    @ApiModelProperty(value="是否认证（0：否；1：是）", dataType = "Integer")
    private Integer isAuthed;
    
    @ApiModelProperty(value="省份", dataType = "String")
    private String provinceCode;

    @ApiModelProperty(value="地市", dataType = "String")
    private String cityCode;

    @ApiModelProperty(value="区县市", dataType = "String")
    private String districtCode;

    @ApiModelProperty(value="地址", dataType = "String")
    private String address;

    @ApiModelProperty(value="版本", dataType = "Long")
    private Long version;
    
	@ApiModelProperty(value="创建时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
	@ApiModelProperty(value="修改时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value="操作员ID", dataType = "Long")
    private Long userId;
    
    @ApiModelProperty(value="商户唯一编码", dataType = "String")
    private String customerUid;

    @ApiModelProperty(value="服务商编号", dataType = "String")
    private String serviceCustomerNo;
    
    @ApiModelProperty(value="商户类目[0:普通商户，2：平台商户，3：服务商]", dataType = "Short")
    private Short category;
    
    @ApiModelProperty(value="会员签约模式：0-商户签约，1-平台签约，不填默认0", dataType = "String")
    private String signMode;
    
    @ApiModelProperty(value="对账单模式:0:结算日模式；1：自然日模式，默认0", dataType = "String")
    private String billMode;
    
    @ApiModelProperty(value="分组ID", dataType = "String")
    private String channelGroupId;

    @ApiModelProperty(value="平台商户编号", dataType = "String")
    private String platCustomerNo;
    
    @ApiModelProperty(value="是否开户：0否，1是", dataType = "Short")
    private Short openAccount;
    
    @ApiModelProperty(value="是否收单：0否，1是", dataType = "Short")
    private Short acceptOrder;

	@ApiModelProperty(value="终端号", dataType = "String")
	private String terminalCode;
	
	@ApiModelProperty(value="商户类别MCC", dataType = "String")
    private String mcc;

	@ApiModelProperty(value="业务员ID", dataType = "Long")
    private Long businessManId;

	@ApiModelProperty(value="分公司ID", dataType = "Long")
    private Long companyId;

	@ApiModelProperty(value="风控状态：0：正常，1：冻结", dataType = "String")
    private String rcStatus;
	
    @ApiModelProperty(value="预算分组", dataType = "String")
    private String budgetGroup;

    @ApiModelProperty(value="临时入金状态【1：正常；2：限制临时入金】", dataType = "String")
    private String temporaryStatus;

    @ApiModelProperty(value="临时入金状态变更原因", dataType = "String")
    private String temporaryStatusReason;
    
    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getParentCustomerId() {
        return parentCustomerId;
    }

    public void setParentCustomerId(Long parentCustomerId) {
        this.parentCustomerId = parentCustomerId;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getName() {
        return name != null ? name.trim() : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName != null ? shortName.trim() : shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getPhone() {
        return phone != null ? phone.trim() : phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile != null ? mobile.trim() : mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email != null ? email.trim() : email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public String getSourceChannel() {
        return sourceChannel;
    }

    public void setSourceChannel(String sourceChannel) {
        this.sourceChannel = sourceChannel;
    }

    public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getCustLevel() {
		return custLevel;
	}

	public void setCustLevel(Integer custLevel) {
		this.custLevel = custLevel;
	}

	public Integer getIsOwn() {
		return isOwn;
	}

	public void setIsOwn(Integer isOwn) {
		this.isOwn = isOwn;
	}

	public Integer getIsAuthed() {
		return isAuthed;
	}

	public void setIsAuthed(Integer isAuthed) {
		this.isAuthed = isAuthed;
	}

	public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getAddress() {
        return address != null ? address.trim() : address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getCustomerUid() {
		return customerUid;
	}

	public void setCustomerUid(String customerUid) {
		this.customerUid = customerUid;
	}

	public String getServiceCustomerNo() {
		return serviceCustomerNo;
	}

	public void setServiceCustomerNo(String serviceCustomerNo) {
		this.serviceCustomerNo = serviceCustomerNo;
	}

	public Short getCategory() {
		return category;
	}

	public void setCategory(Short category) {
		this.category = category;
	}

	public String getSignMode() {
		return signMode;
	}

	public void setSignMode(String signMode) {
		this.signMode = signMode;
	}

	public String getBillMode() {
		return billMode;
	}

	public void setBillMode(String billMode) {
		this.billMode = billMode;
	}

	public String getChannelGroupId() {
		return channelGroupId;
	}

	public void setChannelGroupId(String channelGroupId) {
		this.channelGroupId = channelGroupId;
	}

	public String getPlatCustomerNo() {
		return platCustomerNo;
	}

	public void setPlatCustomerNo(String platCustomerNo) {
		this.platCustomerNo = platCustomerNo;
	}

	public Short getOpenAccount() {
		return openAccount;
	}

	public void setOpenAccount(Short openAccount) {
		this.openAccount = openAccount;
	}

	public Short getAcceptOrder() {
		return acceptOrder;
	}

	public void setAcceptOrder(Short acceptOrder) {
		this.acceptOrder = acceptOrder;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public String getMcc() {
		return mcc;
	}

	public void setMcc(String mcc) {
		this.mcc = mcc;
	}

	public Long getBusinessManId() {
		return businessManId;
	}

	public void setBusinessManId(Long businessManId) {
		this.businessManId = businessManId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getRcStatus() {
		return rcStatus;
	}

	public void setRcStatus(String rcStatus) {
		this.rcStatus = rcStatus;
	}

    public String getBudgetGroup() {
        return budgetGroup;
    }

    public void setBudgetGroup(String budgetGroup) {
        this.budgetGroup = budgetGroup;
    }
	
	
    
}
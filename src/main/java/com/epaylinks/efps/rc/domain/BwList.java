package com.epaylinks.efps.rc.domain;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class BwList implements Serializable {
    private static final long serialVersionUID = 6616582395822421323L;
    /**
     * ID
     */
    private Long bwId;

    /**
     * 适用业务类型
     */
    private String businessType;

    /**
     * 业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡
     */
    @ApiModelProperty(value = "业务对象类型  001身份证 002手机号 003社会统一信用代码 004银行卡", dataType ="String")
    private String businessTagerType;

    /**
     * 业务对象ID
     */
    private String businessTagerId;

    /**
     * 黑白名单 0黑名单 1白名单
     */
    @ApiModelProperty(value = "黑白名单 0黑名单 1白名单", dataType ="String")
    private String bwType;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyyMMdd HHmmss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern="yyyyMMdd HHmmss")
    private Date endTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 备注
     */
    private String remark;

    /**
     * 业务对象hash
     */
    private String tagerHash;
    
    /**
     * 审核状态：0待审核，1正常
     */
    @ApiModelProperty(value = "状态：0待审核，1正常", dataType ="Short")
    private Short status;
    
    
    /**
     * 使用状态：0未启用，1启用
     */
    @ApiModelProperty(value = " 使用状态：0未启用，1启用", dataType ="Short")
    private Short useStatus;

    private Object oldValue;

    private Object newValue;

    /**
     * 风险标签
     */
    private String riskTag;
    
    @Override
    public String toString() {
    	return JSON.toJSONString(this);
    }
    
    public enum BwType{
    	BLACK("0" , "RC:blackList"),
    	WHITE("1" , "RC:whiteList"),
        GREY("2","RC:greyList");
    	public final String code;
    	public final String message;
		private BwType(String code, String message) {
			this.code = code;
			this.message = message;
		}
    }

    public String getBusinessTagerTypeName() {
        String name;
        // 001身份证 002手机号 003社会统一信用代码 004银行卡
        switch (this.businessTagerType) {
            case "001":
                name = "身份证";
                break;
            case "002":
                name = "手机号";
                break;
            case "003":
                name = "社会统一信用代码";
                break;
            case "004":
                name = "银行卡";
                break;
            case "005":
                name = "商户编号";
                break;
            case "006":
                name = "身份证前4位";
                break;
            case "007":
                name = "经营地址";
                break;
            default:
                name = "";
                break;
        }
        return name;
    }
    

}
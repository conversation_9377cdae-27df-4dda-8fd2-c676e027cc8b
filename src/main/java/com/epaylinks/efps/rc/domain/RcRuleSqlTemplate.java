package com.epaylinks.efps.rc.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @date  2024/1/8 10:27
 * @version 1.0
 */
public class RcRuleSqlTemplate {
    private String scene;

    private String sqlTemplate;

    private Date createTime;

    private Date updateTime;

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getSqlTemplate() {
        return sqlTemplate;
    }

    public void setSqlTemplate(String sqlTemplate) {
        this.sqlTemplate = sqlTemplate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
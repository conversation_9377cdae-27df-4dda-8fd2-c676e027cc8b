package com.epaylinks.efps.rc.domain.cum;
/**
* <AUTHOR> :Liuq 
* @date 创建时间：2017年9月13日 下午7:21:45 
* @version 添加客户信息model
* @parameter   
* @return  
*/

public class CustomerObjects {
	
	//客户编码
	public String code;
	
	//状态 0：预开户 1：正常 2：锁定 3：注销  4：加入黑名单
	public Long status;
	
	//初始支付密码
	public String payPassword;
	
	//支付密码过期日期，YYYYMMDD，不填表示永不过期
	public String payPasswordExpiredDate;
	
	//客户基本信息
	public CustomerInfo basicInfo;
	
	//客户结算信息
	public CustomerSettleInfo customerSettmentInfo;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public String getPayPasswordExpiredDate() {
		return payPasswordExpiredDate;
	}

	public void setPayPasswordExpiredDate(String payPasswordExpiredDate) {
		this.payPasswordExpiredDate = payPasswordExpiredDate;
	}

	public CustomerInfo getBasicInfo() {
		return basicInfo;
	}

	public void setBasicInfo(CustomerInfo basicInfo) {
		this.basicInfo = basicInfo;
	}

	public CustomerSettleInfo getCustomerSettmentInfo() {
		return customerSettmentInfo;
	}

	public void setCustomerSettmentInfo(CustomerSettleInfo customerSettmentInfo) {
		this.customerSettmentInfo = customerSettmentInfo;
	}
}
package com.epaylinks.efps.rc.domain;

import java.util.Date;

import com.alibaba.fastjson.JSON;

public class RcLimitData {
    private Long id;

    private String businesstargetid;

    private String definecode;

    private String value;

    private Date createtime;

    private Date updatetime;

    private String status;
    
    private String datetime; // 数据所属日期（包括：yyyyMMdd, yyyyMM ,yyyy）

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getBusinesstargetid() {
        return businesstargetid;
    }

    public void setBusinesstargetid(String businesstargetid) {
        this.businesstargetid = businesstargetid;
    }

    public String getDefinecode() {
        return definecode;
    }

    public void setDefinecode(String definecode) {
        this.definecode = definecode;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }


	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDatetime() {
		return datetime;
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	public RcLimitData(Long id, String businesstargetid, String definecode, String value, Date createtime,
			Date updatetime, String status, String datetime) {
		super();
		this.id = id;
		this.businesstargetid = businesstargetid;
		this.definecode = definecode;
		this.value = value;
		this.createtime = createtime;
		this.updatetime = updatetime;
		this.status = status;
		this.datetime = datetime;
	}

	public RcLimitData() {
		super();
	}
    
    @Override
    public String toString() {
    	// TODO Auto-generated method stub
    	return JSON.toJSONString(this);
    }
    
    public enum Status{
    	PROCESSING("processing" , "处理中的风控数据"),
    	FINISHED("finished" , "已完成的风控数据");
    	public final String code;
    	public final String message;
		Status(String code, String message) {
			this.code = code;
			this.message = message;
		}
    }
}
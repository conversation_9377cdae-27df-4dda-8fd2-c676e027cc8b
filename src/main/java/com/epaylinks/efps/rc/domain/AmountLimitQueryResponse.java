package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.common.business.CommonOuterResponse;

import com.epaylinks.efps.rc.domain.cust.AttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class AmountLimitQueryResponse extends CommonOuterResponse {

    @ApiModelProperty( value="风控档案ID", dataType = "Long")
    private Long archiveId;
    
    @ApiModelProperty( value="审核记录ID（存量或无）", dataType = "Long")
    private Long audId;
    
    @ApiModelProperty( value="风控档案编号", dataType = "String")
    private String archiveCode;

    @ApiModelProperty( value="档案类型：001:身份证；003：统一社会信用代码；005：商户编号", dataType = "String")
    private String archiveType;
    
    @ApiModelProperty( value="风控档案名称", dataType = "String")
    private String archiveName;
    
    @ApiModelProperty( value="风险等级", dataType = "String")
    private String rcLevel;
    
    @ApiModelProperty( value="风控限额审核状态（01：待审核，02：审核不通过，03：审核通过）", dataType = "String")
    private String audStatus;
    
    // 商户限额对象     
    private MainAmountLimit mainAmount;

    private OutAmountLimit outAmountLimit;

    private InstAmountLimit instAmountLimit;
    
    private WithdrawAmountLimit withdrawAmount;

    private CardAmountLimit cardAmount;

    private UserAmountLimit userAmount;
    
    private MerchantAmountLimit merchantAmountLimit;
    
    // 证件维度限额对象
    private CertificateAmountLimit certificateAmountLimit;
    
    // 终端维度限额对象
    private TermAmountLimit termAmountLimit; 

    // 个人维度限额对象
    private PersonAmountLimit personAmountLimit;

    private PlatCustomerAmountLimit platCustomerAmountLimit;

    // 审核记录值
    private MainAmountLimit audMainAmount;

    private OutAmountLimit audOutAmountLimit;

    private InstAmountLimit audInstAmountLimit;
    
    private WithdrawAmountLimit audWithdrawAmount;

    private CardAmountLimit audCardAmount;

    private UserAmountLimit audUserAmount;
    
    private MerchantAmountLimit audMerchantAmountLimit;
    
    private CertificateAmountLimit audCertificateAmountLimit;
    
    private TermAmountLimit audTermAmountLimit; 
    
    private PersonAmountLimit audPersonAmountLimit;

    private PlatCustomerAmountLimit audPlatCustomerAmountLimit;

    private List<AttachmentVO> attachments;

    private List<AttachmentVO> audAttachments;
}

package com.epaylinks.efps.rc.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class RcLimit implements Serializable {
    private static final long serialVersionUID = 5875484697695564759L;

    /**
     * 风控指标
     */
    private Long limitId;

    /**
     * 风控指标定义编码
     */
    private String defineCode;

    /**
     * 业务类型
     */
    private String businnesType;

    /**
     * 业务对象类型,005:商户
     */
    private String businessTagerType;

    /**
     * 业务对象ID
     */
    private String businessTagerId;

    /**
     * 限制值
     */
    private String limitValue;

    /**
     * 限制值中的JAVA类型
     */
    private String limitType;

    /**
     * 优先级,数值越小优先级越高
     */
    private String limitLevel;

    /**
     * 指标版本
     */
    private String limitVersion;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 效果值 -1表示一票否决
     */
    private String effectValue;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 风控指标定义ID
     */
    private Long defineId;

    /**
     * 风控值类型
     *
     */
    private String unit;

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getLimitId() {
        return limitId;
    }

    public void setLimitId(Long limitId) {
        this.limitId = limitId;
    }

    public String getDefineCode() {
        return defineCode;
    }

    public void setDefineCode(String defineCode) {
        this.defineCode = defineCode;
    }

    public String getBusinnesType() {
        return businnesType;
    }

    public void setBusinnesType(String businnesType) {
        this.businnesType = businnesType;
    }

    public String getBusinessTagerType() {
        return businessTagerType;
    }

    public void setBusinessTagerType(String businessTagerType) {
        this.businessTagerType = businessTagerType;
    }

    public String getBusinessTagerId() {
        return businessTagerId;
    }

    public void setBusinessTagerId(String businessTagerId) {
        this.businessTagerId = businessTagerId;
    }

    public String getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(String limitValue) {
        this.limitValue = limitValue;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public String getLimitLevel() {
        return limitLevel;
    }

    public void setLimitLevel(String limitLevel) {
        this.limitLevel = limitLevel;
    }

    public String getLimitVersion() {
        return limitVersion;
    }

    public void setLimitVersion(String limitVersion) {
        this.limitVersion = limitVersion;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getEffectValue() {
        return effectValue;
    }

    public void setEffectValue(String effectValue) {
        this.effectValue = effectValue;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getDefineId() {
        return defineId;
    }

    public void setDefineId(Long defineId) {
        this.defineId = defineId;
    }

	@Override
	public String toString() {
		return "RcLimit{" +
				"limitId=" + limitId +
				", defineCode='" + defineCode + '\'' +
				", businnesType='" + businnesType + '\'' +
				", businessTagerType='" + businessTagerType + '\'' +
				", businessTagerId='" + businessTagerId + '\'' +
				", limitValue='" + limitValue + '\'' +
				", limitType='" + limitType + '\'' +
				", limitLevel='" + limitLevel + '\'' +
				", limitVersion='" + limitVersion + '\'' +
				", startTime=" + startTime +
				", endTime=" + endTime +
				", effectValue='" + effectValue + '\'' +
				", userId=" + userId +
				", userName='" + userName + '\'' +
				", createTime=" + createTime +
				", defineId=" + defineId +
				'}';
	}


	/**
	 * businessType 业务类型
	 * methodName   方法名称
	 * 根据不同的业务，使用反射调用不同的方法，在这里维护，业务类型和方法名称
	 */
	public enum Service{
		IN_OUT("IN,OUT","inOutAmount");
		public final String businessType;
		public final String methodName;
		private Service(String businessType, String methodName) {
			this.businessType = businessType;
			this.methodName = methodName;
		}

	}


}
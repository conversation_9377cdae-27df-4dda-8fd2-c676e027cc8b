package com.epaylinks.efps.rc.domain.cust;

import io.swagger.annotations.ApiModelProperty;

public class CustomerTotalConfig {

    @ApiModelProperty(value = "平台商是否允许入金交易:0否，1是", dataType = "String")
    private String allowPay;

    public String getAllowPay() {
        return allowPay;
    }

    public void setAllowPay(String allowPay) {
        this.allowPay = allowPay;
    }

    @ApiModelProperty(value = "平台商是否允许出金交易:0否，1是", dataType = "String")
    private String allowPayOut;

    public String getAllowPayOut() {
        return allowPayOut;
    }

    public void setAllowPayOut(String allowPayOut) {
        this.allowPayOut = allowPayOut;
    }
}


package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人维度限额
 * <AUTHOR>
 * @since 2021-05-06
 *
 */
@Data
@ApiModel(value = "个人维度限额")
public class PersonAmountLimit {

    @ApiModelProperty( value = "终身出金累计限额（分）", dataType = "Long")
    private Long totalMaxOutAmount;
    
    @ApiModelProperty( value = "同人单日出金累计限额（分）", dataType = "Long")
    private Long userDayMaxOutAmount;

    @ApiModelProperty( value = "同人单年出金累计限额（分）", dataType = "Long")
    private Long userYearMaxOutAmount;

    @ApiModelProperty( value = "个人余额转账单笔最高限额（分）", dataType = "Long")
    private Long balanceTransAmount;

    @ApiModelProperty( value = "余额转账单日累计限额（分）", dataType = "Long")
    private Long balanceTransDayAmount;

    @ApiModelProperty( value = "单笔提现最高限额（分）", dataType = "Long")
    private Long balanceWithdrawAmount;

    @ApiModelProperty( value = "单日提现累计限额（分）", dataType = "Long")
    private Long balanceWithdrawDayAmount;

    @ApiModelProperty( value = "单笔余额消费最高限额（分）", dataType = "Long")
    private Long balanceConsumeAmount;

    @ApiModelProperty( value = "单日余额消费最高限额（分）", dataType = "Long")
    private Long balanceConsumeDayAmount;

    @ApiModelProperty( value = "单笔入金最高限额（分）", dataType = "Long")
    private Long balanceInAmount;

    @ApiModelProperty( value = "单日入金最高限额（分）", dataType = "Long")
    private Long balanceInDayAmount;

    @ApiModelProperty( value = "非同名银行账户转账:0:不限制；1：限制", dataType = "Long")
    private Long accountNameLimit;

    @ApiModelProperty( value = "向单位支付账户转账:0:不限制；1：限制", dataType = "Long")
    private Long personTransCompany;

    @ApiModelProperty( value = "向个人支付账户转账:0:不限制；1：限制", dataType = "Long")
    private Long personTransPerson;
    
    
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
    
}

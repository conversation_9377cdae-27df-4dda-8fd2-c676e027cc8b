package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 证件维度交易限额
 * <AUTHOR>
 * @date 2020-06-22
 *
 */
@Data
@ApiModel
public class CertificateAmountLimit {

    @ApiModelProperty( value = "日转账次数", dataType = "Long")
    private Long transCountLimitDay; // 日转账次数

    @ApiModelProperty( value = "日提现次数", dataType = "Long")
    private Long dayMaxOutCount; // 日提现次数

    @ApiModelProperty( value = "日交易累计限额(分)", dataType = "Long")
    private Long dayMaxInAmount;
    
    @ApiModelProperty( value = "月交易累计限额(分)", dataType = "Long")
    private Long monthMaxInAmount;
    
    @ApiModelProperty( value = "单日信用卡交易累计限额(分)", dataType = "Long")
    private Long dayMaxCreditAmount;
    
    @ApiModelProperty( value = "单月信用卡交易累计限额(分)", dataType = "Long")
    private Long monthMaxCreditAmount;
    

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

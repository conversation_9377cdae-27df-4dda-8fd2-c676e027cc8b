package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Date;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class RcTxsOrder extends RcTxsOrderKey {

    private String outTradeNo;

    private String indexs;

    private Date createTime;

    private String customerCode;

    private String businessCode;

    private Long amount;

    private String payState;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


    private Map<String, String> indexMap = null;
    private Map<String, String> targetIdMap = null;

    public Map<String, String> getIndexMap() {
        if (indexMap == null) {
            if (indexs != null) {
                indexMap = JsonUtils.jsonToMap(indexs);
            } else {
                return Collections.emptyMap();
            }
        }
        return indexMap;
    }

    public Map<String, String> getTargetIdMap() {
        if (targetIdMap == null) {
            if (getBusinessTargetIds() != null) {
                targetIdMap = JsonUtils.jsonToMap(getBusinessTargetIds());
            } else {
                return Collections.emptyMap();
            }
        }
        return targetIdMap;
    }
}
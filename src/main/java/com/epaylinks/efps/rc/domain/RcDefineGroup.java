package com.epaylinks.efps.rc.domain;

import java.math.BigDecimal;
import java.util.Date;

public class RcDefineGroup {
    /**
     * ID
     */
    private Long groupId;

    /**
     * 分组名称(英文)
     */
    private String name;

    /**
     * 父节点ID
     */
    private Long parentId;

    /**
     * 分组名称(中文)
     */
    private String alias;

    /**
     * 说明
     */
    private String remark;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 指标分组编号
     */
    private String groupIdentifier;

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getGroupIdentifier() {
        return groupIdentifier;
    }

    public void setGroupIdentifier(String groupIdentifier) {
        this.groupIdentifier = groupIdentifier;
    }

    @Override
    public String toString() {
        return "RcDefineGroup{" +
                "groupId=" + groupId +
                ", name='" + name + '\'' +
                ", parentId=" + parentId +
                ", alias='" + alias + '\'' +
                ", remark='" + remark + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", createTime='" + createTime + '\'' +
                ", groupIdentifier='" + groupIdentifier + '\'' +
                '}';
    }
}
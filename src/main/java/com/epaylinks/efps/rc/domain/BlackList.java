package com.epaylinks.efps.rc.domain;

import java.util.Date;

public class BlackList {
    /**
     */
    private Long id;

    /**
     * 类型【004：银行卡；001：身份证；015：订单号；005：商户编号】
     */
    private String type;

    /**
     * 类型值
     */
    private String value;

    /**
     */
    private String valueEnc;

    /**
     */
    private String valueHash;

    /**
     * 名称
     */
    private String name;

    /**
     * 风险标签
     */
    private String riskTag;

    /**
     */
    private String display;

    /**
     * 备注
     */
    private String remark;

    /**
     */
    private String remarkEnc;

    /**
     */
    private String remarkHash;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 添加人
     */
    private String operator;

    /**
     * 添加时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueEnc() {
        return valueEnc;
    }

    public void setValueEnc(String valueEnc) {
        this.valueEnc = valueEnc;
    }

    public String getValueHash() {
        return valueHash;
    }

    public void setValueHash(String valueHash) {
        this.valueHash = valueHash;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRiskTag() {
        return riskTag;
    }

    public void setRiskTag(String riskTag) {
        this.riskTag = riskTag;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemarkEnc() {
        return remarkEnc;
    }

    public void setRemarkEnc(String remarkEnc) {
        this.remarkEnc = remarkEnc;
    }

    public String getRemarkHash() {
        return remarkHash;
    }

    public void setRemarkHash(String remarkHash) {
        this.remarkHash = remarkHash;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
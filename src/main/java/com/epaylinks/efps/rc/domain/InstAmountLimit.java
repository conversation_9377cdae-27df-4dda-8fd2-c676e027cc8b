package com.epaylinks.efps.rc.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class InstAmountLimit {
    @ApiModelProperty( value = "同卡入金单日最高限额 (元)", dataType = "Long")
    private Long cardDayInAmount;

    @ApiModelProperty( value = "同人入金单日最高限额(元)", dataType = "Long")
    private Long userDayInAmount;

    @ApiModelProperty( value = "近180天同卡累计入金最高限额(元)", dataType = "Long")
    private Long cardTotalInAmount;

    @ApiModelProperty( value = "近180天同人累计入金最高限额(元)", dataType = "Long")
    private Long userTotalInAmount;
}

package com.epaylinks.efps.rc.domain;

import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 风控档案页面查询响应类
 */
@Data
@ApiModel
public class RcArchivePageResponse {

    @ApiModelProperty(value="风控档案ID", dataType = "String")
    private Long archiveId;

    @ApiModelProperty(value="档案类型：001:身份证；003：统一社会信用代码；005：商户编号；008：终端号；009：个人账户", dataType = "String")
    private String archiveType;

    @ApiModelProperty(value="风控档案编码", dataType = "String")
    private String archiveCode;

    @ApiModelProperty(value="风控档案名称", dataType = "String")
    private String archiveName;

    @ApiModelProperty(value="风控等级", dataType = "String")
    private String rcLevel;

    @ApiModelProperty(value="出金单日限额", dataType = "Long")
    private Long dayMaxOutAmount;

    @ApiModelProperty(value="更新时间", dataType = "Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    @ApiModelProperty(value="证件号码：营业执照号（企业个体）、身份证（小微）", dataType = "String")
    private String certificateNo; // 证件号码：营业执照号（企业个体）、身份证（小微）
    
    @ApiModelProperty(value="审核状态: 01：待审核，02：审核不通过，03：审核通过", dataType = "String")
    private String audStatus;
    
    @ApiModelProperty(value="审核意见", dataType = "String")
    private String audOpinion;
    
    
    @ApiModelProperty(value="入金单笔限额", dataType = "Long")
    private Long singleMaxInAmount;

    @ApiModelProperty(value="入金单日限额", dataType = "Long")
    private Long dayMaxInAmount;

    @ApiModelProperty(value="出金单笔限额", dataType = "Long")
    private Long singleMaxOutAmount;
    
    @ApiModelProperty(value="终端序列号", dataType = "String")
    private String machineCode;
    
    
    @ApiModelProperty(value="客户号", dataType = "String")
    private String clientNo;

    private Long audId;

    private String mcc;

    @ApiModelProperty(value="性质", dataType = "String")
    private Integer type;

    @ApiModelProperty(value="分类", dataType = "String")
    private String category;

    @ApiModelProperty(value="商户类型", dataType = "String")
    private String customerType;

    @ApiModelProperty(value="行业分类", dataType = "String")
    private String industry;
    
    
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

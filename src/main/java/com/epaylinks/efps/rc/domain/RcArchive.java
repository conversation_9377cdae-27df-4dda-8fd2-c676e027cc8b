package com.epaylinks.efps.rc.domain;

import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel
@EqualsAndHashCode(callSuper=false)
public class RcArchive extends CommonOuterResponse{

	/**
	 * 风控档案ID
	 */
	private Long archiveId;

	/**
	 * 类型
	 */
    @ApiModelProperty(value="档案类型：001:身份证；003：统一社会信用代码；005：商户编号", dataType = "String")
	private String archiveType;

	/**
	 * 商户状态
	 */
	private String cusStatus;

	/**
	 * 风控状态 0正常 1冻结
	 */
    @ApiModelProperty(value="风控状态：0：正常；1：冻结 ", dataType = "String")
	private String rcStatus;

	/**
	 * 账户状态 0正常 1账户冻结 2止付 3禁止入金
	 */
    @ApiModelProperty(value="账户状态：0：正常；1：账户冻结 ；2：止付； 3：禁止入金", dataType = "String")
	private String accountStatus;

	/**
	 * 风险等级 0低级 1中级 2高级
	 */
	private String rcLevel;

	/**
	 * 注册时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date regTime;

	/**
	 * 数据来源
	 */
	private String source;

	/**
	 * 商户/个人/代理商编号
	 */
	private String archiveCode;

	/**
	 * 商户/个人/代理商名称
	 */
	private String archiveName;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 用户名称
	 */
	private String userName;

	/**
	 * 商户ID
	 */
	private String customerInfoId;

	/**
	 * 风控状态修改原因
	 */
	private String rcStatusReason;

	/**
	 * 账户状态修改原因
	 */
	private String accountStatusReason;

	/**
	 * 风控等级修改原因
	 */
	private String rcLevelReason;

	/**
	 * 风控冻结金额修改原因
	 */
	private String rcBalanceReason;

	private Map<String,Long> amountLimit;

	private Map<String,Boolean> bwList;
	
    private Long frozenBalance;

    private String bindCard; //  报备卡标识0：不需报备；1：需要报备

    private String bindCardReason;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private String certificateNo; // 证件号码：营业执照号（企业个体）、身份证（小微）

    private String parentCode; // 父级编号（商户编号）
    
    private String audStatus; // 风控限额审核状态（01：待审核，02：审核不通过，03：审核通过）
    
    @ApiModelProperty(value="商户类目[0:普通商户，2：平台商户，3：服务商]", dataType = "Short")
    private Short customerCategory; // 商户类型

    @ApiModelProperty(value="鉴权理财设置:0不可授权；1:可授权", dataType = "String")
    private String authFinacial;

    @ApiModelProperty(value="企业网银充值银行卡校验(0：不校验；1：需校验（非同名卡）；2：需校验所有卡)", dataType = "String")
    private String checkRechargeCard; 

    @ApiModelProperty(value="企业网银充值银行卡校验变更原因", dataType = "String")
    private String checkCardReason;
    
    @ApiModelProperty(value="网银业务设置：1：收单；2：充值；3：取上级", dataType = "String")
    private String eBankSetting;

    @ApiModelProperty(value="交易时段（起）", dataType = "String")
    private String tradeStartTime;
   
    @ApiModelProperty(value="交易时段（止）", dataType = "String")
    private String tradeEndTime;

	@ApiModelProperty(value="抽查比例", dataType = "Integer")
    private Integer bindCardRatio;

	@ApiModelProperty(value = "临时入金状态【1：正常；2：限制临时入金】",dataType = "String")
	private String temporaryStatus;

	@ApiModelProperty(value = "临时入金状态变更原因",dataType = "String")
	private String temporaryStatusReason;

	private Integer type;

	private String mcc;

	private String industry;

	private Long registeredCapital;

	@ApiModelProperty(value="代付占比", dataType = "Integer")
	private Integer proportion;

	@ApiModelProperty(value = "代付设置开始时间",dataType = "String")
	private String withdrawStartTime;

	@ApiModelProperty(value = "代付设置结束时间",dataType = "String")
	private String withdrawEndTime;

	@ApiModelProperty(value = "代付设置变更原因",dataType = "String")
	private String withdrawReason;

	@ApiModelProperty(value = "代付设置附件",dataType = "String")
	private String withdrawUniqueId;

	private String paySetting;

	private String paySettingReason;

	private String paySettingUniqueId;

	@ApiModelProperty(value = "最低提现出金比例设置",dataType = "Integer")
	private Integer minWithdrawRatio;
    
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public enum Level{
		LOW("0","LOW_LEVEL");
		public final String code;
		public final String levelCode;

		private Level(String code,String levelCode){
			this.code = code;
			this.levelCode = levelCode;
		}
	}

	public enum Status{
        CUSTOMER("CUS-STATUS","cusStatus", "商户状态"), 
		RC("RC-STATUS","rcStatus","风控状态"),
		ACCOUNT("ACCOUNT-STATUS","accountStatus","账户状态");
		public final String statusCode;
		public final String statusName;
		public final String alias;

		private Status(String statusCode,String statusName,String alias){
			this.statusCode = statusCode;
			this.statusName = statusName;
			this.alias = alias;
		}
	}

}
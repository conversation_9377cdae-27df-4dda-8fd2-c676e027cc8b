package com.epaylinks.efps.rc.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @date  2023/8/14 17:33
 * @version 1.0
 */
public class RcDroolsThen {
    private Long id;

    private String thenText;

    private String thenDrl;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getThenText() {
        return thenText;
    }

    public void setThenText(String thenText) {
        this.thenText = thenText;
    }

    public String getThenDrl() {
        return thenDrl;
    }

    public void setThenDrl(String thenDrl) {
        this.thenDrl = thenDrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
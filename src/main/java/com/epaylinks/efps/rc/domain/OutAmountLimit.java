package com.epaylinks.efps.rc.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "商户门户出金限额设置")
public class OutAmountLimit {
    @ApiModelProperty( value = "商户门户单日出金限额（分）", dataType = "Long")
    private Long daliyOutLimit;

    @ApiModelProperty( value = "商户门户单笔出金限额（分）", dataType = "Long")
    private Long singleOutLimit;
}

package com.epaylinks.efps.rc.domain;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class UserAmountLimit {

    @ApiModelProperty( value = "同卡入金单日最高限额（分）", dataType = "Long")
    private Long userInAmountDay;

    @ApiModelProperty( value = "同卡入金单日最高笔数", dataType = "Long")
    private Long userInCountDay;

    @ApiModelProperty( value = "同卡入金单月最高限额（分）", dataType = "Long")
    private Long userInAmountMonth;

    @ApiModelProperty( value = "同卡入金单日最高笔数", dataType = "Long")
    private Long userInCountMonth;
    
    
    // 新增统计成功失败总数指标 20210729
    @ApiModelProperty( value = "同人入金单日最高总笔数（成功+失败）", dataType = "Long")
    private Long userInTotalCountDay;
    
    @ApiModelProperty( value = "同人入金单月最高总笔数（成功+失败）", dataType = "Long")
    private Long userInTotalCountMonth; 

    

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

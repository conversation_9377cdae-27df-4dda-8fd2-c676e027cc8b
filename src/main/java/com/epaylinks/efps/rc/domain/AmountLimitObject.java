package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.rc.domain.cust.AttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel
@Data
public class AmountLimitObject {
    
    /**
     * 类型：001:身份证; 003：统一社会信用代码；005：商户编号
     */
    @ApiModelProperty(value = "类型：001：身份证；003：统一社会信用代码；005:商户编号；008：终端号码；009：个人账户", dataType = "String")
    private String type;

    private Integer nature; // 性质
    
    private String customerCode;
    
    private String customerName;
    
    @ApiModelProperty(value = "风险等级：LOW_LEVEL;MIDDLE_LEVEL;HIGHT_LEVEL", dataType = "String")
    private String rcLevel;
    
    private String rcLevelReason;
    
    /**
     * 主账户限额设置
     */
    private MainAmountLimit mainAmount;

    /**
     * 商户门户出金设置
     */
    private OutAmountLimit outAmountLimit;

    private InstAmountLimit instAmountLimit;
    
    /**
     * 业务限额设置(垫子代付设置)
     */
    private WithdrawAmountLimit withdrawAmount;

    /**
     * 交易卡限额设置
     */
    private CardAmountLimit cardAmount;

    /**
     * 用户限额设置
     */
    private UserAmountLimit userAmount;
    
    /**
     * 商户维度限额
     */
    private MerchantAmountLimit merchantAmountLimit;
    
    /**
     * 证件维度限额
     */
    private CertificateAmountLimit certificateAmountLimit;

    /**
     * 终端维度限额
     */
    private TermAmountLimit termAmountLimit;
    
    /**
     * 个人维度限额
     */
    private PersonAmountLimit personAmountLimit;

    private PlatCustomerAmountLimit platCustomerAmountLimit;

    private List<AttachmentVO> attachments;

    @ApiModelProperty(value = "来源（1：页面商户限额），batch:批量设置")
    private String source;
}

package com.epaylinks.efps.rc.domain.pas;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BkCardBin {
    private Long id;

    private Long recordId;

    @ApiModelProperty(value = "发卡行标识取值")
    private String cardNoRange;

    private Long cardNoRangeLen;

    @ApiModelProperty(value = "机构代码")
    private String issueBankNo;

    private String bankIcon;

    @ApiModelProperty(value = "发卡行名称")
    private String issueBankName;

    @ApiModelProperty(value = "卡名")
    private String cardName;

    private Long applyRange;

    @ApiModelProperty(value = "主帐号长度")
    private Long cardNoLen;

    @ApiModelProperty(value = "卡种")
    private String cardType;

    @ApiModelProperty(value = "主帐号长度")
    private String issueBankAccount;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    @ApiModelProperty(value="操作人", dataType = "String")
    private String operatorId;

    @ApiModelProperty(value="状态", dataType = "String")
    private String flag;

    @ApiModelProperty(value="是否银联品牌卡：1是，2否", dataType = "String")
    private String unionBrand;

    @ApiModelProperty(value="卡表父类：1路由类卡表，2业务类卡表", dataType = "String")
    private String parentType;

    @ApiModelProperty(value="卡表子类：银联标准卡表，ALLBIN；非标卡卡表，NONBIN；业务卡表，YWBIN；转账卡表，TFRBIN；农民工卡表，NMGBIN；单位结算卡卡表，DWBIN；", dataType = "String")
    private String sonType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getCardNoRange() {
        return cardNoRange;
    }

    public void setCardNoRange(String cardNoRange) {
        this.cardNoRange = cardNoRange;
    }

    public Long getCardNoRangeLen() {
        return cardNoRangeLen;
    }

    public void setCardNoRangeLen(Long cardNoRangeLen) {
        this.cardNoRangeLen = cardNoRangeLen;
    }

    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public String getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(String bankIcon) {
        this.bankIcon = bankIcon;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Long getApplyRange() {
        return applyRange;
    }

    public void setApplyRange(Long applyRange) {
        this.applyRange = applyRange;
    }

    public Long getCardNoLen() {
        return cardNoLen;
    }

    public void setCardNoLen(Long cardNoLen) {
        this.cardNoLen = cardNoLen;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getIssueBankAccount() {
        return issueBankAccount;
    }

    public void setIssueBankAccount(String issueBankAccount) {
        this.issueBankAccount = issueBankAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getUnionBrand() {
        return unionBrand;
    }

    public void setUnionBrand(String unionBrand) {
        this.unionBrand = unionBrand;
    }

    public String getParentType() {
        return parentType;
    }

    public void setParentType(String parentType) {
        this.parentType = parentType;
    }

    public String getSonType() {
        return sonType;
    }

    public void setSonType(String sonType) {
        this.sonType = sonType;
    }
}
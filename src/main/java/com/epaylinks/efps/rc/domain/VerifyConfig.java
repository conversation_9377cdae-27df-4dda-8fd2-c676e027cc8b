package com.epaylinks.efps.rc.domain;

import lombok.Data;

import java.util.Date;

@Data
public class VerifyConfig {
    /**
     * ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 商户名称
     */
    private String customerName;

    /**
     * 核验业务
     */
    private String checkBusiness;

    /**
     * 首选核验通道【0：系统默认；7：信联；8：羽山】
     */
    private String firstVerifyChannel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态【0：待审核；1：审核通过；2：审核不通过】
     */
    private String auditStatus;

    /**
     * 使用状态【0：未启用；1：已启用】
     */
    private String useStatus;

    /**
     * 操作人ID
     */
    private Long operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
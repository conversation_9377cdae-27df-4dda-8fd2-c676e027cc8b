package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "ResultData", description = "响应結果")
@Data
public class RcCalculateLog {
    /**
     * ID,主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 风控日志编号，日志唯一编号，RCL+年月日+时分秒+3位随机数；
     */
    @ApiModelProperty(value = "风控日志编号")
    @FieldAnnotation(fieldName = "风控日志编号")
    private String logIdentifier;

    /**
     * 触发时间
     */
    @ApiModelProperty(value = "触发时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName = "触发日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 风控对象类型
     */
    @ApiModelProperty(value = "对象")
    @FieldAnnotation(fieldName = "对象")
    private String businessTargetType;

    /**
     * 风控对象ID
     */
    @ApiModelProperty(value = "编号")
    @FieldAnnotation(fieldName = "编号")
    private String businessTargetId;

    /**
     * 风控对象名称
     */
    @ApiModelProperty(value = "名称")
    @FieldAnnotation(fieldName = "名称")
    private String businessTargetName;

    /**
     * 触发类型,001:限额类,002:黑名单类,003:状态类
     */
    @ApiModelProperty(value = "触发类型")
    @FieldAnnotation(fieldName = "触发类型")
    private String rcLimitType;

    /**
     * 触发风控指标
     */
    @ApiModelProperty(value = "触发风控指标")
    @FieldAnnotation(fieldName = "触发风控指标")
    private String rcLimitDefineCode;

    /**
     * 指标值
     */
    @ApiModelProperty(value = "指标值")
    @FieldAnnotation(fieldName = "指标值")
    private String rcLimitValue;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @FieldAnnotation(fieldName = "订单号")
    private String transactionNo;

    /**
     * 交易值
     */
    @ApiModelProperty(value = "交易值")
    @FieldAnnotation(fieldName = "交易值")
    private String transactionValut;

    /**
     * 累计值
     */
    @ApiModelProperty(value = "累计值")
    @FieldAnnotation(fieldName = "累计值")
    private String cumulativeValue;

    /**
     * 风控结果
     */
    @ApiModelProperty(value = "风控结果")
    @FieldAnnotation(fieldName = "风控结果")
    private String rcResult;

    /**
     * 错误反馈
     */
    @ApiModelProperty(value = "错误反馈")
    @FieldAnnotation(fieldName = "错误反馈")
    private String rcMessage;

    /**
     * 风控指标值类型
     */
    private String limitUnit;

    private String messageEncrypt;
}
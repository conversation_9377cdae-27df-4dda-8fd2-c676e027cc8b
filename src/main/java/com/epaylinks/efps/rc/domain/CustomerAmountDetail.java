package com.epaylinks.efps.rc.domain;

import io.swagger.annotations.ApiModelProperty;

public class CustomerAmountDetail {

    @ApiModelProperty(value = "商户号", dataType = "String")
    private String customerCode;

    @ApiModelProperty(value = "年入金额度", dataType = "Long")
    private Long yearInAmount;

    @ApiModelProperty(value = "月入金额度", dataType = "Long")
    private Long monthInAmount;

    @ApiModelProperty(value = "日入金额度", dataType = "Long")
    private Long dayInAmount;

    @ApiModelProperty( value = "入金单笔最高限额（分）", dataType = "Long")
    private Long inAmountSingleLimit;

    @ApiModelProperty( value = "入金单日最高限额（分）", dataType = "Long")
    private Long inAmountDayLimit;

    @ApiModelProperty( value = "入金单月最高限额（分）", dataType = "Long")
    private Long inAmountMonthLimit;

    @ApiModelProperty( value = "入金单年最高限额（分）", dataType = "Long")
    private Long inAmountYearLimit;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getYearInAmount() {
        return yearInAmount;
    }

    public void setYearInAmount(Long yearInAmount) {
        this.yearInAmount = yearInAmount;
    }

    public Long getMonthInAmount() {
        return monthInAmount;
    }

    public void setMonthInAmount(Long monthInAmount) {
        this.monthInAmount = monthInAmount;
    }

    public Long getDayInAmount() {
        return dayInAmount;
    }

    public void setDayInAmount(Long dayInAmount) {
        this.dayInAmount = dayInAmount;
    }

    public Long getInAmountSingleLimit() {
        return inAmountSingleLimit;
    }

    public void setInAmountSingleLimit(Long inAmountSingleLimit) {
        this.inAmountSingleLimit = inAmountSingleLimit;
    }

    public Long getInAmountDayLimit() {
        return inAmountDayLimit;
    }

    public void setInAmountDayLimit(Long inAmountDayLimit) {
        this.inAmountDayLimit = inAmountDayLimit;
    }

    public Long getInAmountMonthLimit() {
        return inAmountMonthLimit;
    }

    public void setInAmountMonthLimit(Long inAmountMonthLimit) {
        this.inAmountMonthLimit = inAmountMonthLimit;
    }

    public Long getInAmountYearLimit() {
        return inAmountYearLimit;
    }

    public void setInAmountYearLimit(Long inAmountYearLimit) {
        this.inAmountYearLimit = inAmountYearLimit;
    }
}

package com.epaylinks.efps.rc.domain;

import lombok.Data;

import java.util.Date;

/**
 * 操作日志表（RC_OPERATE_LOG 实体类）
 *
 * <AUTHOR> 2020-6-9
 */
@Data
public class RcOperateLog {

    public static final String tableName = "RC_OPERATE_LOG";
    /**
     * 主键
     */
    private int id;
    /**
     * 菜单ID,关联PAS_PERM表
     */
    private String permId;
    /**
     * 商户编号/其他号码,修改默认限额该字段填"/"
     */
    private String code;
    /**
     * 商户名称/其他名称,修改默认限额该字段填"/"
     */
    private String name;
    /**
     * 类型：1-新增；2-修改；3-删除
     */
    private String type;
    /**
     * 操作内容
     */
    private String operateContent;
    /**
     * 原值
     */
    private String origValue;
    /**
     * 修改后的值
     */
    private String newValue;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private Date operateTime;
}
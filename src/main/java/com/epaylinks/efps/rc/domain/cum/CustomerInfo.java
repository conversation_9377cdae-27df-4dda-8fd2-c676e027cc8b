package com.epaylinks.efps.rc.domain.cum;

import java.util.Date;

public class CustomerInfo {
    private Long infoId;

    private Long customerId;

    private String customerCode;

    private String name;

    private String shortName;

    private String mobile;

    private String telephone;

    private String businessAddress;

    private Short useUscc;

    private Short customerType;

    private String businessLicenseNo;

    private Date businessLicenseExpDate;

    private String registeredAddress;

    private String natTaxRegCerNo;

    private Date natTaxRegExpDate;

    private String locTaxRegCerNo;

    private Date locTaxRegCerExpDate;

    private String orgStructureCode;

    private String leaPersonName;

    private Short leaPersoniDentificationType;

    private String leaPersoniDentificationNo;

    private Date leaPerDenExpDate;

    private String parentCustomerCode;

    private String webAddress;

    private String icpLicenseNo;

    private String businessScope;

    private String industryDescription;

    private Integer employeeCount;

    private String businessType;

    private String areaCode;

    private String tradeCategory;

    private Long registeredCapital;
    
    /**
     * 客户类别
     */
    private String customerCategory;

    /**
     * 退汇异步通知商户的URL
     */
    private String thNotifyUrl;
    /**
     * 商户注册时间
     */
    private Date regDate;

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public Short getUseUscc() {
        return useUscc;
    }

    public void setUseUscc(Short useUscc) {
        this.useUscc = useUscc;
    }

    public Short getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Short customerType) {
        this.customerType = customerType;
    }

    public String getBusinessLicenseNo() {
        return businessLicenseNo;
    }

    public void setBusinessLicenseNo(String businessLicenseNo) {
        this.businessLicenseNo = businessLicenseNo;
    }

    public Date getBusinessLicenseExpDate() {
        return businessLicenseExpDate;
    }

    public void setBusinessLicenseExpDate(Date businessLicenseExpDate) {
        this.businessLicenseExpDate = businessLicenseExpDate;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getNatTaxRegCerNo() {
        return natTaxRegCerNo;
    }

    public void setNatTaxRegCerNo(String natTaxRegCerNo) {
        this.natTaxRegCerNo = natTaxRegCerNo;
    }

    public Date getNatTaxRegExpDate() {
        return natTaxRegExpDate;
    }

    public void setNatTaxRegExpDate(Date natTaxRegExpDate) {
        this.natTaxRegExpDate = natTaxRegExpDate;
    }

    public String getLocTaxRegCerNo() {
        return locTaxRegCerNo;
    }

    public void setLocTaxRegCerNo(String locTaxRegCerNo) {
        this.locTaxRegCerNo = locTaxRegCerNo;
    }

    public Date getLocTaxRegCerExpDate() {
        return locTaxRegCerExpDate;
    }

    public void setLocTaxRegCerExpDate(Date locTaxRegCerExpDate) {
        this.locTaxRegCerExpDate = locTaxRegCerExpDate;
    }

    public String getOrgStructureCode() {
        return orgStructureCode;
    }

    public void setOrgStructureCode(String orgStructureCode) {
        this.orgStructureCode = orgStructureCode;
    }

    public String getLeaPersonName() {
        return leaPersonName;
    }

    public void setLeaPersonName(String leaPersonName) {
        this.leaPersonName = leaPersonName;
    }

    public Short getLeaPersoniDentificationType() {
        return leaPersoniDentificationType;
    }

    public void setLeaPersoniDentificationType(Short leaPersoniDentificationType) {
        this.leaPersoniDentificationType = leaPersoniDentificationType;
    }

    public String getLeaPersoniDentificationNo() {
        return leaPersoniDentificationNo;
    }

    public void setLeaPersoniDentificationNo(String leaPersoniDentificationNo) {
        this.leaPersoniDentificationNo = leaPersoniDentificationNo;
    }

    public Date getLeaPerDenExpDate() {
        return leaPerDenExpDate;
    }

    public void setLeaPerDenExpDate(Date leaPerDenExpDate) {
        this.leaPerDenExpDate = leaPerDenExpDate;
    }

    public String getParentCustomerCode() {
        return parentCustomerCode;
    }

    public void setParentCustomerCode(String parentCustomerCode) {
        this.parentCustomerCode = parentCustomerCode;
    }

    public String getWebAddress() {
        return webAddress;
    }

    public void setWebAddress(String webAddress) {
        this.webAddress = webAddress;
    }

    public String getIcpLicenseNo() {
        return icpLicenseNo;
    }

    public void setIcpLicenseNo(String icpLicenseNo) {
        this.icpLicenseNo = icpLicenseNo;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getIndustryDescription() {
        return industryDescription;
    }

    public void setIndustryDescription(String industryDescription) {
        this.industryDescription = industryDescription;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getTradeCategory() {
        return tradeCategory;
    }

    public void setTradeCategory(String tradeCategory) {
        this.tradeCategory = tradeCategory;
    }

    public Long getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(Long registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

	public String getCustomerCategory() {
		return customerCategory;
	}

	public void setCustomerCategory(String customerCategory) {
		this.customerCategory = customerCategory;
	}

    public String getThNotifyUrl() {
        return thNotifyUrl;
    }

    public void setThNotifyUrl(String thNotifyUrl) {
        this.thNotifyUrl = thNotifyUrl;
    }

	public Date getRegDate() {
		return regDate;
	}

	public void setRegDate(Date regDate) {
		this.regDate = regDate;
	}
    
}
package com.epaylinks.efps.rc.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25 10:13
 */
public class VRcNoTxn {
    private Long archiveId;

    private String customerCode;

    private String customerName;

    private String customerCategory;

    private Integer customerType;

    private String ruleCode;

    private String limitDays;

    private Integer noTxnDays;

    private Date accountLastUpdateTime;

    public Long getArchiveId() {
        return archiveId;
    }

    public void setArchiveId(Long archiveId) {
        this.archiveId = archiveId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerCategory() {
        return customerCategory;
    }

    public void setCustomerCategory(String customerCategory) {
        this.customerCategory = customerCategory;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getLimitDays() {
        return limitDays;
    }

    public void setLimitDays(String limitDays) {
        this.limitDays = limitDays;
    }

    public Integer getNoTxnDays() {
        return noTxnDays;
    }

    public void setNoTxnDays(Integer noTxnDays) {
        this.noTxnDays = noTxnDays;
    }

    public Date getAccountLastUpdateTime() {
        return accountLastUpdateTime;
    }

    public void setAccountLastUpdateTime(Date accountLastUpdateTime) {
        this.accountLastUpdateTime = accountLastUpdateTime;
    }
}
package com.epaylinks.efps.rc.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 风控管理-可疑事件监控规则
 */
@Data
@ApiModel
public class RiskEventRule {

    private Long ruleId;    //主键ID
    private String ruleCode;    //规则代码
    private String ruleType;    //规则分类
    private String ruleDesc;    //规则描述
    private String ruleParam1;      //参数一
    private String ruleParam2;      //参数二
    private String triggerAction;       //措施
    private String ruleStatus;      //规则状态，01启用，02停用
    private String onTime;      //启用时间
    private String offTime;     //停用时间
    private String paramType;     //参数类型，01单参数，02双参数
    private String changePerson;     //修改人员

    private String ruleTypeName;  // 规则类型名称
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty( value ="修改时间", dataType = "Date")
    private Date updateTime;    //修改时间
    
    @ApiModelProperty( value ="审核状态[0待审核，1审核通过，2审核不通过]", dataType = "Short")
    private Short auditStatus;
    
    @ApiModelProperty( value ="审核意见", dataType = "String")
    private String remarks;

    private String fileName;
    private String uniqueId;
    private String url;
    private List<String> customerNoList;

    private String ruleCondition;

    private String scene;

    private Integer triggerType;

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleParam1() {
        return ruleParam1;
    }

    public void setRuleParam1(String ruleParam1) {
        this.ruleParam1 = ruleParam1;
    }

    public String getRuleParam2() {
        return ruleParam2;
    }

    public void setRuleParam2(String ruleParam2) {
        this.ruleParam2 = ruleParam2;
    }

    public String getTriggerAction() {
        return triggerAction;
    }

    public void setTriggerAction(String triggerAction) {
        this.triggerAction = triggerAction;
    }

    public String getRuleStatus() {
        return ruleStatus;
    }

    public void setRuleStatus(String ruleStatus) {
        this.ruleStatus = ruleStatus;
    }

    public String getOnTime() {
        return onTime;
    }

    public void setOnTime(String onTime) {
        this.onTime = onTime;
    }

    public String getOffTime() {
        return offTime;
    }

    public void setOffTime(String offTime) {
        this.offTime = offTime;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getChangePerson() {
        return changePerson;
    }

    public void setChangePerson(String changePerson) {
        this.changePerson = changePerson;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Short getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Short auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRuleTypeName() {
        return ruleTypeName;
    }

    public void setRuleTypeName(String ruleTypeName) {
        this.ruleTypeName = ruleTypeName;
    }

    public String getRuleCondition() {
        return ruleCondition;
    }

    public void setRuleCondition(String ruleCondition) {
        this.ruleCondition = ruleCondition;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}

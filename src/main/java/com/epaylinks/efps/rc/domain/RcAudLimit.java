package com.epaylinks.efps.rc.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RcAudLimit {
    /**
     * 主键
     */
    private Long limitId;

    /**
     * 申请值
     */
    private String limitValue;

    /**
     * 审核id
     */
    private Long audId;

    /**
     * 风控指标定义编码
     */
    private String defineCode;

    /**
     * 风控指标定义ID
     */
    private Long defineId;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 原额度
     */
    private String oldValue;

   
    @Override
    public String toString() {
        return "RcAudLimit{" +
                "limitId=" + limitId +
                ", limitValue='" + limitValue + '\'' +
                ", audId=" + audId +
                ", defineCode='" + defineCode + '\'' +
                ", defineId=" + defineId +
                ", defaultValue='" + defaultValue + '\'' +
                ", oldValue='" + oldValue + '\'' +
                '}';
    }
}
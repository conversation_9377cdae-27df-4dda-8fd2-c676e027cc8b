package com.epaylinks.efps.rc.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ChannelRiskRecord {
    
    @ApiModelProperty(value="ID", dataType = "Long")
    private Long id;

    @ApiModelProperty(value="商户编号", dataType = "String")
    private String customerCode;

    @ApiModelProperty(value="商户订单号", dataType = "String")
    private String transactionNo;

    @ApiModelProperty(value="支付宝流水号", dataType = "String")
    private String channelTradeNo;
    
    @ApiModelProperty(value="风险交易号样例", dataType = "String")
    private String tradeNos;

    @ApiModelProperty(value="渠道商PID", dataType = "String")
    private String channelId;

    @ApiModelProperty(value="上游商户号", dataType = "String")
    private String channelMchtNo;

    @ApiModelProperty(value="风险类型：:欺诈; 2:赌博; 3:套现; 4:套费率", dataType = "String")
    private String riskType;

    @ApiModelProperty(value="风险情况描述", dataType = "String")
    private String riskLevel;

    @ApiModelProperty(value="风险定位原因说明", dataType = "String")
    private String riskDesc;

    @ApiModelProperty(value="银行卡号", dataType = "String")
    private String bankCardNo;

    @ApiModelProperty(value="创建时间", dataType = "String")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}
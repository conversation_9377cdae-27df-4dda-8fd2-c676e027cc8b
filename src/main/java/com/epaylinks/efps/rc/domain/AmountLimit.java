package com.epaylinks.efps.rc.domain;

import lombok.Data;

@Data
public class AmountLimit {

	private Long inAmountSingle;

	private Long inAmountDay;

	private Long inAmountMonth;

	private Long inAmountYear;

	private Long outAmountSingle;

	private Long outAmountDay;

	private Long outAmountMonth;

	private Long outAmountYear;
	
    private Long withdrawAmountDay; // 单日垫资代付金额
    
    private Long cardOutAmountDay; // 同卡代付单日最高限额
    
    private Long cardOutCountDay; // 同卡代付单日最高笔数
    
    private Long cardOutAmountMonth; // 同卡代付单月最高限额
    
    private Long cardOutCountMonth; // 同卡代付单日最高笔数

    @Override
	public String toString() {
		return "AmountLimit{" +
				"inAmountSingle=" + inAmountSingle +
				", inAmountDay=" + inAmountDay +
				", inAmountMonth=" + inAmountMonth +
				", inAmountYear=" + inAmountYear +
				", outAmountSingle=" + outAmountSingle +
				", outAmountDay=" + outAmountDay +
				", outAmountMonth=" + outAmountMonth +
				", outAmountYear=" + outAmountYear +
                ", withdrawAmountDay=" + withdrawAmountDay +
                ", cardOutAmountDay=" + cardOutAmountDay +
                ", cardOutCountDay=" + cardOutCountDay +
                ", cardOutAmountMonth=" + cardOutAmountMonth +
                ", cardOutCountMonth=" + cardOutCountMonth +
				'}';
	}
}


package com.epaylinks.efps.rc.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @date  2024/1/4 11:49
 * @version 1.0
 */

/**
 * 风控可疑事件禁用终端
 */
public class RcCloseTerm {
    private Long id;

    private String customerCode;

    private String termCode;

    private Date closeTime;

    private Date reopenTime;

    private Date createTime;

    private Date updateTime;

    /**
     * 类型 0-终端 1-禁止入金
     */
    private Integer closeType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getTermCode() {
        return termCode;
    }

    public void setTermCode(String termCode) {
        this.termCode = termCode;
    }

    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    public Date getReopenTime() {
        return reopenTime;
    }

    public void setReopenTime(Date reopenTime) {
        this.reopenTime = reopenTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCloseType() {
        return closeType;
    }

    public void setCloseType(Integer closeType) {
        this.closeType = closeType;
    }
}
package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 风控管理-可疑事件监控记录汇总
 * <AUTHOR>
 * @date 2021-01-26
 *
 */
@Data
@ApiModel
public class RiskEventSummary {
    
    @ApiModelProperty( value = "触发日期", dataType = "String")
    @FieldAnnotation(fieldName = "触发日期")
    private String triggerDay;
    
    @ApiModelProperty( value = "触发商户编号", dataType = "String")
    @FieldAnnotation(fieldName = "触发商户编号")
    private String triggerCustCode;
    
    @ApiModelProperty( value = "触发商户名称", dataType = "String")
    @FieldAnnotation(fieldName = "触发商户名称")
    private String triggerCustName;
   
    @ApiModelProperty( value = "规则代码", dataType = "String")
    @FieldAnnotation(fieldName = "规则代码")
    private String ruleCode;
    
    @ApiModelProperty( value = "规则分类", dataType = "String")
    @FieldAnnotation(fieldName = "规则分类")
    private String ruleType;

    @ApiModelProperty( value = "规则描述", dataType = "String")
    @FieldAnnotation(fieldName = "规则描述")
    private String ruleDesc;
    
    @ApiModelProperty( value = "触发次数", dataType = "String")
    @FieldAnnotation(fieldName = "触发次数")
    private int dayCount;
    
}

package com.epaylinks.efps.rc.domain;

import java.util.Date;

import lombok.Data;

@Data
public class RcLimitLog {
	
    private Long id;
    /**
     * 交易流水号
     */
    private String transactionNo;
    /**
     * 对象id
     */
    private String businessTargetId;
    /**
     * 规则
     */
    private String definecode;
    /**
     * 交易值
     */
    private String currentValue;
    /**
     * 累计值
     */
    private String totalValue;
    /**
     * 规则限额
     */
    private String limitValue;
    /**
     * 银行卡号
     */
    private String bankCardNo;
    /**
     * 单位
     */
    private String unit;
    /**
     * 银行卡号
     */
    private Date createtime;

}
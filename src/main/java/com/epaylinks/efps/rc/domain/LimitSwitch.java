package com.epaylinks.efps.rc.domain;

import lombok.Data;

import java.util.Date;

@Data
public class LimitSwitch {
    /**
     * ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 限额指标（Daliy-Out-Limit:商户门户日限额；Single-Out-Limit：商户门户单笔限额）
     */
    private String limitIndex;

    /**
     * 指标值
     */
    private Long limitValue;

    /**
     * 指标状态
     */
    private String limitStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getLimitIndex() {
        return limitIndex;
    }

    public void setLimitIndex(String limitIndex) {
        this.limitIndex = limitIndex;
    }

    public Long getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Long limitValue) {
        this.limitValue = limitValue;
    }

    public String getLimitStatus() {
        return limitStatus;
    }

    public void setLimitStatus(String limitStatus) {
        this.limitStatus = limitStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
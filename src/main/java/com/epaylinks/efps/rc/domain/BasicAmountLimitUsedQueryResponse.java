package com.epaylinks.efps.rc.domain;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class BasicAmountLimitUsedQueryResponse extends CommonOuterResponse {

    @ApiModelProperty(value = "商户号", dataType = "String")
    private String customerCode;

    @ApiModelProperty(value = "年入金额度", dataType = "Long")
    private Long yearInAmount;

    @ApiModelProperty(value = "月入金额度", dataType = "Long")
    private Long monthInAmount;

    @ApiModelProperty(value = "日入金额度", dataType = "Long")
    private Long dayInAmount;


}

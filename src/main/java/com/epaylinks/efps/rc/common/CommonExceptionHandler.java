package com.epaylinks.efps.rc.common;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.command.RcCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/16 17:25
 */
@ControllerAdvice
public class CommonExceptionHandler {
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public @ResponseBody
    CommonOuterResponse methodArgumentNotValidException(MethodArgumentNotValidException e) {
        CommonOuterResponse response = new CommonOuterResponse();
        response.setReturnCode(RcCode.PARAM_ERROR.code);
        response.setReturnMsg(e.getBindingResult().getFieldErrors().stream().map(fe -> fe.getField() + fe.getDefaultMessage()).collect(Collectors.joining(";")));
        return response;
    }
}

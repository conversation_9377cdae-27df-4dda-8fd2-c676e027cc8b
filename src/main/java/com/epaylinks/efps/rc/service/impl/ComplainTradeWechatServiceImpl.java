package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.dao.ComplainTradeMapper;
import com.epaylinks.efps.rc.domain.ComplainTrade;
import com.epaylinks.efps.rc.service.ComplainTradeWechatService;
import com.epaylinks.efps.rc.vo.ComplainImportDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ComplainTradeWechatServiceImpl implements ComplainTradeWechatService {

    private static final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private CommonLogger logger;

    @Autowired
    private ComplainTradeMapper complainTradeMapper;

    @Override
    public Map<Integer, BatchDetail> importWechatRiskStoreData(ComplainImportDataVo importDataVo) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String batchNo = importDataVo.getBatchNo();
        int colCount = importDataVo.getTitleList().size(); // 以表头列数为数据列数校验
        if (Objects.isNull(importDataVo) || Objects.isNull(importDataVo.getTitleList()) || Objects.isNull(importDataVo.getDataMap())) {
            throw new AppException(RcCode.PARAM_ERROR.code, "微信风险商户批量导入,解析数据为空");
        }
        logger.printMessage("准备进行微信风险商户，批量数据解析..");
        // 解析来自批量导入的数据
        for (Iterator iterator = importDataVo.getDataMap().keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            ComplainTrade complainTrade = new ComplainTrade();
            try{
                List<String> dataList = importDataVo.getDataMap().get(rowNo);
                //上游商户号
                complainTrade.setChannelMchId(dataList.get(0).trim());
                if(StringUtils.isBlank(dataList.get(0))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "上游商户号为空");
                }
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            "微信风险商户批量导入" + ":" + (rowNo + 1) + "行缺少数据");
                }
                //将excel数据设置到对象中
                complainTrade.setBatchNo(batchNo);
                complainTrade.setChannelType("微信");//上游机构
                //处理时间
                if(StringUtils.isBlank(dataList.get(4))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "处理时间为空");
                }
                String riskTime = dataList.get(4).trim();
                try{
                    Boolean flag = checkDateParam(riskTime);
                    if (!flag) {
                        throw new AppException(RcCode.PARAM_ERROR.code,
                                RcCode.PARAM_ERROR.message + ":处理时间格式错误");
                    }
                    if (riskTime.length() > 10) {
                        complainTrade.setRiskTime(dateFormat.parse(riskTime));
                    } else {
                        complainTrade.setRiskTime(sdf.parse(riskTime));
                    }
                }catch (Exception e){
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            RcCode.PARAM_ERROR.message + ":处理时间格式错误.");
                }
                //风险类型
                if(StringUtils.isBlank(dataList.get(7))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "风险类型为空");
                }
                complainTrade.setRiskDesc(dataList.get(7));
                //处理方法
                if(StringUtils.isBlank(dataList.get(5))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "处理方法为空");
                }
                complainTrade.setComplainContent(dataList.get(5));
                complainTrade.setUniRemark("WeChatStore"+riskTime+complainTrade.getChannelMchId());
                if(complainTradeMapper.hasComplainRemark(complainTrade.getUniRemark())){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "上游记录已存在");
                }
                insertRecord(complainTrade);
                result.put(rowNo, buildDetail(rowNo, dataList.get(0), (short)0, complainTrade.getChannelMchId(), "处理成功"));
            }catch (Exception e){
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return result;
    }

    /**
     * 插入上游风险投诉记录
     * @param complainTrade
     */
    private void insertRecord(ComplainTrade complainTrade){
        List<ComplainTrade> list = complainTradeMapper.selectCustByChannel(complainTrade.getChannelMchId());
        if(list!=null && list.size()>0) {
            ComplainTrade custVo = list.get(0);
            complainTrade.setCustomerCode(custVo.getCustomerCode());
            ComplainTrade custInfo = complainTradeMapper.selectByCust(complainTrade.getCustomerCode());
            complainTrade.setCustName(custInfo.getCustName());
            complainTrade.setAgentCustomerCode(custInfo.getAgentCustomerCode());
            complainTrade.setAgentCustName(custInfo.getAgentCustName());
            complainTrade.setPlatCustomerCode(custInfo.getPlatCustomerCode());
            complainTrade.setPlatCustName(custInfo.getPlatCustName());
            complainTrade.setBusinessMan(custInfo.getBusinessMan());
        }
        Long id = complainTradeMapper.selectIdFromSeq();
        complainTrade.setCtId(id);
        complainTrade.setCreateTime(new Date());
        complainTradeMapper.insert(complainTrade);
    }

    /**
     * 封装导出明细
     * @param rowNo
     * @param rowName
     * @param status
     * @param relateId
     * @param remarks
     * @return
     */
    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    /**
     * 校验传入日期
     * @param param
     * @return
     */
    private Boolean checkDateParam(String param) {
        // yyyy-MM-dd || yyyy-MM-dd HH:mm:ss
        String regex = "^[123]\\d{3}-([1-9]|([0][1-9])|([1][0-2]))-([1-9]|([012]\\d)|([3][01]))" +
                "(\\s" +
                "([0-9]|([0][0-9])|([1]\\d)|([2][0-4]))" +
                ":([0-9]|([0][0-9])|([12345][0-9]))" +
                ":([0-9]|([0][0-9])|([12345][0-9]))" +
                ")?";
        Pattern pattern= Pattern.compile(regex);
        Matcher matcher = pattern.matcher(param);
        return matcher.matches();
    }

    @Override
    public Map<Integer, BatchDetail> importWechatComplaindata(ComplainImportDataVo importDataVo) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String batchNo = importDataVo.getBatchNo();
        int colCount = importDataVo.getTitleList().size(); // 以表头列数为数据列数校验
        if (Objects.isNull(importDataVo) || Objects.isNull(importDataVo.getTitleList()) || Objects.isNull(importDataVo.getDataMap())) {
            throw new AppException(RcCode.PARAM_ERROR.code, "微信投诉信息批量导入,解析数据为空");
        }
        logger.printMessage("准备进行微信投诉信息，批量数据解析..");
        // 解析来自批量导入的数据
        for (Iterator iterator = importDataVo.getDataMap().keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            ComplainTrade complainTrade = new ComplainTrade();
            try{
                List<String> dataList = importDataVo.getDataMap().get(rowNo);
                complainTrade.setChannelMchId(dataList.get(2).trim());
                //上游商户号
                if(StringUtils.isBlank(dataList.get(2))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "商户识别码为空");
                }
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            "微信投诉信息批量导入" + ":" + (rowNo + 1) + "行缺少数据");
                }
                //将excel数据设置到对象中
                complainTrade.setBatchNo(batchNo);
                complainTrade.setChannelType("微信");//上游机构
                if(StringUtils.isBlank(dataList.get(16))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "投诉时间为空");
                }
                try{
                    String riskTime = dataList.get(16).trim();
                    Boolean flag = checkDateParam(riskTime);
                    if (!flag) {
                        throw new AppException(RcCode.PARAM_ERROR.code,
                                RcCode.PARAM_ERROR.message + ":投诉时间格式错误");
                    }
                    if (riskTime.length() > 10) {
                        complainTrade.setRiskTime(dateFormat.parse(riskTime));//风险识别时间
                    } else {
                        complainTrade.setRiskTime(sdf.parse(riskTime));
                    }
                }catch (Exception e){
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            RcCode.PARAM_ERROR.message + ":投诉时间格式错误.");
                }
                try{
                    String transTime = dataList.get(17);
                    if(!StringUtils.isBlank(transTime)){
                        transTime = transTime.trim();
                        Boolean flag = checkDateParam(transTime);
                        if (!flag) {
                            throw new AppException(RcCode.PARAM_ERROR.code,
                                    RcCode.PARAM_ERROR.message + ":交易时间格式错误");
                        }
                        if (transTime.length() > 10) {
                            complainTrade.setTradeTime(dateFormat.parse(transTime));//交易时间
                        } else {
                            complainTrade.setTradeTime(sdf.parse(transTime));
                        }
                    }
                }catch (Exception e){
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            RcCode.PARAM_ERROR.message + ":交易时间格式错误");
                }
                //订单号
                if(StringUtils.isBlank(dataList.get(7))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "商户单号为空");
                }
                complainTrade.setOrderNo(dataList.get(7));//订单号
                //金额
                if(StringUtils.isBlank(dataList.get(12))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "订单金额为空");
                }
                complainTrade.setAmount(dataList.get(12));//金额
                //投诉类型
                if(StringUtils.isBlank(dataList.get(10))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "投诉类型为空");
                }
                complainTrade.setRiskDesc(dataList.get(10));//风险描述
                //投诉内容
                if(StringUtils.isBlank(dataList.get(11))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "投诉内容为空");
                }
                //问题类型
                String qus = "";
                if(!StringUtils.isBlank(dataList.get(9))){
                    qus = dataList.get(9).trim()+"：";
                }
                complainTrade.setComplainContent(qus+dataList.get(11).trim());//投诉内容
                //用户手机
                if(StringUtils.isBlank(dataList.get(19))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "联系方式为空");
                }
                complainTrade.setUserMobile(dataList.get(19));//用户手机
                complainTrade.setUniRemark("WeChatComplain"+complainTrade.getOrderNo());
                if(complainTradeMapper.hasComplainRemark(complainTrade.getUniRemark())){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "上游记录已存在");
                }
                insertRecord(complainTrade);
                result.put(rowNo, buildDetail(rowNo, dataList.get(2), (short)0, complainTrade.getChannelMchId(), "处理成功"));

            }catch (Exception e){
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return result;
    }

}

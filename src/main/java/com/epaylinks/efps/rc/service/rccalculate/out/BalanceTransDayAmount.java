package com.epaylinks.efps.rc.service.rccalculate.out;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("BALANCE_TRANS_DAY_AMOUNT")
public class BalanceTransDayAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        if (Constants.BusinessCode.BALANCE_TRANS.code.equals(map.get("businessCode"))) {
            rcCalculteBasic.calculateValueOutAmount(rcLimit, map, RcCalculteBasic.RcCalcDateType.DAY);
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        rcCalculteBasic.resetAmount(rcLimit, RcCalculteBasic.RcCalcDateType.DAY);
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (Constants.BusinessCode.BALANCE_TRANS.code.equals(rcCalculateRequest.getBusinessCode())) {
            return rcCalculteBasic.calculateOutAmount(rcLimit, rcCalculateRequest, RcCalculteBasic.RcCalcDateType.DAY);
        } else {
            return CommonOuterResponse.SUCCEE;
        }
    }
}

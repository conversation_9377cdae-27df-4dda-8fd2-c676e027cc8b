package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.LimitAttachmentMapper;
import com.epaylinks.efps.rc.domain.LimitAttachmentWithBLOBs;
import com.epaylinks.efps.rc.domain.RcArchive;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LimitAttachmentService {
    @Autowired
    private LimitAttachmentMapper limitAttachmentMapper;

    @Autowired
    private LogService logService;

    public int save(LimitAttachmentWithBLOBs record) {
        return limitAttachmentMapper.insert(record);
    }

    public Long querySeq() {
        return limitAttachmentMapper.selectSeq();
    }

    public List<LimitAttachmentWithBLOBs> selectByArchiveTypeAndCode(Map paramMap) {
        return limitAttachmentMapper.selectByArchiveTypeAndCode(paramMap);
    }

    public int updateById(LimitAttachmentWithBLOBs record) {
        return limitAttachmentMapper.updateByPrimaryKeyWithBLOBs(record);
    }

    public int updateAttachmentStatus(RcArchive rcArchive,Boolean auditResult) {
        int i = 0;
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("archiveType",rcArchive.getArchiveType());
        paramMap.put("archiveCode",rcArchive.getArchiveCode());
        paramMap.put("auditStatus","0"); // 待审核
        List<LimitAttachmentWithBLOBs> limitAttachments = limitAttachmentMapper.selectByArchiveTypeAndCode(paramMap);
        if (limitAttachments == null || limitAttachments.size() == 0) {
//            logService.printLog("商户限额管理附件不存在");
            return i;
//            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message + "：商户限额管理附件");
        }
        LimitAttachmentWithBLOBs limitAttachmentWithBLOBs = limitAttachments.get(0);
        if (auditResult) {
            limitAttachmentWithBLOBs.setAuditStatus("1");
            i = limitAttachmentMapper.updateByPrimaryKeyWithBLOBs(limitAttachmentWithBLOBs);
        } else {
            limitAttachmentWithBLOBs.setAuditStatus("1");
            limitAttachmentWithBLOBs.setAttachmentData(limitAttachmentWithBLOBs.getOldData());
            i = limitAttachmentMapper.updateByPrimaryKeyWithBLOBs(limitAttachmentWithBLOBs);
        }
        return i;
    }
}

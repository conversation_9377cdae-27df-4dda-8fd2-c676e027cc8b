package com.epaylinks.efps.rc.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.business.pas.domain.User;
import com.epaylinks.efps.common.business.pas.service.PasService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.service.DataAuthService;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.PageResult;
import com.epaylinks.efps.rc.controller.request.AlipayPushRiskRequest;
import com.epaylinks.efps.rc.dao.ChannelRiskRecordMapper;
import com.epaylinks.efps.rc.domain.ChannelRiskRecord;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.txs.TxsPreOrder;
import com.epaylinks.efps.rc.service.ChannelRiskService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.vo.ChannelRiskRecordVo;

@Service
public class ChannelRiskServiceImpl implements ChannelRiskService {
    
    @Autowired
    private ChannelRiskRecordMapper channelRiskRecordMapper;
    
    @Autowired
    private SequenceService sequenceService;
    
    @Autowired
    private OtherService otherService;

    @Autowired
    private DataAuthService dataAuthService;

    @Override
    public PageResult<ChannelRiskRecordVo> pageQuery(Map map,Long userId) {

        List<ChannelRiskRecordVo> responseList = new ArrayList<>();

        dataAuthService.setMapParam(map,userId);

        int total = channelRiskRecordMapper.queryCount(map);
        List<ChannelRiskRecord> riskList =  channelRiskRecordMapper.queryList(map);
        
        if (riskList != null ) {
            riskList.forEach(riskRecord -> {
                
                ChannelRiskRecordVo vo = new ChannelRiskRecordVo();
                try {
                    BeanUtils.copyProperties(vo, riskRecord);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                
                Customer customer = otherService.queryCustomerByCustomerNo(riskRecord.getCustomerCode());
                if (customer != null) {
                    vo.setCustomerName(customer.getName());
                    // 查询平台商
                    if (customer.getPlatCustomerNo() != null) { 
                        Customer platCustomer = otherService.queryCustomerByCustomerNo(customer.getPlatCustomerNo());
                        if (platCustomer != null) {
                            vo.setPlatCustomerCode(platCustomer.getCustomerNo());
                            vo.setPlatCustomerName(platCustomer.getName());
                        }
                    }
                    // 查询上一级服务商
                    Customer serviceCustomer = otherService.queryUpperServiceCustomer(customer.getCustomerNo());
                    if (serviceCustomer != null) {
                        vo.setServiceCustomerCode(serviceCustomer.getCustomerNo());
                        vo.setServiceCustomerName(serviceCustomer.getName());
                    }
                }
                responseList.add(vo);
            });
        }
        
        return PageResult.success(responseList, total, "查询成功");

    }
    

    @Override
    public void saveAlipayRisk(AlipayPushRiskRequest alipayPushRiskRequest) {
        
        // TODO 验签
        String sign = alipayPushRiskRequest.getSign();
        
        ChannelRiskRecord riskRecord = new ChannelRiskRecord();
        riskRecord.setId(sequenceService.nextValue("channel_risk_record"));
        riskRecord.setCreateTime(new Date());
        
        riskRecord.setBankCardNo(alipayPushRiskRequest.getBank_card_no());
        riskRecord.setChannelId(alipayPushRiskRequest.getPid());
        riskRecord.setChannelMchtNo(alipayPushRiskRequest.getSmid());
        riskRecord.setChannelTradeNo(alipayPushRiskRequest.getTradeNos());
        riskRecord.setRiskLevel(alipayPushRiskRequest.getRisklevel());
        riskRecord.setRiskDesc(alipayPushRiskRequest.getRiskDesc());
        riskRecord.setTradeNos(alipayPushRiskRequest.getTradeNos());
        riskRecord.setRiskType(getEpspRiskType(alipayPushRiskRequest.getRisktype()));

        // 通过流水号查询商户号、商户订单号
        TxsPreOrder txsOrder = otherService.queryTxsOrderByChannelOrder(alipayPushRiskRequest.getAlipayTradeNos());
        if (txsOrder != null ) {
            riskRecord.setCustomerCode(txsOrder.getCustomerCode());
            riskRecord.setTransactionNo(txsOrder.getOutTradeNo()); // 设置商户订单号，非交易订单号
        }
        
        // 风险数据保存
        channelRiskRecordMapper.insert(riskRecord);
        
    }
    
    private String getEpspRiskType (String riskType) {
        String risktype = null;
        // 1:欺诈; 2:赌博; 3:套现; 4:套费率
        switch (riskType){
            case "欺诈" : risktype = "1"; break;
            case "赌博" : risktype = "2"; break;
            case "套现" : risktype = "3"; break;
            case "套费率" : risktype = "4"; break;
            default : break;
        }
        return risktype;
    }

}

package com.epaylinks.efps.rc.service.rccalculate.in;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 同一证件单日交易累计限额（是指相同证件的所有商户总限额），
 * 交易包括：所有收单（包括商户门户充值收单）和分账
 * 
 * <AUTHOR>
 * @date 2021-08-10
 *
 */
@Service("Cert-Day-Max-IN-Amount")
public class CertDayMaxInAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void reset(RcLimit rcLimit) {

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                ? rcLimit.getBusinessTagerId()
                : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(),
                RcDateUtils.getLastDay());

        // 1. 备份昨日数据到数据库
        long dayInAmount = redisTemplate.opsForValue().increment(lastKey, 0L);
        Date date = new Date();
        if (dayInAmount > 0) {
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), tagerId,
                    rcLimit.getDefineCode(), dayInAmount + "", date, date, RcLimitData.Status.FINISHED.code,
                    RcDateUtils.getLastDay());
            rcLimitDataMapper.insert(rcLimitData);
        }

        // 2.创建今日redis 初始数据
        Object object = redisTemplate.opsForValue().get(key);
        if (object == null) {
            redisTemplate.opsForValue().set(key, 0L, 32, TimeUnit.DAYS);
        } else {
            redisTemplate.expire(key, 32, TimeUnit.DAYS);
        }
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                ? rcLimit.getBusinessTagerId()
                : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
        if (amountStr == null) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        Long amount = Long.parseLong(amountStr);
        long dayInAmount = redisTemplate.opsForValue().increment(key, 0);
        if (amount + dayInAmount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(),
                    rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(), dayInAmount + "",
                    amount + "", true, "RC交易受限", rcArchive.getArchiveType());
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String payState = map.get("payState");

        if (payState.equals("00")) {
            String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                    ? rcLimit.getBusinessTagerId()
                    : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
            String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
            long amount = Long.parseLong(map.get(RcConstants.RcIndex.AMOUNT.code));
            redisTemplate.opsForValue().increment(key, amount);
        }

    }

}

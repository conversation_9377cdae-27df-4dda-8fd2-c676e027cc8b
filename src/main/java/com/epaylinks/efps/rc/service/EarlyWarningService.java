package com.epaylinks.efps.rc.service;

/**
 * 预警规则接口服务（临时过检使用）
 * <AUTHOR>
 * @date 2020-11-26
 *
 */
public interface EarlyWarningService {

    /**
     * 检测单笔交易是否预警（风控校验时）
     * @param businessTargetId
     * @param businessType
     * @param businessCode
     * @param amount
     */
    public void checkSingleAmountWarning(String businessTargetId, String businessType, String businessCode, Long amount);
    
    /**
     * 检测交易订单是否预警（订阅kafka消息记录订单时）
     * @param businessTargetId
     * @param businessType
     * @param businessCode
     * @param amount
     */
    public void checkTxsOrderAmountWarning(String businessTargetId, String businessType, String businessCode, Long amount);
  
    /**
     * 检测触发风控日志是否预警（黑白名单、限额）
     * @param businessTargetId
     * @param rcLimitType
     */
    public void checkCalculateLimitWarning(String businessTargetId, String rcLimitType);

    /**
     * 登录检测是否触发预警
     * @param businessTargetId
     * @param businessType
     */
    public void checkLoginCountWarning(String businessTargetId, String businessType);
    
}

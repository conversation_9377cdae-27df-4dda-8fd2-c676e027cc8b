package com.epaylinks.efps.rc.service.rccalculate.account;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 账户状态计算指标
 *
 * <AUTHOR>
 */
@Service("ACCOUNT-STATUS")
public class AccountStatusRc implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    //	@Logable(businessTag = "ACCOUNT-STATUS:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
        // TODO Auto-generated method stub

    }

    @Override
    public void reset(RcLimit rcLimit) {
        // TODO Auto-generated method stub

    }

    @Logable(businessTag = "ACCOUNT-STATUS:calculate")
    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

        String businessType = rcCalculateRequest.getBusinessType();
        String status = rcLimit.getLimitValue();

        if ("logIn".equals(rcCalculateRequest.getBusinessType())) {
            return CommonOuterResponse.SUCCEE;
        }

        if ("1".equals(status)) {
            RcArchive rcArchive = rcArchiveService.selectByCodeOrName(rcLimit.getBusinessTagerId(), null);
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(), rcArchive.getArchiveName(), rcLimit.getDefineCode(), "冻结", "/", rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code), false, "RC账户冻结", rcArchive.getArchiveType());
            return "RC账户冻结";
        }

        if ("2".equals(status)) {
            if ("insidePayOut".equals(businessType) || "withDraw".equals(businessType) || "refund".equals(businessType)) {
                RcArchive rcArchive = rcArchiveService.selectByCodeOrName(rcLimit.getBusinessTagerId(), null);
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(), rcArchive.getArchiveName(), rcLimit.getDefineCode(), "止付", "/", rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code), false, "RC账户止付", rcArchive.getArchiveType());
                return "RC账户止付"; // 注意： 交易系统根据该返回信息判断是否设置分账状态待处理，后续优化修改为code后，修改记得通知交易对应修改 。
            }
        }

        // 禁止入金
        if ("3".equals(status)) {
            if (Constants.rcBusinessType.INSIDE_PAY_IN.code.equals(businessType)
                    || Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)) { // 网关支付属于入金类
                RcArchive rcArchive = rcArchiveService.selectByCodeOrName(rcLimit.getBusinessTagerId(), null);
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(),
                        rcArchive.getArchiveName(), rcLimit.getDefineCode(), "禁止入金", "/",
                        rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code), false, "RC禁止入金",
                        rcArchive.getArchiveType());
                return "RC禁止入金";
            }
        }

        return CommonOuterResponse.SUCCEE;
    }


}

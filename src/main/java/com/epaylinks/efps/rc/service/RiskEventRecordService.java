package com.epaylinks.efps.rc.service;

import java.util.*;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.dao.RiskEventRecordMapper;
import com.epaylinks.efps.rc.domain.RiskEventRecord;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.domain.RiskEventSummary;

/**
 * 风控管理-可疑事件监控记录业务类
 */
@Service
public class RiskEventRecordService {

    @Autowired
    private RiskEventRecordMapper riskEventRecordMapper;

    @Autowired
    private RiskEventRecordService  self;

    @Autowired
    private RiskEventRuleService  riskEventRuleService;
    
    @Autowired
    private OtherService otherService;

    @Autowired
    private DataAuthService dataAuthService;

    @Logable(businessTag = "RiskEventRecordService.logException")
    public void logException(Exception e){
    }

    @Logable(businessTag = "RiskEventRecordService.logInfo")
    public void logInfo(String str){
    }

    /**
     * 分页查询可疑事件监控记录
     * @param startNum
     * @param endNum
     * @param ruleType
     * @param triggerAction
     * @param searchKey
     * @param startTime
     * @param endTime
     * @param user
     * @return
     */
    public PageResult<RiskEventRecord> pageQueryEventRecord(int startNum, int endNum,
                                                            String ruleType, String triggerAction, String searchKey,
                                                            String startTime,String endTime, String user,Long userId){
        if(user==null || startNum < 0 || endNum < 0){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message);
        }
        self.logInfo(user+"分页查询可疑事件监控记录");
        PageResult<RiskEventRecord> pageResult = new PageResult<>();
        // 加数据权限
        Map<String,Object> param = new HashMap<>();
        param.put("startNum",startNum);
        param.put("endNum",endNum);
        param.put("ruleType",ruleType);
        param.put("triggerAction",triggerAction);
        param.put("searchKey",searchKey);
        param.put("startTime",startTime);
        param.put("endTime",endTime);

        dataAuthService.setMapParam(param,userId);

        int total = riskEventRecordMapper.pageCountEventRecord(param);
        pageResult.setTotal(total);
        List<RiskEventRecord> rows = null;
        if (total == 0) {
            pageResult.setRows(new ArrayList<>());
        }else {
            rows = riskEventRecordMapper.pageQueryEventRecord(param);
            pageResult.setRows(rows);
        }
        pageResult.setCode(CommonOuterResponse.SUCCEE);
        return pageResult;
    }

    /**
     * 新增可疑事件监控记录
     * @param ruleCode
     * @param triggerCustCode
     * @param triggerCustName
     * @param triggerValue
     * @return
     */
    public int addEventRecord(String ruleCode, String triggerCustCode, String triggerCustName, String triggerValue){
        RiskEventRule rule = riskEventRuleService.getRule(ruleCode);
        if(rule==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":操作规则不存在");
        }
        RiskEventRecord data = new RiskEventRecord();
        Long dataId = riskEventRecordMapper.selectIdFromSeq();
        data.setEventId(dataId);
        data.setRuleCode(rule.getRuleCode());
        data.setTriggerTime(new Date());
        data.setRuleType(otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", rule.getRuleType(), true));
        String ruleDesc = rule.getRuleDesc();
        String ruleDescFill = "";
        if("01".equals(rule.getParamType())){
            ruleDescFill = ruleDesc.replace("[param1]",rule.getRuleParam1());
        }else if("02".equals(rule.getParamType())){
            ruleDescFill = ruleDesc.replace("[param1]",rule.getRuleParam1());
            ruleDescFill = ruleDescFill.replace("[param2]",rule.getRuleParam2());
        } else {
            ruleDescFill = ruleDesc;
        }
        data.setRuleDesc(ruleDescFill);
        data.setTriggerCustCode(triggerCustCode);
        data.setTriggerCustName(triggerCustName);
        data.setTriggerValue(triggerValue);
        data.setTriggerAction(rule.getTriggerAction());
        data.setParamType(rule.getParamType());
        int res = riskEventRecordMapper.addEventRecord(data);
        return res;
    }

    /**
     * 根据id批量更新预警记录
     * @param ids
     * @param dealResult
     * @param userId
     * @return
     */
    public int batchModify(String ids, String dealResult, Long userId) {

        int count = 0;
        if (ids == null ) {
            return count;
        }
        String[] idArray = ids.split(",");
        if (idArray.length < 1) {
            return count;
        }
        for (String id : idArray) {
            try {
                RiskEventRecord record = riskEventRecordMapper.selectByPrimaryKey(Long.parseLong(id));
                if (record != null) {
                    record.setDealResult(dealResult);
                }
                count += riskEventRecordMapper.updateByPrimaryKey(record);
            } catch (Exception e) {
                self.logException(e);
            }
        }
        return count;
    }
    
    /**
     * 分页查询可疑事件监控汇总
     * @return
     */
    public PageResult<RiskEventSummary> pageRiskEventSummary(Map<String, Object> paramMap, boolean download){
        
        PageResult<RiskEventSummary> pageResult = new PageResult<>();
        int total = riskEventRecordMapper.countEventSummary(paramMap);
        pageResult.setTotal(total);
        if (total == 0) {
            pageResult.setRows(new ArrayList<>());
        } else {
            if (download) { // 导出时，重设分页
                paramMap.put("startNum", 1);
                paramMap.put("endNum", total);
            }
            List<RiskEventSummary> rows = riskEventRecordMapper.pageEventSummary(paramMap);
            pageResult.setRows(rows);
        }
        pageResult.setCode(CommonOuterResponse.SUCCEE);
        return pageResult;
    }

    public List<RiskEventSummary> exportPage(Map<String, Object> paramMap) {
        List<RiskEventSummary> rows = riskEventRecordMapper.pageEventSummary(paramMap);
        return rows;
    }

}

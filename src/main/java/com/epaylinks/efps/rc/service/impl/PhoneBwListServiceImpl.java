package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.util.DesensitizeUtils;
import com.epaylinks.efps.rc.vo.AuditRecordVo;
import com.epaylinks.efps.rc.vo.PhoneBwListVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class PhoneBwListServiceImpl implements PhoneBwListService {
    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID 3009001 huangyuekun 黄月坤
    @Autowired
    private RcAuditRecordService rcAuditRecordService;
    @Autowired
    private OtherMapper otherMapper;
    @Autowired
    private RcArchiveService rcArchiveService;
    @Autowired
    private OtherService otherService;
    @Autowired
    private RcOperateLogService rcOperateLogService;
    @Autowired
    private CustService custService;
    @Autowired
    private LogService logService;

    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(10); // 创建一个线程池

    @Override
    @Transactional
    @Logable(businessTag = "PhoneBwListServiceImpl.saveAuditRecord")
    public RcAuditRecord saveAuditRecord(Long id, String oldVlue, String newValue, Long userId,String code) {
        String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        CommonOuterResponse response = new CommonOuterResponse();
        RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.PHONE_BW_LIST.code,id);
        if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
            throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
        }
        rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.PHONE_BW_LIST.code,
                id, oldVlue, newValue, code, userId,null);
        if (code.equals(RcConstants.AuditActionType.UPDATE.code)) {
            otherMapper.updateAuditStatusByBwId(new Short("2"),id);
        }
        Map transMap = JSONObject.parseObject(newValue,Map.class);
        String phone = (String) transMap.get("bwValue");
        // 添加待办
        try {
            if ("1".equals(flag)) {
                response = custService.checkRCItem("80103", DesensitizeUtils.getHiddenMobilePhone(phone) + "_手机白名单待审核", "MOBILE_WHITELIST_MANAGEMENT",
                        phone,"record-" + rcAuditRecord.getId(),userId);
            } else if ("0".equals(flag)) {
                phoneAutoAudit(String.valueOf(rcAuditRecord.getId()),new Short("1"),defaultAuditUserId);
            }

        } catch (Exception e) {
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }
        if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }

        return rcAuditRecord;
    }

    @Override
    public RcAuditRecord queryRecord(Long bwId, String type, Long userId) {
        return rcAuditRecordService.queryAuditRecord(type,bwId);
    }

    @Override
    @Transactional
    public String auditRecord(String rIds, Short auditResult,String comments,Long userId,String autoAudit) {
        CommonOuterResponse response = new CommonOuterResponse();
        if (StringUtils.isEmpty(rIds)) {
            throw new AppException(RcCode.PARAM_ERROR.code,"：不能为空");
        }
        String[] strs = rIds.split(",");
        if (strs != null && strs.length > 0) {
            for (int i = 0; i < strs.length; i++) {
                Long rId;
                try {
                    rId = Long.parseLong(strs[i]);
                } catch (Exception e) {
                    throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
                }
                RcAuditRecord rcAuditRecord = rcAuditRecordService.selectByPrimaryId(rId);
                if (rcAuditRecord == null) {
                    throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
                }
                if (rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) != 0) {
                    throw new AppException(RcCode.AUD_EXCEPTION.code,RcCode.AUD_EXCEPTION.message);
                }
                // 更新审核记录状态
//                rcAuditRecordService.updateAuditRecordStatus(rId,auditResult,userId);
                rcAuditRecordService.updateAuditRecordStatus(rcAuditRecord.getTargetType(),rcAuditRecord.getTargetId(),auditResult,userId,comments);
                Map<String,Object> paramsMap = new HashMap<>();
                PhoneBwListVo phoneBwListVo = JSONObject.parseObject(rcAuditRecord.getNewValue(),PhoneBwListVo.class);
                // 新增
                int result = 0;
                if (rcAuditRecord.getActionType().equals(RcConstants.AuditActionType.CREATE.code)) {
                    if (auditResult.compareTo(RcConstants.AuditStatus.SUCCESS.code) == 0) { // 审核通过
                        paramsMap.put("id",phoneBwListVo.getId());
                        paramsMap.put("phoneStatus",new Short("1"));
                        paramsMap.put("auditStatus",new Short("1"));
                        while (result == 0) {
                            result = otherMapper.updateAuditByBwId(paramsMap);
                        }
                    } else if (auditResult.compareTo(RcConstants.AuditStatus.FAIL.code) == 0) { // 驳回
                        paramsMap.put("id",phoneBwListVo.getId());
                        paramsMap.put("phoneStatus",new Short("2"));
                        paramsMap.put("auditStatus",new Short("1"));
                        while (result == 0) {
                            result = otherMapper.updateAuditByBwId(paramsMap);
                        }
                    }
                }
                // 更新
                if (rcAuditRecord.getActionType().equals(RcConstants.AuditActionType.UPDATE.code)) {
                    if (auditResult.compareTo(RcConstants.AuditStatus.SUCCESS.code) == 0) { // 审核通过
                        paramsMap.put("id",phoneBwListVo.getId());
                        paramsMap.put("auditStatus",new Short("1"));
                        paramsMap.put("bwValue",phoneBwListVo.getBwValue());
                        paramsMap.put("remark",phoneBwListVo.getRemark());
                        paramsMap.put("phoneStatus",phoneBwListVo.getPhoneStatus());
                        otherMapper.updateAuditByBwId(paramsMap);
                    } else if (auditResult.compareTo(RcConstants.AuditStatus.FAIL.code) == 0) { // 驳回
                        paramsMap.put("id",phoneBwListVo.getId());
                        paramsMap.put("auditStatus",new Short("1"));
                        otherMapper.updateAuditByBwId(paramsMap);
                    }
                }
                try {
                    if ("1".equals(autoAudit)) {
                        response = custService.updateRCItem("MOBILE_WHITELIST_MANAGEMENT","record-" + rcAuditRecord.getId());
                    }
                } catch (Exception e) {
                    logService.printLog(e);
                    throw new AppException(response.getReturnCode(),response.getReturnMsg());
                }
            }
        }
        return "success";
    }

    @Override
    @Transactional
    public String saveAuditRecordList(List<AuditRecordVo> paramsList) {
        if (paramsList != null && paramsList.size() > 0) {
            for (int i = 0; i < paramsList.size(); i++) {
                AuditRecordVo auditRecordVo =  paramsList.get(i);
                saveAuditRecord(auditRecordVo.getId(),auditRecordVo.getOldValue(),auditRecordVo.getNewValue()
                        ,auditRecordVo.getUserId(),auditRecordVo.getCode(),auditRecordVo.getAuditTargetType());
            }
        }
        return "success";
    }

    @Override
    @Transactional
    public RcAuditRecord saveAuditRecord(Long id, String oldValue, String newValue, Long userId, String code, String auditTargetType) {
        CommonOuterResponse response = new CommonOuterResponse();
        RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(auditTargetType,id);
        if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
            throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
        }
        Map newMap = JSON.parseObject(newValue,Map.class);
        String uniqueId = (String) newMap.get("uniqueId");
        rcAuditRecord = rcAuditRecordService.saveAuditRecord(auditTargetType,
                id, oldValue, newValue, code, userId,uniqueId);
        if (auditTargetType.equals(RcConstants.AuditTargetType.TRANSACTION.code)) { // 保存交易位置
            // region 插入操作日志
            RcOperateLog log = new RcOperateLog();
            Date modifyDate = new Date();
            // 在风险档案菜单里
            Map oldMap = JSON.parseObject(oldValue,Map.class);
            String customerNo = (String)oldMap.get("customerNo");
            Customer customer = otherService.queryCustomerByCustomerNo(customerNo);
            String oldCheckPayLocation = StringUtils.isEmpty(
                    oldMap.get("checkPayLocation")) ? null : (String)oldMap.get("checkPayLocation");
            Integer oldPayLocationLimitRange = StringUtils.isEmpty(
                    oldMap.get("checkPayLocation")) ? null : (Integer) oldMap.get("payLocationLimitRange");

            String newCheckPayLocation = StringUtils.isEmpty(
                    newMap.get("checkPayLocation")) ? null : (String)newMap.get("checkPayLocation");
            Integer newPayLocationLimitRange = StringUtils.isEmpty(
                    newMap.get("checkPayLocation")) ? null : (Integer)newMap.get("payLocationLimitRange");

            // 添加待办
            if (customer == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
            }
            try {
                String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
                if ("1".equals(flag)) {
                    response = custService.checkRCItem("80801",customer.getCustomerNo() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
                            customer.getCustomerNo() + "," + RcConstants.AuditTargetType.TRANSACTION.code,"record-" + rcAuditRecord.getId(),userId);
                } else if ("0".equals(flag)) {
                    rcArchiveService.batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultAuditUserId,"0");
                }
            } catch (Exception e) {
                throw new AppException(RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.code,RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.message + ":cust");
            }


            if (oldCheckPayLocation == null || !oldCheckPayLocation.equals(newCheckPayLocation)) {
                log.setPermId("80201");
                log.setCode(customerNo);
                log.setName(customer.getName());
                log.setType("2");
                log.setOperator(String.valueOf(userId));
                log.setOperateTime(modifyDate);
                log.setOperateContent("保存交易设置");
                if (!StringUtils.isEmpty(oldCheckPayLocation)) {
                    if ("0".equals(oldCheckPayLocation)) {
                        oldCheckPayLocation = "不开启交易位置控制";
                    } else if ("1".equals(oldCheckPayLocation)) {
                        oldCheckPayLocation = "是且取本身参数";
                    } else if ("2".equals(oldCheckPayLocation)) {
                        oldCheckPayLocation = "是且取上级参数";
                    }
                }
                log.setOrigValue(oldCheckPayLocation);
                if ("0".equals(newCheckPayLocation)) {
                    newCheckPayLocation = "不开启交易位置控制";
                } else if ("1".equals(newCheckPayLocation)) {
                    newCheckPayLocation = "是且取本身参数";
                } else if ("2".equals(newCheckPayLocation)) {
                    newCheckPayLocation = "是且取上级参数";
                }
                log.setNewValue(newCheckPayLocation);
                rcOperateLogService.insert(log);
            }

            if (oldPayLocationLimitRange == null || !oldPayLocationLimitRange.equals(newPayLocationLimitRange)) {
                log.setId(0);
                log.setPermId("80201");
                log.setCode(customerNo);
                log.setName(customer.getName());
                log.setType("2");
                log.setOperator(String.valueOf(userId));
                log.setOperateTime(modifyDate);
                log.setOperateContent("保存交易设置");
                log.setOrigValue(oldPayLocationLimitRange + "");
                log.setNewValue(newPayLocationLimitRange + "");
                rcOperateLogService.insert(log);
            }
        }
        return rcAuditRecord;
    }

    @Override
    public CommonOuterResponse phoneAutoAudit(String records, Short auditResult, Long defaultUserId) {
        fixedThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    auditRecord(records,auditResult,"",defaultUserId,"0");
                } catch (AppException e) {
                    logService.printLog(e.getErrorCode() + "手机白名单自动审核失败：" + e.getErrorMsg());
                } catch (Exception e) {
                    logService.printLog("手机白名单自动审核失败：" + e.getMessage());
                }
            }
        });
        return CommonOuterResponse.success();
    }
}

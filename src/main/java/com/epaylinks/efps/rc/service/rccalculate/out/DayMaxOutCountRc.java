package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 提现单日最多笔数。
 * 
 * <AUTHOR>
 * @date 2022/01/13 11:34:10
 * @Description
 *
 */
@Service("Day-Max-OUT-Count")
public class DayMaxOutCountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 每日重置提现次数为0
        Date date = new Date();
        String businessTagerId = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId();
        String key = rcCalculteBasic.getKey(businessTagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
        String lastKey = rcCalculteBasic.getKey(businessTagerId, rcLimit.getDefineCode(), RcDateUtils.getLastDay());

        long dayOutCount = redisTemplate.opsForValue().increment(lastKey, 0L);
        if (dayOutCount > 0) {
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), businessTagerId,
                    rcLimit.getDefineCode(), dayOutCount + "", date, date, RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
            rcLimitDataMapper.insert(rcLimitData);
        }
        Object object = redisTemplate.opsForValue().get(key);
        if (object == null) {
            redisTemplate.opsForValue().set(key, 0L, 32, TimeUnit.DAYS);
        } else {
            redisTemplate.expire(key, 32, TimeUnit.DAYS);
        }
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

        // 出金分离为代付、提现，非提现业务不做判断
        if (!rcCalculteBasic.isCashWithdrawal(rcCalculateRequest.getBusinessCode())) {
            return CommonOuterResponse.SUCCEE;
        }

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String businessTagerId = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId();
        String key = rcCalculteBasic.getKey(businessTagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
        String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
        if (StringUtils.isBlank(countStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long count = Long.parseLong(countStr);
        long dayOutCount = redisTemplate.opsForValue().increment(key, count);
        if (dayOutCount > Long.parseLong(rcLimit.getLimitValue())) {
            redisTemplate.opsForValue().increment(key, -count);
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(),
                    dayOutCount - count + "", count + "", false, "RC交易受限", rcArchive.getArchiveType());
            // 实际发生值已经大于限定值
            return "单日累计提现次数已达风控上限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    // @Logable(businessTag = "Day-Max-OUT-Count:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {

        // 出金分离为代付、提现，非提现业务不做判断
        if (!rcCalculteBasic.isCashWithdrawal(map.get("businessCode"))) {
            return;
        }

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if (map.get("payState").equals("01")) {
            long count = -1 * Long.parseLong(map.get(RcConstants.RcIndex.TRADE_NUM.code));
            String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(), 
                    rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
            redisTemplate.opsForValue().increment(key, count);
        }

    }

}

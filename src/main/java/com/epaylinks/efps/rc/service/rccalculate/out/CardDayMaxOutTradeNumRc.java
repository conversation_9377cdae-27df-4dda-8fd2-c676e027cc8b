package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 同卡代付单日最大交易笔数
 * <AUTHOR>
 * @since 2018-12-10
 * 
 */
@Service("Card-Day-Max-OUT-Count")
public class CardDayMaxOutTradeNumRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcArchiveService rcArchiveService;


    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;
    @Autowired
    private SequenceService sequenceService;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 重置每日最大笔数
        Date date = new Date();
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getLastDay());
		String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastKey);  
        for(String bankCardNo: map.keySet()) {
              long dayOutCount = (long) map.get(bankCardNo);
              // businessTargetId 为 ${targetId}_${bankCardNo}
		      if(dayOutCount > 0){
	              RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), rcLimit.getBusinessTagerId() + "_" + bankCardNo,
	                        rcLimit.getDefineCode(), dayOutCount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
	              rcLimitDataMapper.insert(rcLimitData);
		      }              
              if(!redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)){// 设置今天的值
		    	  redisTemplate.opsForHash().put(hashKey, bankCardNo, 0L);
		      }
        }
   	   redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值

    }
    
//    @Logable(businessTag = "Card-Day-Max-OUT-Count:calculate")
    @Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if( !rcCalculteBasic.isWithdrawals(rcCalculateRequest.getBusinessCode())){// 非代付业务不做判断
            return CommonOuterResponse.SUCCEE;
        }
        String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
        if(customerCode == null || bankCardNo == null ) {
            return CommonOuterResponse.SUCCEE;
        }
        String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
        if (StringUtils.isBlank(countStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long count = Long.parseLong(countStr);
        long dayOutCount = 0L;
		String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
        if(redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)) {
            dayOutCount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
            dayOutCount+= count;
            redisTemplate.opsForHash().put(hashKey,bankCardNo,dayOutCount);
        }else {
            dayOutCount= count;
            redisTemplate.opsForHash().put(hashKey,bankCardNo,count);
            redisTemplate.expire(hashKey,32,TimeUnit.DAYS);
        }
        if (dayOutCount > Long.parseLong(rcLimit.getLimitValue())) {
            dayOutCount -= count;
            redisTemplate.opsForHash().put(hashKey,bankCardNo,dayOutCount);
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayOutCount+"",count+"",false,"RC交易受限", rcArchive.getArchiveType());
            //实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
	}
	
	
//    @Logable(businessTag = "Card-Day-Max-OUT-Count:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if (map.get("payState").equals("01")){
           String businessCode = map.get("businessCode");
           if (!rcCalculteBasic.isWithdrawals(businessCode)) {// 非代付业务不做判断
               return;
           }
           String customerCode = map.get("customerCode");
           String bankCardNo = map.get("bankCardNo");
           if(customerCode == null || bankCardNo == null) {// 提现业务不限制
               return;
           }
           long count = Long.parseLong(map.get(RcConstants.RcIndex.TRADE_NUM.code));
           String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
           long currentCount = 0L;
           currentCount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
           redisTemplate.opsForHash().put(hashKey, bankCardNo, -count + currentCount);// 增加值
       }
	}

}

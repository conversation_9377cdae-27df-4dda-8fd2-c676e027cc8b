package com.epaylinks.efps.rc.service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.controller.response.PaySettingResponse;
import com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse;
import com.epaylinks.efps.rc.controller.response.TemporaryLimitResponse;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.vo.EBankConfigVo;
import com.epaylinks.efps.rc.vo.TemporaryLimitVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface RcArchiveService {
    RcArchive selectByCodeOrName(String code, String name);

    RcArchive selectByCodeOrName(String code, String name,Long userId);

    RcArchive selectByTypeAndCode(String type, String code);

    boolean updateStatus(RcLimit rcLimit, RcArchive rcArchive);

    String selectInfoId(String customerCode);

    RcArchive selectById(Long rcArchiveId);

    PageResult<Map<String, String>> pageLimitQuery(String archiveCode, int pageNum, int pageSize);

    void insert(RcArchive rcArchive);

    void updateCusStatus(String customerCode, String oldStatus, String newStatus);

    CommonOuterResponse updateRcBalance(RcArchive rcArchive, Long amount, User user);

    void updateRcLevel(RcArchive rcArchive, String level);

    PageResult<RcArchivePageResponse> pageQuery(Map<String, Object> paramMap);

    PageResult<RcCalculateLog> queryCalculateLog(int startNum, int endNum, Map<String, String> map);

    boolean updateRcArchiveSelective(RcArchive rcArchive);

    boolean updateRcArchiveByPrimaryKey(RcArchive rcArchive);

    public void saveCertRcArchive(String certificateType, String certificateNo, Long userId);

    void saveClientNoRcArchive(String clientNo,Integer type,String mcc,String industry,Long registeredCapital,Long userId);

    /**
     * 导出风控拦截记录
     *
     * @param map
     * @return
     */
    PageResult<RcCalculateLog> exportCalculateLog(Map<String, String> map);

    /**
     * 保存账户状态变更审核记录
     *
     * @param rcArchiveId
     * @param statusName
     * @param newStatus
     * @param reason
     * @param userId
     */
    RcAuditRecord saveChangeStatusAuditRecord(Long rcArchiveId, String statusName, String newStatus, String reason, Long userId);

    /**
     * 保存更新风控档案
     *
     * @param rcArchiveId
     * @param statusName
     * @param status
     * @param reason
     * @param userId
     * @param batchFlag 是否批量；存在kafka同步，批量操作等全部成功再一起发送消息
     * @param jsonObjects 存储需发送kafka的信息
     * @return
     */
    CommonOuterResponse saveRcArchiveStatus(Long rcArchiveId, String statusName, String status, String reason, String remark, Long userId, Boolean batchFlag, List<JSONObject> jsonObjects);

    /**
     * 状态变更审核
     *
     * @param audId
     * @param auditResult 审核状态：1审核通过，2驳回
     * @param userId
     */
    void auditChangeStatus(Long audId, Short auditResult, Long userId);

    /**
     * 批量审核
     *
     * @param audIds
     * @param auditResult
     * @param remark
     * @param userId
     * @param autoAudit   传0是自动审核调用
     */
    void batchAuditRecord(String audIds, Short auditResult, String remark, Long userId, String autoAudit);

    List<RcArchive> selectByCertificateNo(String certificateNo);

    /**
     * 保存或更新风控档案信息
     *
     * @param archiveType
     * @param archiveCode
     * @param archiveName
     * @param userId
     */
    void saveRcArchive(String archiveType, String archiveCode, String archiveName, Long userId);

    /**
     * 查询网银业务设置
     *
     * @param customerCode
     * @return
     */
    EBankConfigVo queryEBankConfig(String customerCode);

    /**
     * 批量操作，保存账户状态和风控状态审核记录
     *
     * @param rcArchive
     * @param statusName
     * @param status
     * @param reason
     * @param userId
     * @param defaultUserId
     * @param flag
     * @return
     */
    RcAuditRecord saveAuditRecordForBatch(RcArchive rcArchive, String statusName, String status, String reason, Long userId, Long defaultUserId, String flag);

    /**
     * 保存账户状态和风控状态审核记录
     *
     * @param rcArchive
     * @param statusName
     * @param status
     * @param reason
     * @param userId
     * @param defaultUserId
     * @return
     */
    RcAuditRecord saveAuditRecord(RcArchive rcArchive, String statusName, String status, String reason, Long userId, Long defaultUserId, String uniqued);

    PageResult<RiskInfoExportResponse> exportRiskInfo(Map<String, Object> paramMap, boolean download) throws Exception;

    /**
     * 设置临时入金限制(风控档案)
     *
     * @param temporaryLimitVo
     * @param userId
     * @return
     * @throws Exception
     */
    CommonOuterResponse temporaryDepositLimit(TemporaryLimitVo temporaryLimitVo, Long userId) throws Exception;

    /**
     * 设置临时入金限制（商户初审）
     *
     * @param temporaryLimitVo
     * @param userId
     * @return
     * @throws Exception
     */
    CommonOuterResponse preTemporaryDepositLimit(TemporaryLimitVo temporaryLimitVo, Long userId) throws Exception;

    void auditTemporary(Long audId, Short auditResult, String remark, Long userId) throws Exception;

    /**
     * 获取临时入金限制（风控档案）
     *
     * @param archiveId
     * @return
     * @throws Exception
     */
    TemporaryLimitVo getTemporaryDepositLimit(Long archiveId) throws Exception;

    /**
     * 获取临时入金限制（商户初审）
     *
     * @param customerNo
     * @param source     sync:同步时取临时数据生成正式数据；1：商户初审
     * @return
     * @throws Exception
     */
    TemporaryLimitVo preGetTemporaryDepositLimit(String customerNo, String source) throws Exception;

    Long getTemporaryLimit(String customerNo, String limitType);

    CommonOuterResponse releaseTemporary(String customerNo, Long userId) throws Exception;

    /**
     * 获取代付可用额度占比
     *
     * @param customerCode
     * @return
     */
    int getWithdrawProportion(String customerCode);

    @Data
    class WithdrawTime {
        private boolean canWithdraw;
        private String startTime;
        private String endTime;
    }

    /**
     * 判断当前时间是否可以代付
     *
     * @param customerCode
     * @return
     */
    WithdrawTime getWithdrawTime(String customerCode);

    void paySetting(Long archiveId,String paySetting,String reason,String uniqueId,Long userId);

    PaySettingResponse getPaySetting(Long archiveId);
}

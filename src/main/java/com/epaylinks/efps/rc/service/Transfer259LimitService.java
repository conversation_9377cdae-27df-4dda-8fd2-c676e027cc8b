package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.tool.error.exception.EpException;
import com.epaylinks.efps.common.tool.pay.AmountUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 14:57
 */
@Service
public class Transfer259LimitService {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private OtherMapper otherMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    public String getRedisKey(String customerCode) {
        return "RC_CANT_TRANSFER_QUOTA:" + customerCode;
    }

    public void calculate(RcCalculateRequestWrapper request) {
        if (!request.getBusinessType().isPresent()) {
            return;
        }

        if (!request.getCustomerCode().isPresent()) {
            return;
        }

        if (request.getTransactionNo() == null) {
            return;
        }

        String amountVal = request.getIndex(RcConstants.RcIndex.AMOUNT);
        if (amountVal == null) {
            return;
        }

        long amount = Long.parseLong(amountVal);
        String customerCode = request.getCustomerCode().get();
        Constants.rcBusinessType businessType = request.getBusinessType().get();
        if ((rcCalculteBasic.isZHFZ(request.getPayMethod()) && businessType == Constants.rcBusinessType.INSIDE_PAY_OUT) ||
                (businessType == Constants.rcBusinessType.WITHDRAW && otherMapper.isChannelTransfer(request.getTransactionNo()) > 0)) {

            RcArchiveService.WithdrawTime withdrawTime = rcArchiveService.getWithdrawTime(customerCode);
            if (withdrawTime != null && !withdrawTime.isCanWithdraw()) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
                rcCalculteBasic.insertCalculateLog(request.getTransactionNo(),
                        rcArchive.getArchiveCode(),
                        rcArchive.getArchiveName(),
                        DefineCode.TRANSFER_TIME.defineCode,
                        withdrawTime.getStartTime() + "-" + withdrawTime.getEndTime(),
                        "/",
                        Timex.now().to(Timex.Format.yyyy$MM$dd$HH$mm$ss),
                        false,
                        "不在允许的交易时间段", rcArchive.getArchiveType());

                throw new EpException(RcCode.RC_CACLCUELATE_ERROR.msg("不在允许的交易时间段"));
            }

            //代付限额
//            long transferQuota = otherMapper.selectBalance(customerCode) - rcCalculteBasic.redisIncr(getRedisKey(customerCode), 0L);
//
//            if (amount > transferQuota) {
//                // 记录触发日记
//                int proportion = rcArchiveService.getWithdrawProportion(customerCode);
//                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
//                rcCalculteBasic.insertCalculateLog(request.getTransactionNo(),
//                        rcArchive.getArchiveCode(),
//                        rcArchive.getArchiveName(),
//                        DefineCode.TRANSFER_QUOTA.defineCode,
//                        proportion + "%",
//                        AmountUtils.fenLongToYuanStr(transferQuota) + "元",
//                        AmountUtils.fenLongToYuanStr(amount) + "元",
//                        false,
//                        "代付额度不足", rcArchive.getArchiveType());
//                //实际发生值已经大于限定值
//                throw new EpException(RcCode.RC_CACLCUELATE_ERROR.msg("代付额度不足"));
//            }
        }
    }
}

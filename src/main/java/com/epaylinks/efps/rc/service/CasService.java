package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.bouncycastle.cert.ocsp.Req;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("CAS")
public interface CasService {
    @RequestMapping(value = "/checkBankCardRisk",method = RequestMethod.POST)
    CommonOuterResponse checkBankCardRisk(@RequestParam("cardNo") String cardNo,@RequestParam("channelNo") String channelNo,
                                          @RequestParam("sysOrderNo") String sysOrderNo,@RequestParam("autoSwitch") String autoSwitch,
                                          @RequestParam("autoSwitchChannelNos") String autoSwitchChannelNos);

    @RequestMapping(value = "/EnterpriseRisk/riskQuery",method = RequestMethod.POST)
    CommonOuterResponse checkEnterpriseRisk(@RequestParam("queryType") String queryType,@RequestParam("queryValue") String queryValue,
                                          @RequestParam("user_id") String user_id,@RequestParam("orderno") String orderno);

    @RequestMapping(value = "/websiteCheck",method = RequestMethod.POST)
    CommonOuterResponse websiteCheck(@RequestParam("website") String website,@RequestParam(value = "channelNo",required = false) String channelNo,
                                     @RequestParam("sysOrderNo") String sysOrderNo);

}

package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 单日最大充值笔数
 * <AUTHOR>
 *
 */
@Service("Year-Max-Out-TradeNum")
public class YearMaxOutTradeNumRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;
	
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
		// TODO Auto-generated method stub
		return CommonOuterResponse.SUCCEE;
	}
	
	@Override
	public void reset(RcLimit rcLimit) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		// TODO Auto-generated method stub
		long amount = Long.parseLong(map.get(RcConstants.RcIndex.AMOUNT.code));
		String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentYear());
		redisTemplate.opsForValue().increment(key, amount);
	}

}

package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcCloseBusiness;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.service.RcRedisService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import com.epaylinks.efps.rc.util.RcDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 14:06
 */
@Service
public class PosRealNameFailMonitor implements TransactionMonitor {
    private static final Logger log = LoggerFactory.getLogger(PosRealNameFailMonitor.class);

    @Autowired
    private RcMonitorService rcMonitorService;

    @Autowired
    private RcRedisService rcRedisService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    /**
     * R017-R019
     *
     * @param order
     */
    @Override
    public void monitor(RcTxsOrder order) {
        String termCode = order.getTargetIdMap().get(RcConstants.BusinessTagerType.TERM.code);

        if (StringUtils.isNotBlank(termCode)) {
            RiskEventRule rule = riskEventRuleService.getRule(
                    order.getCustomerCode(),
                    RcConstants.RiskEventRule.R022);

            if (rule != null && rule.getRuleParam1() != null) {
                String key = "RC_EVENT:" + order.getCustomerCode() + ":" + RcConstants.RiskEventRule.R022.code + ":" + RcDateUtils.getCurrentDay() + ":" + termCode;
                long totalCount = rcRedisService.redisIncr(key, 1L, Duration.standardDays(2));
                long rcCount = Long.parseLong(rule.getRuleParam1());
                log.info("商户[{}]易票联订单号[{}]终端号[{}]监控[{}]当前[{}]",
                        order.getCustomerCode(),
                        order.getTransactionNo(),
                        termCode,
                        RcConstants.RiskEventRule.R022.message.replace("[param1]", rule.getRuleParam1()),
                        totalCount);
                if (totalCount >= rcCount) {
                    log.info("失败次数[{}]>=[{}]超限", totalCount, rcCount);
                    rcMonitorService.closeTermAndReopenAfterDays(order.getCustomerCode(), termCode, 1);
                    CustomerInfo customerInfo = cumCacheService.getCustomerInfo(order.getCustomerCode(), order.getCustomerCode(), UserType.PPS_USER.code);
                    riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R022.code, order.getCustomerCode(), customerInfo.getName(),
                            String.format("终端[%s]次数[%d]", termCode, totalCount));
                }
            }
        }
    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        String posTxnType = order.getIndexMap().get(RcConstants.RcIndex.POS_TXN_TYPE.code);
        return "REAL_NAME_CONSUME".equals(posTxnType) &&
                Constants.PayState.FAIL.code.equals(order.getPayState()) &&
                Constants.rcBusinessType.GATEWAY_PAY.code.equals(order.getBusinessType());
    }
}

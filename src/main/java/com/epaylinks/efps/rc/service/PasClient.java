package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.pas.BkCardBin;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient("PAS")
public interface PasClient {

    /**
     * 通过银行卡号查询卡bin信息
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/bk/cardBin/queryByCardNo", method = RequestMethod.POST)
    CommonOuterResponse<List<BkCardBin>> queryByCardNo(@RequestParam(value = "cardNo") String cardNo);
}

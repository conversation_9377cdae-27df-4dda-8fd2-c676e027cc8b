package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.BwListMapper;
import com.epaylinks.efps.rc.dao.RcLimitAudMapper;
import com.epaylinks.efps.rc.dao.RcOperateLogMapper;
import com.epaylinks.efps.rc.dao.VerifyConfigMapper;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.vo.OperateLogVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR>
 * @date 2020-06-09 15:22
 */
@Service
public class RcOperateLogServiceImpl implements RcOperateLogService {

    @Autowired
    private RcOperateLogMapper rcOperateLogMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private CommonLogger commonLogger;

    @Autowired
    private RcAuditRecordService rcAuditRecordService;

    @Autowired
    private BwListService bwListService;

    @Autowired
    private OtherService otherService;

    @Autowired
    private VerifyConfigMapper verifyConfigMapper;

    @Autowired
    private RcLimitAudMapper rcLimitAudMapper;

    private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Logable(businessTag = "operateLog.insert")
    @Override
    public void insert(RcOperateLog rcOperateLog) {
        try {
            if (rcOperateLog.getId() < 1) {
                rcOperateLog.setId(sequenceService.nextValue(RcOperateLog.tableName).intValue());
            }
            rcOperateLogMapper.insert(rcOperateLog);
        } catch (Exception e) {
            commonLogger.printMessage("打印插入日志信息：" + JSON.toJSONString(rcOperateLog));
            commonLogger.printMessage("插入操作日志失败，原因为：" + e.getLocalizedMessage());
            commonLogger.printLog(e);
        }
    }

    @Override
    public List<OperateLogVo> getOperateLogVoList(Map<String, String> params) {
        List<OperateLogVo> operateLogVos = rcOperateLogMapper.getVoListByParams(params);
        if (operateLogVos.isEmpty()) {
            return operateLogVos;
        }
        for (int i = 0; i < operateLogVos.size(); i++) {
            OperateLogVo operateLogVo = operateLogVos.get(i);
            String permId = operateLogVo.getPermId();
            String type = "";
            if ("80101".equals(permId)) { // 名单管理：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号
                type = RcConstants.AuditTargetType.BW_LIST.code;
                String code = getBwCodeByName(operateLogVo.getName());
                List<BwList> bwLists = new ArrayList<>();
                if (StringUtils.isEmpty(code) || StringUtils.isEmpty(operateLogVo.getCode())) {
                    continue;
                }
                if ("007".equals(code)) { // 经营地址转换后对比
                    bwLists = bwListService.getByTagerType(code);
                    if (bwLists != null && bwLists.size() > 0) {
                        Iterator iterator = bwLists.iterator();
                        while (iterator.hasNext()) {
                            BwList bwList = (BwList) iterator.next();
                            String value = getTargetIdName(bwList);
                            if (!value.equals(operateLogVo.getCode())) iterator.remove();
                        }
                    }
                } else {
                    bwLists = bwListService.getByTagerTypeAndTagerId(code,operateLogVo.getCode());
                }
                if (bwLists == null || bwLists.size() == 0) {
                    continue;
                }
                BwList bwList = bwLists.get(0);
                RcAuditRecord auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, bwList.getBwId(), operateLogVo.getOperateTime(),"audit_time");
                if (auditRecord != null) {
                    String content = operateLogVo.getOperateContent()
                            + (auditRecord.getAuditOperId() == null ? "" : ",审核人：" + otherService.selectUserById(auditRecord.getAuditOperId()).getRealName())
                            + ",创建时间：" + format.format(auditRecord.getCreateTime());
                    operateLogVo.setOperateContent(content);
                }
            } else if ("80205".equals(permId)) { // 风险核验
                type = RcConstants.AuditTargetType.VERIFY_CONFIG.code;
                VerifyConfig verifyConfig = verifyConfigMapper.queryByCustomerNo(operateLogVo.getCode());
                RcAuditRecord auditRecord = null;
                if (verifyConfig != null) {
                    auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, verifyConfig.getId(), operateLogVo.getOperateTime(),"create_time");
                    if (auditRecord == null) {
                        auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, verifyConfig.getId(), operateLogVo.getOperateTime(),"AUDIT_TIME");
                    }
                    if (auditRecord != null ) {
                        String content = operateLogVo.getOperateContent()
                                + ",变更操作人：" + otherService.selectUserById(auditRecord.getOperId()).getRealName()
                                + ",创建时间：" + format.format(auditRecord.getCreateTime());
                        operateLogVo.setOperateContent(content);
                    }
                }
            } else if ("80401".equals(permId) || "80404".equals(permId) || "80406".equals(permId)) { // 商户限额管理/终端限额管理/个人限额管理
                if ("80401".equals(permId)) {
                    type = RcConstants.RCTargetType.CUSTOMER_CODE.code;
                } else if ("80404".equals(permId)) {
                    type = RcConstants.RCTargetType.TERM.code;
                } else {
                    type = RcConstants.RCTargetType.PERSON.code;
                }
                RcLimitAud rcLimitAud = rcLimitAudMapper.queryRecordLog(operateLogVo.getCode(),type,operateLogVo.getOperateTime());
                if (rcLimitAud != null) {
                    String content = operateLogVo.getOperateContent()
                            + ",变更操作人：" + otherService.selectUserById(rcLimitAud.getUserId()).getRealName()
                            + ",创建时间：" + format.format(rcLimitAud.getCreateTime());
                    operateLogVo.setOperateContent(content);
                }
            } else {
                continue;
            }
            operateLogVos.set(i,operateLogVo);
        }
        return operateLogVos;
//        return rcOperateLogMapper.getVoListByParams(params);
    }

    @Override
    public List<OperateLogVo> getOperateLogVoList(List<OperateLogVo> operateLogVos) {
        for (int i = 0; i < operateLogVos.size(); i++) {
            OperateLogVo operateLogVo = operateLogVos.get(i);
            String permId = operateLogVo.getPermId();
            String type = "";
            if ("80101".equals(permId)) { // 名单管理：001身份证； 002手机号 ；003社会统一信用代码 ；004银行卡 ；006 身份证前4位； 007 经营地址（省-市）；005 商户编号
                type = RcConstants.AuditTargetType.BW_LIST.code;
                String code = getBwCodeByName(operateLogVo.getName());
                List<BwList> bwLists = new ArrayList<>();
                if (StringUtils.isEmpty(code) || StringUtils.isEmpty(operateLogVo.getCode())) {
                    continue;
                }
                if ("007".equals(code)) { // 经营地址转换后对比
                    bwLists = bwListService.getByTagerType(code);
                    if (bwLists != null && bwLists.size() > 0) {
                        Iterator iterator = bwLists.iterator();
                        while (iterator.hasNext()) {
                            BwList bwList = (BwList) iterator.next();
                            String value = getTargetIdName(bwList);
                            if (!value.equals(operateLogVo.getCode())) iterator.remove();
                        }
                    }
                } else {
                    bwLists = bwListService.getByTagerTypeAndTagerId(code,operateLogVo.getCode());
                }
                if (bwLists == null || bwLists.size() == 0) {
                    continue;
                }
                BwList bwList = bwLists.get(0);
                RcAuditRecord auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, bwList.getBwId(), operateLogVo.getOperateTime(),"audit_time");
                if (auditRecord != null) {
                    String content = operateLogVo.getOperateContent()
                            + (auditRecord.getAuditOperId() == null ? "" : ",审核人：" + otherService.selectUserById(auditRecord.getAuditOperId()).getRealName())
                            + ",创建时间：" + format.format(auditRecord.getCreateTime());
                    operateLogVo.setOperateContent(content);
                }
            } else if ("80205".equals(permId)) { // 风险核验
                type = RcConstants.AuditTargetType.VERIFY_CONFIG.code;
                VerifyConfig verifyConfig = verifyConfigMapper.queryByCustomerNo(operateLogVo.getCode());
                RcAuditRecord auditRecord = null;
                if (verifyConfig != null) {
                    auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, verifyConfig.getId(), operateLogVo.getOperateTime(),"create_time");
                    if (auditRecord == null) {
                        auditRecord = rcAuditRecordService.queryHistoryAuditRecord(type, verifyConfig.getId(), operateLogVo.getOperateTime(),"AUDIT_TIME");
                    }
                    if (auditRecord != null ) {
                        String content = operateLogVo.getOperateContent()
                                + ",变更操作人：" + otherService.selectUserById(auditRecord.getOperId()).getRealName()
                                + ",创建时间：" + format.format(auditRecord.getCreateTime());
                        operateLogVo.setOperateContent(content);
                    }
                }
            } else if ("80401".equals(permId) || "80404".equals(permId) || "80406".equals(permId) || "80409".equals(permId)) { // 商户限额管理/终端限额管理/个人限额管理
                if ("80401".equals(permId)) {
                    type = RcConstants.RCTargetType.CUSTOMER_CODE.code;
                } else if ("80404".equals(permId)) {
                    type = RcConstants.RCTargetType.TERM.code;
                } else if ("80406".equals(permId)){
                    type = RcConstants.RCTargetType.PERSON.code;
                } else if ("80409".equals(permId)){
                    type = RcConstants.RCTargetType.CLIENT_NO.code;
                }
                RcLimitAud rcLimitAud = rcLimitAudMapper.queryRecordLog(operateLogVo.getCode(),type,operateLogVo.getOperateTime());
                if (rcLimitAud != null) {
                    String content = operateLogVo.getOperateContent()
                            + ",变更操作人：" + otherService.selectUserById(rcLimitAud.getUserId()).getRealName()
                            + ",创建时间：" + format.format(rcLimitAud.getCreateTime());
                    operateLogVo.setOperateContent(content);
                }
            } else if ("80402".equals(permId)) {
                commonLogger.printMessage("打印content1:" + operateLogVo.getOperateContent());
                // 替换标识客户号/商户维度content
                operateLogVo.setOperateContent(operateLogVo.getOperateContent()
                        .replace("参数名称：商户默认限额管理，修改前：；修改后：","参数名称：商户默认限额管理")
                        .replace("参数名称：客户号默认限额管理，修改前：；修改后：","参数名称：客户号默认限额管理"));
                commonLogger.printMessage("打印content2:" + operateLogVo.getOperateContent());
            } else {
                continue;
            }
            operateLogVos.set(i,operateLogVo);
        }
        return operateLogVos;
    }

    @Override
    public int totalOperateLog(Map<String, String> params) {
        return rcOperateLogMapper.totalVo(params);
    }

    private String getBwCodeByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        String code = "";
        switch (name) {
            case "身份证":
                code = "001";
                break;
            case "手机号" :
                code = "002";
                break;
            case "社会统一信用代码":
                code = "003";
                break;
            case "银行卡":
                code = "004";
                break;
            case "商户编号":
                code = "005";
                break;
            case "身份证前4位":
                code = "006";
                break;
            case "经营地址":
                code = "007";
                break;
        }
        return code;
    }

    private String getTargetIdName(BwList bwList) {

        if (RcConstants.BusinessTagerType.BUSINESS_ADDRESS.code.equals(bwList.getBusinessTagerType())
                && bwList.getBusinessTagerId() != null &&  bwList.getBusinessTagerId().contains("-")) {
            // 翻译省市
            String addr[] = bwList.getBusinessTagerId().split("-");
            return (otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[0], true)
                    + "-"
                    + otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[1], true));
        } else {
            return bwList.getBusinessTagerId();
        }

    }
}

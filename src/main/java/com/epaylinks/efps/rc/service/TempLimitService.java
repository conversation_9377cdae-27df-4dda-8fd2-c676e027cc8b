package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.tool.error.exception.EpException;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 14:57
 */
@Service
public class TempLimitService {
    public enum SwitchBizCode {
        ALIPAY("alipaySingleMaxLimit", "支付宝"),
        WX("wechatSingleMaxLimit", "微信"),
        UNION_QR("unionCodeSingleMaxLimit", "银联二维码"),
        UNION_ONLINE("unionOnlineSingleMaxLimit", "银联在线"),
        QUICK_PAY("quickPaySingleMaxLimit", "快捷支付"),
        NET_BANK("bankPaySingleMaxLimit", "网银支付"),
        UNION_APP("unionAppSingleMaxLimit", "银联云闪付"),
        ALL("allSingleMaxLimit", "单日"),
        ;

        public final String limitType;
        public final String desc;

        SwitchBizCode(String limitType, String desc) {
            this.limitType = limitType;
            this.desc = desc;
        }
    }

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private LogService logService;

    private Optional<SwitchBizCode> switchBusinessCode(String businessCode) {
        if (businessCode == null) {
            return Optional.empty();
        }
        switch (businessCode) {
            //支付宝
            case "AliJSAPI":
            case "AliJSAPIDebitCard":
            case "AliMicro":
            case "AliMicroDebitCard":
            case "AliMultiRate":
            case "AliMultiRateDebitCard":
            case "AliNative":
            case "AliNativeDebitCard":
            case "AuthAliMicro":
            case "AuthAliMicroDebitCard":
            case "FZ-AliJSAPI":
            case "FZ-AliMicro":
            case "FZ-AliNative":
                return Optional.of(SwitchBizCode.ALIPAY);
            //微信
            case "AuthWxMicro":
            case "AuthWxMicroDebitCard":
            case "FZ-WxAPP":
            case "FZ-WxJSAPI":
            case "FZ-WxMWEB":
            case "FZ-WxMicro":
            case "FZ-WxMiniProgram":
            case "FZ-WxNatvie":
            case "WxAPP":
            case "WxAPPDebitCard":
            case "WxJSAPI":
            case "WxJSAPIDebitCard":
            case "WxMicro":
            case "WxMicroDebitCard":
            case "WxMiniProgram":
            case "WxMiniProgramDebitCard":
            case "WxMultiRule":
            case "WxMultiRuleDebitCard":
            case "WxNatvie":
            case "WxNatvieDebitCard":
                return Optional.of(SwitchBizCode.WX);
            //银联二维码
            case "FZ-UnionQrcode":
            case "FZ-UnionSweep":
            case "POSUnionQrcodeCreditCard":
            case "POSUnionQrcodeDebitCard":
            case "UnionJS":
            case "UnionJSDebit":
            case "UnionQrcode":
            case "UnionQrcodeDebitCard":
            case "UnionSweep":
            case "UnionSweepDebit":
                return Optional.of(SwitchBizCode.UNION_QR);
            //银联在线
            case "FZ-UnionOnline":
            case "FZ-UnionOnlineCredit":
            case "UnionOnline":
            case "UnionOnlineCredit":
                return Optional.of(SwitchBizCode.UNION_ONLINE);
            //快捷
            case "FZ-NocardPay":
            case "FZ-NocardPayCredit":
            case "FZ-QuickPay":
            case "FZ-QuickPayCredit":
            case "ProtocolPay":
            case "ProtocolPayCredit":
            case "QuickPay":
            case "QuickPayCredit":
                return Optional.of(SwitchBizCode.QUICK_PAY);
            //网银
            case "CreditCardPay":
            case "EnterpriseUnion":
            case "FZ-CreditCardPay":
            case "FZ-EnterpriseUnion":
            case "FZ-SavingCardPay":
            case "SavingCardPay":
                return Optional.of(SwitchBizCode.NET_BANK);
            //云闪付APP
            case "UnionApp":
            case "UnionAppCredit":
            case "UnionAppCreditUs":
            case "UnionAppUs":
                return Optional.of(SwitchBizCode.UNION_APP);
            default:
                return Optional.empty();
        }
    }

    private Optional<Long> getTempLimit(String customerCode, SwitchBizCode switchBizCode) {
        return Optional.ofNullable(rcArchiveService.getTemporaryLimit(customerCode, switchBizCode.limitType));
    }

    private String getRedisKey(String customerCode, SwitchBizCode switchBizCode) {
        return customerCode + ":TempDayLimit:" + switchBizCode + ":" + RcDateUtils.getCurrentDay();
    }

    public void calculate(RcCalculateRequestWrapper request) {
        if (!request.getCustomerCode().isPresent()) {
            return;
        }

        String customerCode = request.getCustomerCode().get();
        String businessCode = request.getBusinessCode();
        String amountVal = request.getIndex(RcConstants.RcIndex.AMOUNT);
        if (amountVal == null) {
            return;
        }
        long amount = Long.parseLong(amountVal);

        getTempLimit(customerCode, SwitchBizCode.ALL).ifPresent(limitAmount -> {
            logService.printLog(SwitchBizCode.ALL.desc + "临时入金限额:" + limitAmount);
            String redisKey = getRedisKey(customerCode, SwitchBizCode.ALL);
            Long dayAmount = rcCalculteBasic.redisGet(redisKey);
            if (dayAmount == null) {
                dayAmount = rcCalculteBasic.redisIncr(redisKey, 0L, Duration.standardDays(2));
            }

            if (dayAmount + amount > limitAmount) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
                rcCalculteBasic.insertCalculateLog(request.getTransactionNo(),
                        rcArchive.getArchiveCode(),
                        rcArchive.getArchiveName(),
                        DefineCode.DAY_IN.defineCode,
                        limitAmount + "",
                        dayAmount + "",
                        amount + "",
                        true,
                        SwitchBizCode.ALL.desc + "交易受限", rcArchive.getArchiveType());
                //实际发生值已经大于限定值
                throw new EpException(RcCode.RC_CACLCUELATE_ERROR.msg(SwitchBizCode.ALL.desc + "交易受限"));
            }
        });

        switchBusinessCode(businessCode).ifPresent(switchCode -> getTempLimit(customerCode, switchCode).ifPresent(limitAmount -> {
            logService.printLog(switchCode.desc + "临时入金限额:" + limitAmount);
            String redisKey = getRedisKey(customerCode, switchCode);
            Long dayAmount = rcCalculteBasic.redisGet(redisKey);
            if (dayAmount == null) {
                dayAmount = rcCalculteBasic.redisIncr(redisKey, 0L, Duration.standardDays(2));
            }

            if (dayAmount + amount > limitAmount) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
                rcCalculteBasic.insertCalculateLog(request.getTransactionNo(),
                        rcArchive.getArchiveCode(),
                        rcArchive.getArchiveName(),
                        DefineCode.DAY_IN.defineCode,
                        limitAmount + "",
                        dayAmount + "",
                        amount + "",
                        true,
                        switchCode.desc + "交易受限", rcArchive.getArchiveType());
                //实际发生值已经大于限定值
                throw new EpException(RcCode.RC_CACLCUELATE_ERROR.msg(switchCode.desc + "交易受限"));
            }
        }));
    }

    public void calculateValue(Map<String, String> paramMap) {
        String payState = paramMap.get("payState");
        if (payState.equals("00")) {
            String businessCode = paramMap.get("businessCode");
            String customerCode = paramMap.get("customerCode");
            String amountValue = paramMap.get(RcConstants.RcIndex.AMOUNT.code);

            if (StringUtils.isNotBlank(customerCode) && StringUtils.isNotBlank(amountValue)) {
                getTempLimit(customerCode, SwitchBizCode.ALL).ifPresent(limitAmount -> {
                    long amount = Long.parseLong(amountValue);
                    rcCalculteBasic.redisIncr(getRedisKey(customerCode, SwitchBizCode.ALL), amount);
                });

                switchBusinessCode(businessCode).ifPresent(switchCode -> getTempLimit(customerCode, switchCode).ifPresent(limitAmount -> {
                    long amount = Long.parseLong(amountValue);
                    rcCalculteBasic.redisIncr(getRedisKey(customerCode, switchCode), amount);
                }));
            }
        }
    }
}

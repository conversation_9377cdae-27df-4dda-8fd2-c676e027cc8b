package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 同一证件单月信用卡交易累计限额（是指相同证件的所有商户总限额）
 * 
 * <AUTHOR>
 * @date 2021-08-10
 *
 */
@Service("Cert-Month-Max-Credit-Amount")
public class CertMonthMaxCreditAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private CertMonthMaxCreditAmountRc self;

    private static final String Cert_Day_Max_Credit_Amount = DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineCode;



    @Override
    public void reset(RcLimit rcLimit) {

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 每月重置月入金为0
        Date date = new Date();
        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                ? rcLimit.getBusinessTagerId()
                : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        if (date.getDate() == 1) {
            // 如果当前时间是每月的一号
            self.historyDataHandler(rcLimit, date, RcLimitData.Status.FINISHED.code);
            redisTemplate.opsForValue().set(key, 0L);
        } else {
            // 如果不是每月的一号
            long monthInAmount = self.historyDataHandler(rcLimit, date, RcLimitData.Status.PROCESSING.code);
            redisTemplate.opsForValue().set(key, monthInAmount);

        }
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

        String businessCode =  rcCalculateRequest.getBusinessCode();
        if (businessCode == null || !businessCode.contains("Credit")) { // 非信用卡支付返回 
            return CommonOuterResponse.SUCCEE;
        }
        
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();

        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                ? rcLimit.getBusinessTagerId()
                : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        String dayKey = rcCalculteBasic.getKey(tagerId, Cert_Day_Max_Credit_Amount, RcDateUtils.getCurrentDay());
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
        if (StringUtils.isBlank(amountStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long amount = Long.parseLong(amountStr);
        long monthInAmount = redisTemplate.opsForValue().increment(key, 0L);
        long dayInAmount = redisTemplate.opsForValue().increment(dayKey, 0L);
        if (amount + monthInAmount + dayInAmount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(),
                    rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(),
                    dayInAmount + monthInAmount + "", amount + "", true, "RC交易受限", rcArchive.getArchiveType());
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        // TODO Auto-generated method stub

    }

    public long historyDataHandler(RcLimit rcLimit, Date date, String status) {

        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())
                ? rcLimit.getBusinessTagerId()
                : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String lastDayKey = rcCalculteBasic.getKey(tagerId, Cert_Day_Max_Credit_Amount, RcDateUtils.getLastDay());
        long dayInAmount = redisTemplate.opsForValue().increment(lastDayKey, 0L);

        Date selectLastDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);
        RcLimitData monthInAmountRcLimitData = rcLimitDataMapper
                .selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(), tagerId,
                        RcDateUtils.getMonthString(selectLastDate), RcLimitData.Status.PROCESSING.code);
        long monthInAmount = 0;
        if (monthInAmountRcLimitData == null) {
            monthInAmountRcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), tagerId,
                    rcLimit.getDefineCode(), dayInAmount + "", date, date, status,
                    RcDateUtils.getMonthString(selectLastDate));
            rcLimitDataMapper.insert(monthInAmountRcLimitData);
            monthInAmount = dayInAmount;
        } else {
            monthInAmount = Long.parseLong(monthInAmountRcLimitData.getValue()) + dayInAmount;
            monthInAmountRcLimitData.setValue(monthInAmount + "");
            monthInAmountRcLimitData.setUpdatetime(date);
            monthInAmountRcLimitData.setStatus(status);
            rcLimitDataMapper.updateByPrimaryKey(monthInAmountRcLimitData);
        }
        return monthInAmount;
    }
}

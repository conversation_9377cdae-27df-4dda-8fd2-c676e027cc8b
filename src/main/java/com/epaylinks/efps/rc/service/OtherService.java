package com.epaylinks.efps.rc.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.rc.domain.RcArchivePageResponse;
import com.epaylinks.efps.rc.domain.cust.MccBusinessType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.txs.TxsPreOrder;

@Service
public class OtherService {

	@Autowired
	private OtherMapper otherMapper;

    private static String KEY_FORMAT = "CUST_PARAM:%s:%s"; // 当前格式：paramType:paramName， 与cust保持一致

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * key格式：paramType:paramName
     * 
     * @param args
     * @return
     */
    private String getParamRedisKey(String paramType, String paramName) {
        return String.format(KEY_FORMAT, paramType, paramName);
    }
    
	/**
	 * 根据ID查询用户
	 */
	public User selectUserById(Long userId){

	    return otherMapper.selectUserById(userId);
	}

	/**
	 * 根据businessCode查询payMethod
	 */
	public String selectPayMethodByBusinessCode(String businessCode){
		return otherMapper.selectPayMethodByBusinessCode(businessCode);
	}

    /**
     * 根据渠道订单号查询商户交易订单号
     */
    public TxsPreOrder queryTxsOrderByChannelOrder(String channelOrder){
        
        return otherMapper.queryTxsOrderByChannelOrder(channelOrder);
    }
    
    
    /**
     * 查询商户信息
     * @param customerNo
     * @return
     */
    public Customer queryCustomerByCustomerNo(String customerNo) {
        
        return otherMapper.queryCustomerByCustomerNo(customerNo);
    }

    public Customer queryCustomerDraftByCustomerNo(String customerNo) {

        return otherMapper.queryCustomerDraftByCustomerNo(customerNo);
    }

    public Integer queryCustomerDraftByCustomerNoList(List<String> list) {
        return otherMapper.queryCustomerDraftByCustomerNoList(list);
    }

    /**
     * 查询上级商户编号
     * @param customerNo
     * @return
     */
    public String queryUpperCustomerCode(String customerNo) {

        Customer customer = otherMapper.queryCustomerByCustomerNo(customerNo);
        if (customer == null) {
            return null;
        }
        return customer.getPlatCustomerNo() != null ? customer.getPlatCustomerNo() : customer.getServiceCustomerNo();
    }
    
    
    /**
     * 查询上级代理商
     * @param customerNo
     * @return
     */
    public Customer queryUpperServiceCustomer(String customerNo) {

        Customer customer = otherMapper.queryCustomerByCustomerNo(customerNo);
        if (customer == null) {
            return null;
        }
        
        if (RcConstants.CustomerCategory.SERVICE.code.equals(customer.getCategory())) {
            return customer;
        }
        
        if (customer.getServiceCustomerNo() != null) {
            
            return queryUpperServiceCustomer(customer.getServiceCustomerNo());
            
        } else if (customer.getPlatCustomerNo() != null) {// 通过平台商户递归

            return queryUpperServiceCustomer(customer.getPlatCustomerNo());
        }
        
        return null;
    }
	
    public String queryParamValueByTypeAndName(String paramType, String paramName, boolean withCache) {
        
        String value = null;
        if (withCache) {
            String redisKey = getParamRedisKey(paramType, paramName);
            value = (String) redisTemplate.opsForValue().get(redisKey);
            if(value == null) {
                String paramValue = otherMapper.selectParamValueByTypeAndName(paramType, paramName);
                value = paramValue != null ? paramValue : "";   // 为空设置到redis空字符串，避免不设置时不断请求db
                redisTemplate.opsForValue().set(redisKey, value, 5, TimeUnit.MINUTES);
            }
        } else {
            value = otherMapper.selectParamValueByTypeAndName(paramType, paramName);
        }
        return value;
    }
    
    /**
     * 通过营业执照号查询商户
     * @param businessLicenseCode
     * @return
     */
    public List<String> selectCustomerByBusinessLicenseNo(String businessLicenseCode){
        return otherMapper.selectCustomerByBusinessLicenseNo(businessLicenseCode);
    }
    
    /**
     * 通过法人身份证号查询商户
     * @param businessLicenseCode
     * @return
     */
    public List<String> selectCustomerByCertificateNo(String certificateNo){
        return otherMapper.selectCustomerByCertificateNo(certificateNo);
    }

    public String selectAuditAuthRC(Long userId,String auditName){
        return otherMapper.selectAuditAuthRC(userId,auditName);
    }

    public List<String> selectQueryAuthRC(Long userId) {
        return otherMapper.selectQueryAuthRC(userId);
    }

    public Integer selectServiceFee(Map paramMap) {
        return otherMapper.selectServiceFee(paramMap);
    }

    public String queryVerifyBusiness(List<String> list) {
        return otherMapper.queryVerifyBusiness(list);
    }

    public Integer updateDraftTemporary(String temporaryStatus,String temporaryStatusReason,String customerNo) {
        return otherMapper.updateDraftTemporary(temporaryStatus,temporaryStatusReason,customerNo);
    }

    public RcArchivePageResponse queryMccInfo(RcArchivePageResponse item) {
        if (StringUtils.isBlank(item.getMcc())) {
            return item;
        }
        MccBusinessType mccBusinessType = otherMapper.queryMccInfo(item.getMcc());
        item.setCategory(mccBusinessType.getCategory());
        item.setCustomerType(mccBusinessType.getMccName());
        item.setIndustry(mccBusinessType.getBusinessName());
        return item;
    }

    public String queryDictBusinessType(String industry) {
        if (StringUtils.isBlank(industry)) {
            return industry;
        }
        String name = otherMapper.queryDictBusinessType(industry);
        return name;
    }

    public String queryIndustryByCode(String customerNo) {
        return otherMapper.selectInnerBizType(customerNo);
    }

    public Long queryRegistCapital(Long customerId) {
        return otherMapper.queryRegistCapital(customerId);
    }

    public Boolean whetherOpenWithdraw(String customerNo) {
        Integer count = otherMapper.whetherOpenWithdraw(customerNo);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    public String queryWithdrawParam(String paramName) {
        return otherMapper.queryWithdrawParam(paramName);
    }

}

package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.CardUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import com.google.common.collect.ImmutableList;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/28 11:07
 */
@Service
public class GreyRcService {
    @Autowired
    private GamblingPyramidService gamblingPyramidService;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private BwListService bwListService;

    @Autowired
    private RcCalculteBasic basicService;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private HessianService hessianService;

    private static final List<String> CHECK_GREY_BUSINESS_CODE_LIST = ImmutableList.of(
            "ProtocolPay",
            "ProtocolPayCredit",
            "QuickPay",
            "QuickPayCredit",
            "Withdraw",
            "Withdraw-CreditCard"
    );

    public Optional<CommonOuterResponse<Object>> getCheckGreyResponse(RcCalculateRequest request) {
        if (request.getBusinessCode() == null || !CHECK_GREY_BUSINESS_CODE_LIST.contains(request.getBusinessCode())) {
            return Optional.empty();
        }

        String bankCardNo = request.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
        if (bankCardNo == null) {
            return Optional.empty();
        }

        String customerCode = request.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        if (customerCode == null) {
            return Optional.empty();
        }

        List<String> checkBusinessCodeList = gamblingPyramidService.getCheckBusinessCode(customerCode);
        if (!checkBusinessCodeList.contains(request.getBusinessCode())) {
            return Optional.empty();
        }

        if (rcCalculteBasic.riskCheck(customerCode, request.getOutTradeNo(), RcConstants.BusinessTagerType.BANK_CARD, bankCardNo)) {
            return Optional.empty();
        }

        return Optional.of(CommonOuterResponse.fail(RcCode.CHECK_CARD_FAIL.code, RcCode.CHECK_CARD_FAIL.message));
    }

    public Optional<CommonOuterResponse<Object>> checkInnerBlackList(RcCalculateRequest request) {
        RcCalculateRequestWrapper requestWrapper = RcCalculateRequestWrapper.wrap(request);
        String customerCode = requestWrapper.getTarget(RcConstants.BusinessTagerType.CUSTOMER_CODE);
        String cardNo = requestWrapper.getTarget(RcConstants.BusinessTagerType.BANK_CARD);
        if (StringUtils.isNotBlank(customerCode) && StringUtils.isNotBlank(cardNo)) {
            Map<String, String> customerCodeCheck = new HashMap<>();
            customerCodeCheck.put(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
            Map<String, String> customerCodeBlack = bwListService.getInnerBlackList(customerCodeCheck);
            if (MapUtils.isNotEmpty(customerCodeBlack)) {
                Map<String, String> valueCheck = new HashMap<>();
                valueCheck.put(RcConstants.BusinessTagerType.BANK_CARD.code, cardNo);
                if (requestWrapper.getTarget(RcConstants.BusinessTagerType.IDENTITY_CARD) != null) {
                    valueCheck.put(RcConstants.BusinessTagerType.IDENTITY_CARD.code, requestWrapper.getTarget(RcConstants.BusinessTagerType.IDENTITY_CARD));
                }

                Map<String, String> valueBlack = bwListService.getInnerBlackList(valueCheck);
                if (MapUtils.isNotEmpty(valueBlack)) {
                    RcArchive rcArchive = rcArchiveService.selectByCodeOrName(customerCode, null);
                    if (rcArchive == null) {
                        rcArchive = new RcArchive();
                        rcArchive.setArchiveCode("未知");
                        rcArchive.setArchiveType("未知");
                        rcArchive.setArchiveName("未知");
                    }

                    if (valueBlack.get(RcConstants.BusinessTagerType.BANK_CARD.code) != null) {
                        String message = "RC" + RcConstants.BusinessTagerType.BANK_CARD.message + "存在风险";
                        String remark = "RC" + RcConstants.BusinessTagerType.BANK_CARD.message + CardUtils.getHiddenBankCardNo(cardNo) + "存在风险。";
                        String keyEncrypt = cardNo;
                        try {
                            keyEncrypt = hessianService.symmetricEncryptData(cardNo);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        basicService.insertCalculateLog(request.getTransactionNo(),
                                customerCode,
                                rcArchive.getArchiveName(),
                                RcConstants.BusinessTagerType.BANK_CARD.blackType.defineCode, "是", "/",
                                requestWrapper.getIndex(RcConstants.RcIndex.AMOUNT) == null ? "0" : requestWrapper.getIndex(RcConstants.RcIndex.AMOUNT),
                                false, remark, rcArchive.getArchiveType(), keyEncrypt);
                        return Optional.of(CommonOuterResponse.fail(RcCode.RC_CACLCUELATE_ERROR.code, message));
                    } else if (valueBlack.get(RcConstants.BusinessTagerType.IDENTITY_CARD.code) != null) {
                        String idCard = valueBlack.get(RcConstants.BusinessTagerType.IDENTITY_CARD.code);
                        String message = "RC" + RcConstants.BusinessTagerType.IDENTITY_CARD.message + "存在风险";
                        String remark = "RC" + RcConstants.BusinessTagerType.IDENTITY_CARD.message + CardUtils.getHiddenCreditCardNo(idCard) + "存在风险。";

                        String keyEncrypt = idCard;
                        try {
                            keyEncrypt = hessianService.symmetricEncryptData(idCard);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        basicService.insertCalculateLog(request.getTransactionNo(),
                                customerCode,
                                rcArchive.getArchiveName(),
                                RcConstants.BusinessTagerType.IDENTITY_CARD.blackType.defineCode, "是", "/",
                                requestWrapper.getIndex(RcConstants.RcIndex.AMOUNT) == null ? "0" : requestWrapper.getIndex(RcConstants.RcIndex.AMOUNT),
                                false, remark, rcArchive.getArchiveType(), keyEncrypt);
                        return Optional.of(CommonOuterResponse.fail(RcCode.RC_CACLCUELATE_ERROR.code, message));
                    } else {
                        return Optional.of(CommonOuterResponse.fail(RcCode.RC_CACLCUELATE_ERROR.code, RcCode.RC_CACLCUELATE_ERROR.message));
                    }
                }
            }
        }
        return Optional.empty();
    }
}

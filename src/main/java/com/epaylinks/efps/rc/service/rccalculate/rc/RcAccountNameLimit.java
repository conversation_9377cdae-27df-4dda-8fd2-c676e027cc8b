package com.epaylinks.efps.rc.service.rccalculate.rc;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import com.google.common.collect.ImmutableSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;


@Service("ACCOUNT-NAME-LIMIT")
public class RcAccountNameLimit implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {

    }

    private static final Set<String> CALC_BUSINESS = ImmutableSet.of(
            Constants.BusinessCode.WITHDRAW.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.WITHDRAW_SAVINGCARD_BATCH.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD_BATCH.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD_BATCH.code
    );

    @Override
    public void reset(RcLimit rcLimit) {
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (!CALC_BUSINESS.contains(rcCalculateRequest.getBusinessCode())) {
            return CommonOuterResponse.SUCCEE;
        }
        if ("1".equals(rcLimit.getLimitValue())) { //不允许非同名银行卡转账
            String bankUserName = rcCalculteBasic.getRcIndex(rcCalculateRequest.getIndexs(), RcConstants.RcIndex.BANK_USER_NAME);
            if (bankUserName != null) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(),
                        rcLimit.getBusinessTagerId());
                if (!rcArchive.getArchiveName().equals(bankUserName)) {

                    rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),
                            rcArchive.getArchiveCode(),
                            rcArchive.getArchiveName(),
                            rcLimit.getDefineCode(), rcLimit.getLimitValue(),
                            rcArchive.getArchiveName(), bankUserName, false, "RC交易受限", rcArchive.getArchiveType());
                    //实际发生值已经大于限定值
                    return "RC交易受限";
                }
            }
        }

        return CommonOuterResponse.SUCCEE;
    }


}

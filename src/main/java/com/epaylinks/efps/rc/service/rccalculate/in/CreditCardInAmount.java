package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("CREDIT_CARD_IN_AMOUNT")
public class CreditCardInAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
    }

    @Override
    public void reset(RcLimit rcLimit) {
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (rcCalculteBasic.isCreditQuickPay(rcCalculateRequest.getBusinessCode()) ||
        rcCalculteBasic.isCreditPosPay(rcCalculateRequest.getBusinessCode())) {
            return rcCalculteBasic.singleAmountRc(rcLimit, rcCalculateRequest);
        }
        return CommonOuterResponse.SUCCEE;
    }
}

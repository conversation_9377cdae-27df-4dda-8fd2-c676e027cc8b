package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 同卡入金单月最多交易（成功+失败）总笔数（笔）,与CardMonthInTradeNumRc的区别是累计了失败的数量
 * <AUTHOR>
 * @date 2021-07-29
 *
 */
@Service("Card-Month-Max-IN-Total-Count")
public class CardMonthInTotalCountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private CardMonthInTotalCountRc self;

    private static final String Card_Day_Max_In_Total_Count = DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineCode;


    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //目前只做快捷支付
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod()) ||
                rcCalculteBasic.isPosCardPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
            if(customerCode == null || bankCardNo == null ) {
                return CommonOuterResponse.SUCCEE;
            }
            //金额
            String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
            long count = Long.parseLong(countStr);
            long dayInCount = 0L;
            long monthInCount = 0L;
            String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentMonth());
            String dayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Card_Day_Max_In_Total_Count, RcDateUtils.getCurrentDay());
            if(redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)) {
                monthInCount = redisTemplate.opsForHash().get(hashKey, bankCardNo) == null ? 0L : (Long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
            }

            if(redisTemplate.opsForHash().hasKey(dayHashKey, bankCardNo)) {
                dayInCount = redisTemplate.opsForHash().get(hashKey, bankCardNo) == null ? 0L : (Long) redisTemplate.opsForHash().get(dayHashKey, bankCardNo);
                dayInCount+=count;
            }

            if (dayInCount + monthInCount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInCount + monthInCount - count +"",count+"",false,"RC交易受限", rcArchive.getArchiveType());
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {
                //不计算
    }

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Date date = new Date();
        String lastDayHahsKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Card_Day_Max_In_Total_Count,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastDayHahsKey);// 根据昨日历史银行卡号迭代，redis会当天的月记录
        for (String bankCardNo : map.keySet()) {
            if (date.getDate() == 1) {
                // 如果当前时间是每月的一号
                self.historyDataHandler(rcLimit, bankCardNo, date, RcLimitData.Status.FINISHED.code);
                redisTemplate.opsForHash().put(hashKey, bankCardNo, 0L);// 初始化今天的值
            } else {
                // 如果不是每月的一号
                long monthInCount = self.historyDataHandler(rcLimit, bankCardNo, date, RcLimitData.Status.PROCESSING.code);
                redisTemplate.opsForHash().put(hashKey, bankCardNo, monthInCount);// 月度数据更新
            }
        }
    }


    public Long historyDataHandler(RcLimit rcLimit , String bankCardNo, Date date , String status) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String lastDayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Card_Day_Max_In_Total_Count, RcDateUtils.getLastDay());
        String businessTargetId =  rcLimit.getBusinessTagerId() + "_" + bankCardNo;

        long dayInCount = 0;
        if(redisTemplate.opsForHash().hasKey(lastDayHashKey, bankCardNo)) {
            dayInCount = (long) redisTemplate.opsForHash().get(lastDayHashKey, bankCardNo);
        }

        Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);// 昨日时间
        RcLimitData monthInCountRcLimitData = rcLimitDataMapper
                .selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
                        businessTargetId, RcDateUtils.getMonthString(selectDate) , RcLimitData.Status.PROCESSING.code);
        long monthInCount = 0;
        if (monthInCountRcLimitData == null) {
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
                    businessTargetId, rcLimit.getDefineCode(), dayInCount + "", date, date ,
                    status, RcDateUtils.getMonthString(selectDate));
            rcLimitDataMapper.insert(rcLimitData);
            monthInCount = dayInCount;
        }else {
            monthInCount = Long.parseLong(monthInCountRcLimitData.getValue()) + dayInCount;
            monthInCountRcLimitData.setValue(monthInCount + "");
            monthInCountRcLimitData.setUpdatetime(date);
            monthInCountRcLimitData.setStatus(status);
            rcLimitDataMapper.updateByPrimaryKey(monthInCountRcLimitData);
        }
        return monthInCount;
    }
}

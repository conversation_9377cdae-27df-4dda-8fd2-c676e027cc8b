package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 代付单月最高限额。出金分离为代付、提现，该为代付单月最高限额 
 * <AUTHOR>
 * @date 2021-07-29
 *
 */
@Service("Month-Max-Withdraw-Amount")
public class MonthMaxWithdrawAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;
	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;
	@Autowired
	private MonthMaxWithdrawAmountRc self;
	private static final String Day_Max_Out_Amount = "Day-Max-Withdraw-Amount";

	@Autowired
	private RcArchiveService rcArchiveService;

	@Override
	public void reset(RcLimit rcLimit) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		Date date = new Date();
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
		if (date.getDate() == 1) {
			// 如果当前时间是每月的一号
			self.historyDataHandler(rcLimit, date, RcLimitData.Status.FINISHED.code);
			redisTemplate.opsForValue().set(key, 0L);
		} else {
			// 如果不是每月的一号
			long monthOutAmount = self.historyDataHandler(rcLimit, date , RcLimitData.Status.PROCESSING.code);
			redisTemplate.opsForValue().set(key, monthOutAmount);
		}
	}


	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
	    
	    // 非代付业务不处理 20210729
        if (!rcCalculteBasic.isWithdrawals(rcCalculateRequest.getBusinessCode()) && !rcCalculteBasic.isZHFZ(rcCalculateRequest.getPayMethod())) {
            return CommonOuterResponse.SUCCEE;
        }

		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
	    String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (amountStr == null) {
			throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
		}
		long amount = Long.parseLong(amountStr);
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        String dayKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), Day_Max_Out_Amount, RcDateUtils.getCurrentDay());
		long monthOutAmount = redisTemplate.opsForValue().increment(key, 0L);
		long dayOutAmount = redisTemplate.opsForValue().increment(dayKey, 0L);
		if (monthOutAmount + dayOutAmount > Long.parseLong(rcLimit.getLimitValue())) {
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayOutAmount + monthOutAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
			//实际发生值已经大于限定值
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}


	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		// TODO Auto-generated method stub
		
	}

	public long historyDataHandler(RcLimit rcLimit , Date date , String status) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String lastDayKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Day_Max_Out_Amount , RcDateUtils.getLastDay());
		long dayOutAmount = redisTemplate.opsForValue().increment(lastDayKey, 0L);
		
		Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);
		RcLimitData monthOutAmountRcLimitData = rcLimitDataMapper.selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
						rcLimit.getBusinessTagerId(), RcDateUtils.getMonthString(selectDate) , RcLimitData.Status.PROCESSING.code);
		long monthOutAmount = 0;
		if (monthOutAmountRcLimitData == null) {
			RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
					rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), dayOutAmount + "", date, date , 
					status, RcDateUtils.getMonthString(selectDate));
			rcLimitDataMapper.insert(rcLimitData);
			monthOutAmount = dayOutAmount;
		}else {
			monthOutAmount = Long.parseLong(monthOutAmountRcLimitData.getValue()) + dayOutAmount;
			monthOutAmountRcLimitData.setValue(monthOutAmount + "");
			monthOutAmountRcLimitData.setUpdatetime(date);
			monthOutAmountRcLimitData.setStatus(status);
			rcLimitDataMapper.updateByPrimaryKey(monthOutAmountRcLimitData);
		}
		return monthOutAmount;
	}
	
}

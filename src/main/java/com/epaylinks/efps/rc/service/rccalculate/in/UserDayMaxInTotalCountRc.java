package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *  * 同人入金单日最多交易（成功+失败）总笔数（笔）。与UserDayMaxInTotalCountRc不同的是累计了失败的
 * <AUTHOR>
 * @date 2021-07-29
 *
 */
@Service("User-Day-Max-IN-Total-Count")
public class UserDayMaxInTotalCountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //目前只做快捷支付
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String identityCard =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.IDENTITY_CARD.code);
            if(customerCode == null || identityCard == null ) {
                return CommonOuterResponse.SUCCEE;
            }
            //金额
            String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
            if (StringUtils.isBlank(countStr)){
                return CommonOuterResponse.SUCCEE;
            }
            long count = Long.parseLong(countStr);
            long dayInCount = 0L;
            String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
            if(redisTemplate.opsForHash().hasKey(hashKey, identityCard)) {
                dayInCount = (long) redisTemplate.opsForHash().get(hashKey, identityCard) + count;
            }else {
                redisTemplate.opsForHash().put(hashKey,identityCard,0L);
                redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
                dayInCount = count;
            }

            if (dayInCount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInCount - count +"",count +"",false,"RC交易受限", rcArchive.getArchiveType());
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String identityNo = paramMap.get("identityCard");
        String customerCode = paramMap.get("customerCode");
        String payMethod = paramMap.get("payMethod");
        if (StringUtils.isNotBlank(identityNo) &&  StringUtils.isNotBlank(customerCode) && rcCalculteBasic.isQuickPayMethod(payMethod)){
            Long count = Long.parseLong(paramMap.get(RcConstants.RcIndex.TRADE_NUM.code));
            if (count != null){
                String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
                Long nowCount = (Long) redisTemplate.opsForHash().get(hashKey, identityNo);
                nowCount = nowCount == null ? 0L : nowCount;
                nowCount += count ;
                redisTemplate.opsForHash().put(hashKey,identityNo,nowCount);
            }
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //每日重置日最大入金为0
        Date date = new Date();
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastKey);
        for(String idCardNo: map.keySet()) {
            long dayInCount = (long) map.get(idCardNo);
            if(dayInCount > 0){
                RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), rcLimit.getBusinessTagerId() + "_" + idCardNo,
                        rcLimit.getDefineCode(), dayInCount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
                rcLimitDataMapper.insert(rcLimitData);
            }
            if(!redisTemplate.opsForHash().hasKey(hashKey, idCardNo)){// 设置今天的值
                redisTemplate.opsForHash().put(hashKey, idCardNo, 0L);
            }
        }
        redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
    }
}

package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 提现单日最高限额。出金分离为代付、提现，该类保留做提现单日最高限额  20210729 fwy 
 * <AUTHOR>
 *
 */
@Service("Day-Max-OUT-Amount")
public class DayMaxOutAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;

	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;

	@Autowired
	private RcArchiveService rcArchiveService;
	
	@Override
	public void reset(RcLimit rcLimit) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		//每日重置日最大出金为0
		Date date = new Date();
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode() , RcDateUtils.getLastDay());

		long dayOutAmount = redisTemplate.opsForValue().increment(lastKey, 0L);
		if(dayOutAmount > 0){
			RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),rcLimit.getBusinessTagerId(),
					rcLimit.getDefineCode(), dayOutAmount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
			rcLimitDataMapper.insert(rcLimitData);
		}
		Object object = redisTemplate.opsForValue().get(key);
		if(object == null){
			redisTemplate.opsForValue().set(key, 0L,32, TimeUnit.DAYS);
		}else {
			redisTemplate.expire(key,32,TimeUnit.DAYS);
		}
	}

//	@Logable(businessTag = "Day-Max-OUT-Amount:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
	    
	    // 出金分离为代付、提现，非提现业务不做判断  20210729
        if (!rcCalculteBasic.isCashWithdrawal(rcCalculateRequest.getBusinessCode())) {
            return CommonOuterResponse.SUCCEE;
        }
        
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
	    String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (StringUtils.isBlank(amountStr)) {
			throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
		}
		long amount = Long.parseLong(amountStr);
		long dayOutAmount = redisTemplate.opsForValue().increment(key, amount);
		if (dayOutAmount> Long.parseLong(rcLimit.getLimitValue())) {
			redisTemplate.opsForValue().increment(key, -amount);
			// 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayOutAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
			//实际发生值已经大于限定值
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}
	

//	@Logable(businessTag = "Day-Max-OUT-Amount:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
	    
	    // 出金分离为代付、提现，非提现业务不做判断  20210729
        if (!rcCalculteBasic.isCashWithdrawal(map.get("businessCode"))) {
            return ;
        }
        
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		if (map.get("payState").equals("01")){
			long amount = -1 * Long.parseLong(map.get(RcConstants.RcIndex.AMOUNT.code));
			String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
			redisTemplate.opsForValue().increment(key,amount);
		}

	}


}

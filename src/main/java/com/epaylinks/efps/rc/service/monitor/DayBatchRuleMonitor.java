package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RiskEventRuleMapper;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.drools.DroolsSceneService;
import com.epaylinks.efps.rc.drools.model.AddOrUpdateRuleRequest;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 16:12
 */
@Service
@Slf4j
public class DayBatchRuleMonitor {
    private static final ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5);

    @Autowired
    private RiskEventRuleMapper riskEventRuleMapper;

    @Autowired
    private DroolsSceneService droolsSceneService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private RcMonitorService rcMonitorService;

    public void monitor() {
        List<RiskEventRule> ruleList = riskEventRuleMapper.selectRuleList(RcConstants.RiskEventRuleStatus.ON.code, RcConstants.AuditStatus.SUCCESS.code).stream().filter(
                r -> droolsSceneService.isDayRule(r.getScene())
        ).collect(Collectors.toList());
        for (RiskEventRule rule : ruleList) {
            fixedThreadPool.execute(() -> {
                try {
                    monitor(rule);
                } catch (Exception e) {
                    log.error("执行规则" + rule.getRuleCode() + "异常", e);
                }
            });
        }
    }

    public void handle(Map<String, Object> result, RiskEventRule rule, AddOrUpdateRuleRequest addOrUpdateRuleRequest) {
        String customerCode = (String) result.get("CUSTOMER_CODE");
        if (addOrUpdateRuleRequest.getTriggerTypeEnum() == AddOrUpdateRuleRequest.TriggerType.INCLUDE) {
            if (!riskEventRuleService.isCustomerExclude(customerCode, rule.getRuleCode())) {
                return;
            }
        } else if (addOrUpdateRuleRequest.getTriggerTypeEnum() == AddOrUpdateRuleRequest.TriggerType.EXCLUDE) {
            if (riskEventRuleService.isCustomerExclude(customerCode, rule.getRuleCode())) {
                return;
            }
        }

        String triggerValue = String.valueOf(result.get("TRIGGER_VALUE"));

        String reason = "触发规则" + rule.getRuleCode();

        Object terminalCode = result.get("TERMINAL_CODE");
        Object businessCode = result.get("BUSINESS_CODE");

        switch (addOrUpdateRuleRequest.getThenText()) {
            case "禁止入金-次日不恢复":
                rcMonitorService.changeAccountStatus(customerCode, RcMonitorService.AccountStatus.BAN_PAY_IN, reason);
                break;
            case "禁止入金-次日恢复":
                rcMonitorService.changeAccountStatusAndRecoverAfterDays(customerCode, RcMonitorService.AccountStatus.BAN_PAY_IN, reason, 1);
                break;
            case "账户止付-次日不恢复":
                rcMonitorService.changeAccountStatus(customerCode, RcMonitorService.AccountStatus.BAN_PAY_OUT, reason);
                break;
            case "账户止付-次日恢复":
                rcMonitorService.changeAccountStatusAndRecoverAfterDays(customerCode, RcMonitorService.AccountStatus.BAN_PAY_OUT, reason, 1);
                break;
            case "停用业务-次日不恢复":
                if (businessCode != null) {
                    rcMonitorService.closeBusiness(customerCode, (String) businessCode);
                }
                break;
            case "停用业务-次日恢复":
                if (businessCode != null) {
                    rcMonitorService.closeBusinessAndReopenAfterDays(customerCode, (String) businessCode, 1);
                }
                break;
            case "停用终端-次日不恢复":
                if (terminalCode != null) {
                    rcMonitorService.closeTerm((String) terminalCode);
                }
                break;
            case "停用终端-次日恢复":
                if (terminalCode != null) {
                    rcMonitorService.closeTermAndReopenAfterDays(customerCode, (String) terminalCode, 1);
                }
                break;
        }

        rcMonitorService.addEventRecord(rule.getRuleCode(), customerCode, triggerValue);
    }

    private void monitor(RiskEventRule rule) {
        log.info("日终规则" + rule.getRuleCode() + "开始");
        AddOrUpdateRuleRequest addOrUpdateRuleRequest = JsonUtils.jsonToObj(rule.getRuleCondition(), AddOrUpdateRuleRequest.class);
        List<Map<String, Object>> resultList = droolsSceneService.executeSql(addOrUpdateRuleRequest, false);
        for (Map<String, Object> result : resultList) {
            try {
                handle(result, rule, addOrUpdateRuleRequest);
            } catch (Exception e) {
                log.error("处理" + result + "异常", e);
            }
        }
        log.info("日终规则" + rule.getRuleCode() + "结束");
    }
}

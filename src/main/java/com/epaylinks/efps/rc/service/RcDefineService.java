package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.domain.RcDefine;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018.09.28
 */
public interface RcDefineService {

	void insertSelective(RcDefine rcDefine);

	RcDefine selectByCode(String code);

	PageResult<Map<String,Object>> pageDefineQuery(String defineIdentifier, Long bigId, Long smallId, String startTime, String endTime, int pageNum, int pageSize);

}

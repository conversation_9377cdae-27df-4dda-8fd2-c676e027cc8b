package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.vo.AccoundAndSubAccounts;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("acc")
public interface AccService {

    @GetMapping("/updateRcBalance")
    CommonOuterResponse updateRcBalance(@RequestParam("customerCode")String customerCode, @RequestParam("amount")Long amount);

    @GetMapping("/accountQueryByPage")
    PageResult<AccoundAndSubAccounts> accountQueryByPage(@RequestParam(value = "page")int page ,
                                                         @RequestParam(value = "size")int size ,
                                                         @RequestParam(value = "customerCode" , required = false)String customerCode,
                                                         @RequestParam(value = "accountStatus" , required = false)Integer accountStatus
                                                         );
}

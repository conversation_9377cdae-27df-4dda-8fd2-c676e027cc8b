package com.epaylinks.efps.rc.service.rccalculate;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.PayMethod;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.QuickPayMethod;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.config.DingTalkConfig;
import com.epaylinks.efps.rc.dao.RcCalculateLogMapper;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcCalculateLog;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.domain.cum.CustomerSettleInfo;
import com.epaylinks.efps.rc.service.CumService;
import com.epaylinks.efps.rc.service.EarlyWarningService;
import com.epaylinks.efps.rc.service.GamblingPyramidService;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.util.DingTalkUtils;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class RcCalculteBasic {

    private static String KEY_FORMAT = "%s:%s:%s"; // 当前格式：targetId:defineCode:dateStr

    private static String LIMIT_LOG_FORMAT = "时间：%s, 交易订单号：%s, 商户：%s, 银行卡号：%s, 触发规则：%s, 规则指标值：%s, 累计值：%s, 交易值：%s.";

    private static String RCL = "RCL";

    @Autowired
    private RcCalculateLogMapper rcCalculateLogMapper;

    @Autowired
    private GamblingPyramidService gamblingPyramidService;

    @Autowired
    private CumService cumService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private DingTalkConfig dingTaklConfig;
    @Autowired
    private LogService logService;
    @Autowired
    private EarlyWarningService earlyWarningService;

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    /**
     * key格式：targetId:defineCode:dateStr
     *
     * @param args
     * @return
     */
    public String getKey(String... args) {
        return String.format(KEY_FORMAT, args);
    }

    /**
     * 告警日志内容
     *
     * @param transactionNo 交易订单号
     * @param targetId      风控对象
     * @param bankCardNo    银行卡号
     * @param defineCode    规则
     * @param limitValue    规则指标值
     * @param totalValue    累计值
     * @param currentValue  交易值
     * @return
     */
    public String getLimitLog(String transactionNo, String targetId, String bankCardNo, String defineCode, String limitValue, String totalValue, String currentValue) {
        return String.format(LIMIT_LOG_FORMAT, new Date(), transactionNo, targetId, bankCardNo, defineCode, limitValue, totalValue, currentValue);
    }

    @Logable(businessTag = "RcException")
    public String logException(String exMessage) {
        return exMessage;
    }

    @Logable(businessTag = "RcCalculteLimitLog")
    public String limitLog(String message) {
        return message;
    }

    @Logable(businessTag = "logDingTalkResult")
    public String logDingTalkResult(String message) {
        return message;
    }

    /**
     * 插入风控告警记录
     *
     * @param transactionNo
     * @param targetId
     * @param targetName
     * @param defineCode
     * @param limitValue
     * @param cumulativeValue
     * @param currentValue
     * @param isNum
     * @param message
     * @param businessTargetType
     */
    public void insertCalculateLog(String transactionNo, String targetId, String targetName, String defineCode, String limitValue, String cumulativeValue, String currentValue, boolean isNum, String message, String businessTargetType) {
        RcCalculateLog log = new RcCalculateLog();
        try {
            RcConstants.RcLimitDefineType rcLimitDefineType = RcConstants.RcLimitDefineType.getRcLimitType(defineCode);
            log.setId(sequenceService.nextValue("RcCalculateLog"));
            log.setLogIdentifier(getCalculateLogIdentifier());
            log.setCreateTime(new Date());
            log.setBusinessTargetType(businessTargetType);
            log.setBusinessTargetId(targetId);
            log.setBusinessTargetName(targetName);
            log.setRcLimitType(rcLimitDefineType.code);  // TODO 添加指标注意这里需要加上 。。。
            log.setRcLimitDefineCode(defineCode);
            log.setRcLimitValue(limitValue);
            log.setTransactionNo(transactionNo);
            log.setTransactionValut(currentValue);
            log.setCumulativeValue(cumulativeValue);
            log.setRcResult("拒绝");
            log.setRcMessage(message);
            if (isNum) {
                log.setLimitUnit("Long");
            } else {
                log.setLimitUnit("String");
            }
            rcCalculateLogMapper.insert(log);

            // 发送钉钉
            if (dingTaklConfig.isSendFlag()) {
                sendDingTalkMessage(log);
            }

            // 预警判断 2020.11.26
            try {
                earlyWarningService.checkCalculateLimitWarning(targetId, rcLimitDefineType.code);
            } catch (Exception e) {
                logService.printLog(e);
            }
        } catch (Exception e) {
            logService.printLog(e);
        }
    }

    public void insertCalculateLog(String transactionNo, String targetId, String targetName, String defineCode, String limitValue, String cumulativeValue, String currentValue, boolean isNum, String message, String businessTargetType,String key) {
        RcCalculateLog log = new RcCalculateLog();
        try {
            RcConstants.RcLimitDefineType rcLimitDefineType = RcConstants.RcLimitDefineType.getRcLimitType(defineCode);
            log.setId(sequenceService.nextValue("RcCalculateLog"));
            log.setLogIdentifier(getCalculateLogIdentifier());
            log.setCreateTime(new Date());
            log.setBusinessTargetType(businessTargetType);
            log.setBusinessTargetId(targetId);
            log.setBusinessTargetName(targetName);
            log.setRcLimitType(rcLimitDefineType.code);
            log.setRcLimitDefineCode(defineCode);
            log.setRcLimitValue(limitValue);
            log.setTransactionNo(transactionNo);
            log.setTransactionValut(currentValue);
            log.setCumulativeValue(cumulativeValue);
            log.setRcResult("拒绝");
            log.setRcMessage(message);
            log.setMessageEncrypt(key);
            if (isNum) {
                log.setLimitUnit("Long");
            } else {
                log.setLimitUnit("String");
            }
            rcCalculateLogMapper.insert(log);

            // 发送钉钉
            if (dingTaklConfig.isSendFlag()) {
                sendDingTalkMessage(log);
            }

            // 预警判断 2020.11.26
            try {
                earlyWarningService.checkCalculateLimitWarning(targetId, rcLimitDefineType.code);
            } catch (Exception e) {
                logService.printLog(e);
            }
        } catch (Exception e) {
            logService.printLog(e);
        }
    }

    /**
     * 建立线程发送钉钉通知
     *
     * @param log
     */
    private void sendDingTalkMessage(RcCalculateLog log) {
        new Thread() {
            @Override
            public void run() {
                String sendMsg = buildDingTalkMessage(transLogToDisplay(log));
                if (dingTaklConfig.getUrl() == null || dingTaklConfig.getToken() == null || sendMsg == null) {
                    logService.printLog("=== dingtalk send fail : config or message is null ");
                    return;
                }
                String sendResult = DingTalkUtils.sendMessage(dingTaklConfig.getUrl(), dingTaklConfig.getToken(), sendMsg);
                if (!sendResult.contains("ok")) {// 通知失败打印日志
                    logService.printLog("=== dingtalk send fail: " + sendResult);
                }
            }
        }.start();
    }

    /**
     * 转换日志相关字段为展示值
     *
     * @param log
     * @return
     */
    RcCalculateLog transLogToDisplay(RcCalculateLog log) {

        DefineCode defineCode = DefineCode.getDefineCode(log.getRcLimitDefineCode());
        RcConstants.RcLimitType rcLimitType = RcConstants.RcLimitType.getLimitType(log.getRcLimitType());
        RcConstants.RCTargetType targetType = RcConstants.RCTargetType.getTargetType(log.getBusinessTargetType());
        if (DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode.equals(log.getRcLimitDefineCode())) { // 特殊处理
            log.setRcLimitDefineCode(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(log.getBusinessTargetType())
                    ? "商户当日累计转账限次" : "证件当日累计转账限次");
        }
        if (DefineCode.DAY_MAX_OUT_COUNT.defineCode.equals(defineCode.defineCode)) { // 特殊处理
            log.setRcLimitDefineCode(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(log.getBusinessTargetType())
                    ? "商户当日累计提现限次" : "证件当日累计提现限次");
        } else {
            log.setRcLimitDefineCode(defineCode.name);
        }
        log.setBusinessTargetType(targetType.message);
        log.setRcLimitType(rcLimitType.message);
        try {
            if (log.getLimitUnit().equals("Long")) {
                long transactionValue = Long.valueOf(log.getTransactionValut());
                log.setTransactionValut(new BigDecimal(transactionValue).divide(new BigDecimal(100)) + "元");
                long rcLimitValue = Long.valueOf(log.getRcLimitValue());
                long totalValue = log.getCumulativeValue().equals("/") ? -1 : Long.valueOf(log.getCumulativeValue());
                log.setRcLimitValue(new BigDecimal(rcLimitValue).divide(new BigDecimal(100)) + "元");
                if (totalValue != -1) {
                    log.setCumulativeValue(new BigDecimal(totalValue).divide(new BigDecimal(100)) + "元");
                }
            }

            // 这里因为有几个特殊的类型，需要转换
            if (log.getRcLimitType().equals(RcConstants.RcLimitType.BLACK.code)
                    || log.getRcLimitDefineCode().equals(DefineCode.ACCOUNT_STATUS.defineCode)) {
                long transactionValue = Long.valueOf(log.getTransactionValut());
                log.setTransactionValut(new BigDecimal(transactionValue).divide(new BigDecimal(100)) + "元");
            }
        } catch (Exception e) {
            logService.printLog(e);
        }

        return log;
    }

    private String buildDingTalkMessage(RcCalculateLog log) {

        StringBuffer sb = new StringBuffer();
        sb.append("RC告警：\n");
        sb.append("风控日志编号：").append(log.getLogIdentifier()).append("\n");
        sb.append("触发时间：").append(DateUtils.formatDate(log.getCreateTime())).append("\n");
        sb.append("对象：").append(log.getBusinessTargetType()).append("\n");
        sb.append("编号：").append(log.getBusinessTargetId()).append("\n");
        sb.append("名称：").append(log.getBusinessTargetName()).append("\n");
        sb.append("触发类型：").append(log.getRcLimitType()).append("\n");
        sb.append("触发风控指标：").append(log.getRcLimitDefineCode()).append("\n");
        sb.append("指标值：").append(log.getRcLimitValue()).append("\n");
        sb.append("订单号：").append(log.getTransactionNo()).append("\n");
        sb.append("交易值：").append(log.getTransactionValut()).append("\n");
        sb.append("累计交易值：").append(log.getCumulativeValue()).append("\n");
        sb.append("风控结果：").append(log.getRcResult()).append("\n");
        sb.append("错误反馈：").append(log.getRcMessage()).append("\n");

        return sb.toString();

    }

    String getCalculateLogIdentifier() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(RCL).append(DateUtils.formatDate(new Date(), "yyyyMMddhhmmssSSS"));
        return stringBuilder.toString();
    }

    /**
     * 判断是否垫资代付业务。 实际近3年没使用 ********
     *
     * @param businessCode
     * @return
     */
    public static boolean isDzWithdraw(String businessCode) {
        if (RcConstants.BusinessCode.DZ_WITHDRAW_CREDITCARD.code.equals(businessCode)
                || RcConstants.BusinessCode.DZ_WITHDRAW_CREDITCARD_BATCH.code.equals(businessCode)
                || RcConstants.BusinessCode.DZ_WITHDRAW_SAVINGCARD.code.equals(businessCode)
                || RcConstants.BusinessCode.DZ_WITHDRAW_SAVINGCARD_BATCH.code.equals(businessCode)
        ) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否转账业务（纯代付）
     *
     * @param businessCode
     * @return
     */
    public static boolean isTransfer(String businessCode) {
        if (RcConstants.BusinessCode.WITHDRAW.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_SAVINGCARD_BATCH.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_CREDITCARD.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_CREDITCARD_BATCH.code.equals(businessCode)
        ) {
            return true;
        }
        return false;
    }

    /**
     * 是否代付业务
     *
     * @param businessCode
     * @return
     */
    public boolean isWithdrawals(String businessCode) {
        if (RcConstants.BusinessCode.WITHDRAW.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_SAVINGCARD_BATCH.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_CREDITCARD.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAW_CREDITCARD_BATCH.code.equals(businessCode)
        ) {
            return true;
        }
        return false;
    }

    public boolean isZHFZ(String payMethod) {
        return payMethod != null && payMethod.equals("48");
    }

    /**
     * 是否提现业务  ******** 按业务判断
     *
     * @param customerCode
     * @param bankCardNo
     * @return
     */
    public boolean isCashWithdrawal(String businessCode) {
        return RcConstants.BusinessCode.WITHDRAWTOSETTMENT_DEBITCARD.code.equals(businessCode)
                || RcConstants.BusinessCode.WITHDRAWTOSETTMENT_CREDITCARD.code.equals(businessCode);
    }

    /**
     * 是否提现业务
     *
     * @param customerCode
     * @param bankCardNo
     * @return
     */
    @Deprecated //  ******** 不一定提现到默认结算卡才当做提现
    public boolean isCashWithdrawal(String customerCode, String bankCardNo) {
        CustomerSettleInfo customerSettleInfo = cumService.queryCustomerSettleInfo(customerCode);
        if (customerSettleInfo != null && bankCardNo.equals(customerSettleInfo.getBankAccountNo())) {// 记录的银行卡号一致表示提现
            return true;
        }
        return false;
    }

    public boolean isQuickPayMethod(String code) {
        for (QuickPayMethod quickPayMethod : QuickPayMethod.values()) {
            if (quickPayMethod.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

    public boolean isPosCardPayMethod(String payMethod) {
        return "38".equals(payMethod);
    }

    public boolean isCreditQuickPay(String businessCode) {
        return "FZ-NocardPayCredit".equals(businessCode) ||
                "FZ-QuickPayCredit".equals(businessCode) ||
                "ProtocolPayCredit".equals(businessCode) ||
                "QuickPayCredit".equals(businessCode);
    }

    public boolean isDebitQuickPay(String businessCode) {
        return "FZ-NocardPay".equals(businessCode) ||
                "FZ-QuickPay".equals(businessCode) ||
                "ProtocolPay".equals(businessCode) ||
                "QuickPay".equals(businessCode);
    }

    public boolean isCreditPosPay(String businessCode) {
        return "POSUnionCreditCardPay_ABROAD".equals(businessCode) ||
                "POSUnionCreditCardPayRealName".equals(businessCode) ||
                "POSUnionCreditCardPay".equals(businessCode) ||
                "POSUnionNfcCreditCardPay".equals(businessCode);
    }

    public boolean isDebitPosPay(String businessCode) {
        return "POSUnionDebitCardPay_ABROAD".equals(businessCode) ||
                "POSUnionDebitCardPay".equals(businessCode) ||
                "POSUnionDebitCardPayRealName".equals(businessCode) ||
                "POSUnionNfcDebitCardPay".equals(businessCode);
    }

    public Long redisIncr(String key, Long value, Duration expireDuration) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Long incr = redisTemplate.opsForValue().increment(key, value);
        if (expireDuration != null) {
            redisTemplate.expire(key, expireDuration.getMillis(), TimeUnit.MILLISECONDS); // 设置有效期
        }
        return incr;
    }

    public Long redisGet(String key) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Object value = redisTemplate.opsForValue().get(key);
        if (value != null) {
            return Long.parseLong(String.valueOf(value));
        } else {
            return null;
        }
    }

    private void redisExpire(String key, Duration expireDuration) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Object value = redisTemplate.opsForValue().get(key);
        if (value != null) {
            redisTemplate.expire(key, expireDuration.getMillis(), TimeUnit.MILLISECONDS); // 设置有效期
        }
    }

    public Long redisIncr(String key, Long value) {
        return redisIncr(key, value, null);
    }

    private String getTagerId(RcLimit rcLimit) {
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) {
            return rcLimit.getBusinessTagerId();
        } else {
            //todo by www 临时处理，需要按账户号维度
            if (rcLimit.getDefineCode().equals(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineCode) ||
                    rcLimit.getDefineCode().equals(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineCode)) {
                String actNo = getPersonAccountNo(rcLimit.getBusinessTagerId());
                if (actNo != null) {
                    return rcLimit.getBusinessTagerType() + "_" + actNo;
                }
            }
            return rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId();
        }
    }

    private String getRedisKey(RcLimit rcLimit) {
        return getTagerId(rcLimit) + ":" + rcLimit.getDefineCode();
    }

    private String getRedisKey(RcLimit rcLimit, String date) {
        return getRedisKey(rcLimit) + ":" + date;
    }

    private String getRedisKey(RcLimit rcLimit, RcCalcDateType dateType) {
        String dateTime = getDateTime(dateType);
        return dateTime == null ? getRedisKey(rcLimit) : getRedisKey(rcLimit, dateTime);
    }

    private String getLastRedisKey(RcLimit rcLimit, RcCalcDateType dateType) {
        String dateTime = getLastDateTime(dateType);
        return dateTime == null ? getRedisKey(rcLimit) : getRedisKey(rcLimit, dateTime);
    }

    private String getDateTime(RcCalcDateType dateType) {
        switch (dateType) {
            case DAY:
                return RcDateUtils.getCurrentDay();
            case MONTH:
                return RcDateUtils.getCurrentMonth();
            case YEAR:
                return RcDateUtils.getCurrentYear();
            default:
                return null;
        }
    }

    private String getLastDateTime(RcCalcDateType dateType) {
        DateTime dateTime = new DateTime();

        switch (dateType) {
            case DAY:
                return Timex.ofDate(dateTime.minusDays(1).toDate()).to(Timex.Format.yyyyMMdd);
            case MONTH:
                return Timex.ofDate(dateTime.minusMonths(1).toDate()).to(Timex.Format.yyyyMM);
            case YEAR:
                return Timex.ofDate(dateTime.minusYears(1).toDate()).to(Timex.Format.yyyy);
            default:
                return null;
        }
    }

    public String getRcIndex(Map<String, String> data, RcConstants.RcIndex index) {
        return data == null ? null : data.get(index.code);
    }

    public Long getLongRcIndex(Map<String, String> data, RcConstants.RcIndex index) {
        String value = EpAssert.notNull(getRcIndex(data, index), RcCode.INDEX_NOT_ENOUGH);
        return Long.parseLong(value);
    }

    private RcLimitData createRcLimitData(RcLimit rcLimit, String value, RcLimitData.Status status, String dateTime) {
        RcLimitData limitData = new RcLimitData();
        limitData.setId(sequenceService.nextValue("RcLimitData"));
        limitData.setBusinesstargetid(getTagerId(rcLimit));
        limitData.setDefinecode(rcLimit.getDefineCode());
        limitData.setValue(value);
        limitData.setCreatetime(new Date());
        limitData.setUpdatetime(limitData.getCreatetime());
        limitData.setStatus(status.code);
        limitData.setDatetime(dateTime);
        return limitData;
    }

    public String calculateOutAmount(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest, RcCalcDateType rcCalcDateType) {
        String key = getRedisKey(rcLimit, rcCalcDateType);

        long amount = this.getLongRcIndex(rcCalculateRequest.getIndexs(), RcConstants.RcIndex.AMOUNT);

        long totalOutAmount = this.redisIncr(key, amount);

        if (totalOutAmount == amount && rcCalcDateType != null) {
            if (rcCalcDateType == RcCalcDateType.DAY) {
                redisExpire(key, Duration.standardDays(32));
            } else if (rcCalcDateType == RcCalcDateType.MONTH || rcCalcDateType == RcCalcDateType.YEAR) {
                redisExpire(key, Duration.standardDays(400));
            }
        }

        if (totalOutAmount > Long.parseLong(rcLimit.getLimitValue())) {
            this.redisIncr(key, -amount);
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            this.insertCalculateLog(rcCalculateRequest.getTransactionNo(),
                    rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(),
                    rcLimit.getDefineCode(),
                    rcLimit.getLimitValue(),
                    totalOutAmount - amount + "",
                    amount + "",
                    true,
                    "RC交易受限",
                    rcArchive.getArchiveType());
            //实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    public String calculateInAmount(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest, RcCalcDateType rcCalcDateType) {
        String key = getRedisKey(rcLimit, rcCalcDateType);

        long amount = this.getLongRcIndex(rcCalculateRequest.getIndexs(), RcConstants.RcIndex.AMOUNT);

        long totalInAmount = this.redisIncr(key, 0L) + amount;

        if (totalInAmount == amount && rcCalcDateType != null) {
            if (rcCalcDateType == RcCalcDateType.DAY) {
                redisExpire(key, Duration.standardDays(32));
            } else if (rcCalcDateType == RcCalcDateType.MONTH || rcCalcDateType == RcCalcDateType.YEAR) {
                redisExpire(key, Duration.standardDays(400));
            }
        }

        if (totalInAmount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            this.insertCalculateLog(rcCalculateRequest.getTransactionNo(),
                    rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(),
                    rcLimit.getDefineCode(),
                    rcLimit.getLimitValue(),
                    totalInAmount - amount + "",
                    amount + "",
                    true,
                    "RC交易受限",
                    rcArchive.getArchiveType());
            //实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    public void calculateValueOutAmount(RcLimit rcLimit, Map<String, String> map, RcCalcDateType rcCalcDateType) {
        String key = getRedisKey(rcLimit, rcCalcDateType);
        if (map.get("payState").equals("01")) { //失败回滚统计
            long amount = -1 * this.getLongRcIndex(map, RcConstants.RcIndex.AMOUNT);
            this.redisIncr(key, amount);
        }
    }

    public void calculateValueInAmount(RcLimit rcLimit, Map<String, String> map, RcCalcDateType rcCalcDateType) {
        String key = getRedisKey(rcLimit, rcCalcDateType);
        if (map.get("payState").equals("00")) { //成功，增加累计
            long amount = this.getLongRcIndex(map, RcConstants.RcIndex.AMOUNT);
            this.redisIncr(key, amount);
        }
    }

    public static enum RcCalcDateType {
        DAY,
        MONTH,
        YEAR,
        FOREVER
    }


    public void resetAmount(RcLimit rcLimit, RcCalcDateType rcCalcDateType) {
        String lastKey = this.getLastRedisKey(rcLimit, rcCalcDateType);
        String lastDateTime = this.getLastDateTime(rcCalcDateType);

        RcLimitData lastLimitData = rcLimitDataMapper.selectOneByBusinesstargetidAndDefinecodeAndDatetime(
                this.getTagerId(rcLimit), rcLimit.getDefineCode(), lastDateTime
        );
        Long lastOutAmount = this.redisGet(lastKey);
        if (lastOutAmount != null && lastOutAmount > 0) {
            if (lastLimitData == null) {
                lastLimitData = this.createRcLimitData(rcLimit, String.valueOf(lastOutAmount),
                        rcCalcDateType == RcCalcDateType.FOREVER ? RcLimitData.Status.PROCESSING : RcLimitData.Status.FINISHED,
                        lastDateTime);
                rcLimitDataMapper.insert(lastLimitData);
            } else {
                if (RcLimitData.Status.PROCESSING.code.equals(lastLimitData.getStatus())) {
                    lastLimitData.setValue(String.valueOf(lastOutAmount));
                    lastLimitData.setUpdatetime(new Date());
                    if (rcCalcDateType != RcCalcDateType.FOREVER) {
                        lastLimitData.setStatus(RcLimitData.Status.FINISHED.code);
                    }
                    rcLimitDataMapper.updateByPrimaryKeySelective(lastLimitData);
                }
            }
        }

        String key = this.getRedisKey(rcLimit, rcCalcDateType);
        String dateTime = this.getDateTime(rcCalcDateType);

        if (rcCalcDateType == RcCalcDateType.DAY) {
            this.redisIncr(key, 0L, Duration.standardDays(32));
        }

        if (rcCalcDateType == RcCalcDateType.MONTH || rcCalcDateType == RcCalcDateType.YEAR) {
            long outAmount = this.redisIncr(key, 0L, Duration.standardDays(400));

            if (outAmount > 0) {
                RcLimitData limitData = rcLimitDataMapper.selectOneByBusinesstargetidAndDefinecodeAndDatetime(
                        this.getTagerId(rcLimit), rcLimit.getDefineCode(), dateTime
                );
                if (limitData == null) {
                    limitData = this.createRcLimitData(rcLimit, String.valueOf(outAmount),
                            RcLimitData.Status.PROCESSING,
                            dateTime);
                    rcLimitDataMapper.insert(limitData);
                } else {
                    lastLimitData.setValue(String.valueOf(outAmount));
                    lastLimitData.setUpdatetime(new Date());
                    rcLimitDataMapper.updateByPrimaryKeySelective(lastLimitData);
                }
            }
        }
    }

    private static final Set<String> CALC_BUSINESS = ImmutableSet.of(
            Constants.BusinessCode.BALANCE_CONSUME.code,
            Constants.BusinessCode.BALANCE_TRANS.code,
            Constants.BusinessCode.WITHDRAW.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.WITHDRAW_SAVINGCARD_BATCH.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD_BATCH.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD_BATCH.code
    );

    public boolean isCalcPersonUserOut(RcLimit rcLimit, String businessCode, Map<String, String> indexes) {
        if (!CALC_BUSINESS.contains(businessCode)) {
            return false;
        }

        if (Constants.BusinessCode.BALANCE_CONSUME.code.equals(businessCode)) {
            return true;
        }

        String bankUserName = getRcIndex(indexes, RcConstants.RcIndex.BANK_USER_NAME);
        if (bankUserName != null) { //非同名转账
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            return !rcArchive.getArchiveName().equals(bankUserName);
        }

        return false;
    }

    public boolean riskCheck(RcConstants.BusinessTagerType targetType, String value) {
        return this.riskCheck(null, null, targetType, value);
    }

    /**
     * 调用风险核验，由核验去加黑名单。不关心风险，只关心有没核验
     *
     * @param customerCode 商户号 选填
     * @param outTradeNo   订单号 选填
     * @param targetType   核验类型
     * @param value        值
     * @return true: 核验成功 false 核验失败
     */
    public boolean riskCheck(String customerCode, String outTradeNo, RcConstants.BusinessTagerType targetType, String value) {
        try {
            GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
            if (RcConstants.BusinessTagerType.BUSINESS_LICENSE.equals(targetType)
                    || RcConstants.BusinessTagerType.IDENTITY_CARD.equals(targetType)) {
                checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.NETWORK_ACCESS);
            } else if (RcConstants.BusinessTagerType.BANK_CARD.equals(targetType)) {
                checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.TRANS);
            }
            CommonOuterResponse<?> commonOuterResponse = gamblingPyramidService.commonRiskCheck(customerCode,
                    outTradeNo, targetType.code, value, outTradeNo, 0L, checkExtraParam);
            if (commonOuterResponse.isSuccess()) {
                return true;
            } else {
                //已在黑名单或已存在核验，当核验成功
                return RcCode.ADD_BWLIST_EXIS.code.equals(commonOuterResponse.getReturnCode()) ||
                        RcCode.VERIFICATION_PERIOD.code.equals(commonOuterResponse.getReturnCode());
            }
        } catch (Exception e) {
            logService.printLog(e);
            return false;
        }
    }

    public String singleAmountRc(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
        if (StringUtils.isBlank(amountStr)) {
            throw new AppException(RcConstants.RcIndex.AMOUNT.code);
        }
        long amount = Long.parseLong(amountStr);
        if (amount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(), rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(), "/", amount + "", true, "RC交易受限", rcArchive.getArchiveType());
            //实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    public String getPersonAccountNo(String customerCode) {
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(customerCode, customerCode, UserType.PPS_USER.code);
        if (customerInfo != null && Constants.customerCategory.EFPS_PERSON.code.equals(customerInfo.getCustomerCategory())) {
            return customerInfo.getClientNo() + customerInfo.getCustomerType();
        }
        return null;
    }
}

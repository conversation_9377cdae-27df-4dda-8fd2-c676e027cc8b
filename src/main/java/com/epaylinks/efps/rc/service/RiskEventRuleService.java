package com.epaylinks.efps.rc.service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.BeanUtil;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RiskEventRuleMapper;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;
import com.epaylinks.efps.rc.vo.AuditRiskEventRuleVo;
import com.epaylinks.efps.rc.vo.AuditRiskRuleQueryResponse;
import com.epaylinks.efps.rc.vo.TxsNfcRcMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 风控管理-可疑事件监控规则业务类
 */
@Service
public class RiskEventRuleService {
    
    private static SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID 3009001 huangyuekun 黄月坤

    @Autowired
    private RiskEventRuleMapper riskEventRuleMapper;

    @Autowired
    private RiskEventRuleService  self;
    
    @Autowired
    private OtherService otherService;
    
    @Autowired
    private RcAuditRecordService rcAuditRecordService;

    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private CustService custService;

    @Autowired
    private FsService fsService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CommonLogger logger;
    
    @Logable(businessTag = "RiskEventRuleService.logException")
    public void logException(Exception e){
    }

    @Logable(businessTag = "RiskEventRuleService.logInfo")
    public void logInfo(String str){
    }

    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(10);

    /**
     * 商户是否排除风控规则
     * @param customerCode 商户编号
     * @param ruleCode 规则编号
     * @return true: 排除 false: 不排除
     */
    public boolean isCustomerExclude(String customerCode, String ruleCode) {
        if (customerCode == null) {
            return false;
        }
        Customer customer = otherService.queryCustomerDraftByCustomerNo(customerCode);
        if (customer == null) {
            return false;
        }
        List<String> filterList = new ArrayList<>();
        filterList.add(customerCode);
        filterList.add(customer.getPlatCustomerNo());
        filterList.add(customer.getServiceCustomerNo());
        filterList.removeIf(Objects::isNull);
        Set<String> list = redisTemplate.opsForSet().members("rc:ky:" + ruleCode + "#");

        if (list == null || list.size() == 0) {
            RiskEventRule riskEventRule = riskEventRuleMapper.selectRuleByCode(ruleCode);
            if (!StringUtils.isEmpty(riskEventRule.getUniqueId()) && !StringUtils.isEmpty(riskEventRule.getFileName())) {
                try {
                    List<String> customerNoList = getCustomerInfo(null,riskEventRule.getFileName(), riskEventRule.getUniqueId());
                    if (customerNoList.size() > 0) {
                        Long count = redisTemplate.opsForSet().add("rc:ky:" + ruleCode + "#",customerNoList.toArray());
                        logger.printMessage("count:" + count);
                    }

                    // 存在交集返回true
                    if (filterList.stream()
                            .filter(customerNoList::contains)
                            .collect(Collectors.toList()).size() > 0) {
                        return true;
                    }
                } catch (Exception e) {
                    logger.printMessage("设置免疫商户列表error：" + e.getMessage());
                    logger.printLog(e);
                }
            }
            return false;
        } else {
            List<String> customerNoList = objToList(list);
            if (customerNoList.size() == 0) {
                return false;
            }
            // 存在交集返回true
            if (filterList.stream()
                    .filter(customerNoList::contains)
                    .collect(Collectors.toList()).size() > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询单个可疑事件规则
     * @param ruleCode
     * @return
     */
    public RiskEventRule getRule(String ruleCode){
        if(ruleCode==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message);
        }
        return riskEventRuleMapper.selectRuleByCode(ruleCode);
    }

    /**
     * 查询可疑事件规则列表(触发专用)
     * @param ruleStatus
     * @return
     */
    public List<RiskEventRule> selectRuleListForTrigger(String ruleStatus){
        List<RiskEventRule> list = riskEventRuleMapper.selectRuleList(ruleStatus, null);
        if(list==null){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 查询可疑事件规则列表
     * @param ruleStatus
     * @return
     */
    public List<RiskEventRule> selectRuleList(String ruleStatus, Short auditStatus, String user,Map map){
        if(user==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message);
        }
        String callInfo = "查询可疑事件规则列表";
        if(RcConstants.RiskEventRuleStatus.ON.code.equals(ruleStatus)){
            callInfo = "查询启用状态-可疑事件规则列表";
        }else if(RcConstants.RiskEventRuleStatus.OFF.code.equals(ruleStatus)){
            callInfo = "查询停用状态-可疑事件规则列表";
        }
        self.logInfo(user+callInfo);
//        List<RiskEventRule> list = riskEventRuleMapper.selectRuleList(ruleStatus, auditStatus);
        List<RiskEventRule> list = riskEventRuleMapper.selectRulePage(map);
        if(list==null){
            list = new ArrayList<>();
        }
        list.forEach( rule ->{
            rule.setRuleTypeName(otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", rule.getRuleType(), true));
            if (!StringUtils.isEmpty(rule.getUniqueId())) {
                Map<String,String> fileMap = fsService.filePath(rule.getUniqueId(),30,100,"download");
                rule.setUrl(fileMap.get("filePath"));
            }
        });
        self.logInfo("查询到规则记录条数："+list.size());
        return list;
    }

    public Integer countRulePage(Map map) {
        return riskEventRuleMapper.countRulePage(map);
    }

    /**
     * 查询单条可疑事件规则
     * @param ruleId
     * @return
     */
    public RiskEventRule selectRuleById(Long ruleId){
        if(ruleId==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message);
        }
        RiskEventRule rule = riskEventRuleMapper.selectRuleByID(ruleId);
        return rule;
    }

    /**
     * 修改可疑事件规则
     * @param ruleId
     * @param ruleParam1
     * @param ruleParam2
     * @param triggerAction
     * @param user
     * @return
     */
    public int updateRule(Long ruleId,String ruleParam1,String ruleParam2,String triggerAction,Long userId,String uniqueId,String fileName) throws Exception {
        User user = otherService.selectUserById(userId);
        if (user == null){
            throw new AppException(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
        }
        
        RiskEventRule rule = selectRuleById(ruleId);
        if(rule==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":规则ID不存在");
        }
        if (RcConstants.AuditStatus.WAITING.code.equals(rule.getAuditStatus())) { // 审核中不允许修改 
            throw new AppException(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code, RcCode.AUD_STATUS_UPDATE_EXCEPTION.message);
        }
        if("01".equals(rule.getParamType()) && StringUtils.isBlank(ruleParam1)){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":规则参数不能为空");
        }
        if("02".equals(rule.getParamType()) && (StringUtils.isBlank(ruleParam1) || StringUtils.isBlank(ruleParam2))){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":规则参数不能为空");
        }
        
        if("01".equals(rule.getParamType())){
            rule.setRuleParam1(ruleParam1);
        }else if("02".equals(rule.getParamType())){
            rule.setRuleParam1(ruleParam1);
            rule.setRuleParam2(ruleParam2);
        }
        rule.setTriggerAction(triggerAction);
        rule.setUpdateTime(new Date());
        rule.setChangePerson(user.getName());

        rule.setFileName(fileName == null ? "" : fileName);
        rule.setUniqueId(uniqueId == null ? "" : uniqueId);
        
        // 判断是否需要审核
        int res = 0;
        String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        if ("1".equals(audFlag)) {
            self.saveUpdateRuleAuditRecord(rule, user);
        } else {
//            res = updateRule(rule, user.getName());
            // 需保持审核记录，免审直接更新改为自动审核 20220310
            self.saveUpdateRuleAuditRecord(rule, user);
            // 自动审核
            auditRiskRule(rule.getRuleId(), new Short("1"), "审核通过", defaultAuditUserId);
            res = 1;
        }
        
        return res;
    }

    /**
     * 保存更新规则审核记录
     * @param rule
     * @param
     */
    @Transactional
    public void saveUpdateRuleAuditRecord(RiskEventRule rule, User user) {
        RiskEventRule oldRule = selectRuleById(rule.getRuleId());
        if (oldRule == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        
        AuditRiskEventRuleVo oldVo = new AuditRiskEventRuleVo();
        BeanUtils.copyProperties(oldRule, oldVo);
        
        AuditRiskEventRuleVo newVo = new AuditRiskEventRuleVo();
        BeanUtils.copyProperties(rule, newVo);
        
        oldRule.setAuditStatus(RcConstants.AuditStatus.WAITING.code);

        // 更新
        riskEventRuleMapper.updateParamById(oldRule); // 更新原值状态为待审核
        
        // 保存待审核记录
        rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code, 
                oldRule.getRuleId(), oldVo.toString(), newVo.toString(), RcConstants.AuditActionType.UPDATE.code, user.getUid(),null);
    }
    

    private int updateRule(RiskEventRule rule, String userName) {
        
        int res = riskEventRuleMapper.updateParamById(rule);
        
        // 碰一碰规则，需要同步更新txs_nfc_rc_mode阈值等信息
        if ("PYP_MONITOR".equals(rule.getRuleType())) {
            Long value = Long.parseLong(rule.getRuleParam1());
            TxsNfcRcMode nfcRcMode = new TxsNfcRcMode();
            nfcRcMode.setRcRuleCode(rule.getRuleCode());
            nfcRcMode.setValue("R011".equals(rule.getRuleCode())|| "R016".equals(rule.getRuleCode()) ?  value * 100 : value); // 金额限额需乘以100
            nfcRcMode.setBandOutCome("账户止付".equals(rule.getTriggerAction()) ? "1" : "0"); // 账户止付
            nfcRcMode.setBandBusiness("停用标签".equals(rule.getTriggerAction()) ? "1" : "0"); // 停用标签
            nfcRcMode.setValid("01".equals(rule.getRuleStatus()) ? "1" : "0"); // 1为有效

            riskEventRuleMapper.updateNFCByRuleCodeSelective(nfcRcMode);
        }
        self.logInfo(userName+"修改可疑事件规则"+rule.getRuleCode());
        return res;
    }
    
    /**
     * 校验规则IDs
     * @param ruleIds
     * @return
     */
    public boolean checkRuleIds(String[] ruleIds){
        if(ruleIds==null || ruleIds.length==0){
            return false;
        }
        boolean isNum = true;
        for(String id : ruleIds){
            if(!StringUtils.isNumeric(id)){
                isNum = false;
                break;
            }
        }
        if(!isNum){
            return false;
        }
        return true;
    }

    /**
     * 启用可疑事件规则
     * @param ruleId
     * @param user
     * @return
     */
    public int setRuleON(Long ruleId, User user){
        RiskEventRule rule = selectRuleById(ruleId);
        if(user==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":操作用户不存在");
        }
        if(rule==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":规则ID不存在");
        }
        if(RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())){
            return 0;
        }
        if (RcConstants.AuditStatus.WAITING.code.equals(rule.getAuditStatus())) { // 审核中不允许修改 
            throw new AppException(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code, RcCode.AUD_STATUS_UPDATE_EXCEPTION.message);
        }
        
        RiskEventRule data = new RiskEventRule();
        data.setRuleId(ruleId);
        data.setUpdateTime(new Date());
        data.setChangePerson(user.getName());
        data.setOnTime(formatDate.format(new Date()));
        
        int res = 0;
        String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        if ("1".equals(audFlag)) {
            data.setRuleStatus(RcConstants.RiskEventRuleStatus.ON.code);
            self.saveUpdateRuleAuditRecord(data, user);
        } else { // 不需审核，走原逻辑
            /*res = riskEventRuleMapper.setRuleON(data);
            
            // 碰一碰规则，需要同步更新txs_nfc_rc_mode 状态
            if ("R010".equals(rule.getRuleCode()) || "R011".equals(rule.getRuleCode()) 
                    || "R012".equals(rule.getRuleCode()) || "R013".equals(rule.getRuleCode())) {
                TxsNfcRcMode nfcRcMode = new TxsNfcRcMode();
                nfcRcMode.setRcRuleCode(rule.getRuleCode());
                nfcRcMode.setValid("1"); // 1为有效
                
                riskEventRuleMapper.updateNFCByRuleCodeSelective(nfcRcMode);
            }*/
            
            // 需保持审核记录，免审直接更新改为自动审核 20220310
            data.setRuleStatus(RcConstants.RiskEventRuleStatus.ON.code);
            self.saveUpdateRuleAuditRecord(data, user);
            // 自动审核
            auditRiskRule(rule.getRuleId(), new Short("1"), "审核通过", defaultAuditUserId);
            self.logInfo(user+"启用可疑事件规则"+rule.getRuleCode());
        }
        return res;
    }

    /**
     * 停用可疑事件规则
     * @param ruleId
     * @param user
     * @return
     */
    public int setRuleOFF(Long ruleId, User user){
        
        RiskEventRule rule = selectRuleById(ruleId);
        if(user==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":操作用户不存在");
        }
        if(rule==null){
            throw new AppException(RcCode.PARAM_ERROR.code, RcCode.PARAM_ERROR.message+":规则ID不存在");
        }
        if(RcConstants.RiskEventRuleStatus.OFF.code.equals(rule.getRuleStatus())){
            return 0;
        }
        if (RcConstants.AuditStatus.WAITING.code.equals(rule.getAuditStatus())) { // 审核中不允许修改 
            throw new AppException(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code, RcCode.AUD_STATUS_UPDATE_EXCEPTION.message);
        }
        
        RiskEventRule data = new RiskEventRule();
        data.setRuleId(ruleId);
        data.setUpdateTime(new Date());
        data.setChangePerson(user.getName());
        data.setOffTime(formatDate.format(new Date()));
        
        int res = 0;
        String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        if ("1".equals(audFlag)) {
            data.setRuleStatus(RcConstants.RiskEventRuleStatus.OFF.code);
            self.saveUpdateRuleAuditRecord(data, user);
        } else { // 不需审核，走原逻辑
           /* res = riskEventRuleMapper.setRuleOFF(data);
            
            // 碰一碰规则，需要同步更新txs_nfc_rc_mode 状态
            if ("R010".equals(rule.getRuleCode()) || "R011".equals(rule.getRuleCode()) 
                    || "R012".equals(rule.getRuleCode()) || "R013".equals(rule.getRuleCode())) {
                TxsNfcRcMode nfcRcMode = new TxsNfcRcMode();
                nfcRcMode.setRcRuleCode(rule.getRuleCode());
                nfcRcMode.setValid("0"); // 0为无效
                
                riskEventRuleMapper.updateNFCByRuleCodeSelective(nfcRcMode);
            }*/
            // 需保持审核记录，免审直接更新改为自动审核 20220310
            data.setRuleStatus(RcConstants.RiskEventRuleStatus.OFF.code);
            self.saveUpdateRuleAuditRecord(data, user);
            // 自动审核
            auditRiskRule(rule.getRuleId(), new Short("1"), "审核通过", defaultAuditUserId);
            self.logInfo(user+"启用可疑事件规则"+rule.getRuleCode());
            self.logInfo(user+"停用可疑事件规则"+rule.getRuleCode());
        }
        return res;
    }

    public void saveExcludeCustomer(RiskEventRule rule) throws Exception {
        List<String> customerNoList = new ArrayList<>();
        if (!StringUtils.isEmpty(rule.getFileName()) && !StringUtils.isEmpty(rule.getUniqueId())) {
            customerNoList = getCustomerInfo(null,rule.getFileName(), rule.getUniqueId());
        }
        // 免疫商户放入redis中
        redisTemplate.delete("rc:ky:" + rule.getRuleCode() + "#");
        if (customerNoList.size() > 0) {
            redisTemplate.opsForSet().add("rc:ky:" + rule.getRuleCode() + "#",customerNoList.toArray());
        }

        logger.printMessage("打印免疫商户list：" + customerNoList.size());
    }

    /**
     * 审核可疑事件规则
     * @param ruleId
     * @param auditResult
     * @param remarks
     * @param userId
     */
    @Transactional
    public void auditRiskRule(Long ruleId, Short auditResult, String remarks, Long userId) {

        RiskEventRule rule = selectRuleById(ruleId);
        if (rule == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code, ruleId);

        if (RcConstants.AuditStatus.SUCCESS.code.equals(auditResult)) {// 审核成功
            
            if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())){
                AuditRiskEventRuleVo newValueObj = JSONObject.parseObject(auditRecord.getNewValue(), AuditRiskEventRuleVo.class);
                BeanUtil.copyPropertiesIgnoreNull(rule, newValueObj);
                rule.setChangePerson(newValueObj.getChangePerson()); // 操作人按原修改记录操作人记录
                rule.setUpdateTime(new Date()); // 更新时间按审核时间记录
                rule.setAuditStatus(RcConstants.AuditStatus.SUCCESS.code); // 审核状态
                rule.setRemarks(remarks);
                rule.setUniqueId(newValueObj.getUniqueId());
                rule.setFileName(newValueObj.getFileName());
                updateRule(rule, newValueObj.getChangePerson());
                try {
                    List<String> customerNoList = new ArrayList<>();
                    if (!StringUtils.isEmpty(newValueObj.getFileName()) && !StringUtils.isEmpty(newValueObj.getUniqueId())) {
                        customerNoList = getCustomerInfo(null,newValueObj.getFileName(), newValueObj.getUniqueId());
                    }
                    // 免疫商户放入redis中
                    redisTemplate.delete("rc:ky:" + rule.getRuleCode() + "#");
                    if (customerNoList.size() > 0) {
                        redisTemplate.opsForSet().add("rc:ky:" + rule.getRuleCode() + "#",customerNoList.toArray());
                    }

                    logger.printMessage("打印免疫商户list：" + customerNoList.size());
                } catch (Exception e) {
                    logger.printMessage("审核通过设置免疫商户error：" + e.getMessage());
                    logger.printLog(e);
                    if (e instanceof AppException) {
                        throw new AppException(((AppException) e).getErrorCode(),((AppException) e).getErrorMsg());
                    } else {
                        throw new AppException(RcCode.PARAM_ERROR.code,e.getMessage());
                    }
                }
            } 
            
        } else {// 驳回
           if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())
                    || RcConstants.AuditActionType.DELETE.code.equals(auditRecord.getActionType())){
                // 更新或删除被驳回时，更新记录状态即可，不需其他处理
               rule.setAuditStatus(RcConstants.AuditStatus.FAIL.code); // 审核状态
               rule.setRemarks(remarks);
               riskEventRuleMapper.updateParamById(rule);
            }
        }
        // 更新审核记录状态
        rcAuditRecordService.updateAuditRecordStatus(RcConstants.AuditTargetType.RISK_RULE.code, ruleId, auditResult, userId, remarks);

    }

    public AuditRiskRuleQueryResponse queryAuditBwList(Long ruleId) {

        RiskEventRule oldRule = selectRuleById(ruleId);
        if (oldRule == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_RULE.code, ruleId);
        if (auditRecord == null) {
            throw new AppException(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        // 旧数据
//        AuditRiskEventRuleVo oldValueObj = JSONObject.parseObject(auditRecord.getOldValue(), AuditRiskEventRuleVo.class);
        // 新数据        
        AuditRiskEventRuleVo newValueObj = JSONObject.parseObject(auditRecord.getNewValue(), AuditRiskEventRuleVo.class);
        
        RiskEventRule newRule = new RiskEventRule();
        BeanUtil.copyPropertiesIgnoreNull(newRule, oldRule);
        BeanUtil.copyPropertiesIgnoreNull(newRule, newValueObj);
        
        oldRule.setRuleTypeName(otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", oldRule.getRuleType(), true));
        newRule.setRuleTypeName(otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", newRule.getRuleType(), true));
        if (!StringUtils.isEmpty(oldRule.getUniqueId())) {
            Map<String,String> fileMap = fsService.filePath(oldRule.getUniqueId(),30,100,"download");
            oldRule.setUrl(fileMap.get("filePath"));
        }
        if (!StringUtils.isEmpty(newRule.getUniqueId())) {
            Map<String,String> fileMap = fsService.filePath(newRule.getUniqueId(),30,100,"download");
            newRule.setUrl(fileMap.get("filePath"));
        }

        AuditRiskRuleQueryResponse bwObj = new AuditRiskRuleQueryResponse();
        bwObj.setActionType(auditRecord.getActionType());
        bwObj.setNewObject(newRule);
        bwObj.setOldObject(oldRule);
        bwObj.setRuleId(ruleId);
        
        return bwObj;
    }

    public RiskEventRule getRule(String customerCode, RcConstants.RiskEventRule ruleCode) {
        RiskEventRule rule = this.getRule(ruleCode.code);
        if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())) {
            return isCustomerExclude(customerCode, ruleCode.code) ? null : rule;
        } else {
            return null;
        }
    }

    public List<String> getCustomerInfo(MultipartFile multipartFile,String fileName, String uniqueId) throws Exception {
        List<String> customerNoList = new ArrayList<>();
        Map<Integer, List<String>> dataMap = batchTaskService.getFileInfo(multipartFile,fileName,uniqueId);
        for(Integer rowNo : dataMap.keySet()) {
            List<String> cellList = dataMap.get(rowNo);

            String customerNo = cellList.get(1);
            String customerName = cellList.get(2);
            if (StringUtils.isEmpty(customerNo) || StringUtils.isEmpty(customerName)) {
                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：商户编号或商户名称不能为空");
            }
//            Customer customer = otherService.queryCustomerDraftByCustomerNo(customerNo);
////            Customer customer = custService.queryCustomerByCustomerNo(customerNo);
//            if (customer == null || StringUtils.isEmpty(customer.getCustomerNo())) {
//                throw new AppException(RcCode.PARAM_ERROR.code,"商户编号不存在：" + customerNo);
//            }
            if (!customerNoList.contains(customerNo.trim())) {
                customerNoList.add(customerNo.trim());
            }
        }
        if (customerNoList.size() > 0) {
            Integer count = otherService.queryCustomerDraftByCustomerNoList(customerNoList);
            if (count != customerNoList.size()) {
                throw new AppException(RcCode.PARAM_ERROR.code,"商户编号不存在：" + (customerNoList.size() - count));
            }
        }
        return customerNoList;
    }

    public CommonOuterResponse uploadExcel(MultipartFile file) {
        Map<String,String> result = new HashMap<>();
        try {
            String uploadToken = fsService.uploadToken("excel_attachments","rc","0","免触发商户附件");
            //上传文件
            FileUploadResponse resp = fsService.uploadFile(file, uploadToken,"rc");
            if(!CommonResponse.SUCCEE.equals(resp.getResultCode())) {
                throw new AppException(resp.getResultCode(), resp.getResultMsg());
            }
            Map<String,String> fileMap = fsService.filePath(resp.getUniqueId(),30,100,"download");
            result.put("filePath",fileMap.get("filePath"));
            result.put("fileName",fileMap.get("fileName"));
            result.put("uniqueId",resp.getUniqueId());
        } catch (Exception e) {
            logger.printMessage("上传Excel失败：" + e.getMessage());
            logger.printLog(e);
            throw new AppException(RcCode.UPLOAD_FAIL.code,RcCode.UPLOAD_FAIL.message + "：" + e.getMessage());
        }
        return CommonOuterResponse.success(result);
    }


    public List<String> objToList(Object obj) {
        List<String> list = new ArrayList<>();
        if (obj instanceof ArrayList<?>) {
            for (String o : (List<String>) obj) {
                list.add(o);
            }
            return list;
        }
        if (obj instanceof Set<?>) {
            for (String o : (Set<String>) obj) {
                list.add(o);
            }
            return list;
        }
        return null;
    }
}


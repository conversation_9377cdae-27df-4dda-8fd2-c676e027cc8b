package com.epaylinks.efps.rc.service;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.vo.AuditBwQueryResponse;
import com.epaylinks.efps.rc.vo.BwListVo;

/**
 * <AUTHOR>
 */
public interface BwListService {


    /**
	 * 新增黑白名单，并设置缓存
	 * @param bwList
	 */
	void saveBwList(BwList bwList) throws Exception;

	/**
	 * 审核黑白名单
	 * @param bwId
	 * @param auditResult 1：审核通过； 2：驳回
	 * @param userId
	 * @throws Exception
	 */
    public void auditBwList(Long bwId, Short auditResult, Long userId,String comments) throws Exception;

	/**
	 * 批量审核黑白名单
	 * @param bwIds
	 * @param auditResult
	 * @param userId
	 * @throws Exception
	 */
	void batchAuditBwList(String bwIds, Short auditResult, Long userId,String comments) throws Exception;

	/**
	 * 根据业务类型和ID查询黑白名单
	 * @param bwType
	 * @param list
	 * @return
	 */
	Map<String,Boolean> queryValueInBw(String bwType, List<String> list);

	/**
	 * 查询某个黑白名单是否已存在
	 * @param bwList
	 * @return
	 */
	boolean queryExistence(BwList bwList);

	/**
	 * 根据ID删除黑白名单
	 * @param bwId
	 * @return
	 */
	void deleteByPrimaryKey(Long bwId);

	/**
	 * 更新黑白名单
	 * @param bwList
	 */
	void updateByPrimaryKeySelective(BwList bwList) throws Exception;

	/**
	 * 条件分页查询
	 * @param pageNum
	 * @param pageSize
	 * @param map
	 * @return
	 */
	PageResult<BwListVo> pageQuery(int pageNum, int pageSize, Map<String, Object> map,Boolean download) throws Exception;

	List<BwListVo> exportBwList(Map<String, Object> map);

	/**
	 * 是否黑名单
	 *
	 * @param businessTagerType 业务对象类型
	 * @param businessTagerId   业务对象Id
	 * @return
	 */
	boolean isBlackList(String businessTagerType, String businessTagerId);
	
	/**
     * 
     * 判断是黑名单或是白名单（同属黑白名单，则白名单优先）
     * @param businessTagerType 业务对象类型
     * @param businessTagerId   业务对象Id
     * @return 黑名单: < 0, 白名单： > 0，其他：= 0
     */
    int blackOrWhite(String businessTagerType, String businessTagerId);

    /**
     * 查询审核中黑白名单信息
     * @param bwId
     * @return
     */
    public AuditBwQueryResponse queryAuditBwList(Long bwId);

    /**
     * 保存新增名单待审核记录
     * @param bwList
     * @return 
     * @throws Exception
     */
    public RcAuditRecord saveCreateBwAuditRecord(BwList bwList, Long userId) throws Exception;
    
    /**
     * 保存删除名单审核记录
     * @param bwId
     */
    public void saveDelBwAuditRecord(Long bwId, Long userId);

    /**
     * 保存更新名单审核记录
     * @param bwList
     */
    public void saveUpdateBwAuditRecord(BwList bwList, Long userId);
    
    /**
     * 根据证件号冻结商户（添加证件号黑名单暂不需要根据证件号冻结2021.2.5）
     * @param bwRecord
     */
    void frozenByBwListCertNo(BwList bwRecord);

	/**
	 * 根据类型和名单查询
	 * @param businessTargetType
	 * @param businessTargetId
	 * @return
	 */
	List<BwList> getByTagerTypeAndTagerId(String businessTargetType,String businessTargetId);

	List<BwList> getByTagerType(String businessTargetType);

	/**
	 * 给名单加哈希
	 */
	void hashOldInfo();

	/**
	 * 获取对象风险标签，
	 * @param businessTargetType
	 * @param businessTargetId
	 * @return 风险标签中文描述的列表
	 */
	List<String> getBwRiskTags(String businessTargetType,String businessTargetId);

	/**
	 * 将表里的风险标签字段翻译中文标签列表
	 * @param riskTag 表里的风险标签字段
	 * @return 风险标签中文描述的列表
	 */
	List<String> translateRiskTag(String riskTag);

	void processStockData();

	/**
	 * 获取风险标签代码
	 * @param businessTargetType
	 * @param businessTargetId
	 * @return 非黑名单时:norisk ； 黑名单无风险标签时 none；其它取表里的标签值
	 */
	List<String> getBwRiskTagCode(String businessTargetType, String businessTargetId);

	/**
	 * 获取校验名单中命中的黑名单
	 * @param typeValue 待校验的名单
	 * @return 命中的内部黑名单
	 */
	Map<String, String> getInnerBlackList(Map<String, String> typeValue);
}

package com.epaylinks.efps.rc.service.rccalculate.out;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("Daliy-Out-Limit")
public class DailyOutLimit implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        if ("0".equals(map.get(RcConstants.RcIndex.WITHDRAW_CHANNEL_TYPE.code))) {
            rcCalculteBasic.calculateValueOutAmount(rcLimit, map, RcCalculteBasic.RcCalcDateType.DAY);
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        rcCalculteBasic.resetAmount(rcLimit, RcCalculteBasic.RcCalcDateType.DAY);
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if ("0".equals(rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.WITHDRAW_CHANNEL_TYPE.code))) {
            return rcCalculteBasic.calculateOutAmount(rcLimit, rcCalculateRequest, RcCalculteBasic.RcCalcDateType.DAY);
        }
        return CommonOuterResponse.SUCCEE;
    }
}

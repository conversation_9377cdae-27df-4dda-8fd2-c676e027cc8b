package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service("User-Day-Max-IN-Amount")
public class UserDayMaxInAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //目前只做快捷支付
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String identityCard =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.IDENTITY_CARD.code);
            if(customerCode == null || identityCard == null ) {
                return CommonOuterResponse.SUCCEE;
            }
            //金额
            String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
            long amount = Long.parseLong(amountStr);
            long dayInAmount = 0L;
            String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
            if(redisTemplate.opsForHash().hasKey(hashKey, identityCard)) {
                dayInAmount = (long) redisTemplate.opsForHash().get(hashKey, identityCard) + amount;
            }else {
                redisTemplate.opsForHash().put(hashKey,identityCard,0L);
                redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
                dayInAmount = amount;
            }

            if (dayInAmount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInAmount - amount +"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String payState =  paramMap.get("payState");
        if(payState.equals("00")){
            String identityCard = paramMap.get("identityCard");
            String customerCode = paramMap.get("customerCode");
            String payMethod = paramMap.get("payMethod");
            if (StringUtils.isNotBlank(identityCard) &&  StringUtils.isNotBlank(customerCode) && rcCalculteBasic.isQuickPayMethod(payMethod)){
                long amount = Long.parseLong(paramMap.get(RcConstants.RcIndex.AMOUNT.code));
                String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
                Long nowAmount = (Long) redisTemplate.opsForHash().get(hashKey, identityCard);
                nowAmount = nowAmount == null ? 0L : nowAmount;
                nowAmount += amount ;
                redisTemplate.opsForHash().put(hashKey,identityCard,nowAmount);
                if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) {
                    rcCalculteBasic.redisIncr(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode + ":" + RcDateUtils.getCurrentDay() + ":" + identityCard, amount,
                            Duration.standardDays(2));
                }
            }
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //每日重置日最大入金为0
        Date date = new Date();
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastKey);
        for(String idCardNo: map.keySet()) {
            long dayInAmount = (long) map.get(idCardNo);
            if(dayInAmount > 0){
                RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), rcLimit.getBusinessTagerId() + "_" + idCardNo,
                        rcLimit.getDefineCode(), dayInAmount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
                rcLimitDataMapper.insert(rcLimitData);
            }
            if(!redisTemplate.opsForHash().hasKey(hashKey, idCardNo)){// 设置今天的值
                redisTemplate.opsForHash().put(hashKey, idCardNo, 0L);
            }
        }
        redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
    }
}

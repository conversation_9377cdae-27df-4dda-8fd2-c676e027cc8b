package com.epaylinks.efps.rc.service.rccalculate.wd;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 单日转账最大交易次数限制
 * 
 * <AUTHOR>
 * @date 2020-06-23
 *
 */
@Service("Withdraw-Day-Max-Count")
public class WithdrawDayMaxCountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate;
    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Logable(businessTag = "Withdraw-Day-Max-Count:reset")
    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 每日重置日最大垫资代付金额为0
        Date date = new Date();
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(),
                rcLimit.getDefineCode(), RcDateUtils.getCurrentDay());
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(),
                rcLimit.getDefineCode(), RcDateUtils.getLastDay());
        long dayCount = redisTemplate.opsForValue().increment(lastKey, 0L);
        if (dayCount > 0) { // 日数据为0不写入
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
                    rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(),
                    dayCount + "", date, date, RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
            rcLimitDataMapper.insert(rcLimitData);
        }
        Object object = redisTemplate.opsForValue().get(key);
        if (object == null) {
            redisTemplate.opsForValue().set(key, 0L, 32, TimeUnit.DAYS);
        } else {
            redisTemplate.expire(key, 32, TimeUnit.DAYS);
        }

    }

//    @Logable(businessTag = "Withdraw-Day-Max-Count:calculate")
    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if (!RcCalculteBasic.isTransfer(rcCalculateRequest.getBusinessCode())) {// 非转账业务返回true
            return CommonOuterResponse.SUCCEE;
        }
        String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
        if (StringUtils.isBlank(countStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long count = Long.parseLong(countStr);
        String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(),
                RcDateUtils.getCurrentDay());
        long dayCount = redisTemplate.opsForValue().increment(key, count);
        if (dayCount > Long.parseLong(rcLimit.getLimitValue())) {
            redisTemplate.opsForValue().increment(key, -count);
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(),
                    rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(),
                    dayCount - count + "", count + "", false, "RC交易受限", rcArchive.getArchiveType());
            // 实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

//    @Logable(businessTag = "Withdraw-Day-Max-Count:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        
        if (map.get("payState").equals("01")) {
            String businessCode = map.get("businessCode");
            if (!RcCalculteBasic.isTransfer(businessCode)) {
                return;
            }
            long amount = Long.parseLong(map.get(RcConstants.RcIndex.TRADE_NUM.code));
            String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(),
                    RcDateUtils.getCurrentDay());
            redisTemplate = myRedisTemplateService.getMyRedisTemplate();
            redisTemplate.opsForValue().increment(key, -amount);
        }
    }

}

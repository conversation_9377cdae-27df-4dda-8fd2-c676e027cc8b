package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.RcLimitMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 9:36
 */
@Service
public class RcLimitHelper {
    @Value("${rcLimitRedisTtlSeconds}")
    private Long rcLimitRedisTtlSeconds; // 缓存存活秒数

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RcLimitMapper rcLimitMapper;

    @Autowired
    private RcArchiveMapper rcArchiveMapper;

    private String getRedisKey(String businessTagerType, String businessTagerId, String businessType) {
        String redisKey = businessTagerId + businessType;
        if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(businessTagerType)) { // 证件类型/终端维度扩展
            redisKey = businessTagerType + "_" + redisKey;
        }
        return redisKey;
    }

    /**
     * @param businessTagerType 如：005 001
     * @param businessTagerId   如：LOW_LEVEL，商户号，证件号
     * @param businessType      如：SD RC WD CJ DF
     * @return 如果查询不到则返回null
     */
    public Map<String, RcLimit> getRcLimit(String businessTagerType, String businessTagerId, String businessType) {
        String redisKey = getRedisKey(businessTagerType, businessTagerId, businessType);

        //查缓存
        Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(redisKey, businessType);

        if (map == null) {
            //无缓存，查数据库
            List<RcLimit> list = rcLimitMapper.selectAllByBusinnesTypeAndBusinessTagerTypeAndBusinessTagerId(
                    businessType, businessTagerType, businessTagerId);

            if (!list.isEmpty()) {
                map = list.stream().collect(Collectors.toMap(RcLimit::getDefineCode, limit -> limit));

                //个人风控RC指标ACCOUNT-NAME-LIMIT，如果没有配置，需要取默认配置
                if ("RC".equals(businessType) &&
                        RcConstants.BusinessTagerType.PERSON.code.equals(businessTagerType) &&
                        !map.containsKey(DefineCode.ACCOUNT_NAME_LIMIT.defineCode)) {
                    RcArchive rcArchive = rcArchiveMapper.selectByCodeOrName(businessTagerId, null);
                    if (rcArchive != null) {
                        List<RcLimit> rcList = rcLimitMapper.selectAllByBusinnesTypeAndBusinessTagerTypeAndBusinessTagerId(
                                businessType, businessTagerType, rcArchive.getRcLevel());
                        map.putAll(rcList.stream().collect(Collectors.toMap(RcLimit::getDefineCode, limit -> {
                            limit.setBusinessTagerId(businessTagerId);
                            return limit;
                        })));
                    }
                }
            } else {
                map = new HashMap<>();
            }

            redisTemplate.opsForHash().put(redisKey, businessType, map);
            redisTemplate.expire(redisKey, rcLimitRedisTtlSeconds, TimeUnit.SECONDS);
        }

        return map;
    }

    public Map<String, RcLimit> getRcLimitIfNotExistDefault(String businessTagerType, String businessTagerId, String businessType) {
        Map<String, RcLimit> cache = getRcLimit(businessTagerType, businessTagerId, businessType);
        if (CollectionUtils.isEmpty(cache)) {
            //取父级
            if (RcConstants.BusinessTagerType.PERSON.code.equals(businessTagerType) ||
                    RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(businessTagerType) ||
                    RcConstants.BusinessTagerType.PLAT_CUSTOMER.code.equals(businessTagerType)) {
                RcArchive rcArchive = rcArchiveMapper.selectByCodeOrName(businessTagerId, null);
                if (rcArchive != null) {
                    // 优先取父级商户配置，父级商户指标为空，再取默认等级限额值 2020.6.28
                    if (rcArchive.getParentCode() != null) {
                        cache = getRcLimit(businessTagerType, rcArchive.getParentCode(), businessType);
                    }

                    if (CollectionUtils.isEmpty(cache)) {
                        // 父级商户指标为空，则获取默认限额值
                        cache = getRcLimit(businessTagerType, rcArchive.getRcLevel(), businessType);
                    }
                }
            } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(businessTagerType)) {
                RcArchive rcArchive = rcArchiveMapper.selectByTypeAndCode(businessTagerType, businessTagerId);
                if (rcArchive != null) {
                    cache = getRcLimit(businessTagerType, rcArchive.getRcLevel(), businessType);
                }
            }

            if (!CollectionUtils.isEmpty(cache)) { //如果没有独立的限额，去掉机构限额
                cache.remove(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode);
                cache.remove(DefineCode.INST_USER_DAY_IN_AMOUNT.defineCode);
            }
        }
        if (!CollectionUtils.isEmpty(cache)) {
            cache.forEach((s, limit) -> limit.setBusinessTagerId(businessTagerId));
        }
        return cache;
    }
}

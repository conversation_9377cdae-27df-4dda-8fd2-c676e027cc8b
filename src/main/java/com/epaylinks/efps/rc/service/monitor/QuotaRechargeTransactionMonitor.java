package com.epaylinks.efps.rc.service.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.tool.pay.AmountUtils;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.service.RcRedisService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.util.RcDateUtils;
import org.joda.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 14:06
 */
@Service
public class QuotaRechargeTransactionMonitor implements TransactionMonitor {
    @Autowired
    RcRedisService rcRedisService;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    private static final Logger log = LoggerFactory.getLogger(QuotaRechargeTransactionMonitor.class);

    private static String getChinese(String content) {
        return Pattern.compile("[^\u4e00-\u9fa5]").matcher(content).replaceAll("").trim();
    }

    @Override
    public void monitor(RcTxsOrder order) {
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(order.getCustomerCode(), order.getCustomerCode(), UserType.PPS_USER.code);

        Map<String, String> businessTargetIds = JSON.parseObject(order.getBusinessTargetIds(), new TypeReference<Map<String, String>>() {
        });
        Map<String, String> params = JSON.parseObject(order.getIndexs(), new TypeReference<Map<String, String>>() {
        });

        String bankUserName = params.get(RcConstants.RcIndex.BANK_USER_NAME.code);
        String debtorAccount = businessTargetIds.get(RcConstants.BusinessTagerType.BANK_CARD.code);

        String debtorAccountName = bankUserName == null ? "" : getChinese(bankUserName);
        String customerName = getChinese(customerInfo.getName());

        if (customerName.equals(debtorAccountName)) {
            return;
        }

        log.info("易票联单号[{}][{}]!=[{}]", order.getTransactionNo(), debtorAccountName, customerName);

        String key = "RC_EVENT:" +
                debtorAccount + ":" +
                RcConstants.RiskEventRule.R021.code + ":"
                + order.getBusinessCode() + ":"
                + RcDateUtils.getCurrentDay();
        long count = rcRedisService.redisIncr(key, 1L, Duration.standardDays(2));
        RiskEventRule rule21 = riskEventRuleService.getRule(order.getCustomerCode(), RcConstants.RiskEventRule.R021);
        if (rule21 != null && rule21.getRuleParam1() != null) {
            long rcCount = Long.parseLong(rule21.getRuleParam1());
            if (count >= rcCount) {
                log.info("同一银行卡当日入金次数[{}]>=[{}]笔", count, rcCount);
                riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R021.code,
                        order.getCustomerCode(), customerName, String.valueOf(count));
            }
        }

        RiskEventRule rule20 = riskEventRuleService.getRule(order.getCustomerCode(), RcConstants.RiskEventRule.R020);
        if (rule20 != null && rule20.getRuleParam1() != null) {
            long rcAmount = Long.parseLong(rule20.getRuleParam1()) * 100;
            if (order.getAmount() >= rcAmount) {
                log.info("单笔入金[{}]>=[{}]元", order.getAmount(), rcAmount);
                riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R020.code,
                        order.getCustomerCode(), customerName, AmountUtils.fenLongToYuanStr(order.getAmount()));
            }
        }
    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        return Constants.PayState.SUCCESS.code.equals(order.getPayState()) &&
                Constants.rcBusinessType.ACCT_QUOTA.code.equals(order.getBusinessType()) &&
                ("EnterpriseUnion".equals(order.getBusinessCode()) || "AcsRecharge".equals(order.getBusinessCode()));
    }
}

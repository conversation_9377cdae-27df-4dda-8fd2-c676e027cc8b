package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 代付单年最高限额。出金分离为代付、提现，该类为代付单年最高限额 
 * <AUTHOR>
 * @date 2021-07-29
 *
 */
@Service("Year-Max-Withdraw-Amount")
public class YearMaxWithdrawAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;
	@Autowired
	private YearMaxWithdrawAmountRc self;
	@Autowired
	private RcArchiveService rcArchiveService;
	
	private static final String Day_Max_Out_Amount = "Day-Max-Withdraw-Amount";
	
	@Override
	public void reset(RcLimit rcLimit) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		//每年重置年入金限额为0
		Date date = new Date();
		String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), RcDateUtils.getCurrentYear());
		if (date.getMonth() == 0 && date.getDate() == 1) {
			// 如果当天是一月一日
			
			self.historyDataHandler(rcLimit, date , RcLimitData.Status.FINISHED.code);
			redisTemplate.opsForValue().set(key, 0L);
		} else {
			long yearOutAmount = self.historyDataHandler(rcLimit, date, RcLimitData.Status.PROCESSING.code);
			redisTemplate.opsForValue().set(key, yearOutAmount);
		}
	}


	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
	    
	    // 非代付业务不处理 20210729
        if (!rcCalculteBasic.isWithdrawals(rcCalculateRequest.getBusinessCode()) && !rcCalculteBasic.isZHFZ(rcCalculateRequest.getPayMethod())) {
            return CommonOuterResponse.SUCCEE;
        }
        
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		String key = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), RcDateUtils.getCurrentYear());
		String dayKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), Day_Max_Out_Amount, RcDateUtils.getCurrentDay());
	    String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (StringUtils.isBlank(amountStr)) {
			throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
		}
		long amount = Long.parseLong(amountStr);
		long yearOutAmount = redisTemplate.opsForValue().increment(key, 0L);
		long dayOutAmount = redisTemplate.opsForValue().increment(dayKey, 0L);
		if (yearOutAmount + dayOutAmount > Long.parseLong(rcLimit.getLimitValue())) {
			// 记录触发日记
			RcArchive rcArchive = rcArchiveService.selectByCodeOrName(rcLimit.getBusinessTagerId(),null);
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayOutAmount + yearOutAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
			//实际发生值已经大于限定值
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}


	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		// TODO Auto-generated method stub
		
	}

	public long historyDataHandler(RcLimit rcLimit , Date date , String status) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String lastDayKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Day_Max_Out_Amount , RcDateUtils.getLastDay());
		long dayOutAmount = redisTemplate.opsForValue().increment(lastDayKey, 0L);
		
		Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);
		RcLimitData yearOutAmountRcLimitData = rcLimitDataMapper.selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(	rcLimit.getDefineCode(), 
				rcLimit.getBusinessTagerId(), RcDateUtils.getYearString(selectDate), RcLimitData.Status.PROCESSING.code);
		long yearOutAmount = 0;
		if (yearOutAmountRcLimitData == null) {
			yearOutAmountRcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
					rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), dayOutAmount + "", date, 
					date , status, RcDateUtils.getYearString(selectDate));
			rcLimitDataMapper.insert(yearOutAmountRcLimitData);
			yearOutAmount = dayOutAmount;
		} else {
			yearOutAmount = Long.parseLong(yearOutAmountRcLimitData.getValue()) + dayOutAmount;
			yearOutAmountRcLimitData.setValue(yearOutAmount + "");
			yearOutAmountRcLimitData.setUpdatetime(date);
			yearOutAmountRcLimitData.setStatus(status);
			rcLimitDataMapper.updateByPrimaryKey(yearOutAmountRcLimitData);
		}
		return yearOutAmount;
	}
	
}

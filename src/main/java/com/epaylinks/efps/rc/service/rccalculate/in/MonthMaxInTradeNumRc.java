package com.epaylinks.efps.rc.service.rccalculate.in;

import java.util.Date;
import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 单月最大交易笔数
 * <AUTHOR>
 * @date 2021-04-09
 *
 */
@Service("Month-Max-IN-Count")
public class MonthMaxInTradeNumRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;
    
    @Autowired
    private MonthMaxInTradeNumRc self;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcArchiveService rcArchiveService;
    
    //单日最大入金指标
    private static final String Day_Max_In_Count = "Day-Max-IN-Count";
    
    
    
    @Override
    public void reset(RcLimit rcLimit) {
        
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        // 每月重置月入金交易笔数为0
        Date date = new Date();
        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
                
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        if (date.getDate() == 1) {
            // 如果当前时间是每月的一号
            historyDataHandler(rcLimit, date , RcLimitData.Status.FINISHED.code);
            redisTemplate.opsForValue().set(key, 0L);
        } else {
            // 如果不是每月的一号
            long monthInCount = historyDataHandler(rcLimit, date , RcLimitData.Status.PROCESSING.code);
            redisTemplate.opsForValue().set(key, monthInCount);

        }
    }
    

//  @Logable(businessTag = "Month-Max-IN-Count:calculate")
    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
     
        // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
                
        String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        String dayKey = rcCalculteBasic.getKey(tagerId, Day_Max_In_Count , RcDateUtils.getCurrentDay());
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
        if (StringUtils.isBlank(amountStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long count = Long.parseLong(amountStr);
        long monthInCount = redisTemplate.opsForValue().increment(key, 0L);
        long dayInCount = redisTemplate.opsForValue().increment(dayKey, 0L);
        if (count + monthInCount + dayInCount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInCount + monthInCount +"",count+"",false,"RC交易受限", rcArchive.getArchiveType());
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

//  @Logable(businessTag = "Month-Max-IN-Count:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        // TODO Auto-generated method stub
        
    }

    private long historyDataHandler(RcLimit rcLimit , Date date , String status) {

           // 拓展其他对象类型，兼容旧的商户处理
        String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
        String lastDayKey = rcCalculteBasic.getKey(tagerId, Day_Max_In_Count, RcDateUtils.getLastDay());
        long dayInCount = redisTemplate.opsForValue().increment(lastDayKey, 0L);

        Date selectLastDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);
        RcLimitData monthInCountRcLimitData = rcLimitDataMapper.selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
                 rcLimit.getBusinessTagerId(), RcDateUtils.getMonthString(selectLastDate) , RcLimitData.Status.PROCESSING.code);
        long monthInCount = 0;
        if (monthInCountRcLimitData == null) {
            monthInCountRcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
                    rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), dayInCount + "", date, date , 
                    status, RcDateUtils.getMonthString(selectLastDate));
            rcLimitDataMapper.insert(monthInCountRcLimitData);
            monthInCount = dayInCount;
        } else {
            monthInCount = Long.parseLong(monthInCountRcLimitData.getValue()) + dayInCount;
            monthInCountRcLimitData.setValue(monthInCount + "");
            monthInCountRcLimitData.setUpdatetime(date);
            monthInCountRcLimitData.setStatus(status);
            rcLimitDataMapper.updateByPrimaryKey(monthInCountRcLimitData);
        }
        return monthInCount;
    }

}

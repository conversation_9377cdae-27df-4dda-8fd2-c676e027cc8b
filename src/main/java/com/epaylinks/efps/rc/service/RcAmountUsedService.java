package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.domain.BasicAmountLimitUsedQueryResponse;
import com.epaylinks.efps.rc.domain.CustomerAmountDetail;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service
public class RcAmountUsedService {

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private MyRedisTemplateService myRedisTemplate;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private RcLimitService rcLimitService;

    private static final String DAY_MAX = "Day-Max-IN-Count";
    private static final String MONTH_MAX = "Month-Max-IN-Amount";
    private static final String YEAR_MAX = "Year-Max-IN-Amount";

    public CommonOuterResponse<List<CustomerAmountDetail> > process(List<String> customerCodeList) {

        CommonOuterResponse resp = new CommonOuterResponse();

        if(null == customerCodeList ||  customerCodeList.size() == 0){
            return resp;
        }

        List<CustomerAmountDetail> listDetail = new ArrayList<>();

        MyRedisTemplate redisTemplate = myRedisTemplate.getMyRedisTemplate();
        for(String customerCode: customerCodeList){
            String yearKey = rcCalculteBasic.getKey(customerCode, YEAR_MAX, RcDateUtils.getCurrentYear());
            String monthKey = rcCalculteBasic.getKey(customerCode, MONTH_MAX, RcDateUtils.getCurrentMonth());
            String dayKey = rcCalculteBasic.getKey(customerCode, DAY_MAX, RcDateUtils.getCurrentDay());

            long yearAmount = redisTemplate.opsForValue().increment(yearKey, 0L);
            long monthInAmount = redisTemplate.opsForValue().increment(monthKey, 0L);
            long dayInAmount = redisTemplate.opsForValue().increment(dayKey, 0L);


            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode("005", customerCode);
            if (rcArchive == null){
                resp.setReturnCode(RcCode.USER_NOT_EXIXT.code);
                resp.setReturnMsg(RcCode.USER_NOT_EXIXT.message+":"+customerCode);
                return resp;
            }

            Map<String,Long> limitMap = rcLimitService.queryCustomerAmountLimit(customerCode, rcArchive.getParentCode(), rcArchive.getRcLevel());
            long yearInAmountLimit = limitMap.get(DefineCode.YEAR_IN.defineCode);
            long monthInAmountLimit = limitMap.get(DefineCode.MONTH_IN.defineCode);
            long dayInAmountLimit = limitMap.get(DefineCode.DAY_IN.defineCode);
            long singleAmountLimit = limitMap.get(DefineCode.SINGLT_IN.defineCode);

            CustomerAmountDetail customerAmountDetail = new CustomerAmountDetail();
            customerAmountDetail.setCustomerCode(customerCode);
            customerAmountDetail.setDayInAmount(dayInAmount);
            customerAmountDetail.setMonthInAmount(monthInAmount);
            customerAmountDetail.setYearInAmount(yearAmount);

            customerAmountDetail.setInAmountSingleLimit(singleAmountLimit);
            customerAmountDetail.setInAmountDayLimit(dayInAmountLimit);
            customerAmountDetail.setInAmountMonthLimit(monthInAmountLimit);
            customerAmountDetail.setInAmountYearLimit(yearInAmountLimit);

            listDetail.add(customerAmountDetail);
        }

        resp.setData(listDetail);

        return resp;
    }


}

package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.controller.request.VerifyCheckBusinessRequest;
import com.epaylinks.efps.rc.controller.request.VerifyConfigRequest;
import com.epaylinks.efps.rc.controller.response.VerifyConfigResponse;
import com.epaylinks.efps.rc.dao.VerifyConfigMapper;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.domain.VerifyConfig;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcAuditRecordService;
import com.epaylinks.efps.rc.service.RcOperateLogService;
import com.epaylinks.efps.rc.service.VerifyConfigService;
import com.epaylinks.efps.rc.vo.CheckBusinessVo;
import com.epaylinks.efps.rc.vo.VerifyConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service("VerifyConfigServiceImpl")
public class VerifyConfigServiceImpl implements VerifyConfigService, BatchService {
    @Autowired
    private VerifyConfigMapper verifyConfigMapper;

    @Autowired
    private RcAuditRecordService rcAuditRecordService;

    @Autowired
    private OtherService otherService;

    @Value("${riskVerificationConfiguration}")
    private String defaultSetting;

    @Autowired
    private RcOperateLogService rcOperateLogService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private VerifyConfigServiceImpl self;

    @Autowired
    private RedisTemplate redisTemplate;

    private static Map<String,String> codeMap;
    static {
        codeMap = new HashMap<>();
        codeMap.put("ProtocolPayCredit","信用卡快捷协议支付");
        codeMap.put("ProtocolPay","储蓄卡快捷协议支付");
        codeMap.put("QuickPay","储蓄卡快捷直接支付");
        codeMap.put("QuickPayCredit","信用卡快捷直接支付");
        codeMap.put("Withdraw-CreditCard","代付到信用卡");
        codeMap.put("Withdraw","代付到储蓄卡");
    }

    @Override
    public void check(VerifyConfigRequest request, Long userId) {
        if (!StringUtils.isEmpty(request.getRemark())) {
            request.setRemark(request.getRemark().replaceAll("\r\n", ""));
        }
    }

    @Override
    @Transactional
    public CommonOuterResponse add(VerifyConfigRequest request, Long userId) {
        if (!StringUtils.isEmpty(request.getRemark())) {
            request.setRemark(request.getRemark().replaceAll("\r\n", ""));
        }
        VerifyConfig verifyConfig = verifyConfigMapper.queryByCustomerNo(request.getCustomerNo());
        if (verifyConfig != null) {
            throw new AppException(RcCode.RECORD_EXISTS_EXCEPTION.code,RcCode.RECORD_EXISTS_EXCEPTION.message);
        }
        verifyConfig = new VerifyConfig();
        verifyConfig.setCustomerNo(request.getCustomerNo());
        verifyConfig.setCustomerName(request.getCustomerName());
        verifyConfig.setFirstVerifyChannel(request.getFirstVerifyChannel());
        verifyConfig.setCheckBusiness(request.getCheckBusiness());
        verifyConfig.setRemark(request.getRemark());
        verifyConfig.setAuditStatus(String.valueOf(RcConstants.AuditStatus.WAITING.code));
        verifyConfig.setUseStatus(String.valueOf(RcConstants.UseStatus.DISABLE.code));
        verifyConfig.setOperator(userId);
        Long verifyId = verifyConfigMapper.querySEQ();
        verifyConfig.setId(verifyId);
        Date modify = new Date();
        verifyConfig.setCreateTime(modify);
        verifyConfig.setUpdateTime(modify);
        verifyConfigMapper.insert(verifyConfig);
        // 插入审核记录
        Map<String,Object> newValue = new HashMap<>();
        newValue.put("firstVerifyChannel", request.getFirstVerifyChannel());
        newValue.put("checkBusiness", request.getCheckBusiness());
        newValue.put("remark", request.getRemark());
        newValue.put("userId",userId);
        rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.VERIFY_CONFIG.code,verifyId,null, JSON.toJSONString(newValue),RcConstants.AuditActionType.CREATE.code,userId,null);
        // 风控操作日志
        // 首选核验通道
        RcOperateLog log = new RcOperateLog();
        Date modifyDate = new Date();
        log.setPermId("80205");
        log.setCode(request.getCustomerNo());
        log.setName(request.getCustomerName());
        log.setType("1");
        log.setOperator(String.valueOf(userId));
        log.setOperateTime(modifyDate);
        log.setOperateContent("首选核验通道");
        log.setOrigValue(null);
        log.setNewValue(RcConstants.VerifyConfig.getMessageByCode(request.getFirstVerifyChannel()));
        rcOperateLogService.insert(log);
        // 核验业务
        log = new RcOperateLog();
        log.setPermId("80205");
        log.setCode(request.getCustomerNo());
        log.setName(request.getCustomerName());
        log.setType("1");
        log.setOperator(String.valueOf(userId));
        log.setOperateTime(modifyDate);
        log.setOperateContent("核验业务");
        log.setOrigValue(null);
        log.setNewValue(otherService.queryVerifyBusiness(Arrays.asList(request.getCheckBusiness().split(","))));
        rcOperateLogService.insert(log);
        if (!StringUtils.isEmpty(request.getRemark())) {
            log = new RcOperateLog();
            log.setPermId("80205");
            log.setCode(request.getCustomerNo());
            log.setName(request.getCustomerName());
            log.setType("1");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("备注");
            log.setOrigValue(null);
            log.setNewValue(request.getRemark());
            rcOperateLogService.insert(log);
        }
        return CommonOuterResponse.success(verifyId);
    }

    @Override
    @Transactional
    public CommonOuterResponse edit(VerifyConfigRequest request, Long userId) {
        if (request.getId() == null) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message);
        }
        if (!StringUtils.isEmpty(request.getRemark())) {
            request.setRemark(request.getRemark().replaceAll("\r\n", ""));
        }
        VerifyConfig verifyConfig = verifyConfigMapper.selectByPrimaryKey(request.getId());
        if (verifyConfig == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        if (String.valueOf(RcConstants.AuditStatus.WAITING.code).equals(verifyConfig.getAuditStatus())) {
            throw new AppException(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code,RcCode.AUD_STATUS_UPDATE_EXCEPTION.message);
        }
        Map<String,Object> oldValue = new HashMap<>();
        oldValue.put("firstVerifyChannel", verifyConfig.getFirstVerifyChannel());
        oldValue.put("checkBusiness", verifyConfig.getCheckBusiness());
        oldValue.put("remark", verifyConfig.getRemark());
        oldValue.put("userId",verifyConfig.getOperator());

        verifyConfig.setAuditStatus(String.valueOf(RcConstants.AuditStatus.WAITING.code));
        verifyConfigMapper.updateByPrimaryKey(verifyConfig);
        // 插入审核记录
        Map<String,Object> newValue = new HashMap<>();
        newValue.put("firstVerifyChannel", request.getFirstVerifyChannel());
        newValue.put("checkBusiness", request.getCheckBusiness());
        newValue.put("remark", request.getRemark());
        newValue.put("userId",userId);
        rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.VERIFY_CONFIG.code,verifyConfig.getId(),JSON.toJSONString(oldValue), JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,userId,null);
        // 风控操作日志
        // 首选核验通道
        RcOperateLog log = null;
        Date modifyDate = new Date();
        if (!request.getFirstVerifyChannel().equals(verifyConfig.getFirstVerifyChannel())) {
            log = new RcOperateLog();
            log.setPermId("80205");
            log.setCode(request.getCustomerNo());
            log.setName(request.getCustomerName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("首选核验通道");
            log.setOrigValue(RcConstants.VerifyConfig.getMessageByCode(verifyConfig.getFirstVerifyChannel()));
            log.setNewValue(RcConstants.VerifyConfig.getMessageByCode(request.getFirstVerifyChannel()));
            rcOperateLogService.insert(log);
        }
        // 核验业务
        if (!request.getCheckBusiness().equals(verifyConfig.getCheckBusiness())) {
            log = new RcOperateLog();
            log.setPermId("80205");
            log.setCode(request.getCustomerNo());
            log.setName(request.getCustomerName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("核验业务");
            log.setOrigValue(otherService.queryVerifyBusiness(Arrays.asList(verifyConfig.getCheckBusiness().split(","))));
            log.setNewValue(otherService.queryVerifyBusiness(Arrays.asList(request.getCheckBusiness().split(","))));
            rcOperateLogService.insert(log);
        }
        // 备注
        if (!Objects.equals(verifyConfig.getRemark(),request.getRemark())) {
            log = new RcOperateLog();
            log.setPermId("80205");
            log.setCode(request.getCustomerNo());
            log.setName(request.getCustomerName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("备注");
            log.setOrigValue(verifyConfig.getRemark());
            log.setNewValue(request.getRemark());
            rcOperateLogService.insert(log);
        }
        return CommonOuterResponse.success();
    }

    @Override
    @Transactional
    public CommonOuterResponse delete(String ids, Long userId) {
        List<String> idList = Arrays.asList(ids.split(","));
        for (int i = 0; i < idList.size(); i++) {
            Long id = Long.parseLong(idList.get(i));
            VerifyConfig verifyConfig = verifyConfigMapper.selectByPrimaryKey(id);
            if (verifyConfig == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
            }
            if (String.valueOf(RcConstants.AuditStatus.WAITING.code).equals(verifyConfig.getAuditStatus())) {
                throw new AppException(RcCode.AUD_STATUS_UPDATE_EXCEPTION.code,"待审核记录不允许删除");
            }
            verifyConfig.setAuditStatus(String.valueOf(RcConstants.AuditStatus.WAITING.code));
            verifyConfigMapper.updateByPrimaryKey(verifyConfig);
            // 插入审核记录
            Map<String,Object> oldValue = new HashMap<>();
            oldValue.put("firstVerifyChannel", verifyConfig.getFirstVerifyChannel());
            oldValue.put("checkBusiness", verifyConfig.getCheckBusiness());
            oldValue.put("remark", verifyConfig.getRemark());
            oldValue.put("userId",verifyConfig.getOperator());
            Map<String,Object> newValue = new HashMap<>();
            newValue.put("userId",userId);
            rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.VERIFY_CONFIG.code,verifyConfig.getId(),JSON.toJSONString(oldValue), JSON.toJSONString(newValue),RcConstants.AuditActionType.DELETE.code,userId,null);
            // 风控操作日志
            RcOperateLog log = new RcOperateLog();
            Date modifyDate = new Date();
            log.setPermId("80205");
            log.setCode(verifyConfig.getCustomerNo());
            log.setName(verifyConfig.getCustomerName());
            log.setType("3");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("删除核验配置");
            log.setOrigValue(RcConstants.VerifyConfig.getMessageByCode(verifyConfig.getFirstVerifyChannel()));
            log.setNewValue(null);
            rcOperateLogService.insert(log);
        }
        return CommonOuterResponse.success();
    }

    @Override
    @Transactional
    public CommonOuterResponse audit(String ids,String auditResult,String opinion,Long userId) {
        List<String> idList = Arrays.asList(ids.split(","));
        for (int i = 0; i < idList.size(); i++) {
            Long id = Long.parseLong(idList.get(i));
            VerifyConfig verifyConfig = verifyConfigMapper.selectByPrimaryKey(id);
            if (verifyConfig == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
            }
            RcAuditRecord rcAuditRecord = rcAuditRecordService.queryByTargetTypeAndId(RcConstants.AuditTargetType.VERIFY_CONFIG.code,id);
            if (rcAuditRecord == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message + "：审核记录");
            }
            if (!String.valueOf(RcConstants.AuditStatus.WAITING.code).equals(verifyConfig.getAuditStatus())) {
                throw new AppException(RcCode.AUD_EXCEPTION.code,RcCode.AUD_EXCEPTION.message);
            }
            if (rcAuditRecord != null && !StringUtils.isEmpty(rcAuditRecord.getNewValue())) {
                VerifyConfigVo verifyConfigVo = JSONObject.parseObject(rcAuditRecord.getNewValue(),VerifyConfigVo.class);
                if (userId.longValue() == verifyConfigVo.getUserId().longValue()) {
                    throw new AppException(RcCode.NO_AUDIT_PERMISSION.code,RcCode.NO_AUDIT_PERMISSION.message + "：操作修改和审核不能为同一人");
                }
            }
            Date modify = new Date();
            if (String.valueOf(RcConstants.AuditStatus.SUCCESS.code).equals(auditResult)) {
                if (RcConstants.AuditActionType.CREATE.code.equals(rcAuditRecord.getActionType())) {
                    verifyConfig.setAuditStatus(auditResult);
                    verifyConfig.setUseStatus(String.valueOf(RcConstants.UseStatus.ENABLE.code));
                    verifyConfig.setUpdateTime(modify);
                    verifyConfigMapper.updateByPrimaryKey(verifyConfig);
                } else if (RcConstants.AuditActionType.UPDATE.code.equals(rcAuditRecord.getActionType())) {
                    String newValue = rcAuditRecord.getNewValue();
                    VerifyConfigVo verifyConfigVo = JSONObject.parseObject(newValue, VerifyConfigVo.class);
                    verifyConfig.setFirstVerifyChannel(verifyConfigVo.getFirstVerifyChannel());
                    verifyConfig.setCheckBusiness(verifyConfigVo.getCheckBusiness());
                    verifyConfig.setRemark(verifyConfigVo.getRemark());
                    verifyConfig.setOperator(verifyConfigVo.getUserId());
                    verifyConfig.setUseStatus(String.valueOf(RcConstants.UseStatus.ENABLE.code));
                    verifyConfig.setAuditStatus(auditResult);
                    verifyConfig.setUpdateTime(modify);
                    verifyConfigMapper.updateByPrimaryKey(verifyConfig);
                } else if (RcConstants.AuditActionType.DELETE.code.equals(rcAuditRecord.getActionType())) {
                    verifyConfigMapper.deleteByPrimaryKey(id);
                }
            } else {
                if (RcConstants.AuditActionType.CREATE.code.equals(rcAuditRecord.getActionType())) {
//                    verifyConfigMapper.deleteByPrimaryKey(id);
                    // 新创建记录审核不通过不删除数据--yejiawei20230808
                    verifyConfig.setAuditStatus(auditResult);
                    verifyConfig.setUpdateTime(modify);
                    verifyConfigMapper.updateByPrimaryKey(verifyConfig);
                } else if (RcConstants.AuditActionType.UPDATE.code.equals(rcAuditRecord.getActionType())) {
                    verifyConfig.setAuditStatus(auditResult);
                    verifyConfig.setUpdateTime(modify);
                    verifyConfigMapper.updateByPrimaryKey(verifyConfig);
                } else if (RcConstants.AuditActionType.DELETE.code.equals(rcAuditRecord.getActionType())) {
                    verifyConfig.setAuditStatus(auditResult);
                    verifyConfig.setUpdateTime(modify);
                    verifyConfigMapper.updateByPrimaryKey(verifyConfig);
                }
            }
            // 更新审核记录
            rcAuditRecord.setAuditResult(Short.parseShort(auditResult));
            rcAuditRecord.setRemarks(opinion);
            rcAuditRecord.setAuditTime(modify);
            rcAuditRecord.setAuditOperId(userId);
            rcAuditRecordService.updateAuditRecord(rcAuditRecord);
            // 风控操作日志
            RcOperateLog log = new RcOperateLog();
            Date modifyDate = new Date();
            log.setPermId("80205");
            log.setCode(verifyConfig.getCustomerNo());
            log.setName(verifyConfig.getCustomerName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("审核核验配置");
            log.setOrigValue("待审核");
            log.setNewValue(auditResult.equals(String.valueOf(RcConstants.AuditStatus.SUCCESS.code)) ? "审核通过" : "审核不通过");
            rcOperateLogService.insert(log);
        }
        return CommonOuterResponse.success();
    }

    @Override
    public PageResult pageQuery(Map<String, Object> paramMap, Long userId,Boolean download) {
        Integer count = verifyConfigMapper.count(paramMap);
        if (download) {
            paramMap.put("endRowNo", count);
        }
        List<VerifyConfigResponse> responseList = verifyConfigMapper.pageQuery(paramMap);
        if (responseList != null && responseList.size() > 0) {
            responseList.forEach(response -> {
                if (download) {
                    List<String> nameList = Arrays.asList(response.getCheckBusiness().split(","));
                    response.setProtocolPayCredit(nameList.contains("ProtocolPayCredit") ? "是":"否");
                    response.setProtocolPay(nameList.contains("ProtocolPay") ? "是":"否");
                    response.setQuickPayCredit(nameList.contains("QuickPayCredit") ? "是":"否");
                    response.setQuickPay(nameList.contains("QuickPay") ? "是":"否");
                    response.setWithdrawCreditCard(nameList.contains("Withdraw-CreditCard") ? "是":"否");
                    response.setWithdraw(nameList.contains("Withdraw") ? "是":"否");
                }
                RcAuditRecord rcAuditRecord = rcAuditRecordService.queryByTargetTypeAndId(RcConstants.AuditTargetType.VERIFY_CONFIG.code,response.getId());
                if (rcAuditRecord != null) {
                    if (!StringUtils.isEmpty(rcAuditRecord.getOldValue())) {
                        List<String> values = new ArrayList<>();
                        VerifyConfigVo oldValue = JSONObject.parseObject(rcAuditRecord.getOldValue(),VerifyConfigVo.class);
                        Arrays.asList(oldValue.getCheckBusiness().split(",")).forEach(code -> {
                            values.add(codeMap.get(code));
                        });
                        values.removeIf(Objects::isNull);
                        oldValue.setCheckBusiness(String.join(",",values));
                        response.setOldValue(oldValue);
                    }
                    if (!StringUtils.isEmpty(rcAuditRecord.getNewValue()) && !RcConstants.AuditActionType.DELETE.code.equals(rcAuditRecord.getActionType())) {
                        List<String> values = new ArrayList<>();
                        VerifyConfigVo newValue = JSONObject.parseObject(rcAuditRecord.getNewValue(),VerifyConfigVo.class);
                        Arrays.asList(newValue.getCheckBusiness().split(",")).forEach(code -> {
                            values.add(codeMap.get(code));
                        });
                        values.removeIf(Objects::isNull);
                        newValue.setCheckBusiness(String.join(",",values));
                        response.setNewValue(newValue);
                    }
                    response.setOpeType(rcAuditRecord.getActionType());
                }
            });
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotal(count);
        pageResult.setRows(responseList);
        return pageResult;
    }

    @Override
    public List<VerifyConfigResponse> exportPage(Map<String, Object> paramMap) {
        List<VerifyConfigResponse> responseList = verifyConfigMapper.pageQuery(paramMap);
        if (responseList != null && responseList.size() > 0) {
            responseList.forEach(response -> {
                List<String> nameList = Arrays.asList(response.getCheckBusiness().split(","));
                response.setProtocolPayCredit(nameList.contains("ProtocolPayCredit") ? "是":"否");
                response.setProtocolPay(nameList.contains("ProtocolPay") ? "是":"否");
                response.setQuickPayCredit(nameList.contains("QuickPayCredit") ? "是":"否");
                response.setQuickPay(nameList.contains("QuickPay") ? "是":"否");
                response.setWithdrawCreditCard(nameList.contains("Withdraw-CreditCard") ? "是":"否");
                response.setWithdraw(nameList.contains("Withdraw") ? "是":"否");
                RcAuditRecord rcAuditRecord = rcAuditRecordService.queryByTargetTypeAndId(RcConstants.AuditTargetType.VERIFY_CONFIG.code,response.getId());
                if (rcAuditRecord != null) {
                    if (!StringUtils.isEmpty(rcAuditRecord.getOldValue())) {
                        List<String> values = new ArrayList<>();
                        VerifyConfigVo oldValue = JSONObject.parseObject(rcAuditRecord.getOldValue(),VerifyConfigVo.class);
                        Arrays.asList(oldValue.getCheckBusiness().split(",")).forEach(code -> {
                            values.add(codeMap.get(code));
                        });
                        values.removeIf(Objects::isNull);
                        oldValue.setCheckBusiness(String.join(",",values));
                        response.setOldValue(oldValue);
                    }
                    if (!StringUtils.isEmpty(rcAuditRecord.getNewValue()) && !RcConstants.AuditActionType.DELETE.code.equals(rcAuditRecord.getActionType())) {
                        List<String> values = new ArrayList<>();
                        VerifyConfigVo newValue = JSONObject.parseObject(rcAuditRecord.getNewValue(),VerifyConfigVo.class);
                        Arrays.asList(newValue.getCheckBusiness().split(",")).forEach(code -> {
                            values.add(codeMap.get(code));
                        });
                        values.removeIf(Objects::isNull);
                        newValue.setCheckBusiness(String.join(",",values));
                        response.setNewValue(newValue);
                    }
                    response.setOpeType(rcAuditRecord.getActionType());
                }
            });
        }
        return responseList;
    }

    @Override
    public CommonOuterResponse checkBusiness(VerifyCheckBusinessRequest request,Long userId) {
        List<String> codes = new ArrayList<>();
        Customer customer = otherService.queryCustomerDraftByCustomerNo(request.getCustomerNo());
        if (customer == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,"商户不存在");
        }
        List<CheckBusinessVo> checkBusinessVos = request.getBusinessVoList();
        if (checkBusinessVos == null || checkBusinessVos.size() == 0) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message);
        }
        if ("verifyConfig".equals(request.getSource())) {
            checkBusinessVos.forEach(checkBusinessVo -> {
                Map<String,Object> paramMap = new HashMap<>();
                paramMap.put("customerId",customer.getCustomerId());
                paramMap.put("businessCode",checkBusinessVo.getBusinessCode());
                Integer count = otherService.selectServiceFee(paramMap);
                if (count == 0) {
                    codes.add(checkBusinessVo.getBusinessCode());
                }
            });
        } else if ("businessManage".equals(request.getSource())) {
            checkBusinessVos.forEach(checkBusinessVo -> {
                if (checkBusinessVo.getPayServicdeFee() == null || checkBusinessVo.getPayServicdeFee() == 0) {
                    VerifyConfig verifyConfig = verifyConfigMapper.queryByCustomerNoValid(request.getCustomerNo());
                    if (verifyConfig != null && Arrays.asList(verifyConfig.getCheckBusiness().split(",")).contains(checkBusinessVo.getBusinessCode())) {
                        codes.add(checkBusinessVo.getBusinessCode());
                    }
                }
            });
        }
        if (codes.size() > 0) {
            List<String> values = new ArrayList<>();
            codes.forEach(code -> {
                values.add(codeMap.get(code));
            });
            return CommonOuterResponse.fail(RcCode.PAY_SERVICE_FEE_NOT_CONFIG.code,RcCode.PAY_SERVICE_FEE_NOT_CONFIG.message,values);
        } else {
            return CommonOuterResponse.success();
        }
    }

    @Override
    public String queryVerifyChannel(VerifyConfig verifyConfig,String customerNo) {
        if (verifyConfig == null) {
            verifyConfig = verifyConfigMapper.queryByCustomerNoValid(customerNo);
        }
        if (verifyConfig == null || "0".equals(verifyConfig.getFirstVerifyChannel())) {
            return defaultSetting;
        }
        if ("5".equals(verifyConfig.getFirstVerifyChannel())) {
            return "5,10,7";
        }
        if ("7".equals(verifyConfig.getFirstVerifyChannel())) {
            return "7,10,5";
        }
        if ("8".equals(verifyConfig.getFirstVerifyChannel())) {
            return "8,10,5";
        }
        if ("10".equals(verifyConfig.getFirstVerifyChannel())) {
            return "10,5,7";
        }
        return defaultSetting;
    }

    @Override
    public VerifyConfig queryByCustomerNoValid(String customerNo) {
        return verifyConfigMapper.queryByCustomerNoValid(customerNo);
    }

    @Override
    public CommonOuterResponse getFirstVerifyChannel() {
        return CommonOuterResponse.success(RcConstants.VerifyConfig.toList());
    }

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        Long userId = Long.parseLong(extraData.get("userId") + "");
        String batchNo = extraData.get("batchNo") + "";
        Map<Integer, BatchDetail> resultMap = new HashMap<>();
        // 校验重复
        List<String> repeatList = redisTemplate.opsForValue().get("verifyConfig" + batchNo) == null ? new ArrayList<>() : (List<String>) redisTemplate.opsForValue().get("verifyConfig" + batchNo);
        for(Integer rowNo : dataMap.keySet()) {
            try {
                VerifyConfigRequest request = new VerifyConfigRequest();
                List<String> cellList = dataMap.get(rowNo);
                // 核验业务列表
                List<String> checkBusinessList= new ArrayList<>();
                for (int i = 0; i < cellList.size(); i++) {
                    String data = cellList.get(i);
                    switch (i) {
                        case 0: // 序号
                            if (StringUtils.isEmpty(data)) {
                                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：序号");
                            }
                            continue;
                        case 1: // 商户号
                            if (StringUtils.isEmpty(data)) {
                                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：商户号不能为空");
                            }
                            if (repeatList.contains(data)) {
                                throw new AppException(RcCode.DATA_DUPLICATION.code,"重复数据[商户号：" + data + "]");
                            } else {
                                repeatList.add(data);
                                redisTemplate.opsForValue().set("verifyConfig" + batchNo,repeatList,30,TimeUnit.MINUTES);
                            }
                            Customer customer = otherService.queryCustomerDraftByCustomerNo(data);
                            if (customer == null) {
                                throw new AppException(RcCode.RC_ARG_ERROR.code,RcCode.RC_ARG_ERROR.message + "：商户号不存在");
                            }
                            request.setCustomerNo(customer.getCustomerNo());
                            request.setCustomerName(customer.getName());
                            continue;
                        case 2: // 首选核验通道
                            String channel = RcConstants.VerifyConfig.getCodeByMessage(data);
                            request.setFirstVerifyChannel(channel);
                            continue;
                        case 3: // 信用卡快捷协议支付
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("ProtocolPayCredit");
                            }
                            continue;
                        case 4: // 储蓄卡快捷协议支付
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("ProtocolPay");
                            }
                            continue;
                        case 5: // 信用卡快捷直接支付
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("QuickPayCredit");
                            }
                            continue;
                        case 6: // 储蓄卡快捷直接支付
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("QuickPay");
                            }
                            continue;
                        case 7: // 代付到信用卡
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("Withdraw-CreditCard");
                            }
                            continue;
                        case 8: // 代付到储蓄卡
                            if (!StringUtils.isEmpty(data) && "是".equals(data.trim())) {
                                checkBusinessList.add("Withdraw");
                            }
                            continue;
                        case 9: // 内部备注
                            request.setRemark(data);
                            break;
                    }
                }
                if (checkBusinessList.size() == 0) {
                    throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：需最少设置1个核验业务");
                }
                request.setCheckBusiness(String.join(",",checkBusinessList));
                CommonOuterResponse response = self.batchAdd(request,userId);
                if (RcCode.PAY_SERVICE_FEE_NOT_CONFIG.code.equals(response.getReturnCode())) {
                    String values = String.join("，",(ArrayList<String>)response.getData());
                    resultMap.put(rowNo,buildDetail(rowNo,null, RcConstants.SuccessFail.SUCCESS.code,null,"提醒：商户以下业务未配置支付服务费[" + values + "]"));
                } else {
                    resultMap.put(rowNo,buildDetail(rowNo,null, RcConstants.SuccessFail.SUCCESS.code,null,"导入成功"));
                }

            } catch (Exception e) {
                logger.printMessage("核验配置error:" + e.getMessage());
                logger.printLog(e);
                if (e instanceof AppException) {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                } else {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return resultMap;
    }

    private BatchDetail buildDetail( Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    @Transactional
    public CommonOuterResponse batchAdd(VerifyConfigRequest request, Long userId) {
        VerifyConfig verifyConfig = verifyConfigMapper.queryByCustomerNo(request.getCustomerNo());
        if (verifyConfig != null) {
            if (String.valueOf(RcConstants.AuditStatus.WAITING.code).equals(verifyConfig.getAuditStatus())) {
                throw new AppException(RcCode.AUD_EXCEPTION.code,"修改失败，因存在\"待审核\"的记录");
            }
            request.setId(verifyConfig.getId());
            self.edit(request,userId);
            self.audit(String.valueOf(request.getId()),"1","批量导入自动审核",0L);
        } else {
            CommonOuterResponse response = self.add(request,userId);
            Long verifyId = (Long) response.getData();
            self.audit( String.valueOf(verifyId),"1","批量导入自动审核",0L);
        }
        VerifyCheckBusinessRequest verifyCheckBusinessRequest = new VerifyCheckBusinessRequest();
        List<CheckBusinessVo> checkBusinessVos = new ArrayList<>();
        String[] codes = request.getCheckBusiness().split(",");
        for (int i = 0; i < codes.length; i++) {
            CheckBusinessVo checkBusinessVo = new CheckBusinessVo();
            checkBusinessVo.setBusinessCode(codes[i]);
            checkBusinessVos.add(checkBusinessVo);
        }
        verifyCheckBusinessRequest.setCustomerNo(request.getCustomerNo());
        verifyCheckBusinessRequest.setBusinessVoList(checkBusinessVos);
        verifyCheckBusinessRequest.setSource("verifyConfig");
        return self.checkBusiness(verifyCheckBusinessRequest,userId);
    }
}

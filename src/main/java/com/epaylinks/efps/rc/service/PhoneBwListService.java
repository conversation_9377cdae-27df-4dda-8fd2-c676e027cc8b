package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.vo.AuditRecordVo;

import java.util.List;

public interface PhoneBwListService {
    /**
     * 保存审核记录(手机白名单)
     * @param id
     * @param oldVlue
     * @param newValue
     * @param userId
     * @param code
     * @return
     */
    RcAuditRecord saveAuditRecord(Long id, String oldVlue, String newValue, Long userId,String code);

    RcAuditRecord queryRecord(Long bwId,String type,Long userId);

    /**
     * 审核（手机白名单管理）
     * @param rIds
     * @param auditResult
     * @param userId
     */
    String auditRecord(String rIds,Short auditResult,String comments,Long userId,String autoAudit);

    String saveAuditRecordList(List<AuditRecordVo> paramsList);

    /**
     * 保存审核记录
     * @param id
     * @param oldValue
     * @param newValue
     * @param userId
     * @param code
     * @param auditTargetType
     * @return
     */
    RcAuditRecord saveAuditRecord(Long id, String oldValue, String newValue, Long userId,String code,String auditTargetType);

    /**
     * 自动审核调用
     * @param records
     * @param auditResult
     * @param defaultUserId
     * @return
     */
    CommonOuterResponse phoneAutoAudit(String records,Short auditResult,Long defaultUserId);
}

package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.CustomerStopCashInVo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.cust.CustomerTotalConfig;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6 16:27
 */
@Service
public class CustomerManageLimitService {
    @Autowired
    private CustService custService;
    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    public void calculate(CustomerInfo customerInfo, RcCalculateRequestWrapper request) {
        if (request.getBusinessType().isPresent()) {
            Constants.rcBusinessType businessType = request.getBusinessType().get();
            if (businessType == Constants.rcBusinessType.INSIDE_PAY_IN || businessType == Constants.rcBusinessType.GATEWAY_PAY) {
                if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) { //平台商
                    if (businessType == Constants.rcBusinessType.GATEWAY_PAY) { //仅对收单限制
                        CustomerTotalConfig customerTotalConfig = custService.queryTotalConfig(customerInfo.getCustomerCode());
                        EpAssert.state(!"0".equals(customerTotalConfig.getAllowPay()), RcCode.CUSTOMER_MANAGE_LIMIT);
                    }
                } else {
                    CustomerStopCashInVo customerStopCashInVo = cumCacheServiceImpl.getCustomerStopCashInV2(customerInfo.getCustomerCode());
                    if (customerStopCashInVo != null && customerStopCashInVo.isLimitFlag()) { //有限制
                        long stopCashAmount = Optional.ofNullable(customerStopCashInVo.getStopCashAmount()).orElse(0L);
                        EpAssert.state(stopCashAmount != 0, RcCode.CUSTOMER_MANAGE_LIMIT);
                        String txnAmountStr = request.getIndex(RcConstants.RcIndex.AMOUNT);
                        if (txnAmountStr != null) { //有交易金额
                            Long txnAmount = Long.parseLong(txnAmountStr);
                            String key = rcCalculteBasic.getKey(customerInfo.getCustomerCode(), "Day-Max-IN-Amount", RcDateUtils.getCurrentDay());
                            long dayInAmount = myRedisTemplateService.getMyRedisTemplate().opsForValue().increment(key, 0);
                            EpAssert.state(txnAmount + dayInAmount <= stopCashAmount, RcCode.CUSTOMER_MANAGE_LIMIT);
                        }
                    }
                }
            } else if (businessType == Constants.rcBusinessType.WITHDRAW) {
                if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())) { //平台商
                    CustomerTotalConfig customerTotalConfig = custService.queryTotalConfig(customerInfo.getCustomerCode());
                    EpAssert.state(!"0".equals(customerTotalConfig.getAllowPayOut()), RcCode.CUSTOMER_MANAGE_LIMIT);
                }
            }
        }
    }
}

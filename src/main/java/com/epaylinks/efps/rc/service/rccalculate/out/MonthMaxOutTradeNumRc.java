package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 单月最大出金笔数
 * <AUTHOR>
 *
 */
@Service("Month-Max-Out-TradeNum")
public class MonthMaxOutTradeNumRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
		// TODO Auto-generated method stub
		return CommonOuterResponse.SUCCEE;
	}
	
	@Override
	public void reset(RcLimit rcLimit) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		// TODO Auto-generated method stub
		
	}

}

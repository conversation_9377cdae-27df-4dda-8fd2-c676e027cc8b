package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.dataimport.util.DataImportConstants;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcAuditRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BatchRcStatusServiceImpl implements BatchService {

    @Autowired
    private CommonLogger logger;

    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId; // 自动审核默认用户ID 3009001 huangyuekun 黄月坤

    @Autowired
    private RcArchiveMapper rcArchiveMapper;
    @Autowired
    private RcAuditRecordService rcAuditRecordService;
    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String batchNo = extraData.get("batchNo").toString();
        Object userId = extraData.get("userId");
        Long realUserId = null;
        if(userId!=null){
            realUserId = Long.valueOf(userId.toString());
        }
        int colCount = titleList.size(); // 以表头列数为数据列数校验
        if (titleList == null || dataMap == null) {
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code, CustReturnCode.EXCEL_DATA_ERROR.message);
        }
        logger.printMessage("批量风控状态设置，具体数据处理..");
        Map<String,String> oldCustMap = new HashMap<>();
        for (Iterator iterator = dataMap.keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            List<String> dataList = dataMap.get(rowNo);
            if(Objects.isNull(dataList)){
                dataList = new ArrayList<>();
                dataList.add("");
                dataList.add("");
                logger.printMessage("读取导入数据行，空数据行："+rowNo);
            }
            try{
                logger.printMessage("读取导入数据行："+String.join(",",dataList));
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                            CustReturnCode.EXCEL_DATA_ERROR.message + ":" + (rowNo + 1) + "行缺少数据");
                }
                //序号
                String index = dataList.get(0);
                //商户编号
                String customerNo = dataList.get(1);
                //状态
                String status = dataList.get(2);
                //原因
                String reason = dataList.get(3);
                //校验非空项及长度
                checkEmptyParam(customerNo,"商户编号");
                checkParamLength(customerNo,"商户编号",32);
                checkEmptyParam(status,"风控状态");
                checkParamLength(status,"风控状态",2);
                checkEmptyParam(reason,"变更原因");
                checkParamLength(reason,"变更原因",150);
                //校验重复数据
                if(!StringUtils.isBlank(oldCustMap.get(customerNo))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "商户编号重复");
                }
                oldCustMap.put(customerNo,customerNo);
                //校验具体格式
                RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerNo);
                if(Objects.isNull(rcArchive)){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "商户编号不存在或不是正式商户");
                }
                if(!"正常".equals(status) && !"冻结".equals(status)){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "风控状态格式不正确");
                }
                String realStatus = ""; //0：正常;1：冻结
                if("正常".equals(status)){
                    realStatus = "0";
                }else if("冻结".equals(status)){
                    realStatus = "1";
                }
                if(realStatus.equals(rcArchive.getRcStatus())){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "风控状态已是对应状态，无需重复设置");
                }
                if (RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())
                        && RcConstants.RcCustomerStatus.CANCEL.code.equals(rcArchive.getCusStatus())) {
                    throw new AppException(RcCode.CUSTOMER_HAS_CANCELED.code, RcCode.CUSTOMER_HAS_CANCELED.message);
                }
                RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code,rcArchive.getArchiveId());
                if (rcAuditRecord != null && rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
                    throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
                }
                RcAuditRecord rcAuditResult = rcArchiveService.saveAuditRecordForBatch(rcArchive, "rcStatus", realStatus, reason, realUserId,defaultAuditUserId,"0");
                logger.printMessage("风控状态设置，返回结果："+rcAuditResult);
                result.put(rowNo, buildDetail(rowNo, customerNo, DataImportConstants.SuccessFail.SUCCESS.code, index, null));
            }catch (Exception e){
                if (e instanceof AppException) {
                    logger.printMessage("风控状态设置，程序结果："+((AppException) e).getErrorMsg());
                    result.put(rowNo, buildDetail(rowNo, dataList.get(1), DataImportConstants.SuccessFail.FAIL.code, dataList.get(0), ((AppException) e).getErrorMsg()));
                }else {
                    logger.printMessage("风控状态设置，异常结果："+e.getLocalizedMessage());
                    result.put(rowNo, buildDetail(rowNo, dataList.get(1), DataImportConstants.SuccessFail.FAIL.code, dataList.get(0), CustReturnCode.SYSTEM_EXCEPTION.message));
                }
                logger.printLog(e);
            }
        }

        return result;
    }

    /**
     * 校验空字符串
     * @param value
     * @param name
     */
    private void checkEmptyParam(String value, String name) {
        if(StringUtils.isBlank(value)) {
            throw new AppException(RcCode.PARAM_ERROR.code,  name + "不能为空");
        }
    }

    /**
     * 校验空字符串
     * @param value
     * @param name
     * @param length
     */
    private void checkParamLength(String value, String name, int length) {
        if(StringUtils.isBlank(value)) {
            throw new AppException(RcCode.PARAM_ERROR.code,  name + "长度过长");
        }
    }

    /**
     * 批量导入详情
     * @param rowNo
     * @param rowName
     * @param status
     * @param relateId
     * @param remarks
     * @return
     */
    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }
}

package com.epaylinks.efps.rc.service.rccalculate.out;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import com.google.common.collect.ImmutableSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;


@Service("BALANCE_WITHDRAW_DAY_AMOUNT")
public class BalanceWithdrawDayAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {
    private static final Set<String> CALC_BUSINESS = ImmutableSet.of(
            Constants.BusinessCode.WITHDRAW.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.WITHDRAW_SAVINGCARD_BATCH.code,
            Constants.BusinessCode.WITHDRAW_CREDITCARD_BATCH.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_CREDITCARD.code,
            Constants.BusinessCode.DZ_WITHDRAW_SAVINGCARD_BATCH.code
    );


    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        if (CALC_BUSINESS.contains(map.get("businessCode"))) {
            rcCalculteBasic.calculateValueOutAmount(rcLimit, map, RcCalculteBasic.RcCalcDateType.DAY);
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        rcCalculteBasic.resetAmount(rcLimit, RcCalculteBasic.RcCalcDateType.DAY);
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (CALC_BUSINESS.contains(rcCalculateRequest.getBusinessCode())) {
            return rcCalculteBasic.calculateOutAmount(rcLimit, rcCalculateRequest, RcCalculteBasic.RcCalcDateType.DAY);
        } else {
            return CommonOuterResponse.SUCCEE;
        }
    }
}

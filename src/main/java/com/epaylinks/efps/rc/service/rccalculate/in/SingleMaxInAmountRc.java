package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 单笔最大充值限额
 *
 * <AUTHOR>
 */
@Service("Single-Max-IN-Amount")
public class SingleMaxInAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Value("${posRealNameSingleMaxInAmount:********}")
    private String posRealNameSingleMaxInAmount;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    //	@Logable(businessTag = "Single-Max-IN-Amount:calculate")
    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
//        String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
        String posTxnType = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.POS_TXN_TYPE.code);
        if ("REAL_NAME_PRE_ORDER".equals(posTxnType) || "REAL_NAME_CONSUME".equals(posTxnType)) {
            rcLimit.setLimitValue(posRealNameSingleMaxInAmount);
        }
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
        if (StringUtils.isBlank(amountStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long amount = Long.parseLong(amountStr);
        if (amount > Long.parseLong(rcLimit.getLimitValue())) {
            // 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(), rcArchive.getArchiveCode(), rcArchive.getArchiveName(), rcLimit.getDefineCode(), rcLimit.getLimitValue(), "/", amount + "", true, "RC交易受限", rcArchive.getArchiveType());
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void reset(RcLimit rcLimit) {
        // TODO Auto-generated method stub

    }

    //	@Logable(businessTag = "Single-Max-IN-Amount:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
        // TODO Auto-generated method stub

    }

}

package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.spring.SpringUtils;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.LimitSwitchMapper;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.RcLimitMapper;
import com.epaylinks.efps.rc.domain.LimitSwitch;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcLimitService;
import com.epaylinks.efps.rc.service.RcOperateLogService;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018.09.28
 */
@Service
public class RcLimitServiceImpl implements RcLimitService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RcLimitMapper rcLimitMapper;

    @Autowired
    private RcArchiveMapper rcArchiveMapper;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcOperateLogService rcOperateLogService;

    @Autowired
    private CommonLogger logger;

    @Value("${rcLimitRedisTtlSeconds}")
    private Long rcLimitRedisTtlSeconds;

    @Autowired
    private OtherService otherService;

    private static List<Long> DONT_DELETE = new ArrayList<>();
    private static List<Long> HASH_TRAN = new ArrayList<>();

    static {
        DONT_DELETE.add(DefineCode.ACCOUNT_STATUS.defineId);
        DONT_DELETE.add(DefineCode.RC_BALANCE.defineId);
        DONT_DELETE.add(DefineCode.RC_STATUS.defineId);
        DONT_DELETE.add(DefineCode.RC_LEVEL.defineId);

        HASH_TRAN.add(DefineCode.CARD_DAY_OUT_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_DAY_OUT_COUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_MONTH_OUT_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_MONTH_OUT_COUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_DAY_IN_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_DAY_IN_COUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_MONTH_IN_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_MONTH_IN_COUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_DAY_IN_TOTAL_COUNT.defineId);
        HASH_TRAN.add(DefineCode.CARD_MONTH_IN_TOTAL_COUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_DAY_IN_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_DAY_IN_COUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_MONTH_IN_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_MONTH_IN_COUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_DAY_MAX_OUT_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_YEAR_MAX_OUT_AMOUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_DAY_IN_TOTAL_COUNT.defineId);
        HASH_TRAN.add(DefineCode.USER_MONTH_IN_TOTAL_COUNT.defineId);
        
        
		/*HASH_TRAN.add(DefineCode.WITHDRAW_DAY_MAX_COUNT.defineId);
		HASH_TRAN.add(DefineCode.DAY_IN_COUNT.defineId);
		HASH_TRAN.add(DefineCode.MONTH_IN_COUNT.defineId);
	    HASH_TRAN.add(DefineCode.YEAR_IN_COUNT.defineId);
	    HASH_TRAN.add(DefineCode.ACCOUNT_NAME_LIMIT.defineId);
	    HASH_TRAN.add(DefineCode.TOTAL_MAX_OUT_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.SINGLE_MAX_WITHDRAW_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.DAY_MAX_WITHDRAW_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.MONTH_MAX_WITHDRAW_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.YEAR_MAX_WITHDRAW_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.CERT_DAY_MAX_IN_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.CERT_MONTH_MAX_IN_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.CERT_DAY_MAX_CREDIT_AMOUNT.defineId);
	    HASH_TRAN.add(DefineCode.CERT_MONTH_MAX_CREDIT_AMOUNT.defineId);*/
    }


    @Override
    public boolean save(RcLimit rcLimit) {
        //先存到oracle中
        int i = rcLimitMapper.insertSelective(rcLimit);
        if (i != 0) {
//            saveToRedis(rcLimit);
            updateToRedis(rcLimit);
        } else {
            return false;
        }
        return true;
    }

    @Override
    public void saveToRedis(RcLimit rcLimit) {
        //再存到Redis中
        String key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
        if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型扩展
            key = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
        }

        Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(key, rcLimit.getBusinnesType());

        if (map == null || map.size() == 0) {
            //说明该业务类型之前没有创建过
            redisTemplate.expire(key, -1, TimeUnit.SECONDS);
            map = new HashedMap();
        }

        map.put(rcLimit.getDefineCode(), rcLimit);
        redisTemplate.opsForHash().put(key, rcLimit.getBusinnesType(), map);
    }

    @Override
    public void updateToRedis(RcLimit rcLimit) {
        //再存到Redis中
        String key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
        if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型扩展
            key = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
        }
        Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(key, rcLimit.getBusinnesType());

        if (map == null) {
            return;
        }
        map.put(rcLimit.getDefineCode(), rcLimit);
        redisTemplate.opsForHash().put(key, rcLimit.getBusinnesType(), map);
        redisTemplate.expire(key, rcLimitRedisTtlSeconds, TimeUnit.SECONDS);
    }

    @Override
    public boolean queryExistence(RcLimit rcLimit) {
        return rcLimitMapper.queryExistence(rcLimit) == 0 ? true : false;
    }


    //	@Logable(businessTag = "queryByBusinessTypeAndTaregetId")
    @Override
    public List<RcLimit> queryByBusinessTypeAndTaregetId(String businessType, String businessTargetType, String businessTargetId) {
        List<RcLimit> list = new ArrayList<>();

        //根据BusinessType反射类型
        try {
            Class<?> impl = RcLimitServiceImpl.class;
            Method method = impl.getMethod(businessType, String.class, String.class, RedisTemplate.class);
            list = (List<RcLimit>) method.invoke(impl.newInstance(), businessTargetType, businessTargetId, redisTemplate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Collections.sort(list, new Comparator<RcLimit>() {
            @Override
            public int compare(RcLimit o1, RcLimit o2) {
                if (o1.getDefineId() > o2.getDefineId()) {
                    return 1;
                } else {
                    return -1;
                }
            }
        });
        return list;
    }


    @Logable(businessTag = "queryByBusinessTaregetId")
    @Override
    public List<RcLimit> queryByBusinessTaregetId(String businessTargetId) {
        List<RcLimit> list = rcLimitMapper.queryAllLimit(businessTargetId);

        //判断商户是否已经设置了
        List<RcLimit> amountLimit = rcLimitMapper.queryAmountLimits(businessTargetId);
        if (amountLimit.isEmpty()) {
            RcArchive rcArchive = rcArchiveMapper.selectByCodeOrName(businessTargetId, null);
            if (rcArchive != null) {
                if (rcArchive.getParentCode() != null) { // 读取父级商户限额
                    amountLimit = rcLimitMapper.queryAmountLimits(rcArchive.getParentCode()).stream()
                            .filter(l -> !RcConstants.BusinessTagerType.PLAT_CUSTOMER.code.equals(l.getBusinessTagerType()))
                            .collect(Collectors.toList());
                }
                if (amountLimit == null || amountLimit.isEmpty()) { // 读取风控等级默认值
                    amountLimit = rcLimitMapper.queryAmountLimits(rcArchive.getRcLevel());
                }
                for (RcLimit rcLimit : amountLimit) {
                    if (rcLimit.getBusinessTagerType().equals(rcArchive.getArchiveType())) { // 根据风控档案类型，过滤其他类型默认指标
                        rcLimit.setBusinessTagerId(businessTargetId);
                    }
                }
                list.addAll(amountLimit);
            }
        }
        return list;
    }


    @Override
    public Map<String, Long> queryCustomerAmountLimit(String code, String parentCode, String level) {

        List<Map<String, String>> list = rcLimitMapper.queryAmountLimitMap(code, RcConstants.BusinessTagerType.CUSTOMER_CODE.code);

        if (parentCode != null && list.isEmpty()) {
            // 如果为空，读取父级商户限额
            list = rcLimitMapper.queryAmountLimitMap(parentCode, RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        }
        if (list.isEmpty()) {
            //如果为空，读取默认风控等级
            list = rcLimitMapper.queryAmountLimitMap(level, RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        }
        Map<String, Long> returnMap = new HashedMap();

        for (Map<String, String> map : list) {
            returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
        }

        return returnMap;
    }

    @Override
    public Map<String, Long> queryMinAmountLimit(String targetType, String code, String level, RcArchive rcArchive) {
        // 商户/客户号限额
        List<Map<String, String>> clientList = rcLimitMapper.queryAmountLimitMap(code,targetType);
        List<Map<String, String>> industryList = new ArrayList<>();
        if (Constants.CustomerType.BUSINESSMAN.code == rcArchive.getType() || Constants.CustomerType.MICRO.code == rcArchive.getType()) {
            industryList = rcLimitMapper.queryAmountLimitMap(rcArchive.getType() + "_" + rcArchive.getMcc(), RcConstants.BusinessTagerType.INDUSTRY.code);
        } else if (Constants.CustomerType.ENTERPRISE.code == rcArchive.getType() || Constants.CustomerType.ABROAD.code == rcArchive.getType()
                || Constants.CustomerType.GOVERNMENT.code == rcArchive.getType() || Constants.CustomerType.OTHERS.code == rcArchive.getType()) {
            industryList = rcLimitMapper.queryAmountLimitMap(rcArchive.getType() + "_" + rcArchive.getIndustry(), RcConstants.BusinessTagerType.INDUSTRY.code);
        }
        Map<String, Long> returnMap = new HashedMap();
        if (industryList.isEmpty()) {
            for (Map<String, String> map : clientList) {
                returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
            }
        } else {
            Map<String, Long> clientMap = new HashMap<>();
            Map<String, Long> industryMap = new HashMap<>();
            for (Map<String, String> map : clientList) {
                clientMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
            }
            for (Map<String, String> map : industryList) {
                industryMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
            }

            returnMap = getMinValue(clientMap,industryMap);
        }
        // 入金单日最高限额指标取值：
        // 商户注册资本 ≤ 5w ，默认设置值为5w；
        // 5w < 商户注册资本 ≤ 筛选值 ，默认设置值为注册资本；
        // 商户注册资本 ≥ 筛选值 ＞ 5w，默认设置值为筛选值；
        // 小微商户进件无营业执照信息，故无注册资本，按筛选值配置；
        // 筛选值：客户号默认限额+行业默认限额取较低值
        // 20240126: 商户注册资本 ≤ 5w ，默认设置值为5w；商户注册资本 > 5w;取筛选值和注册资本的较小值----（最新）
        Long dayMaxInAmount = returnMap.get("Day-Max-IN-Amount");
        Long registeredCapital = rcArchive.getRegisteredCapital() == null ? 0L : rcArchive.getRegisteredCapital();
        /*
        if (Constants.CustomerType.MICRO.code == rcArchive.getType()) {
            return returnMap;
        } else if (registeredCapital <= 50000) {
            returnMap.put("Day-Max-IN-Amount",5000000L);
        } else if (registeredCapital > 50000L && dayMaxInAmount >= registeredCapital) {
            returnMap.put("Day-Max-IN-Amount",rcArchive.getRegisteredCapital() * 100);
        }
         */
        if (Constants.CustomerType.MICRO.code == rcArchive.getType()) {
            return returnMap;
        } else if (registeredCapital <= 50000L) {
            returnMap.put("Day-Max-IN-Amount",5000000L);
        } else {
            if (dayMaxInAmount >= registeredCapital * 100) {
                returnMap.put("Day-Max-IN-Amount",registeredCapital * 100);
            }
        }
        return returnMap;
    }

    @Override
    public Map<String, Long> queryAmountLimit(String targetType, String code, String level, RcArchive rcArchive) {
        RcArchive tmpArchive = new RcArchive();
        BeanUtils.copyProperties(rcArchive,tmpArchive);
        Map<String, Long> result = new HashMap<>();
        // 查询商户/客户号当前限额值
        // 1、无存量数据：查询风险等级+行业限额取较小值
        // 2、有存量数据：先查询已设置的限额，再根据“风险等级+行业限额取较小值”查询其他未插表的限额指标
        List<Map<String, String>> list = rcLimitMapper.queryAmountLimitMap(code, targetType);
        // 为空时取父级
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType) && list.isEmpty()) {
            list = rcLimitMapper.queryAmountLimitMap(rcArchive.getParentCode(), RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            code = rcArchive.getParentCode();
        }
        // 性质、MCC、行业类别为空时查询cust表,商户注册资本查询企业表
        setRcArchive(tmpArchive);
        logger.printMessage("print-tmpArchive:" + JSON.toJSONString(tmpArchive));
        if (list.isEmpty()) { // 无存量
            return queryMinAmountLimit(targetType,tmpArchive.getRcLevel(),level,tmpArchive);
        } else { // 有存量
            for (Map<String, String> map : list) {
                result.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
            }
            // 补充风险等级默认值
            Map<String, Long> levelAddMap = queryAddAmountLimitMap(code, tmpArchive.getArchiveType(), tmpArchive.getRcLevel());
            if (levelAddMap == null) {
                return result;
            }
            // 入金单日最高限额
            Long dayMaxInAmount = result.get("Day-Max-IN-Amount");

            String industryCode = null;
            if (Constants.CustomerType.BUSINESSMAN.code == tmpArchive.getType() || Constants.CustomerType.MICRO.code == tmpArchive.getType()) {
                industryCode = tmpArchive.getType() + "_" + tmpArchive.getMcc();
            } else if (Constants.CustomerType.ENTERPRISE.code == tmpArchive.getType() || Constants.CustomerType.ABROAD.code == tmpArchive.getType()
                    || Constants.CustomerType.GOVERNMENT.code == tmpArchive.getType() || Constants.CustomerType.OTHERS.code == tmpArchive.getType()) {
                industryCode = tmpArchive.getType() + "_" + tmpArchive.getIndustry();
            }
            // 补充行业默认值
            Map<String, Long> industryAddMap = queryAddIndustryAmountLimitMap(code, tmpArchive.getArchiveType(), industryCode);
            if (industryAddMap == null) {
                result.putAll(levelAddMap);
                return result;
            }
            Map<String,Long> minMap = getMinValue(levelAddMap,industryAddMap);
            if (dayMaxInAmount == null) {
                // 入金单日最高限额指标取值：
                // 商户注册资本 ≤ 5w ，默认设置值为5w；
                // 5w < 商户注册资本 ≤ 筛选值 ，默认设置值为注册资本；
                // 商户注册资本 ≥ 筛选值 ＞ 5w，默认设置值为筛选值；
                // 小微商户进件无营业执照信息，故无注册资本，按筛选值配置；
                // 筛选值：客户号默认限额+行业默认限额取较低值
                // 20240126: 商户注册资本 ≤ 5w ，默认设置值为5w；商户注册资本 > 5w;取筛选值和注册资本的较小值----（最新）
                Long registeredCapital = tmpArchive.getRegisteredCapital() == null ? 0L : tmpArchive.getRegisteredCapital();
                /*
                if (registeredCapital <= 50000) {
                    minMap.put("Day-Max-IN-Amount",5000000L);
                } else if (registeredCapital > 50000L && dayMaxInAmount >= registeredCapital) {
                    minMap.put("Day-Max-IN-Amount",tmpArchive.getRegisteredCapital() * 100);
                }
                 */
                dayMaxInAmount = minMap.get("Day-Max-IN-Amount"); // 筛选值
                if (registeredCapital <= 50000L) {
                    minMap.put("Day-Max-IN-Amount",5000000L);
                } else {
                    if (dayMaxInAmount >= registeredCapital * 100) {
                        minMap.put("Day-Max-IN-Amount",registeredCapital * 100);
                    }
                }
            }
            result.putAll(minMap);
        }
        return result;
    }

    public void setRcArchive(RcArchive rcArchive) {
        Integer type = rcArchive.getType();
        String mcc = rcArchive.getMcc();
        String industry = rcArchive.getIndustry();
        Customer customer = otherService.queryCustomerByCustomerNo(rcArchive.getArchiveCode());
        if (type == null && RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {
            if (customer != null && customer.getMcc() != null) {
                type = customer.getType();
                mcc = customer.getMcc();
                rcArchive.setType(type);
                rcArchive.setMcc(mcc);
            }
            if ((Constants.CustomerType.ENTERPRISE.code == type || Constants.CustomerType.ABROAD.code == type
                    || Constants.CustomerType.GOVERNMENT.code == type || Constants.CustomerType.OTHERS.code == type)) {
                industry = otherService.queryIndustryByCode(customer.getCustomerNo());
                rcArchive.setIndustry(industry);
            }
        }
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcArchive.getArchiveType())) {
            rcArchive.setRegisteredCapital(otherService.queryRegistCapital(customer.getCustomerId()));
        }
    }

    @Override
    public Map<String, Long> queryCertAmountLimit(String code, String businessTargetType, String level) {

        List<Map<String, String>> list = rcLimitMapper.queryAmountLimitMap(code, businessTargetType);

        if (list.isEmpty()) {
            //如果为空，读取默认风控等级
            list = rcLimitMapper.queryAmountLimitMap(level, RcConstants.BusinessTagerType.IDENTITY_CARD.code); // 默认等级证件类型记录为001
        }
        Map<String, Long> returnMap = new HashedMap();

        for (Map<String, String> map : list) {
            returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
        }

        return returnMap;
    }

    @Override
    public Map<String, Long> queryIndustryAmountLimit(String code, String businessTargetType) {
        Map<String, Long> returnMap = new HashedMap();
        List<Map<String, String>> list = rcLimitMapper.queryAmountLimitMap(code, businessTargetType);
        if (!list.isEmpty()) {
            for (Map<String, String> map : list) {
                returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
            }
        }
        return returnMap;
    }

    @Override
    public Map<String, Long> queryAmountLimit(String code, String businessTargetType, String level) {

        List<Map<String, String>> list = rcLimitMapper.queryAmountLimitMap(code, businessTargetType);

        if (list.isEmpty()) {
            //如果为空，读取默认风控等级
            list = rcLimitMapper.queryAmountLimitMap(level, businessTargetType); // 默认等级证件类型记录为008
        }
        Map<String, Long> returnMap = new HashedMap();

        for (Map<String, String> map : list) {
            returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
        }

        return returnMap;
    }

    @Override
    public Map<String, Long> queryAddAmountLimitMap(String code, String businessTargetType, String level) {
        List<Map<String, String>> list = rcLimitMapper.queryAddAmountLimitMap(code, businessTargetType,level);
        if (list.isEmpty()) {
            return null;
        }
        Map<String, Long> returnMap = new HashedMap();
        for (Map<String, String> map : list) {
            returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
        }
        return returnMap;
    }

    @Override
    public Map<String, Long> queryAddIndustryAmountLimitMap(String code, String businessTargetType, String level) {
        List<Map<String, String>> list = rcLimitMapper.queryAddIndustryAmountLimitMap(code, businessTargetType,level);
        if (list.isEmpty()) {
            return null;
        }
        Map<String, Long> returnMap = new HashedMap();
        for (Map<String, String> map : list) {
            returnMap.put(map.get("DEFINE_CODE"), Long.valueOf(map.get("LIMIT_VALUE")));
        }
        return returnMap;
    }

    @Override
    public int deleteRcLimit(RcLimit rcLimit) {
        int i = rcLimitMapper.deleteByPrimaryKey(rcLimit.getLimitId());
        if (i == 1) {
            String key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型扩展
                key = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            }
            Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(key, rcLimit.getBusinnesType());
            if (map != null && map.get(rcLimit.getDefineCode()) != null) {
                map.remove(rcLimit.getDefineCode());
                redisTemplate.opsForHash().put(key, rcLimit.getBusinnesType(), map);
                redisTemplate.expire(key, rcLimitRedisTtlSeconds, TimeUnit.SECONDS);
            }
        }
        return i;
    }


    @Override
    public boolean updateLimit(RcLimit rcLimit) {
        //先更新数据库
        int i = rcLimitMapper.updateByPrimaryKey(rcLimit);
        if (i == 1) {
            //再更新redis
            String key = rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) { // 证件类型扩展
                key = rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId() + rcLimit.getBusinnesType();
            }
            Map<String, RcLimit> map = (Map<String, RcLimit>) redisTemplate.opsForHash().get(key, rcLimit.getBusinnesType());

            if (map == null) {
                /*
                //说明可能是存量商户
                redisTemplate.expire(key, -1, TimeUnit.SECONDS);
                map = new HashedMap();
                 */
                return true;
            }
            map.put(rcLimit.getDefineCode(), rcLimit);

            redisTemplate.opsForHash().put(key, rcLimit.getBusinnesType(), map);
            redisTemplate.expire(key, rcLimitRedisTtlSeconds, TimeUnit.SECONDS);
        } else {
            return false;
        }

        return true;
    }


    @Override
    public RcLimit queryLimit(Long defineId, String businessTagerId) {

        return queryLimit(defineId, RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTagerId);
    }

    @Override
    public RcLimit queryLimit(Long defineId, String businessTagerType, String businessTagerId) {

        return rcLimitMapper.queryLimit(defineId, businessTagerType, businessTagerId);
    }


    @Override
    public PageResult<Map<String, Object>> queryAllLimit(String customerCode, int pageNum, int pageSize) {
        PageResult<Map<String, Object>> pageResult = new PageResult<>();

        //总记录数
        int total = rcLimitMapper.totalLimitByCustomer(customerCode);
        pageResult.setTotal(total);

        //当前页面
        int endNum = pageSize * pageNum;
        int startNum = endNum - pageSize + 1;
        List<Map<String, Object>> rcLimits = rcLimitMapper.pageLimitByCustomer(customerCode, startNum, endNum);

        //单位处理
        for (Map<String, Object> map : rcLimits) {
            if ("Long".equals(map.get("type"))) {
                BigDecimal b = new BigDecimal(map.get("limitValue").toString());
                b = b.divide(new BigDecimal(100));
                map.put("limitValue", b.toString());
            }
        }
        pageResult.setRows(rcLimits);
        return pageResult;
    }

////////////////////////////////分割线/////////////////////////////////////////////////

    /**
     * 内转入金
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> insidePayIn(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        Map<String, RcLimit> sdMap = querySD(businessTagerType, businessTagerId, redisTemplate);

        trans(rcMap, list);
        trans(sdMap, list);

        return list;
    }

    /**
     * 内转出金
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> insidePayOut(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        Map<String, RcLimit> cjMap = queryCJ(businessTagerType, businessTagerId, redisTemplate);

        trans(rcMap, list);
        trans(cjMap, list);

        return list;
    }

    /**
     * 转账
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> withDraw(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        Map<String, RcLimit> cjMap = queryCJ(businessTagerType, businessTagerId, redisTemplate);
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(businessTagerType) && cjMap != null) {
            cjMap.remove(DefineCode.DALIY_OUT_LIMIT.defineCode);
            cjMap.remove(DefineCode.SINGLE_OUT_LIMIT.defineCode);
            LimitSwitchMapper limitSwitchMapper = SpringContextUtils.getBean(LimitSwitchMapper.class);
            List<LimitSwitch> daliyLimits = limitSwitchMapper.selectOpenByCustomerNo(businessTagerId);
            for (LimitSwitch daliyLimit : daliyLimits) {
                RcLimit rcLimit = new RcLimit();
                rcLimit.setDefineCode(daliyLimit.getLimitIndex());
                DefineCode defineCode = DefineCode.getDefineCode(rcLimit.getDefineCode());
                if (defineCode != null) {
                    rcLimit.setDefineId(defineCode.defineId);
                } else {
                    rcLimit.setDefineId(999L);
                }
                rcLimit.setBusinnesType("CJ");
                rcLimit.setBusinessTagerType(businessTagerType);
                rcLimit.setBusinessTagerId(businessTagerId);
                rcLimit.setLimitValue(String.valueOf(daliyLimit.getLimitValue()));
                rcLimit.setLimitType("Long");
                cjMap.put(daliyLimit.getLimitIndex(), rcLimit);
            }
        }
        Map<String, RcLimit> dzMap = queryWD(businessTagerType, businessTagerId, redisTemplate); // 垫资代付
        Map<String, RcLimit> dfMap = queryDF(businessTagerType, businessTagerId, redisTemplate); // 同卡代付


        trans(rcMap, list);
        trans(cjMap, list);
        trans(dzMap, list);
        trans(dfMap, list);

        return list;
    }

    /**
     * 退款出金
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> refund(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        Map<String, RcLimit> cjMap = queryCJ(businessTagerType, businessTagerId, redisTemplate);

        trans(rcMap, list);
        trans(cjMap, list);
        return list;
    }


    /**
     * 网关支付
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> gateWayPay(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        Map<String, RcLimit> sdMap = querySD(businessTagerType, businessTagerId, redisTemplate);

        trans(rcMap, list);
        trans(sdMap, list);

        return list;
    }

    /**
     * 登陆
     *
     * @param businessTagerType
     * @param businessTagerId
     * @return
     */
    public List<RcLimit> logIn(String businessTagerType, String businessTagerId, RedisTemplate redisTemplate) {
        List<RcLimit> list = new ArrayList<>();

        Map<String, RcLimit> rcMap = queryRC(businessTagerType, businessTagerId, redisTemplate);
        trans(rcMap, list);

        return list;
    }


    /**
     * 获取tager指标
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @param businnesType
     * @return
     */
    Map<String, RcLimit> queryRcLimit(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate, String type) {
        RcLimitHelper rcLimitHelper = SpringUtils.getBean(RcLimitHelper.class);
        return rcLimitHelper.getRcLimitIfNotExistDefault(businessTagerType, bussinessTagerId, type);
    }

    /**
     * 获取收单指标
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @return
     */
    Map<String, RcLimit> querySD(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate) {

        return queryRcLimit(businessTagerType, bussinessTagerId, redisTemplate, "SD");
    }

    /**
     * 获取出金风控制指标
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @return
     */
    Map<String, RcLimit> queryCJ(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate) {

        return queryRcLimit(businessTagerType, bussinessTagerId, redisTemplate, "CJ");
    }

    /**
     * 获取垫资代付金额
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @param redisTemplate
     * @return
     */
    Map<String, RcLimit> queryWD(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate) {

        return queryRcLimit(businessTagerType, bussinessTagerId, redisTemplate, "WD");
    }

    /**
     * 获取同卡代付金额
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @param redisTemplate
     * @return
     */
    Map<String, RcLimit> queryDF(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate) {

        return queryRcLimit(businessTagerType, bussinessTagerId, redisTemplate, "DF");
    }

    /**
     * 获取风控指标
     *
     * @param businessTagerType
     * @param bussinessTagerId
     * @return
     */
    Map<String, RcLimit> queryRC(String businessTagerType, String bussinessTagerId, RedisTemplate redisTemplate) {
        Map<String, RcLimit> map = null;
        try {
            RcLimitHelper rcLimitHelper = SpringUtils.getBean(RcLimitHelper.class);
            map = rcLimitHelper.getRcLimit(businessTagerType, bussinessTagerId, "RC");
        } catch (Exception e) {
        }
        return map;
    }

    List<RcLimit> trans(Map<String, RcLimit> map, List<RcLimit> list) {
        if (map == null) {
            return list;
        }
        Iterator entries = map.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry entry = (Map.Entry) entries.next();
            list.add((RcLimit) entry.getValue());
        }
        return list;
    }

    @Override
    public boolean updateAmountLimit(Long defineId, String businessTagerId, String limitValue, Long userId,
                                     String userName) {

        return updateAmountLimit(defineId, RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTagerId, limitValue, userId, userName);
    }

    @Override
    public boolean updateAmountLimit(Long defineId, String businessTagerType, String businessTagerId, String limitValue,
                                     Long userId, String userName) {

        RcLimit rcLimit = queryLimit(defineId, businessTagerType, businessTagerId);
        String origValue = rcLimit.getLimitValue();
        //把新的指标值放进去
        rcLimit.setLimitValue(limitValue);
        rcLimit.setUserId(userId);
        rcLimit.setUserName(userName);
        //执行更新
        boolean rst = updateLimit(rcLimit);
        if (rst) {
            // region 写入操作日志
            String rcLevelName = "";
            for (RcConstants.RcLevel rcLevel1 : RcConstants.RcLevel.values()) {
                if (rcLevel1.code.equals(businessTagerId)) {
                    rcLevelName = rcLevel1.message;
                    break;
                }
            }
            if (StringUtils.isNotBlank(rcLevelName) && !limitValue.equals(origValue)) {
                String defineName = "";
                for (DefineCode e : DefineCode.values()) {
                    if (e.defineId.equals(defineId)) {
                        defineName = e.name;
                    }
                }
                if (StringUtils.isNotBlank(defineName)) {
                    RcOperateLog log = new RcOperateLog();
                    // 在风险档案菜单里
                    log.setPermId("80402"); // 商户默认限额管理
                    if (RcConstants.BusinessTagerType.PERSON.code.equals(businessTagerType)) {
                        log.setPermId("80405");   // 个人默认限额管理
                    }
                    log.setCode("/");
                    log.setName("/");
                    log.setType("2");
                    log.setOperator(String.valueOf(userId));
                    log.setOperateTime(new Date());
                    log.setOperateContent(rcLevelName + "-" + defineName);
                    log.setOrigValue(origValue);
                    log.setNewValue(limitValue);
                    rcOperateLogService.insert(log);
                }
            }
            // endregion
        }
        return rst;
    }

    @Override
    public Map<String, Object> queryRedisLimit(String targetId, String datetime) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<RcLimit> list = queryByBusinessTaregetId(targetId);
        if (list == null || list.isEmpty()) {
            return resultMap;
        }
        String dateStr = "";
        for (RcLimit rcLimit : list) {
            if (rcLimit.getDefineCode().contains("Year")) {
                dateStr = datetime.substring(0, 4);
            } else if (rcLimit.getDefineCode().contains("Month")) {
                dateStr = datetime.substring(0, 6);
            } else {
                dateStr = datetime;
            }
            String redisKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), dateStr);
            if (!RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) {
                redisKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId(), rcLimit.getDefineCode(), dateStr);
            }
            if (HASH_TRAN.contains(rcLimit.getDefineId())) {
                try {
                    Map<String, Object> map = redisTemplate.opsForHash().entries(redisKey);
                    for (String bankCardNo : map.keySet()) {
                        long dayOutAmount = (long) map.get(bankCardNo);
                        resultMap.put(redisKey + ":" + bankCardNo, dayOutAmount);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                if (redisTemplate.hasKey(redisKey)) {
                    resultMap.put(redisKey, redisTemplate.opsForValue().get(redisKey));
                } else {
                    if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType())) {
                        redisKey = rcLimit.getBusinessTagerType() + "_" + redisKey;
                        resultMap.put(redisKey, redisTemplate.opsForValue().get(redisKey));
                    }
                }
            }
        }
        return resultMap;
    }

    @Override
    public RcLimit queryByTargerIdIfNullByLevel(Long defineId, String businessTagerType, String businessTargetId) {

        RcLimit rcLimit = rcLimitMapper.queryLimit(defineId, businessTagerType, businessTargetId);
        if (rcLimit == null) {
            //如果为空，则找风控等级默认限额
            RcArchive rcArchive = rcArchiveMapper.selectByTypeAndCode(businessTagerType, businessTargetId);
            rcLimit = rcLimitMapper.queryLimit(defineId, rcArchive.getArchiveType(), rcArchive.getRcLevel());
            rcLimit.setBusinessTagerId(businessTargetId);
        }
        return rcLimit;
    }

    @Override
    public void updateRcLevel(String archiveCode) {
        //删除原有redis中的rcLimit
        List<RcLimit> rcLimits = rcLimitMapper.queryAllLimit(archiveCode);
        for (RcLimit rcLimit : rcLimits) {
            if (!DONT_DELETE.contains(rcLimit.getDefineId())) {
                redisTemplate.expire(archiveCode + rcLimit.getBusinnesType(), 0L, TimeUnit.SECONDS);
            }
        }
    }

    // 合并两个Map并获取较小值
     public Map<String,Long> getMinValue(Map<String, Long> map1,Map<String, Long> map2) {
        Map<String, Long> result = Stream.concat(map1.entrySet().stream(), map2.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1.compareTo(v2) < 0 ? v1 : v2, HashMap::new));
        logger.printMessage("打印合并后的Map：" + JSON.toJSONString(result));
        return result;
    }

    public static void main(String[] args) {
        Map<String, Long> map1 = new HashMap<>();
        Map<String, Long> map2 = new HashMap<>();
        map1.put("a1",11L);
        map1.put("a2",11L);
        map1.put("a3",null);
        map1.put("a4",11L);
        map1.put("a5",11L);

        map2.put("a1",12L);
        map2.put("a2",10L);
//        map2.put("a3",null);
        map2.put("a4",11L);
        map2.put("a5",11L);
        Map<String, Long> result = Stream.concat(map1.entrySet().stream(), map2.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1.compareTo(v2) < 0 ? v1 : v2, HashMap::new));
        System.out.println("result:" + result);

    }

}

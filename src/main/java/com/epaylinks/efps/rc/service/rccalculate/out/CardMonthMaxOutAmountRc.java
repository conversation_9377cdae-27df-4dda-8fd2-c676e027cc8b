package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 同卡代付单月最大出金限额
 * <AUTHOR>
 * @since 2018-12-10
 *
 */
@Service("Card-Month-Max-OUT-Amount")
public class CardMonthMaxOutAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;
	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private SequenceService sequenceService;
    @Autowired
    private CardMonthMaxOutAmountRc self;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;
    
    private static final String Card_Day_Max_Out_Amount = "Card-Day-Max-OUT-Amount";

    @Autowired
    private RcArchiveService rcArchiveService;

	@Override
	public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Date date = new Date();
        String lastDayHahsKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,Card_Day_Max_Out_Amount ,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastDayHahsKey);// 根据昨日历史银行卡号迭代，redis会当天的月记录
        for (String bankCardNo : map.keySet()) {
            if (date.getDate() == 1) {
                // 如果当前时间是每月的一号
                self.historyDataHandler(rcLimit, bankCardNo, date, RcLimitData.Status.FINISHED.code);
                redisTemplate.opsForHash().put(hashKey, bankCardNo, 0L);// 初始化今天的值
            } else {
                // 如果不是每月的一号
                long monthOutAmount = self.historyDataHandler(rcLimit, bankCardNo, date, RcLimitData.Status.PROCESSING.code);
                redisTemplate.opsForHash().put(hashKey, bankCardNo, monthOutAmount);// 月度数据更新 
            }
        }
	        
	}

//	@Logable(businessTag = "Card-Month-Max-OUT-Amount:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if( !rcCalculteBasic.isWithdrawals(rcCalculateRequest.getBusinessCode())){// 非代付业务不做判断
            return CommonOuterResponse.SUCCEE;
        }
        String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
        if(customerCode == null || bankCardNo == null ) {
            return CommonOuterResponse.SUCCEE;
        }
        String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
        if (StringUtils.isBlank(amountStr)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
        }
        long amount = Long.parseLong(amountStr);
        long cardMonthOutAmount = 0L;
        long cardDayOutAmount = 0L;
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        String dayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Card_Day_Max_Out_Amount, RcDateUtils.getCurrentDay());
        if(redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)) {
            cardMonthOutAmount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo);    
        }
        if(redisTemplate.opsForHash().hasKey(dayHashKey, bankCardNo)) {
            cardDayOutAmount = (long) redisTemplate.opsForHash().get(dayHashKey,bankCardNo);
        }

        if (cardMonthOutAmount + cardDayOutAmount > Long.parseLong(rcLimit.getLimitValue())) {
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
            rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),cardDayOutAmount + cardMonthOutAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
            //实际发生值已经大于限定值
            return "RC交易受限";
        }
        return CommonOuterResponse.SUCCEE;
        
	}

//	@Logable(businessTag = "Card-Month-Max-OUT-Amount:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		// TODO Auto-generated method stub
	}

    public Long historyDataHandler(RcLimit rcLimit , String bankCardNo, Date date , String status) {
	    redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String lastDayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , Card_Day_Max_Out_Amount, RcDateUtils.getLastDay());
		String businessTargetId =  rcLimit.getBusinessTagerId() + "_" + bankCardNo;

		long dayOutAmount = 0;
        if(redisTemplate.opsForHash().hasKey(lastDayHashKey, bankCardNo)) {
        	dayOutAmount = (long) redisTemplate.opsForHash().get(lastDayHashKey, bankCardNo);
        }
       
        Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);// 昨日时间
        RcLimitData monthOutAmountRcLimitData = rcLimitDataMapper
                .selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
                        businessTargetId, RcDateUtils.getMonthString(selectDate) , RcLimitData.Status.PROCESSING.code);
        long monthOutAmount = 0;
        if (monthOutAmountRcLimitData == null) {
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
                    businessTargetId, rcLimit.getDefineCode(), dayOutAmount + "", date, date , 
                    status, RcDateUtils.getMonthString(selectDate));
            rcLimitDataMapper.insert(rcLimitData);
            monthOutAmount = dayOutAmount;
        }else {
            monthOutAmount = Long.parseLong(monthOutAmountRcLimitData.getValue()) + dayOutAmount;
            monthOutAmountRcLimitData.setValue(monthOutAmount + "");
            monthOutAmountRcLimitData.setUpdatetime(date);
            monthOutAmountRcLimitData.setStatus(status);
            rcLimitDataMapper.updateByPrimaryKey(monthOutAmountRcLimitData);
        }
        return monthOutAmount;
    }

}

package com.epaylinks.efps.rc.service.rccalculate.out;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("PERSON_TRANS_PERSON")
public class PersonTransPersonAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private CumCacheServiceImpl cumCacheServiceImpl;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
    }

    @Override
    public void reset(RcLimit rcLimit) {
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (Constants.BusinessCode.BALANCE_TRANS.code.equals(rcCalculateRequest.getBusinessCode()) &&
                "1".equals(rcLimit.getLimitValue())
        ) {
            String bankAccount = rcCalculteBasic.getRcIndex(rcCalculateRequest.getIndexs(), RcConstants.RcIndex.BANK_ACCOUNT);
            if (bankAccount != null) {
                CustomerInfo customerInfo = cumCacheServiceImpl.getCustomerInfo(bankAccount, bankAccount, UserType.PPS_USER.code);
                if (customerInfo != null && Constants.customerCategory.EFPS_PERSON.code.equals(customerInfo.getCustomerCategory())) {
                    // 记录触发日记
                    RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(),
                            rcLimit.getBusinessTagerId());
                    rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),
                            rcArchive.getArchiveCode(),
                            rcArchive.getArchiveName(),
                            rcLimit.getDefineCode(), rcLimit.getLimitValue(),
                            customerInfo.getName(), bankAccount, false, "RC交易受限", rcArchive.getArchiveType());
                    //实际发生值已经大于限定值
                    return "RC交易受限";
                }
            }
        }
        return CommonOuterResponse.SUCCEE;
    }
}

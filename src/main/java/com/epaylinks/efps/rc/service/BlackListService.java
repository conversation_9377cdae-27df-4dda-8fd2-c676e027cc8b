package com.epaylinks.efps.rc.service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.BlackListMapper;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.BlackList;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;
import com.epaylinks.efps.rc.service.impl.ExportWebsiteEffectivenessService;
import com.epaylinks.efps.rc.util.CardUtils;
import com.epaylinks.efps.rc.util.RcCvsUtils;
import com.epaylinks.efps.rc.vo.BlackListVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class BlackListService {
    @Autowired
    private BlackListMapper blackListMapper;

    @Autowired
    private OtherMapper otherMapper;

    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private HessianService hessianService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private FsService fsService;

    private String defaultOperator = "系统";

    public BatchResponse getBlackList(MultipartFile file, String type, Long userId) throws Exception {
        String fileName = file.getOriginalFilename();
        BatchResponse response = new BatchResponse();
        String title = "";
        if ("CARDHOLDER".equals(type)) {
            title = "序号,类型,类型值,风险标签,添加人";
        } else if ("MERCHANT".equals(type)) {
            title = "商户编号,商户名称,渠道,添加人";
        } else {
            throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
        }
        Map<Integer, List<String>> dataList = batchTaskService.getDataList(file,userId,0,title);

        // 输出list
        List<List<String>> allOutList = new ArrayList<>();
        // 错误数量统计
        Integer failCount = 0;

        if ("CARDHOLDER".equals(type)) {
            for(Integer rowNo : dataList.keySet()) {
                List<String> cellList = dataList.get(rowNo);
                List<String> outList = new ArrayList<>();
                cellList.forEach(data -> {
                    outList.add(data);
                });

                try {
                    BlackListVo blackListVo = new BlackListVo();
                    for (int i = 0; i < cellList.size(); i++) {
                        String data = cellList.get(i);
                        switch (i) {
                            case 0:
                                if (StringUtils.isNotBlank(data)) {
                                    outList.set(i,data.replace(".0",""));
                                }
                                break;
                            case 1:
                                if (!RcConstants.BusinessTagerType.IDENTITY_CARD.message.equals(data)
                                        && !RcConstants.BusinessTagerType.BANK_CARD.message.equals(data)
                                        && !RcConstants.BusinessTagerType.ORDER_NO.message.equals(data)) {
                                    throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：类型");
                                }
                                blackListVo.setType(RcConstants.BusinessTagerType.getCodeByComment(data));
                                break;
                            case 2:
                                blackListVo.setValue(data);
                                break;
                            case 3:
                                blackListVo.setRiskTag(RcConstants.BlackListRiskTag.getCodeByComment(data));
                                break;
                            case 4:
                                blackListVo.setOperator(data);
                                break;
                        }
                    }
                    logger.printMessage("blackListVo:" + JSON.toJSONString(blackListVo));
                    add(blackListVo);
                    outList.add("导入成功");
                } catch (AppException e) {
                    failCount++;
                    outList.add(e.getErrorMsg());
                } catch (Exception e) {
                    logger.printMessage("CARDHOLDER_ERROR:" + e.getMessage());
                    logger.printLog(e);
                    failCount++;
                    outList.add(RcCode.SYSTEM_EXCEPTION.message);
                }
                allOutList.add(outList);
            }
        }
        if ("MERCHANT".equals(type)) {
            for(Integer rowNo : dataList.keySet()) {
                List<String> cellList = dataList.get(rowNo);
                List<String> outList = new ArrayList<>();
                cellList.forEach(data -> {
                    outList.add(data);
                });
                try {
                    BlackListVo blackListVo = new BlackListVo();
                    blackListVo.setType(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
                    for (int i = 0; i < cellList.size(); i++) {
                        String data = cellList.get(i);
                        switch (i) {
                            case 0:
                                blackListVo.setValue(data);
                                break;
                            case 1:
                                blackListVo.setName(data);
                                break;
                            case 2:
                                blackListVo.setChannel(RcConstants.BlackListChannel.getCodeByComment(data));
                                break;
                            case 3:
                                blackListVo.setOperator(data);
                                break;
                        }
                    }
                    logger.printMessage("blackListVo:" + JSON.toJSONString(blackListVo));
                    add(blackListVo);
                    outList.add("导入成功");
                } catch (AppException e) {
                    failCount++;
                    outList.add(e.getErrorMsg());
                } catch (Exception e) {
                    logger.printMessage("CARDHOLDER_ERROR:" + e.getMessage());
                    logger.printLog(e);
                    failCount++;
                    outList.add(RcCode.SYSTEM_EXCEPTION.message);
                }
                allOutList.add(outList);
            }
        }
        response.setFailCount(failCount);
        response.setTotalCount(allOutList.size());
        response.setSuccessCount(allOutList.size() - failCount);
        if (allOutList.size() == 0) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,"数据为空");
        }
        Long current = System.currentTimeMillis();
        String parentFilePath = "tmp/batch/批量处理结果_" + current + "/";
        File parentFile = new File(parentFilePath);
        if(!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String filePath = parentFilePath + fileName.split("\\.")[0] + current +".csv";
        File resultFile = new File(filePath);
        // 写入详情
        if (allOutList != null && allOutList.size() > 0) {
            RcCvsUtils.doExport(title + ",错误信息", allOutList, resultFile, null);
        }
        try {
            MultipartFile multipartFile = ExportWebsiteEffectivenessService.getMultipartFile(resultFile);
            String uploadToken = fsService.uploadToken("upload", "rc", "0", "上传导入结果文件");
            FileUploadResponse resp = fsService.uploadFile(multipartFile, uploadToken, "rc");
            if(!FileUploadResponse.SUCCESS.equals(resp.getResultCode())) {
                throw new AppException(resp.getResultCode(), resp.getResultMsg());
            }
            //文件路径
            Map<String,String> map = fsService.filePath(resp.getUniqueId(),30,100,"download");
            if(!FileUploadResponse.SUCCESS.equals(map.get("resultCode"))){
                throw new AppException(map.get("resultCode"), map.get("resultMsg"));
            }
            response.setResultFileUrl(map.get("filePath"));
        } catch (Exception e) {
            logger.printMessage("查黑名单库批量设置上传错误：" + e.getMessage());
            logger.printLog(e);
            throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：文件处理失败");
        }
        if (resultFile.exists()) {
            resultFile.delete();
        }
        if(parentFile.exists()) {
            parentFile.delete();
        }
        return response;
    }

    private void add(BlackListVo blackListVo) throws MalformedURLException {
        if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(blackListVo.getType())) {
            BlackList blackList = buildBlackList(blackListVo.getType(),blackListVo.getValue(),null,blackListVo.getRiskTag(),null,null,blackListVo.getOperator());
            blackListMapper.insert(blackList);
        } else if (RcConstants.BusinessTagerType.BANK_CARD.code.equals(blackListVo.getType())) {
            BlackList cardBlackList = buildBlackList(blackListVo.getType(),blackListVo.getValue(),blackListVo.getName(),blackListVo.getRiskTag(),blackListVo.getChannel(),null,blackListVo.getOperator());
            blackListMapper.insert(cardBlackList);
            // 获取身份证号
            String certNoEnc = otherMapper.queryCertNoEnc(cardBlackList.getValueEnc());
            if (StringUtils.isNotBlank(certNoEnc)) {
                String certNo = hessianService.symmetricDecryptData(certNoEnc);
                BlackList certBlackList = buildBlackList(RcConstants.BusinessTagerType.IDENTITY_CARD.code,certNo,null,blackListVo.getRiskTag(),null,blackListVo.getValue(),defaultOperator);
                blackListMapper.insert(certBlackList);
            }
        } else if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(blackListVo.getType())) {
            BlackList blackList = buildBlackList(RcConstants.BusinessTagerType.CUSTOMER_CODE.code,blackListVo.getValue(),blackListVo.getName(),null,blackListVo.getChannel(),null,blackListVo.getOperator());
            blackListMapper.insert(blackList);
        } else if (RcConstants.BusinessTagerType.ORDER_NO.code.equals(blackListVo.getType())) {
            BlackList orderBlackList = buildBlackList(RcConstants.BusinessTagerType.ORDER_NO.code,blackListVo.getValue(),null,blackListVo.getRiskTag(),null,null,blackListVo.getOperator());
            blackListMapper.insert(orderBlackList);

            String cardNoEnc = otherMapper.queryCardNoEnc(blackListVo.getValue());
            if (StringUtils.isNotBlank(cardNoEnc)) {
                String cardNo = hessianService.symmetricDecryptData(cardNoEnc);
                BlackList cardBlackList = buildBlackList(RcConstants.BusinessTagerType.BANK_CARD.code,cardNo,null,blackListVo.getRiskTag(),null,blackListVo.getValue(),defaultOperator);
                blackListMapper.insert(cardBlackList);

                String certNoEnc = otherMapper.queryCertNoEnc(cardNoEnc);
                if (StringUtils.isNotBlank(certNoEnc)) {
                    String certNo = hessianService.symmetricDecryptData(certNoEnc);
                    BlackList certBlackList = buildBlackList(RcConstants.BusinessTagerType.IDENTITY_CARD.code,certNo,null,blackListVo.getRiskTag(),null,cardNo,defaultOperator);
                    blackListMapper.insert(certBlackList);
                }
            }
        }
    }

    private BlackList buildBlackList(String type,String value,String name,String riskTag,String channel,String remark,String operator) throws MalformedURLException {
        BlackList blackList = new BlackList();
        blackList.setType(type);
        if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(type) || RcConstants.BusinessTagerType.BANK_CARD.code.equals(type)) {
            blackList.setValue(CardUtils.getHiddenBankCardNo(value));
            blackList.setValueEnc(hessianService.symmetricEncryptData(value));
            blackList.setValueHash(CardUtils.getCardNoHash(value));
        } else {
            blackList.setValue(value);
        }
        blackList.setRemark(remark);
        if (StringUtils.isNotBlank(remark) && RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(type)) {
            blackList.setRemark(CardUtils.getHiddenBankCardNo(remark));
            blackList.setRemarkEnc(hessianService.symmetricEncryptData(remark));
            blackList.setRemarkHash(CardUtils.getCardNoHash(remark));
        }
        blackList.setName(name);
        blackList.setRiskTag(riskTag);
        blackList.setChannel(channel);
        blackList.setOperator(operator);
        blackList.setCreateTime(new Date());
        blackList.setId(blackListMapper.querySeq());
        logger.printMessage("blackList:" + JSON.toJSONString(blackList));
        return blackList;
    }

    public BlackList queryByTypeAndValue(String type,String value) {
        logger.printMessage("TypeAndValue:" + type + "-" + (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(type) || RcConstants.BusinessTagerType.ORDER_NO.code.equals(type) ? value : CardUtils.getHiddenBankCardNo(value)));
        BlackList blackList = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(type)
                && "1".equals(otherMapper.selectParamValueByTypeAndName("RC_BLACK_LIST_FLAG","CUSTOMER_BLACK_LIST"))
                ? new BlackList() : blackListMapper.queryOneByTypeAndValue(type,value,CardUtils.getHash(value));
        return blackList;
    }
}

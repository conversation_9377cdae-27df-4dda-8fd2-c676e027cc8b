package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.rc.dao.VRcNoTxnMapper;
import com.epaylinks.efps.rc.domain.VRcNoTxn;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.service.impl.RcMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25 9:19
 */
@Service
public class NoTxnMonitor {
    private static final Logger log = LoggerFactory.getLogger(NoTxnMonitor.class);

    @Autowired
    private VRcNoTxnMapper vRcNoTxnMapper;

    @Autowired
    private RcMonitorService rcMonitorService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    private void handle(VRcNoTxn vRcNoTxn) {
        try {
            if (riskEventRuleService.isCustomerExclude(vRcNoTxn.getCustomerCode(), vRcNoTxn.getRuleCode())) {
                log.info("商户[{}]免疫规则[{}]", vRcNoTxn.getCustomerCode(), vRcNoTxn.getRuleCode());
                return;
            }
            log.info("商户[{}]{}天无交易，禁止入金", vRcNoTxn.getCustomerCode(), vRcNoTxn.getNoTxnDays());
            rcMonitorService.changeAccountStatus(vRcNoTxn.getCustomerCode(),
                    RcMonitorService.AccountStatus.BAN_PAY_IN,
                    "长期无交易商户进入休眠");
            riskEventRecordService.addEventRecord(vRcNoTxn.getRuleCode(),
                    vRcNoTxn.getCustomerCode(), vRcNoTxn.getCustomerName(),
                    Timex.ofDate(vRcNoTxn.getAccountLastUpdateTime()).to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
        } catch (Exception e) {
            log.error(vRcNoTxn.getCustomerCode() + "异常", e);
        }
    }


    public void monitor() {
        monitor(3000);
    }

    /**
     * @param n 每次循环处理条数
     */
    public void monitor(int n) {
        int i = 0;
        int count = vRcNoTxnMapper.count();
        log.info("本次处理[{}]个商户", count);
        List<VRcNoTxn> vRcNoTxnList;
        while (!(vRcNoTxnList = vRcNoTxnMapper.selectFirstN(n)).isEmpty()) {
            for (VRcNoTxn vRcNoTxn : vRcNoTxnList) {
                handle(vRcNoTxn);
                ++i;
            }
            if (i >= count) { //避免出现商户账户状态更新失败，导致死循环
                break;
            }
        }
    }
}

package com.epaylinks.efps.rc.service.rccalculate.out;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("User-Year-Max-OUT-Amount")
public class UserYearMaxOutAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        if (rcCalculteBasic.isCalcPersonUserOut(rcLimit, map.get("businessCode"), map)) {
            rcCalculteBasic.calculateValueOutAmount(rcLimit, map, RcCalculteBasic.RcCalcDateType.YEAR);
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        rcCalculteBasic.resetAmount(rcLimit, RcCalculteBasic.RcCalcDateType.YEAR);
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (rcCalculteBasic.isCalcPersonUserOut(rcLimit, rcCalculateRequest.getBusinessCode(), rcCalculateRequest.getIndexs())) {
            return rcCalculteBasic.calculateOutAmount(rcLimit, rcCalculateRequest, RcCalculteBasic.RcCalcDateType.YEAR);
        } else {
            return CommonOuterResponse.SUCCEE;
        }
    }


}

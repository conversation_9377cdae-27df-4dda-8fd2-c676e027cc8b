package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * 同卡单日最大入金
 */
@Service("INST_CARD_DAY_IN_AMOUNT")
public class InstCardDayInAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {
    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        //目前只做快捷支付，POS刷卡
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod()) ||
        rcCalculteBasic.isPosCardPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
            if(customerCode == null || bankCardNo == null ) {
                return CommonOuterResponse.SUCCEE;
            }
            //金额
            String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
            long amount = Long.parseLong(amountStr);
            long dayInAmount = Optional.ofNullable(rcCalculteBasic.redisGet(DefineCode.INST_CARD_DAY_IN_AMOUNT.defineCode + ":" + RcDateUtils.getCurrentDay() + ":" + bankCardNo)).orElse(0L) + amount;


            if (dayInAmount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
                //实际发生值已经大于限定值
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {
    }

    @Override
    public void reset(RcLimit rcLimit) {
    }



}

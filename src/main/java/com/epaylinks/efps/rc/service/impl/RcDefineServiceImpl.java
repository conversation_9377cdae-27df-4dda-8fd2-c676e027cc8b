package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.dao.RcDefineGroupMapper;
import com.epaylinks.efps.rc.dao.RcDefineMapper;
import com.epaylinks.efps.rc.domain.RcDefine;
import com.epaylinks.efps.rc.service.RcDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018.09.28
 */
@Service
public class RcDefineServiceImpl implements RcDefineService {

	@Autowired
	private RcDefineMapper rcDefineMapper;

	@Autowired
	private RcDefineGroupMapper rcDefineGroupMapper;


	@Override
	public void insertSelective(RcDefine rcDefine) {
		rcDefineMapper.insertSelective(rcDefine);
	}


	@Override
	public RcDefine selectByCode(String code) {
		return rcDefineMapper.selectByCode(code);
	}

	@Override
	public PageResult<Map<String,Object>> pageDefineQuery(String defineIdentifier, Long bigId, Long smallId, String startTime, String endTime, int pageNum, int pageSize) {
		PageResult<Map<String,Object>> pageResult = new PageResult<>();

		//总记录数
		int total = rcDefineMapper.totalDefineQuery(defineIdentifier,bigId,smallId,startTime,endTime);
		pageResult.setTotal(total);

		//当前页面
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;
		List<Map<String,Object>> list = rcDefineMapper.pageDefineQuery(defineIdentifier,bigId,smallId,startTime,endTime,startNum,endNum);
		//找到大类名称
		for (Map<String,Object> map : list){
			//根据小类名称找到大类名称
			String bigName = rcDefineGroupMapper.queryName(map.get("smallName").toString());
			map.put("bigName",bigName);
		}

		pageResult.setRows(list);

		return pageResult;
	}
}

package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("DEBIT_CARD_IN_DAY_AMOUNT")
public class DebitCardInDayAmount implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        String businessCode = map.get("businessCode");
        if (rcCalculteBasic.isDebitQuickPay(businessCode) ||
                rcCalculteBasic.isDebitPosPay(businessCode)) {
            rcCalculteBasic.calculateValueInAmount(rcLimit, map, RcCalculteBasic.RcCalcDateType.DAY);
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        rcCalculteBasic.resetAmount(rcLimit, RcCalculteBasic.RcCalcDateType.DAY);
    }

    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        if (rcCalculteBasic.isDebitQuickPay(rcCalculateRequest.getBusinessCode()) ||
                rcCalculteBasic.isDebitPosPay(rcCalculateRequest.getBusinessCode())) {
            return rcCalculteBasic.calculateInAmount(rcLimit, rcCalculateRequest, RcCalculteBasic.RcCalcDateType.DAY);
        }
        return CommonOuterResponse.SUCCEE;
    }
}

package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.controller.response.GamblingPyramidResponse;
import lombok.Data;

import java.net.MalformedURLException;
import java.util.List;
import java.util.Map;

public interface GamblingPyramidService {

    @Data
    public static class CheckExtraParam {
        public static enum Source {
            CUSTOMER_API("0", "商户API"),
            @Deprecated
            INNER("1", "内部使用"), // 废弃
            REGULAR("2","定期核验"),
            TRANS("3","交易中核验"),
            MANUAL("4","风控手动核验"),
            REPORT_CARD("5","报备转账卡时核验"),
            NETWORK_ACCESS("6","商户入网时核验");

            public final String code;
            public final String desc;

            Source(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        public static String trans(String code) {
            for (Source source : Source.values()) {
                if (source.code.equals(code)) {
                    return source.desc;
                }
            }
            return null;
        }

        private Source source = Source.INNER; //默认 内部使用

        private String transactionNo; //易票联流水号，非空时，用这个来记录RC_GAMBLING_PYRAMID_RECORD表reqSerialNo，不需要重新生成
    }
    /**
     * 统一风险核验
     * @param customerNo
     * @param transOrderNo
     * @param targetType 核验类型：001身份证号；002手机号；003社会统一信用代码；004银行账号；
     * @param targetValue
     * @param batchNo
     * @param userId
     * @return
     * @throws Exception
     */
    CommonOuterResponse commonRiskCheck(String customerNo, String transOrderNo, String targetType, String targetValue, String batchNo, Long userId) throws Exception;

    CommonOuterResponse commonRiskCheck(String customerNo, String transOrderNo, String targetType, String targetValue, String batchNo, Long userId, CheckExtraParam checkExtraParam) throws Exception;

    /**
     * 涉赌传销核验
     * @param cardAccount
     * @param batchNo
     * @param userId
     * @throws Exception
     */
    CommonOuterResponse gamblingPyramidCheck(String customerNo, String transOrderNo, String cardAccount, String batchNo, Long userId,CheckExtraParam checkExtraParam) throws Exception;

    /**
     * 分页查询
     * @param paramsMap
     * @param userId
     * @param display 是否展示完整数据 0不展示，1展示
     * @return
     * @throws MalformedURLException
     */
    PageResult<GamblingPyramidResponse> pageQuery(Map<String,Object> paramsMap, Long userId,String display,boolean download) throws MalformedURLException;

    List<GamblingPyramidResponse> exportPage(Map<String, Object> paramsMap,String display) throws MalformedURLException;

    /**
     * 单个核验
     * @param customerNo
     * @param cardAccount
     * @param userId
     * @return
     * @throws Exception
     */
    GamblingPyramidResponse singleCheck(String customerNo,String cardAccount,String source,Long userId) throws Exception;

    /**
     * 获取商户需要核验的业务
     * @param customerCode 商户号
     * @return 业务码列表，如无核验业务，返回空列表
     */
    List<String> getCheckBusinessCode(String customerCode);
}

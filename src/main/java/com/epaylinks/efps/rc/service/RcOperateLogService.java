package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.vo.OperateLogVo;

import java.util.List;
import java.util.Map;

/**
 * 操作日志服务类
 *
 * <AUTHOR>
 * @date 2020-06-09 15:22
 */
public interface RcOperateLogService {

    /**
     * 插入操作日志数据
     *
     * @param rcOperateLog 操作日志对象
     */
    void insert(RcOperateLog rcOperateLog);

    /**
     * 根据参数获取操作日志Vo对象列表
     *
     * @param params
     * @return
     */
    List<OperateLogVo> getOperateLogVoList(Map<String, String> params);

    List<OperateLogVo> getOperateLogVoList(List<OperateLogVo> operateLogVos);

    /**
     * 根据参数获取操作日志Vo对象总数
     *
     * @param params
     * @return
     */
    int totalOperateLog(Map<String, String> params);

}

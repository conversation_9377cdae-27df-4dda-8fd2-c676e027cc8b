package com.epaylinks.efps.rc.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;
import com.epaylinks.efps.rc.service.FsService;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.RcAuditRecordMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcAuditRecordService;
import com.epaylinks.efps.rc.vo.AuditBwListVo;
import com.epaylinks.efps.rc.vo.AuditStatusVo;
import com.epaylinks.efps.rc.vo.StatusAuditRecordPageVo;
import org.springframework.util.StringUtils;

@Service
public class RcAuditRecordServiceImpl implements RcAuditRecordService {

    @Autowired
    private RcAuditRecordMapper rcAuditRecordMapper;
    
    @Autowired
    private RcArchiveMapper rcArchiveMapper;
    
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private OtherService otherService;

    @Autowired
    private FsService fsService;

    @Override
    public RcAuditRecord selectByPrimaryId(Long id) {

        return rcAuditRecordMapper.selectByPrimaryKey(id);
    }
    
    
    @Override
    public RcAuditRecord queryAuditRecord(String targetType, Long targetId) {

        return rcAuditRecordMapper.queryByTargetTypeAndId(targetType, targetId);
    }

    @Override
    public List<Long> queryListByTargetTypeAndId(String targetType, Long targetId, Long id) {
        return rcAuditRecordMapper.queryListByTargetTypeAndId(targetType, targetId, id);
    }

    @Override
    public RcAuditRecord queryHistoryAuditRecord(String targetType, Long targetId, String auditTime,String field) {
        return rcAuditRecordMapper.queryHistoryAuditRecord(targetType, targetId,auditTime,field);
    }

    @Override
    public RcAuditRecord saveAuditRecord(String targetType, Long targetId, String oldValue, String newValue, String actionType,
            Long userId,String uniqued) {

        return saveAuditRecord(targetType, targetId, oldValue, newValue, actionType, null, userId,uniqued);
    }
    

    @Override
    public RcAuditRecord saveAuditRecord(String targetType, Long targetId, String oldValue, String newValue, String actionType,
            String reason, Long userId,String uniqued) {
        if (getByteLength(reason) > 300) {
            throw new AppException(RcCode.DATA_TOO_LONG.code,RcCode.DATA_TOO_LONG.message + "：变更原因");
        }
        if (getByteLength(oldValue) > 4000 || getByteLength(newValue) > 4000) {
            throw new AppException(RcCode.DATA_TOO_LONG.code,RcCode.DATA_TOO_LONG.message);
        }
        RcAuditRecord record = new RcAuditRecord();
        record.setId(sequenceService.nextValue("RC_AUDIT_RECORD"));
        record.setTargetType(targetType);
        record.setTargetId(targetId);
        record.setOldValue(oldValue);
        record.setNewValue(newValue);
        record.setCreateTime(new Date());
        record.setOperId(userId);
        record.setReason(reason);
        record.setActionType(actionType);
        record.setAuditResult(RcConstants.AuditStatus.WAITING.code);
        record.setUniqued(uniqued);

        rcAuditRecordMapper.insert(record);
        return record;
    }

    @Override
    public void updateAuditRecordStatus(Long auditId, Short auditResult, Long userId) {
        
        RcAuditRecord record = rcAuditRecordMapper.selectByPrimaryKey(auditId);
        if (record == null) {
            throw new AppException(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
        }

        record.setAuditResult(auditResult);
        record.setAuditOperId(userId);
        record.setAuditTime(new Date());
        rcAuditRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateAuditRecord(RcAuditRecord auditRecord) {
        return rcAuditRecordMapper.updateByPrimaryKey(auditRecord);
    }

    @Override
    public int updateByPrimaryKeySelective(RcAuditRecord auditRecord) {
        return rcAuditRecordMapper.updateByPrimaryKeySelective(auditRecord);
    }

    @Override
    public void updateAuditRecordStatus(String targetType, Long targetId, Short auditResult, Long userId) {
        
        updateAuditRecordStatus(targetType,  targetId, auditResult, userId, null);
    }
    
    @Override
    public void updateAuditRecordStatus(String targetType, Long targetId, Short auditResult, Long userId, String remarks) {
        
        RcAuditRecord record = rcAuditRecordMapper.queryByTargetTypeAndId(targetType, targetId);
        if (record == null) {
            throw new AppException(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
        }

        record.setAuditResult(auditResult);
        record.setAuditOperId(userId);
        record.setAuditTime(new Date());
        record.setRemarks(remarks);
        rcAuditRecordMapper.updateByPrimaryKey(record);
    }


    @Override
    public PageResult<StatusAuditRecordPageVo> pageQuery(Map<String, Object> paramMap,Long userId) {
        
        PageResult<StatusAuditRecordPageVo> page = new PageResult<StatusAuditRecordPageVo>();
        // 添加审核类型查看权限
        List<String> targetTypeList = new ArrayList<>();
        List<String> queryList = otherService.selectQueryAuthRC(userId);
        if (queryList != null && queryList.size() > 0) {
            if (queryList.contains("8080103")) {
                targetTypeList.add(RcConstants.AuditTargetType.RISK_LEVEL.code);// 风险等级
            }
            if (queryList.contains("8080105")) {
                targetTypeList.add(RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code); // 风控状态
            }
            if (queryList.contains("8080107")) {
                targetTypeList.add(RcConstants.AuditTargetType.ACCOUNT_STATUS.code);// 账户状态
            }
            if (queryList.contains("8080109")) {
                targetTypeList.add(RcConstants.AuditTargetType.RISK_CONTROL_FREEZING.code); // 风控冻结金额
            }
            if (queryList.contains("8080111")) {
                targetTypeList.add(RcConstants.AuditTargetType.WITHDRAWAL_REPORT.code); // 出金报备
            }
            if (queryList.contains("8080113")) {
                targetTypeList.add(RcConstants.AuditTargetType.AUTHENTICATION_FINANCING.code); // 鉴权理财
            }
            if (queryList.contains("8080115")) {
                targetTypeList.add(RcConstants.AuditTargetType.RECHARGE.code); // 充值设置
            }
            if (queryList.contains("8080117")) {
                targetTypeList.add(RcConstants.AuditTargetType.TRANSACTION.code); // 交易设置
            }
            if (queryList.contains("8080119")) {
                targetTypeList.add(RcConstants.AuditTargetType.PAYMENT_SETTING.code); // 代付设置
            }
            if (queryList.contains("8080121")) {
                targetTypeList.add(RcConstants.AuditTargetType.ORDER_TRANS_PAY_SETTING.code); // 订单转账支付设置
            }
        }
        paramMap.put("targetTypeList",targetTypeList);

        int count = rcAuditRecordMapper.countByParam(paramMap);
        page.setTotal(count);
        page.setCode(PageResult.SUCCEE);
        if (count < 1) {
            return page; 
        }
        List<RcAuditRecord> list = rcAuditRecordMapper.listByParam(paramMap);
        List<StatusAuditRecordPageVo> resultList = new ArrayList<StatusAuditRecordPageVo>();

        for (RcAuditRecord record: list) {
            StatusAuditRecordPageVo vo = new StatusAuditRecordPageVo();
            try {
                BeanUtils.copyProperties(vo, record);
            } catch (IllegalAccessException | InvocationTargetException e) {
                e.printStackTrace();
            }
            vo.setAuditType(record.getTargetType());
            vo.setOldRecord(JSONObject.parseObject(record.getOldValue(),Map.class));
            vo.setNewRecord(JSONObject.parseObject(record.getNewValue(),Map.class));
//            if (RcConstants.AuditTargetType.ACCOUNT_STATUS.code.equals(record.getTargetType())) {
//                vo.setOldRecord(JSONObject.parseObject(record.getOldValue(), AuditStatusVo.class));
//                vo.setNewRecord(JSONObject.parseObject(record.getNewValue(), AuditStatusVo.class));
                
                RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(record.getTargetId());
                vo.setCustomerCode(rcArchive != null ? rcArchive.getArchiveCode() : null);
                vo.setCustomerName(rcArchive != null ? rcArchive.getArchiveName() : null);
                if (vo.getAuditType().equals(RcConstants.AuditTargetType.TRANSACTION.code)) {
                    Map transMap = JSONObject.parseObject(record.getOldValue(),Map.class);
                    String customerNo = (String) transMap.get("customerNo");
                    Customer customer = otherService.queryCustomerByCustomerNo(customerNo);
                    if (customer != null) {
                        vo.setCustomerCode(customer.getCustomerNo());
                        vo.setCustomerName(customer.getName());
                    }
                }
               
                User user = otherService.selectUserById(record.getAuditOperId());
                vo.setAuditOperName(user != null ? user.getRealName() : null);
                User operUser = otherService.selectUserById(record.getOperId());
                vo.setOperName(operUser != null ? operUser.getRealName() : null);
//            }
            if (!StringUtils.isEmpty(vo.getUniqued())) {
                Map<String,String> map = fsService.filePath(vo.getUniqued(), 30, 100, "download");
                if(!FileUploadResponse.SUCCESS.equals(map.get("resultCode"))){
                    throw new AppException(RcCode.FILE_DOWNLOAD_FAIL.code + "-" + vo.getUniqued(), vo.getUniqued() + "-" + map.get("resultMsg"));
                }
                String fileName = map.get("fileName");
                String fileUrl = map.get("filePath");
                vo.setFileName(fileName);
                vo.setFileUrl(fileUrl);
            }
            resultList.add(vo);
        }
        page.setRows(resultList);
        return page;
    }

    @Override
    public List<RcAuditRecord> listByParam(Map<String, Object> paramMap) {
        return rcAuditRecordMapper.listByParam(paramMap);
    }

    @Override
    public RcAuditRecord queryByTargetTypeAndId(String type, Long id) {
        return rcAuditRecordMapper.queryByTargetTypeAndId(type,id);
    }

    private Integer getByteLength(String param) {
        try {
            if (null == param) {
                return 0;
            }
            return param.getBytes("GBk").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 999;
    }

}

package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.controller.response.GamblingPyramidResponse;
import com.epaylinks.efps.rc.dao.GamblingPyramidRecordMapper;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.GamblingPyramidRecord;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.domain.VerifyConfig;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.util.CardUtils;
import com.epaylinks.efps.rc.util.GetProtocol;
import com.epaylinks.efps.rc.vo.CheckEnterpriseRiskVo;
import com.epaylinks.efps.rc.vo.CustomerStateInfoVo;
import com.epaylinks.efps.rc.vo.UrlConnVo;
import com.epaylinks.efps.rc.vo.WebsiteCheckVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("gamblingPyramidServiceImpl")
public class GamblingPyramidServiceImpl implements GamblingPyramidService, BatchService {
    @Value("${defaultAuditUserId:3009001}")
    private Long defaultAuditUserId;

    @Autowired
    private LogService logService;

    @Autowired
    private BwListService bwListService;

    @Autowired
    private GamblingPyramidRecordMapper gamblingPyramidRecordMapper;

    @Autowired
    private CasService casService;

    @Autowired
    private OtherService otherService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private HessianService hessianService;

    @Autowired
    private CustService custService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private VerifyConfigService verifyConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RcArchiveMapper rcArchiveMapper;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        Long userId = Long.parseLong(extraData.get("userId") + "");
        String batchNo = extraData.get("batchNo") + "";
        Short type = (Short) extraData.get("type");
        Map<Integer, BatchDetail> resultMap = new HashMap<>();
        for(Integer rowNo : dataMap.keySet()) {
            try {
                CommonOuterResponse response = new CommonOuterResponse();
                List<String> cellList = dataMap.get(rowNo);

                if (30 == type.intValue()) {
                    String customerNo = cellList.get(1);
                    if (StringUtils.isEmpty(customerNo)) {
                        throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：商户编号不能为空");
                    }
                    // 重复校验
                    List<String> repeatList = redisTemplate.opsForValue().get("webCheck" + batchNo) == null ? new ArrayList<>() : (List<String>) redisTemplate.opsForValue().get("webCheck" + batchNo);
                    if (repeatList.contains(customerNo)) {
                        throw new AppException(RcCode.DATA_DUPLICATION.code,"重复数据[商户号：" + customerNo + "]");
                    } else {
                        repeatList.add(customerNo);
                        redisTemplate.opsForValue().set("webCheck" + batchNo,repeatList,10,TimeUnit.MINUTES);
                    }
                    CustomerStateInfoVo customerStateInfoVo = rcArchiveMapper.queryCustomerStateInfo(customerNo);
                    if (customerStateInfoVo == null) {
                        throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：商户编号不存在");
                    }
//                    if (customerStateInfoVo.getArchiveId() == null) {
//                        throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
//                    }
//                    if (RcConstants.RcCustomerStatus.CANCEL.code.equals(customerStateInfoVo.getCusState())) {
//                        throw new AppException(RcCode.CUSTOMER_HAS_CANCELED.code,RcCode.CUSTOMER_HAS_CANCELED.message);
//                    }
//                    if (!RcConstants.RcStatus.NORMAL.code.equals(customerStateInfoVo.getRcState())) {
//                        throw new AppException(RcCode.RC_CACLCUELATE_ERROR.code,"账户状态处于冻结");
//                    }
//                    Boolean isBlack = bwListService.isBlackList(RcConstants.RCTargetType.CUSTOMER_CODE.code,customerNo);
//                    if (isBlack) {
//                        throw new AppException(RcCode.BLACK_BWLIST_LIMIT.code,RcCode.BLACK_BWLIST_LIMIT.message);
//                    }
                    List<String> webValidList = redisTemplate.opsForValue().get("webValid" + batchNo) == null ? new ArrayList<>() : (List<String>) redisTemplate.opsForValue().get("webValid" + batchNo);
                    webValidList.add(customerNo);
                    redisTemplate.opsForValue().set("webValid" + batchNo,webValidList,30, TimeUnit.MINUTES);
                } else {
                    String cardAccount = cellList.get(1);
                    String customerNo = cellList.get(2);
                    if (StringUtils.isEmpty(cardAccount) || StringUtils.isEmpty(customerNo)) {
                        throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：银行卡号或商户编号不能为空");
                    }
                    Customer customer = custService.queryCustomerByCustomerNo(customerNo);
                    if (customer == null || StringUtils.isEmpty(customer.getCustomerNo())) {
                        throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：商户编号不存在");
                    }
                    // 卡号重复校验
                    Map<String, Object> paramsMap = new HashMap<>();
                    paramsMap.put("beginRowNo",1);
                    paramsMap.put("endRowNo",10);
                    paramsMap.put("batchNo",batchNo);
                    paramsMap.put("customerNo",customerNo);
                    paramsMap.put("cardAccountHash", CardUtils.getCardNoHash(cardAccount));
                    PageResult<GamblingPyramidResponse> result = pageQuery(paramsMap,userId,"0",false);
                    if (result != null && result.getTotal() > 0) {
                        throw new AppException(RcCode.DATA_DUPLICATION.code,RcCode.DATA_DUPLICATION.message);
                    }
                    // 涉赌涉传销核验
                    CheckExtraParam checkExtraParam = new CheckExtraParam();
                    checkExtraParam.setSource(CheckExtraParam.Source.MANUAL);
                    response = gamblingPyramidCheck(customerNo,null,cardAccount,batchNo,userId,checkExtraParam);
                }
                if ("0000".equals(response.getReturnCode())) {
                    resultMap.put(rowNo,buildDetail(rowNo,null, RcConstants.SuccessFail.SUCCESS.code,null,"导入成功"));
                } else {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, response.getReturnMsg()));
                }

            }  catch (Exception e) {
                logger.printMessage("涉赌传销核验error:" + e.getMessage());
                logger.printLog(e);
                if (e instanceof AppException) {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                } else {
                    logService.printLog(e);
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return resultMap;
    }

    private BatchDetail buildDetail( Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    @Override
    public CommonOuterResponse commonRiskCheck(String customerNo, String transOrderNo, String targetType, String targetValue, String batchNo, Long userId) throws Exception {
        return this.commonRiskCheck(customerNo, transOrderNo, targetType, targetValue, batchNo, userId, new CheckExtraParam());
    }

    @Override
    public CommonOuterResponse commonRiskCheck(String customerNo, String transOrderNo, String targetType, String targetValue, String batchNo, Long userId, CheckExtraParam checkExtraParam) throws Exception {
        BwList record = new BwList();
        String targetTypeName = RcConstants.BusinessTagerType.getCommentByCode(targetType);
        if (StringUtils.isEmpty(targetTypeName)){
            throw new AppException(RcCode.RC_ARG_ERROR.code,"核验类型有误");
        }
        targetTypeName = targetTypeName.replace("银行卡","银行卡号").replace("身份证","身份证号");
        String cardAccount = targetValue;
        // 已设置黑名单或有90天记录不送上游
        List<BwList> bwLists = bwListService.getByTagerTypeAndTagerId(targetType,cardAccount);
        if (bwLists != null &&  bwLists.size() > 0) {
            GamblingPyramidRecord gamblingPyramidRecord = buildBaseRecord(customerNo,transOrderNo,cardAccount,batchNo,userId, checkExtraParam);
            gamblingPyramidRecord.setCardAccountType(targetType);
            record = bwLists.get(0);
            if (StringUtils.isEmpty(record.getStatus())) {
                record.setStatus(Short.valueOf("1"));
            }
            logger.printMessage("黑白名单记录：" + JSON.toJSONString(record));
            if (record.getBwType().equals("0") && !(record.getStatus() == 0)) { // 黑名单、正常
                gamblingPyramidRecord.setJoinBlacklist("0");
                gamblingPyramidRecord.setVerificationResults("2"); // 未核验
                gamblingPyramidRecord.setSubmitUpstreamOrNot("0");
                gamblingPyramidRecord.setCheckCost("0");
                gamblingPyramidRecord.setRemarks(targetTypeName+"已在黑名单库");
                gamblingPyramidRecordMapper.insert(gamblingPyramidRecord);
                logger.printMessage("黑名单记录状态正常");
                return CommonOuterResponse.fail(RcCode.ADD_BWLIST_EXIS.code,targetTypeName+"已在黑名单库",gamblingPyramidRecord);
            } else if (record.getBwType().equals("0") && record.getStatus() == 0) { // 黑名单、待审核
                gamblingPyramidRecord.setJoinBlacklist("0");
                gamblingPyramidRecord.setVerificationResults("2"); // 未核验
                gamblingPyramidRecord.setSubmitUpstreamOrNot("0");
                gamblingPyramidRecord.setCheckCost("0");
                gamblingPyramidRecord.setRemarks(targetTypeName+"已在黑名单库");
                gamblingPyramidRecordMapper.insert(gamblingPyramidRecord);
                logger.printMessage("黑名单记录状态待审核");
                // 调用审核
                bwListService.auditBwList(record.getBwId(), new Short("1"), defaultAuditUserId,null);
                record.setBwType("0");
                bwListService.saveUpdateBwAuditRecord(record, userId);
                bwListService.auditBwList(record.getBwId(), new Short("1"), defaultAuditUserId,null);
                return CommonOuterResponse.fail(RcCode.ADD_BWLIST_EXIS.code,targetTypeName+"已在黑名单库",gamblingPyramidRecord);
            } else if (record.getBwType().equals("1")) { // 白名单、正常/待审核
                logger.printMessage("白名单记录状态正常/待审核");
                if (!RcConstants.BusinessTagerType.BANK_CARD.code.equals(targetType)) {
                    return callCas(record,customerNo,transOrderNo,cardAccount,targetType,1,batchNo,userId, checkExtraParam,null,null);
                }
                // 银行卡核验判断单/双通道
                VerifyConfig verifyConfig = verifyConfigService.queryByCustomerNoValid(customerNo);
                if (verifyConfig != null && (RcConstants.VerifyConfig.YS_XL.code.equals(verifyConfig.getFirstVerifyChannel()) ||
                        RcConstants.VerifyConfig.YS_LR.code.equals(verifyConfig.getFirstVerifyChannel()))) { // 双通道模式
                    // A通道
                    CommonOuterResponse response = new CommonOuterResponse();
                    String autoSwitchChannelNos = getABChannel("A",verifyConfig.getFirstVerifyChannel());
                    response = callCas(record,customerNo,transOrderNo,cardAccount,targetType,1,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"1");
                    // B通道
                    if (analysisResponse(response,checkExtraParam)) {
                        autoSwitchChannelNos = getABChannel("B",verifyConfig.getFirstVerifyChannel());
                        response = callCas(record,customerNo,transOrderNo,cardAccount,targetType,1,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"2");
                    }
                    return response;
                } else {
                    String autoSwitchChannelNos = verifyConfigService.queryVerifyChannel(verifyConfig,customerNo);
                    return callCas(record,customerNo,transOrderNo,cardAccount,targetType,1,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"1");
                }
//                return callCas(record,customerNo,transOrderNo,cardAccount,targetType,1,batchNo,userId, checkExtraParam);
            }
        } else { // 黑白名单无记录时
            logger.printMessage("黑白名单无记录");
            if (!RcConstants.BusinessTagerType.BANK_CARD.code.equals(targetType)) {
                return callCas(record,customerNo,transOrderNo,cardAccount,targetType,2,batchNo,userId, checkExtraParam,null,null);
            }
            VerifyConfig verifyConfig = verifyConfigService.queryByCustomerNoValid(customerNo);
            if (verifyConfig != null && (RcConstants.VerifyConfig.YS_XL.code.equals(verifyConfig.getFirstVerifyChannel()) ||
                    RcConstants.VerifyConfig.YS_LR.code.equals(verifyConfig.getFirstVerifyChannel()))) { // 双通道模式
                // A通道
                CommonOuterResponse response = new CommonOuterResponse();
                String autoSwitchChannelNos = getABChannel("A",verifyConfig.getFirstVerifyChannel());
                response = callCas(record,customerNo,transOrderNo,cardAccount,targetType,2,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"1");
                // B通道
                if (analysisResponse(response,checkExtraParam)) {
                    autoSwitchChannelNos = getABChannel("B",verifyConfig.getFirstVerifyChannel());
                    response = callCas(record,customerNo,transOrderNo,cardAccount,targetType,2,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"2");
                }
                return response;
            } else {
                String autoSwitchChannelNos = verifyConfigService.queryVerifyChannel(verifyConfig,customerNo);
                return callCas(record,customerNo,transOrderNo,cardAccount,targetType,2,batchNo,userId, checkExtraParam,autoSwitchChannelNos,"1");
            }
//            return callCas(record,customerNo,transOrderNo,cardAccount,targetType,2,batchNo,userId, checkExtraParam);
        }
        return CommonOuterResponse.success();
    }

    /**
     * 获取AB通道
     * @param firstVerifyChannel
     * @return
     */
    private String getABChannel(String channel,String firstVerifyChannel) {
        if ("A".equals(channel)) {
            if (RcConstants.VerifyConfig.YS_XL.code.equals(firstVerifyChannel)
                    || RcConstants.VerifyConfig.YS_LR.code.equals(firstVerifyChannel)) {
                return "8";
            }
        }
        if ("B".equals(channel)) {
            if (RcConstants.VerifyConfig.YS_XL.code.equals(firstVerifyChannel)) {
                return "7,10";
            }
            if (RcConstants.VerifyConfig.YS_LR.code.equals(firstVerifyChannel)) {
                return "5,10";
            }
        }
        return null;
    }

    /**
     * 分析response返回值，判断是否继续调用核验方法（用于双通道模式核验）
     * @param response
     * @return
     */
    private Boolean analysisResponse(CommonOuterResponse response,CheckExtraParam checkExtraParam) {
        GamblingPyramidRecord gamblingPyramidRecord = (GamblingPyramidRecord) response.getData();
        checkExtraParam.setTransactionNo(gamblingPyramidRecord.getReqSerialNo());
        if (RcCode.VERIFICATION_PERIOD.code.equals(response.getReturnCode()) || "0000".equals(response.getReturnCode())) {
            String gamblingState = gamblingPyramidRecord.getGamblingOrNot();
            String pyramidState = gamblingPyramidRecord.getPyramidOrNot();
            String fraudState = gamblingPyramidRecord.getFraudOrNot();
            String submitOrNot = gamblingPyramidRecord.getSubmitUpstreamOrNot();
            if ("1".equals(gamblingState) || "1".equals(pyramidState) || "1".equals(fraudState)) {
                return false;
            }
            if ("0".equals(submitOrNot)) { // 有缓存不调用第二次
                return false;
            }
        } else {
            return true;
        }
        return true;
    }

    @Override
    @Logable(businessTag = "GamblingPyramidServiceImpl.gamblingPyramidCheck")
    public CommonOuterResponse gamblingPyramidCheck(String customerNo,String transOrderNo,String cardAccount,String batchNo,Long userId,CheckExtraParam checkExtraParam) throws Exception {
        return commonRiskCheck(customerNo,transOrderNo,RcConstants.RCTargetType.BANK_CARD.code,cardAccount,batchNo,userId,checkExtraParam);
    }

    @Override
    public PageResult<GamblingPyramidResponse> pageQuery(Map<String, Object> paramsMap, Long userId,String display,boolean download) throws MalformedURLException {
        int count = gamblingPyramidRecordMapper.selectPageCount(paramsMap);
        if (download) {
            paramsMap.put("endRowNo",count);
        }
        List<GamblingPyramidResponse> responseList = gamblingPyramidRecordMapper.selectGamblingPyramidList(paramsMap);
        if (responseList != null && responseList.size() > 0) {
            Map<String,String> encrypt = new HashMap<>();
            for (int i = 0; i < responseList.size(); i++) {
                GamblingPyramidResponse gamblingPyramidResponse = responseList.get(i);
                // 显示卡号明文
                if ("1".equals(display)) {
                    encrypt.put(String.valueOf(gamblingPyramidResponse.getId()),gamblingPyramidResponse.getCardEncrypt());
//                    String cardAccount = hessianService.symmetricDecryptData(gamblingPyramidResponse.getCardEncrypt());
//                    gamblingPyramidResponse.setCardAccount(cardAccount);
                }
                // 获取操作人信息
                User operUser = otherService.selectUserById(gamblingPyramidResponse.getOperId());
                gamblingPyramidResponse.setOperName(operUser != null ? operUser.getRealName() : null);
                responseList.set(i,gamblingPyramidResponse);
                //转换风险标签
                String riskTag = gamblingPyramidResponse.getRiskTag();
                gamblingPyramidResponse.setRiskTagShow(transRiskTag(riskTag));
                //转换核验类型
                String checkTypeShow = gamblingPyramidResponse.getCheckType();
                gamblingPyramidResponse.setCheckTypeShow(transRiskTag(checkTypeShow));
                // 转换来源
                gamblingPyramidResponse.setSource(GamblingPyramidService.CheckExtraParam.trans(gamblingPyramidResponse.getSource()));
            }
            if ("1".equals(display)) {
                logger.printMessage("批量解密调用前：" + sdf.format(new Date()));
                Map<String,String> resultMap = hessianService.decryptDataMap(encrypt);
                logger.printMessage("批量解密调用后：" + sdf.format(new Date()));
                for (int i = 0; i < responseList.size(); i++) {
                    GamblingPyramidResponse gamblingPyramidResponse = responseList.get(i);
                    gamblingPyramidResponse.setCardAccount(resultMap.get(String.valueOf(gamblingPyramidResponse.getId())));
                    responseList.set(i,gamblingPyramidResponse);
                }
            }
        }

        PageResult<GamblingPyramidResponse> pageResult = new PageResult<>();
        pageResult.setTotal(count);
        pageResult.setRows(responseList);
        return pageResult;
    }

    public List<GamblingPyramidResponse> exportPage(Map<String, Object> paramsMap,String display) throws MalformedURLException {
        List<GamblingPyramidResponse> responseList = gamblingPyramidRecordMapper.selectGamblingPyramidList(paramsMap);
        if (responseList != null && responseList.size() > 0) {
            Map<String,String> encrypt = new HashMap<>();
            for (int i = 0; i < responseList.size(); i++) {
                GamblingPyramidResponse gamblingPyramidResponse = responseList.get(i);
                // 显示卡号明文
                if ("1".equals(display)) {
                    encrypt.put(String.valueOf(gamblingPyramidResponse.getId()),gamblingPyramidResponse.getCardEncrypt());
//                    String cardAccount = hessianService.symmetricDecryptData(gamblingPyramidResponse.getCardEncrypt());
//                    gamblingPyramidResponse.setCardAccount(cardAccount);
                }
                // 获取操作人信息
                User operUser = otherService.selectUserById(gamblingPyramidResponse.getOperId());
                gamblingPyramidResponse.setOperName(operUser != null ? operUser.getRealName() : null);
                responseList.set(i,gamblingPyramidResponse);
                //转换风险标签
                String riskTag = gamblingPyramidResponse.getRiskTag();
                gamblingPyramidResponse.setRiskTagShow(transRiskTag(riskTag));
                //转换核验类型
                String checkTypeShow = gamblingPyramidResponse.getCheckType();
                gamblingPyramidResponse.setCheckTypeShow(transRiskTag(checkTypeShow));
                // 转换来源
                gamblingPyramidResponse.setSource(GamblingPyramidService.CheckExtraParam.trans(gamblingPyramidResponse.getSource()));
            }
            if ("1".equals(display)) {
                logger.printMessage("批量解密调用前：" + sdf.format(new Date()));
                Map<String,String> resultMap = hessianService.decryptDataMap(encrypt);
                logger.printMessage("批量解密调用后：" + sdf.format(new Date()));
                for (int i = 0; i < responseList.size(); i++) {
                    GamblingPyramidResponse gamblingPyramidResponse = responseList.get(i);
                    gamblingPyramidResponse.setCardAccount(resultMap.get(String.valueOf(gamblingPyramidResponse.getId())));
                    responseList.set(i,gamblingPyramidResponse);
                }
            }
        }
        return responseList;
    }

    private String transRiskTag(String riskTag){
        if(StringUtils.isEmpty(riskTag)){
            return "无";
        }
        /*
        String showData = riskTag.replace("gambling","涉赌")
                .replace("pyramid","涉传销").replace("fraud","涉诈")
                .replace("cashout","疑似套现").replace("violation","疑似违规");
         */
        String showData =  RcConstants.RiskTag.trans(riskTag);
        return showData;
    }

    @Override
    public GamblingPyramidResponse singleCheck(String customerNo, String cardAccount,String source, Long userId) throws Exception {
        GamblingPyramidResponse result = new GamblingPyramidResponse();
        Customer customer = custService.queryCustomerByCustomerNo(customerNo);
        if (customer == null || customer.getCustomerNo() == null) {
            throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：商户编号不存在");
        }
        CheckExtraParam checkExtraParam = new CheckExtraParam();
        if (CheckExtraParam.Source.REPORT_CARD.code.equals(source)) {
            checkExtraParam.setSource(CheckExtraParam.Source.REPORT_CARD);
        } else {
            checkExtraParam.setSource(CheckExtraParam.Source.MANUAL);
        }
        CommonOuterResponse response = gamblingPyramidCheck(customerNo,null,cardAccount,null,userId,checkExtraParam);
        if (response.getData() != null) {
            GamblingPyramidRecord gamblingPyramidRecord = (GamblingPyramidRecord)response.getData();
            BeanUtils.copyProperties(gamblingPyramidRecord,result);
            result.setRiskTagShow(transRiskTag(gamblingPyramidRecord.getRiskTag()));
            result.setCardEncrypt(gamblingPyramidRecord.getCardAccountEncrypt());
            result.setRemarks(StringUtils.isEmpty(gamblingPyramidRecord.getRemarks())
                    ? gamblingPyramidRecord.getResponseCode() + " " + gamblingPyramidRecord.getResponseMsg() : gamblingPyramidRecord.getRemarks());
            result.setCustomerNo(customerNo);
            result.setCustomerName(customer.getName());
            result.setOperId(gamblingPyramidRecord.getUserId());
            result.setCreateTime(sdf.format(gamblingPyramidRecord.getCreateTime()));
        }
        return result;
    }

    @Override
    public List<String> getCheckBusinessCode(String customerCode) {
        List<String> codeList = new ArrayList<>();
        VerifyConfig verifyConfig = verifyConfigService.queryByCustomerNoValid(customerCode);
        if (verifyConfig == null) {
            return codeList;
        }
        codeList = Arrays.asList(verifyConfig.getCheckBusiness().split(","));
        return codeList;
    }

    @Logable(businessTag = "GamblingPyramidServiceImpl.callCas")
    private CommonOuterResponse callCas(BwList record,String customerNo,String transOrderNo,
                                        String cardAccount,String targetType,int state,
                                        String batchNo,Long userId, CheckExtraParam checkExtraParam,
                                        String autoSwitchChannelNos,String autoSwitch) throws Exception {
        User user = otherService.selectUserById(userId);
        if(user == null){
            throw new AppException(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
        }
        CommonOuterResponse response = new CommonOuterResponse<>();

        //创建核验记录
        GamblingPyramidRecord gamblingPyramidRecord = buildBaseRecord(customerNo,transOrderNo,cardAccount,batchNo,userId, checkExtraParam);

        //如果易票联流水号为空，生成易票联流水号
        String serialNum = Optional.ofNullable(gamblingPyramidRecord.getReqSerialNo()).orElse(generateSerialNum());
        gamblingPyramidRecord.setCardAccountType(targetType);

        gamblingPyramidRecord.setReqSerialNo(serialNum);
        gamblingPyramidRecord.setJoinBlacklist("0");
        gamblingPyramidRecord.setVerificationResults("0");
        gamblingPyramidRecord.setCheckCost("0");
        gamblingPyramidRecordMapper.insertSelective(gamblingPyramidRecord);
        // 检验网站连通性和获取标题
        if (RcConstants.BusinessTagerType.WEB.code.equals(targetType)) {
            gamblingPyramidRecord.setCheckType(RcConstants.RiskTag.VIOLATION.code);
            UrlConnVo urlConnVo = new UrlConnVo();
            try {
                if (!cardAccount.startsWith("http")) {
//                    cardAccount = "https://" + cardAccount;
                    cardAccount = GetProtocol.getUrl(cardAccount) != null ? GetProtocol.getUrl(cardAccount) : cardAccount;
                }
                urlConnVo = getTitleFromURLRedirect(cardAccount);
                gamblingPyramidRecord.setUrlConnVo(urlConnVo);
                if (HttpURLConnection.HTTP_OK != urlConnVo.getCode()) {
                    logger.printMessage("1无法访问-" + cardAccount + ":" + urlConnVo.getCode());
                    if (2847 == urlConnVo.getCode()) {
                        gamblingPyramidRecord.setResponseCode(RcCode.URL_TYPE_ERROR_OR_SIZE_EXCEEDING_LIMIT.code);
                        gamblingPyramidRecord.setResponseMsg(RcCode.URL_TYPE_ERROR_OR_SIZE_EXCEEDING_LIMIT.message);
                    } else {
                        gamblingPyramidRecord.setResponseCode(RcCode.WEBSITE_NOT_AVAILABLE.code);
                        gamblingPyramidRecord.setResponseMsg(RcCode.WEBSITE_NOT_AVAILABLE.message);
                    }
                    gamblingPyramidRecord.setVerificationResults("2");
                    gamblingPyramidRecord.setCheckCost("0");
                    gamblingPyramidRecordMapper.updateByPrimaryKey(gamblingPyramidRecord);
                    return CommonOuterResponse.success(gamblingPyramidRecord);
                }
            } catch (Exception e) {
                logger.printMessage("2无法访问-" + cardAccount + ":" + e.getMessage());
                gamblingPyramidRecord.setResponseCode(RcCode.WEBSITE_NOT_AVAILABLE.code);
                gamblingPyramidRecord.setResponseMsg(RcCode.WEBSITE_NOT_AVAILABLE.message);
                gamblingPyramidRecord.setVerificationResults("2");
                gamblingPyramidRecord.setCheckCost("0");
                gamblingPyramidRecord.setUrlConnVo(urlConnVo);
                gamblingPyramidRecordMapper.updateByPrimaryKey(gamblingPyramidRecord);
                return CommonOuterResponse.success(gamblingPyramidRecord);
            }
        }
        // 调用上游并解析更新
        try {
            logger.printMessage("开始调用CAS：" + sdf.format(new Date()));
            if(RcConstants.BusinessTagerType.BANK_CARD.code.equals(targetType)){
                response = casService.checkBankCardRisk(cardAccount,null,serialNum,autoSwitch,autoSwitchChannelNos);
            } else if(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)){
                response = casService.checkEnterpriseRisk("01",cardAccount,"rc_enter_risk",serialNum);
            } else if(RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)){
                response = casService.checkEnterpriseRisk("02",cardAccount,"rc_enter_risk",serialNum);
            } else if (RcConstants.BusinessTagerType.WEB.code.equals(targetType)) {
                response = casService.websiteCheck(cardAccount,null,serialNum);
            }
            logger.printMessage(targetType + "-" + cardAccount + ":" + JSON.toJSONString(response));
            logger.printMessage("结束调用CAS：" + sdf.format(new Date()));
        } catch (Exception e) {
            logger.printMessage("调用CAS异常："+e.getLocalizedMessage());
            logger.printLog(e);
            gamblingPyramidRecord.setRemarks("调用核验服务失败");
            gamblingPyramidRecordMapper.updateByPrimaryKey(gamblingPyramidRecord);
            throw new AppException(RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.code,RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.message + "：调用CAS失败");
        }
        List<String> riskTagList = new ArrayList<>();
        // 根据结果是否添加、更新为黑名单并审核通过
        if ("0000".equals(response.getReturnCode()) && response.getData() != null) {
            String gamblingState = null;
            String pyramidState = null;
            String fraudState = null;
            String cashoutState = null;
            String violationState = null;
            String channelName = null;
            String validDate = null;
            //银行卡，羽山、信联上游核验
            if(RcConstants.BusinessTagerType.BANK_CARD.code.equals(targetType)){
                Map dataMap = JSON.parseObject(JSON.toJSONString(response.getData()),Map.class);
                gamblingState = dataMap.get("gambling") == null ? null : String.valueOf(dataMap.get("gambling"));
                pyramidState = dataMap.get("pyramid") == null ? null : String.valueOf(dataMap.get("pyramid"));
                fraudState = dataMap.get("swindle") == null ? null : String.valueOf(dataMap.get("swindle"));
                channelName = transChannel(dataMap.get("channelNo"));
                gamblingPyramidRecord.setJoinBlacklist("1".equals(gamblingState) || "1".equals(pyramidState) || "1".equals(fraudState) ? "1" : "0");
                gamblingPyramidRecord.setPaySerialNo(String.valueOf(dataMap.get("channelOrderNo")));
                gamblingPyramidRecord.setCheckCost((String) dataMap.get("costs"));
                gamblingPyramidRecord.setSubmitUpstreamOrNot("0".equals(dataMap.get("costs")) ? "0" : "1"); // 根据成本判断是否提交上游
                gamblingPyramidRecord.setVerificationResults("0".equals(dataMap.get("costs")) ? "2" : "1"); // cas成功返回
                gamblingPyramidRecord.setRemarks(gamblingPyramidRecord.getSubmitUpstreamOrNot().equals("0") ? "银行卡号已存在有效的核验（" + dataMap.get("cacheDays") + "天）" : "");
                validDate = String.valueOf(dataMap.get("cacheDays"));
             //营业执照或身份证，银联商户上游核验
            }else if(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)
                    || RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)){
                CheckEnterpriseRiskVo data = JSON.parseObject(JSON.toJSONString(response.getData()),CheckEnterpriseRiskVo.class);
                if(org.apache.commons.lang3.StringUtils.isNotBlank(data.getHitInRiskTp())){
                    /*
                    if(data.getHitInRiskTp().contains("07")){   //疑似套现
                        cashoutState = data.getHitIn();
                    }
                    if(data.getHitInRiskTp().contains("08")){   //疑似违规
                        violationState = data.getHitIn();
                    }
                     */
                    List<String> riskTps = Arrays.asList(data.getHitInRiskTp().split(","));
                    if (riskTps.contains(RcConstants.RiskTag.GAMBLING.unionNum)) {
                        gamblingPyramidRecord.setGamblingOrNot(RcConstants.AnyRisk.YES.code);
                    }
                    String checkRemark = "";
                    for (String riskTp : riskTps) {
                        String code = RcConstants.RiskTag.getCode(riskTp);
                        riskTagList.add(code);
//                        checkRemark = org.apache.commons.lang3.StringUtils.isNotBlank(checkRemark) ? checkRemark + "," + RcConstants.RiskTag.trans(code) : checkRemark + RcConstants.RiskTag.trans(code);
                    }
                    gamblingPyramidRecord.setRemarks(checkRemark);
                }
                channelName = transChannel(data.getChannelNo());
                gamblingPyramidRecord.setJoinBlacklist(data.getHitIn());
                gamblingPyramidRecord.setCheckCost("0");
                gamblingPyramidRecord.setSubmitUpstreamOrNot("1"); // 根据成本判断是否提交上游
                gamblingPyramidRecord.setVerificationResults("1"); // cas成功返回
                gamblingPyramidRecord.setCheckType(RcConstants.RiskTag.CASHOUT.code + "," + RcConstants.RiskTag.VIOLATION.code);
            } else if (RcConstants.BusinessTagerType.WEB.code.equals(targetType)) { // 网站合规检测
                WebsiteCheckVo data = JSON.parseObject(JSON.toJSONString(response.getData()),WebsiteCheckVo.class);
                channelName = transChannel(data.getChannelNo());
                if ("2".equals(data.getState())) {
                    violationState = "1";
                }
                String costs = data.getCosts();
                List<String> hits = data.getHits();
                if (hits != null && !hits.isEmpty()) {
                    UrlConnVo urlConnVo = gamblingPyramidRecord.getUrlConnVo();
                    urlConnVo.setKeyword(String.join("、",hits));
                    gamblingPyramidRecord.setUrlConnVo(urlConnVo);
                }
                gamblingPyramidRecord.setJoinBlacklist("0");
                gamblingPyramidRecord.setCheckCost(costs);
                gamblingPyramidRecord.setSubmitUpstreamOrNot("0".equals(costs) ? "0" : "1");
                gamblingPyramidRecord.setVerificationResults("0".equals(costs) ? "2" : "1");
            }
            // 核验记录
            String riskTag = null;
            if("信联".equals(channelName) || "联润-涉赌涉传销B".equals(channelName)){
                gamblingPyramidRecord.setCheckType("gambling,pyramid");
                if("1".equals(gamblingState)){
                    riskTagList.add("gambling");
                }
                if("1".equals(pyramidState)){
                    riskTagList.add("pyramid");
                }
            }else if("羽山".equals(channelName) || "联润-涉赌涉欺诈G".equals(channelName)){
                gamblingPyramidRecord.setCheckType("gambling,fraud");
                if("1".equals(gamblingState)){
                    riskTagList.add("gambling");
                }
                if("1".equals(fraudState)){
                    riskTagList.add("fraud");
                }
            }/*else if("银联-商户黑名单".equals(channelName)){
                gamblingPyramidRecord.setCheckType("cashout,violation");
                if("1".equals(cashoutState)){
                    riskTagList.add("cashout");
                }
                if("1".equals(violationState)){
                    riskTagList.add("violation");
                }
            }*/ else if (RcConstants.UpstreamName.YL_HGJC.comment.equals(channelName)) {
                gamblingPyramidRecord.setCheckType(RcConstants.RiskTag.VIOLATION.code);
                if ("1".equals(violationState)) {
                    riskTagList.add(RcConstants.RiskTag.VIOLATION.code);
                }
            }
            riskTag = String.join(",",riskTagList);
            logger.printMessage("风险核验，核验后对应标签为："+riskTag);
            gamblingPyramidRecord.setChannelName(channelName);
            if(!StringUtils.isEmpty(riskTag)){
                gamblingPyramidRecord.setRiskTag(riskTag);
            }
            gamblingPyramidRecord.setGamblingOrNot(gamblingState);
            gamblingPyramidRecord.setPyramidOrNot(pyramidState);
            gamblingPyramidRecord.setFraudOrNot(fraudState);
            gamblingPyramidRecord.setCashoutOrNot(cashoutState);
            gamblingPyramidRecord.setViolationOrNot(violationState);
            gamblingPyramidRecord.setResponseCode(response.getReturnCode());
            gamblingPyramidRecord.setResponseMsg(response.getReturnMsg());
            if (gamblingPyramidRecord.getSubmitUpstreamOrNot().equals("0")) {
                gamblingPyramidRecord.setJoinBlacklist("0");
            }
            gamblingPyramidRecordMapper.updateByPrimaryKey(gamblingPyramidRecord);
            logger.printMessage(JSON.toJSONString(gamblingPyramidRecord));
            BwList bwList = new BwList();
            bwList.setBusinessTagerType(targetType);
            bwList.setBusinessTagerId(cardAccount);
            bwList.setUserId(userId);
            bwList.setBwType("0");
            bwList.setUserName(user.getName());
            if(!StringUtils.isEmpty(riskTag)){
                bwList.setRiskTag(riskTag);
            }
            List<String> remarkList = new ArrayList<>();
            boolean needSave = false;
            /*
            if ("1".equals(gamblingState) || "1".equals(pyramidState) || "1".equals(fraudState)) { // 存在涉赌、涉传销、涉诈
//                String audFlag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
                needSave = true;
                if ("1".equals(gamblingState)) {
                    remarkList.add("涉赌");
                }
                if ("1".equals(pyramidState)) {
                    remarkList.add("涉传销");
                }
                if ("1".equals(fraudState)) {
                    remarkList.add("涉诈");
                }
                bwList.setRemark("外部风险核验新增黑名单(" + String.join("、",remarkList) + ")");
            }else if ("1".equals(cashoutState) || "1".equals(violationState) ){
                needSave = true;
                if ("1".equals(cashoutState)) {
                    remarkList.add("疑似套现");
                }
                if ("1".equals(violationState)) {
                    remarkList.add("疑似违规");
                }
                bwList.setRemark("命中银联风险联防联控商户黑名单");
            }
             */
            if (!riskTagList.isEmpty()) {
                needSave = true;
                for (String code : riskTagList) {
                    remarkList.add(RcConstants.RiskTag.trans(code));
                }
                if (RcConstants.UpstreamName.YL_HMD.comment.equals(channelName)) {
//                    bwList.setRemark(String.join("、",remarkList));
                } else if (RcConstants.UpstreamName.XL.comment.equals(channelName) || RcConstants.UpstreamName.YS.comment.equals(channelName)
                        || RcConstants.UpstreamName.LR.comment.equals(channelName) || RcConstants.UpstreamName.LR_G.comment.equals(channelName)) {
                    bwList.setRemark("外部风险核验新增黑名单(" + String.join("、",remarkList) + ")");
                }
            }
            // 网站检测不修改黑白名单
            if (RcConstants.UpstreamName.YL_HGJC.comment.equals(channelName)) {
                needSave = false;
            }
            if(needSave){
                switch (state) {
                    case 1 : // 已有白名单数据
                        bwList.setBwId(record.getBwId());
                        if (record.getStatus() == 0) {
                            bwListService.auditBwList(record.getBwId(), new Short("1"), defaultAuditUserId,null);
                        }
                        bwListService.saveUpdateBwAuditRecord(bwList, userId);
                        bwListService.auditBwList(record.getBwId(), new Short("1"), defaultAuditUserId,null);
                        break;
                    case 2 : // 黑白名单无数据时，新增和审核
                        bwList.setCreateTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                        bwList.setBwId(sequenceService.nextValue("rc"));
                        bwListService.saveCreateBwAuditRecord(bwList, userId);
                        bwListService.auditBwList(bwList.getBwId(), new Short("1"), defaultAuditUserId,null);
                        break;
                }
                logger.printMessage(JSON.toJSONString(bwList));
            }
            logger.printMessage("风险核验，处理完毕");
            if (gamblingPyramidRecord.getSubmitUpstreamOrNot().equals("0")) {
                return CommonOuterResponse.fail(RcCode.VERIFICATION_PERIOD.code,"银行卡号已存在有效的核验（" + validDate + "天）",gamblingPyramidRecord);
            }
            return CommonOuterResponse.success(gamblingPyramidRecord);
        } else {
            if(RcConstants.BusinessTagerType.BANK_CARD.code.equals(targetType)){
                Map dataMap = JSON.parseObject(JSON.toJSONString(response.getData()),Map.class);
                if (dataMap != null) {
                    gamblingPyramidRecord.setPaySerialNo(dataMap.get("channelOrderNo") != null ?String.valueOf(dataMap.get("channelOrderNo")) : "");
                    gamblingPyramidRecord.setChannelName(transChannel(dataMap.get("channelNo")));
                }
                /*if("信联".equals(gamblingPyramidRecord.getChannelName())){
                    gamblingPyramidRecord.setCheckType("gambling,pyramid");
                }else if("羽山".equals(gamblingPyramidRecord.getChannelName())){
                    gamblingPyramidRecord.setCheckType("gambling,fraud");
                }*/
            }else if(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)
                    || RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)){
                //gamblingPyramidRecord.setCheckType("cashout,violation");
                gamblingPyramidRecord.setChannelName("银联-商户黑名单");
            } else if (RcConstants.BusinessTagerType.WEB.code.equals(targetType)) {
                gamblingPyramidRecord.setChannelName(RcConstants.UpstreamName.YL_HGJC.comment);
            }
            gamblingPyramidRecord.setJoinBlacklist("0");
            gamblingPyramidRecord.setVerificationResults("0");
            gamblingPyramidRecord.setCheckCost("0");
            gamblingPyramidRecord.setResponseCode(response.getReturnCode());
            gamblingPyramidRecord.setResponseMsg(response.getReturnMsg());
//            gamblingPyramidRecord.setReqSerialNo(String.valueOf(dataMap.get("sysOrderNo")));
            gamblingPyramidRecordMapper.updateByPrimaryKey(gamblingPyramidRecord);
            return CommonOuterResponse.fail(response.getReturnCode(),response.getReturnMsg(),gamblingPyramidRecord);
        }
    }

    private GamblingPyramidRecord buildBaseRecord(String customerNo,String transOrderNo,String cardAccount,String batchNo,Long userId, CheckExtraParam checkExtraParam) throws MalformedURLException {
        GamblingPyramidRecord gamblingPyramidRecord = new GamblingPyramidRecord();
        gamblingPyramidRecord.setId(gamblingPyramidRecordMapper.querySeqNext());
        gamblingPyramidRecord.setBatchNo(batchNo);
        gamblingPyramidRecord.setCardAccount(CardUtils.getHiddenBankCardNo(cardAccount));
        gamblingPyramidRecord.setCardAccountHash(CardUtils.getCardNoHash(cardAccount));
        gamblingPyramidRecord.setCardAccountEncrypt(hessianService.symmetricEncryptData(cardAccount));
        gamblingPyramidRecord.setUserId(userId);
//        gamblingPyramidRecord.setChannelName("信联");
        gamblingPyramidRecord.setCheckTime(new Date());
        gamblingPyramidRecord.setCreateTime(new Date());
        gamblingPyramidRecord.setUpdateTime(new Date());
        gamblingPyramidRecord.setCustomerNo(customerNo);
        gamblingPyramidRecord.setTransOrderNo(transOrderNo);

        gamblingPyramidRecord.setSource(checkExtraParam.getSource().code);
        if (checkExtraParam.getTransactionNo() != null) {
            gamblingPyramidRecord.setReqSerialNo(checkExtraParam.getTransactionNo());
        }
        return gamblingPyramidRecord;
    }

    /**
     * 是否超过90天
     * @return
     */
    private boolean judgeDay() {
        Integer count = gamblingPyramidRecordMapper.judgeCheckRecordTime();
        if (count > 0) { // 90天内不送上游
            return false;
        }
        return true;
    }

    /**
     * 生成易票联流水号
     * @return
     */
    private String generateSerialNum() {
        return format.format(new Date()) + getRandomString(8);
    }

    /**
     * 转换渠道名称
     * @param channel
     * @return
     */
    private String transChannel(Object channel) {
        if (channel == null) {
            return null;
        }
        String channelNo = String.valueOf(channel);
        String channelName = null;
        switch (channelNo) {
            case "5" :
                channelName = "联润-涉赌涉传销B";
                break;
            case "7" :
                channelName = "信联";
                break;
            case "8" :
                channelName = "羽山";
                break;
            case "9" :
                channelName = "银联-商户黑名单";
                break;
            case "10" :
                channelName = "联润-涉赌涉欺诈G";
                break;
            case "11" :
                channelName = "银联-网站合规检测";
                break;
        }
        return channelName;
    }

    private String getRandomString(int length) {
        String base = "abcdefghijklmnopqrstuvwxyzABCDENGHIJKLMNOPQRSTUVWXYZ";// abcdefghijklmnopqrstuvwxyz
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 校验网站连通性
     * @param url
     * @return
     */
    private Boolean checkUrlValid(String url) {
        boolean flag = false;
        try {
            URL u = new URL(url);
            HttpURLConnection huc = (HttpURLConnection) u.openConnection();
            huc.setRequestMethod("GET");
            huc.setConnectTimeout(5000); // 设置连接超时，单位为毫秒
            huc.connect();
            int responseCode = huc.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                flag = true;
            }
            huc.disconnect();
        } catch (Exception e) {
            System.out.println("连接失败，异常信息为：" + e.getMessage());
            logger.printMessage("连接失败，异常信息为：" + e.getMessage());
            logger.printLog(e);
        }
        return flag;
    }

    /**
     * 获取网站状态码和title
     * @param urlString
     * @return
     * @throws Exception
     */
    private UrlConnVo getTitleFromURL(String urlString) throws Exception {
        UrlConnVo urlConnVo = new UrlConnVo();
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setInstanceFollowRedirects(true);
        conn.addRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 4.2.1; M040 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36");
        urlConnVo.setCode(conn.getResponseCode());
        urlConnVo.setUrl(urlString);
        if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
            String charset = "UTF-8";
            Pattern pattern = Pattern.compile("charset=\\S*");
            Matcher matcher = pattern.matcher(conn.getContentType());
            if (matcher.find()) {
                charset = matcher.group().replace("charset=", "");
            }
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(),charset));
            String result = "";
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("<title>")) {
                    int startIndex = line.indexOf("<title>") + 7;
                    int endIndex = line.indexOf("</title>");
                    result = line.substring(startIndex, endIndex);
                }
            }
            reader.close();
            urlConnVo.setTitle(result);
        }
        return urlConnVo;
    }

    private UrlConnVo getTitleFromURLRedirect(String urlString) throws Exception {
        HttpURLConnection conn = null;
        UrlConnVo urlConnVo = new UrlConnVo();
        URL url = new URL(urlString);
        conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);
        conn.setInstanceFollowRedirects(true);
        conn.setRequestProperty("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36");
        //conn.addRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 4.2.1; M040 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36");
        if (urlString.startsWith("https:")) {
            ignoreCertificateValidation(conn);
        }
        for (int i = 0; i < 5; i++) {
            if (conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_TEMP || conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_PERM || conn.getResponseCode() == HttpURLConnection.HTTP_SEE_OTHER) {
                String redirectUrl = conn.getHeaderField("Location");
                URL redirect = new URL(redirectUrl);
                conn = (HttpURLConnection) redirect.openConnection();
                conn.setConnectTimeout(5000);
                conn.setReadTimeout(5000);
                conn.setInstanceFollowRedirects(false);
                conn.setRequestProperty("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36");
                //conn.addRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 4.2.1; M040 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36");
                if (redirectUrl.startsWith("https:")) {
                    ignoreCertificateValidation(conn);
                }
            } else {
                break;
            }
        }
        urlConnVo.setCode(conn.getResponseCode());
        urlConnVo.setUrl(urlString);
        if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
            String charset = "UTF-8";
            Pattern pattern = Pattern.compile("charset=\\S*");
            Matcher matcher = pattern.matcher(conn.getContentType());
            if (matcher.find()) {
                charset = matcher.group().replace("charset=", "")
                        .replace(";","")
                ;
            }
            String contentType = conn.getContentType();
            Long size = conn.getContentLengthLong();
            logger.printMessage(urlString + "-contentType:" + contentType + "-size:" + size);
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), charset));
            String result = "";
            if (org.apache.commons.lang3.StringUtils.isNotBlank(contentType)
                    && contentType.contains("text/html")
                    && 100 * 1024 > size) {
                String inputLine;
                StringBuffer response = new StringBuffer();
                while ((inputLine = reader.readLine()) != null) {
                    response.append(inputLine);
                }
                int startIndex = response.indexOf("<title>") + 7;
                int endIndex = response.indexOf("</title>");
                if (startIndex != -1 && endIndex != -1) {
                    result = response.substring(startIndex, endIndex);
                }
            } else {
                urlConnVo.setCode(2847);
            }
            reader.close();
            conn.disconnect();
            urlConnVo.setTitle(result);
        }
        return urlConnVo;
    }

    /**
     * https忽略证书校验
     * @param conn
     * @throws Exception
     */
    public static void ignoreCertificateValidation(HttpURLConnection conn) throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
        };
        // Install the all-trusting trust manager
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        sc.getClientSessionContext().setSessionTimeout(5);
        sc.getClientSessionContext().setSessionCacheSize(1000);
        HostnameVerifier verifier = new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                //return false;
                return true;
            }
        };
        HttpsURLConnection httpsURLConnection = (HttpsURLConnection)conn;
        httpsURLConnection.setSSLSocketFactory(sc.getSocketFactory());
        httpsURLConnection.setHostnameVerifier(verifier);
    }

    public static void main(String[] args) throws Exception {
        GamblingPyramidServiceImpl gamblingPyramidService = new GamblingPyramidServiceImpl();
//        String serialNum = gamblingPyramidService.generateSerialNum();
//        System.out.println("epl流水号：" + serialNum);
        final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println("开始：" + dateFormat.format(new Date()));
        String url = "https://dev-efps.epaylinks.cn/admin/#/login"; // http://www.zhibohun888.com/ 需要设置true
        url = GetProtocol.getUrl(url) != null ? GetProtocol.getUrl(url) : url;
//        url = GetProtocol.getUrl(url) != null ? GetProtocol.getUrl(url) : url;
        System.out.println("url:" + url);
        //url = "https://" + url;
//        for (int i = 0;i < 100;i++) {
            System.out.println(gamblingPyramidService.getTitleFromURLRedirect(url));
//        }
        System.out.println("结束：" + dateFormat.format(new Date()));
    }

}

package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.dataimport.feign.CustService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.dataimport.model.BatchTask;
import com.epaylinks.efps.common.dataimport.util.ExcelUtils;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.ComplainTradeMapper;
import com.epaylinks.efps.rc.service.ComplainTradeAliService;
import com.epaylinks.efps.rc.service.ComplainTradeImportService;
import com.epaylinks.efps.rc.service.ComplainTradeWechatService;
import com.epaylinks.efps.rc.service.RcRedisService;
import com.epaylinks.efps.rc.vo.ComplainImportDataVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Service
public class ComplainTradeImportServiceImpl implements ComplainTradeImportService {

    private static final String SUFFIX_2003 = ".xls";    //03版本EXCEL后缀
    private static final String SUFFIX_2007 = ".xlsx";    //07版本EXCEL后缀
    private static final String BATCH_NO_REDIS_PREFIX = "RC:COMPLAIN:BATCH_NO:";     //缓存的批次号前缀
    private static final DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

    @Autowired
    private CommonLogger logger;

    @Autowired
    private CustService custService;

    @Autowired
    private RcRedisService redisService;

    @Autowired
    private ComplainTradeWechatService complainTradeWechatService;

    @Autowired
    private ComplainTradeAliService complainTradeAliService;

    @Autowired
    private ComplainTradeMapper complainTradeMapper;

    @Override
    public CommonResponse importByFile(MultipartFile file, Long userId) {
        //校验上传文件
        if(Objects.isNull(file)) {
            throw new AppException(RcCode.PARAM_ERROR.code, "获取上传文件为空");
        }
        String fileName = file.getOriginalFilename();
        if(StringUtils.isBlank(fileName)){
            throw new AppException(RcCode.PARAM_ERROR.code, "获取上传文件名称为空");
        }
        if(!ExcelUtils.isExcel(fileName) && !fileName.endsWith(".csv")){
            throw new AppException(RcCode.PARAM_ERROR.code, "文件格式不正确,只支持EXCEL文件");
        }
        //获取输入文件流
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            logger.printMessage("投诉交易记录批量导入，获取输入流错误："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }
        //根据文件类型，调用不同读取数据方法
        ComplainImportDataVo importDataVo = null;
        if(fileName.endsWith(".csv")){
            importDataVo = importByCsv(inputStream,fileName);
        }else {
            importDataVo = importByExcel(inputStream,fileName);
        }
        if(Objects.isNull(importDataVo)) {
            throw new AppException(RcCode.PARAM_ERROR.code, "获取上传文件数据失败");
        }
        importDataVo.setUserId(userId);
        //生成批次号
        Date now = new Date();
        String dateFormat = sdf.format(now);
        String redisKey = BATCH_NO_REDIS_PREFIX+dateFormat;
        String batchNo = null;
        int i = 1;
        String num = String.valueOf(i);
        batchNo = makeBatchNo(i,dateFormat);
        //缓存和数据库都没有的批次号，才算生成成功
        while(!redisService.setIfAbsent(redisKey+num,num,10, TimeUnit.MINUTES) || Objects.nonNull(custService.selectByBatchNo(batchNo))){
            i++;
            num = String.valueOf(i);
            batchNo =  makeBatchNo(i,dateFormat);
        }
        if(StringUtils.isBlank(batchNo)){
            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, "自动生成批次号失败");
        }
        //保存导入任务记录，类型23
        custService.insertTask(batchNo,fileName,(short)23,null,userId);
        importDataVo.setBatchNo(batchNo);
        importDataVo.setFileName(fileName);
        //另开线程处理导入任务
        final ComplainImportDataVo finalImportDataVo = importDataVo;
        ExecutorService es = Executors.newSingleThreadExecutor();
        es.submit(() -> {
            //统计总数、成功、失败
            int totalCount = 0;
            int successCount = 0;
            int failCount = 0;
            Map<Integer, BatchDetail> result = null;
            try {
                logger.printMessage("开始进行批量导入投诉交易记录处理");
                //按文件类型进行数据导入
                if (RcConstants.ComplainImportType.ALI_RISK_GO.code.equals(finalImportDataVo.getImportFileType())) {
                    result = complainTradeAliService.importRiskGoData(finalImportDataVo);
                } else if (RcConstants.ComplainImportType.WECHAT_RISK_STORE.code.equals(finalImportDataVo.getImportFileType())) {
                    result = complainTradeWechatService.importWechatRiskStoreData(finalImportDataVo);
                } else if (RcConstants.ComplainImportType.WECHAT_COMPLAIN.code.equals(finalImportDataVo.getImportFileType())) {
                    result = complainTradeWechatService.importWechatComplaindata(finalImportDataVo);
                }
                logger.printMessage("投诉交易记录保存数据库完毕");
                if (Objects.nonNull(result)) {
                    //遍历处理结果，逐条插入处理明细
                    totalCount = finalImportDataVo.getDataMap().size();
                    BatchTask bt = custService.selectByBatchNo(finalImportDataVo.getBatchNo());
                    for (Integer key : result.keySet()) {
                        int rowNum = key + 1; // key从0开始，对应excel中为key+1行
                        BatchDetail detail = result.get(key);
                        if ("0".equals(String.valueOf(detail.getStatus()))) {
                            insertDetail(finalImportDataVo.getBatchNo(), (long) rowNum, detail.getRowName(), (short) 0, detail.getRelateId(), detail.getRemarks(), null,bt.getTaskId());
                            successCount++;
                        } else {
                            insertDetail(finalImportDataVo.getBatchNo(), (long) rowNum, detail.getRowName(), (short) 1, detail.getRelateId(), detail.getRemarks(), null,bt.getTaskId());
                            failCount++;
                        }
                    }
                }
                // 更新批量处理任务, 就算所有处理失败，也更新为finish，再次批量进件的批次号需要用新的
                custService.updateTask(finalImportDataVo.getBatchNo(), (short) 3, (long) totalCount, (long) successCount, (long) failCount, "处理完成", userId);
                logger.printMessage("批量导入投诉交易记录任务处理完毕");
            }catch (Exception e){
                logger.printMessage("投诉交易记录批量导入，具体导入处理错误："+e.getLocalizedMessage());
                logger.printLog(e);
                custService.updateTask(finalImportDataVo.getBatchNo(), (short) 1, (long) totalCount, 0L, 0L, RcCode.SYSTEM_EXCEPTION.message, userId);
            }
        });
        CommonResponse response = new CommonResponse();
        response.setCode(CommonResponse.SUCCEE);
        response.setErrorMsg("文件已导入，请在导入文件管理中查看处理结果");
        return response;
    }

    /**
     * 添加导入明细
     * @param batchNo
     * @param rowNo
     * @param rowName
     * @param status
     * @param relateId
     * @param remarks
     * @param sourceData
     * @param taskId
     * @return
     */
    private int insertDetail(String batchNo, Long rowNo, String rowName, short status, String relateId, String remarks, String sourceData,Long taskId) {
        BatchDetail detail = new BatchDetail();
        detail.setDetailId(complainTradeMapper.selectBatchDetailId());
        detail.setTaskId(taskId);
        detail.setRowNo(rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRemarks(remarks);
        detail.setCreateTime(new Date());
        detail.setRelateId(relateId);
        detail.setSourceData(sourceData);
        return complainTradeMapper.insertBatchDetail(detail);
    }

    /**
     * 获取批次号
     * @param i
     * @param sdf
     * @return
     */
    private String makeBatchNo(int i,String sdf){
        String num = String.valueOf(i);
        String index = num.length()<2 ? ("0"+num) : num;
        return sdf + index;
    }

    /**
     * 转换列表为字符串
     * @param list
     * @return
     */
    private String transSourceData(List<String> list) {
        if(list == null || list.isEmpty()) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        list.forEach(data ->{
            sb.append("," + data);
        });
        return sb.toString().substring(1) ;
    }

    /**
     * 读取excel导入数据
     * @param inputStream
     * @return
     */
    private ComplainImportDataVo importByExcel(InputStream inputStream,String fileName){
        Workbook workbook;
        try {
            if (fileName.endsWith(SUFFIX_2003)) {// 03版
                workbook = new HSSFWorkbook(inputStream);
            } else {// 07版
                workbook = new XSSFWorkbook(inputStream);
            }
        } catch (Exception e) {
            logger.printMessage("投诉交易记录批量导入，加载Excel错误："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, "读取Excel文件错误");
        }
        ComplainImportDataVo dataVo = new ComplainImportDataVo();
        try {
            //获取sheet
            Sheet sheet = workbook.getSheetAt(0);
            if(Objects.isNull(sheet)){
                throw new AppException(RcCode.PARAM_ERROR.code, "获取Excel数据页失败");
            }
            //获取标题行内容
            Row firstRow = sheet.getRow(0);
            if(Objects.isNull(firstRow)){
                throw new AppException(RcCode.PARAM_ERROR.code, "获取Excel标题行失败");
            }
            int columnNum = firstRow.getLastCellNum();
            List<String> titleList = new LinkedList<>();
            int nullCount = 0;
            for (int j = 0; j < columnNum; j++) {
                if (Objects.isNull(firstRow.getCell(j))
                        || "".equals(ExcelUtils.getCellValueToString(firstRow.getCell(j)).trim())) {
                    titleList.add("");
                    nullCount++;
                    continue;
                }
                titleList.add(ExcelUtils.getCellValueToString(firstRow.getCell(j)).trim().replace("\n", "").replace("\r", ""));
            }
            if(String.valueOf(columnNum).equals(nullCount)){
                throw new AppException(RcCode.PARAM_ERROR.code, "获取Excel标题数据为空");
            }
            logger.printMessage("读取到标题行数据："+transSourceData(titleList));
            //目前excel格式只有微信投诉一种，简单校验标题格式
            if(titleList.get(0).indexOf("渠道号")<0 || titleList.get(2).indexOf("商户识别码")<0 || titleList.get(3).indexOf("微信支付订单号")<0 || titleList.get(4).indexOf("投诉单号")<0){
                throw new AppException(RcCode.PARAM_ERROR.code, "无法识别导入文件类型，Excel文件数据格式有误");
            }
            dataVo.setTitleList(titleList);
            //获取数据行内容
            Map<Integer, List<String>> dataMap = ExcelUtils.read(workbook, 0,  1, titleList.size()); // 更新列数以标题行为准
            if(Objects.isNull(dataMap)){
                throw new AppException(RcCode.PARAM_ERROR.code, "获取Excel导入数据为空");
            }
            dataVo.setDataMap(dataMap);
            logger.printMessage("获取Excel导入数据条数："+dataMap.size());
            //目前excel格式只有微信投诉一种，设置文件类型
            dataVo.setImportFileType(RcConstants.ComplainImportType.WECHAT_COMPLAIN.code);
        } catch (AppException e) {
            logger.printMessage("投诉交易记录批量导入，读取Excel数据错误："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("投诉交易记录批量导入，读取Excel数据异常："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, "读取Excel数据错误");
        } finally {
            if(Objects.nonNull(workbook)){
                try {
                    workbook.close();
                }catch (Exception e){
                    logger.printMessage("投诉交易记录批量导入，关闭workbook异常："+e.getLocalizedMessage());
                    logger.printLog(e);
                }
            }
        }
        return dataVo;
    }

    /**
     * 读取csv导入数据
     * @param inputStream
     * @return
     */
    private ComplainImportDataVo importByCsv(InputStream inputStream,String fileName){
        BufferedReader bufferedReader = null;
        ComplainImportDataVo dataVo = new ComplainImportDataVo();
        try {
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream,"gb2312"));
            String firstLine = null;
            String line = null;
            firstLine = bufferedReader.readLine();
            if(StringUtils.isBlank(firstLine)){
                throw new AppException(RcCode.PARAM_ERROR.code, "获取csv标题行数据为空");
            }
            logger.printMessage("读取csv文件首行内容："+firstLine);
            List<String> titleList = new LinkedList<>();
            //判断是否支付宝风险交易
            if(firstLine.indexOf("RiskGo风险交易明细记录")>-1){
                for(int i=0;i<4;i++){
                    //支付宝风险交易四次依次读取信息：账号、时间、明细表、标题
                    firstLine = bufferedReader.readLine();
                    if(StringUtils.isBlank(firstLine)){
                        throw new AppException(RcCode.PARAM_ERROR.code, "获取支付宝csv标题行信息为空");
                    }
                }
                /*if(StringUtils.isBlank(firstLine)){
                    throw new AppException(RcCode.PARAM_ERROR.code, "获取支付宝csv标题行数据为空");
                }*/
                if(!firstLine.startsWith("全部类型,风险描述,风险交易号,风险识别时间")){
                    throw new AppException(RcCode.PARAM_ERROR.code, "获取支付宝csv标题行数据格式有误");
                }
                //支付宝风险交易，设置文件类型
                dataVo.setImportFileType(RcConstants.ComplainImportType.ALI_RISK_GO.code);
            }else {
                //微信风险商户
                if(firstLine.indexOf("商户号")<0 || firstLine.indexOf("渠道商号")<0 || firstLine.indexOf("处理方法")<0 || firstLine.indexOf("风险类型")<0){
                    throw new AppException(RcCode.PARAM_ERROR.code, "无法识别导入文件类型，csv文件数据格式有误");
                }
                //微信风险商户，设置文件类型
                dataVo.setImportFileType(RcConstants.ComplainImportType.WECHAT_RISK_STORE.code);
            }
            //读取标题数据
            String[] tempStrArr = firstLine.split(",");
            for(String str : tempStrArr){
                if(StringUtils.isBlank(str)){
                    titleList.add("");
                }else {
                    titleList.add(str.trim().replaceAll("\"",""));
                }
            }
            dataVo.setTitleList(titleList);
            logger.printMessage("读取到标题行数据："+transSourceData(titleList));
            //读取支付宝风险交易明细数据
            Map<Integer, List<String>> dataMap = new LinkedHashMap<Integer, List<String>>();
            if(RcConstants.ComplainImportType.ALI_RISK_GO.code.equals(dataVo.getImportFileType())){
                //支付宝风险交易明细数据从第5行开始
                int index = 4;
                while((line = bufferedReader.readLine()) != null){
                    //如果数据行为空，跳过
                    if(StringUtils.isBlank(line) ||
                            StringUtils.isBlank(line.trim().replace("\r","").replace("\n","").replace("\t",""))){
                        continue;
                    }
                    if(line.contains("笔记录") || line.contains("导出时间")){
                        continue;
                    }
                    List<String> cellList = new LinkedList<>();
                    String[] tempdataArr = line.split(",");
                    for(String str : tempdataArr){
                        if(StringUtils.isBlank(str)){
                            cellList.add("");
                        }else {
                            cellList.add(str.trim());
                        }
                    }
                    index++;
                    dataMap.put(index,cellList);
                }
            //读取微信风险商户数据
            }else {
                //微信风险商户数据从第2行开始
                int index = 1;
                while((line = bufferedReader.readLine()) != null){
                    //如果数据行为空，跳过
                    if(StringUtils.isBlank(line) ||
                            StringUtils.isBlank(line.trim().replace("\r","").replace("\n","").replace("\t",""))){
                        continue;
                    }
                    List<String> cellList = new LinkedList<>();
                    String[] tempdataArr = line.split("\",");
                    for(String str : tempdataArr){
                        if(StringUtils.isBlank(str)){
                            cellList.add("");
                        }else {
                            cellList.add(str.trim().replaceAll("\"",""));
                        }
                    }
                    dataMap.put(index,cellList);
                    index++;
                }
            }
            dataVo.setDataMap(dataMap);
            logger.printMessage("获取Csv导入数据条数："+dataMap.size());
        } catch (AppException e) {
            logger.printMessage("投诉交易记录批量导入，读取csv数据错误："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("投诉交易记录批量导入，加载csv数据异常："+e.getLocalizedMessage());
            logger.printLog(e);
            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, "读取csv文件错误");
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    logger.printMessage("投诉交易记录批量导入，关闭bufferedReader异常："+e.getLocalizedMessage());
                    logger.printLog(e);
                }
            }
        }
        return dataVo;
    }
}

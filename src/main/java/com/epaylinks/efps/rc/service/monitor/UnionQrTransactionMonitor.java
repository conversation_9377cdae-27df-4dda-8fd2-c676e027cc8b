package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.CommonSysParamMapper;
import com.epaylinks.efps.rc.dao.RcCloseBusinessMapper;
import com.epaylinks.efps.rc.domain.CommonSysParam;
import com.epaylinks.efps.rc.domain.RcCloseBusiness;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.service.CustService;
import com.epaylinks.efps.rc.service.RcRedisService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 14:06
 */
@Service
public class UnionQrTransactionMonitor implements TransactionMonitor {
    private static final Logger log = LoggerFactory.getLogger(UnionQrTransactionMonitor.class);

    @Autowired
    RcRedisService rcRedisService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    @Autowired
    private CommonSysParamMapper commonSysParamMapper;

    @Autowired
    private RcCloseBusinessMapper rcCloseBusinessMapper;

    @Autowired
    private CustService custService;

    private static final String PARAM_OWNER = "RC_QR_PC";

    public void addQrMonitorPlatCustomer(String customerCode) {
        CommonSysParam commonSysParam = commonSysParamMapper.selectOneByOwnerAndKey(PARAM_OWNER, customerCode);
        if (commonSysParam == null) {
            commonSysParam = new CommonSysParam();
            commonSysParam.setOwner(PARAM_OWNER);
            commonSysParam.setKey(customerCode);
            commonSysParamMapper.insert(commonSysParam);
        }
    }

    public void delQrMonitorPlatCustomer(String customerCode) {
        CommonSysParam commonSysParam = commonSysParamMapper.selectOneByOwnerAndKey(PARAM_OWNER, customerCode);
        if (commonSysParam != null) {
            commonSysParamMapper.deleteByPrimaryKey(commonSysParam.getId());
        }
    }

    private Set<String> getQrMonitorPlatCustomers() {
        return commonSysParamMapper.selectByOwner(PARAM_OWNER).stream().map(CommonSysParam::getKey).collect(Collectors.toSet());
    }

    private boolean isQrMonitorCustomer(CustomerInfo customerInfo) {
        Set<String> qrMonitorPlatCustomers = getQrMonitorPlatCustomers();

        //平台商自己的交易不监控
        if (qrMonitorPlatCustomers.contains(customerInfo.getCustomerCode())) {
            return false;
        }

        if (StringUtils.isNotBlank(customerInfo.getCustomerPath())) {
            Set<String> customerParents = Arrays.stream(customerInfo.getCustomerPath().split("\\.")).collect(Collectors.toSet());
            customerParents.retainAll(qrMonitorPlatCustomers);
            return !customerParents.isEmpty();
        }
        return false;
    }

    private static final Map<String, RcConstants.RiskEventRule> UNION_QR_CARD_RULE = ImmutableMap.<String, RcConstants.RiskEventRule>builder()
            .put("UnionSweepDebit", RcConstants.RiskEventRule.R017)
            .put("UnionSweep", RcConstants.RiskEventRule.R018)
            .put("UnionQrcodeDebitCard", RcConstants.RiskEventRule.R017)
            .put("UnionQrcode", RcConstants.RiskEventRule.R018)
            .put("UnionJSDebit", RcConstants.RiskEventRule.R017)
            .put("UnionJS", RcConstants.RiskEventRule.R018)
            .build();

    public void autoReopenBusiness(Date reopenTime) {
        List<RcCloseBusiness> reopenBusiness = rcCloseBusinessMapper.selectByReopenTime(reopenTime);
        for (RcCloseBusiness business : reopenBusiness) {
            log.info("商户[{}]重启业务[{}]", business.getCustomerCode(), business.getBusinessCode());
            try {
                CommonOuterResponse<?> resp = custService.changStatus(business.getCustomerCode(), business.getBusinessCode(), null, "1");
                if (!resp.isSuccess()) {
                    log.info("重启业务失败[{}][{}]", resp.getReturnCode(), resp.getReturnMsg());
                }
            } catch (Exception e) {
                log.error("重启业务异常", e);
            }
        }
    }

    private void closeBusiness(String customerCode) {
        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);

        Set<String> closeBusinessCodes = customerInstanceList.stream()
                .map(CustomerBusinessInstance::getBusinessCode)
                .filter(businessCode -> UNION_QR_CARD_RULE.get(businessCode) != null)
                .collect(Collectors.toSet());

        for (String businessCode : closeBusinessCodes) {
            Timex now = Timex.now();
            log.info("商户[{}]关闭业务[{}]", customerCode, businessCode);
            RcCloseBusiness rcCloseBusiness = new RcCloseBusiness();
            rcCloseBusiness.setCustomerCode(customerCode);
            rcCloseBusiness.setBusinessCode(businessCode);
            rcCloseBusiness.setCloseTime(now.toDate());
            rcCloseBusiness.setCreateTime(now.toDate());
            rcCloseBusiness.setUpdateTime(now.toDate());
            rcCloseBusiness.setReopenTime(now.plus(java.time.Duration.ofDays(1)).start().toDate());
            try {
                rcCloseBusinessMapper.insert(rcCloseBusiness);
                CommonOuterResponse<?> resp = custService.changStatus(customerCode, businessCode, null, "0");
                if (!resp.isSuccess()) {
                    log.info("关闭业务失败[{}][{}]", resp.getReturnCode(), resp.getReturnMsg());
                }
            } catch (Exception e) {
                log.error("关闭业务异常", e);
            }
        }
    }

    /**
     * R017-R019
     *
     * @param order
     */
    @Override
    public void monitor(RcTxsOrder order) {
        if (!Constants.rcBusinessType.GATEWAY_PAY.code.equals(order.getBusinessType())) {
            return;
        }
        RcConstants.RiskEventRule cardRuleCode = UNION_QR_CARD_RULE.get(order.getBusinessCode());
        if (cardRuleCode == null) {
            return;
        }

        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(order.getCustomerCode(), order.getCustomerCode(), UserType.PPS_USER.code);
        if (!isQrMonitorCustomer(customerInfo)) {
            return;
        }

        String customerName = Optional.ofNullable(customerInfo.getName()).orElse("");


        String cardNo = order.getTargetIdMap().get(RcConstants.BusinessTagerType.BANK_CARD.code);

        boolean isCloseBusiness = false;

        if (StringUtils.isNotBlank(cardNo)) {
            RiskEventRule cardRule = riskEventRuleService.getRule(order.getCustomerCode(), cardRuleCode);
            if (cardRule != null && cardRule.getRuleParam1() != null) {
                String key = "RC_EVENT:" + order.getCustomerCode() + ":" + cardRuleCode.code + ":" + RcDateUtils.getCurrentDay() + ":" + cardNo;
                long totalCount = rcRedisService.redisIncr(key, 1L, Duration.standardDays(2));
                long rcCount = Long.parseLong(cardRule.getRuleParam1());
                log.info("商户[{}]易票联订单号[{}]卡号[{}]监控[{}]",
                        order.getCustomerCode(),
                        order.getTransactionNo(),
                        cardNo,
                        cardRuleCode.message.replace("[param1]", cardRule.getRuleParam1()));
                if (totalCount >= rcCount) {
                    log.info("笔数[{}]>=[{}]超限", totalCount, rcCount);
                    isCloseBusiness = true;
                    riskEventRecordService.addEventRecord(cardRuleCode.code, order.getCustomerCode(), customerName,
                            String.format("卡号[%s]笔数[%d]", cardNo, totalCount));
                }
            }
        }

        RiskEventRule amountRule = riskEventRuleService.getRule(order.getCustomerCode(), RcConstants.RiskEventRule.R019);
        if (amountRule != null && amountRule.getRuleParam1() != null) {
            String key = "RC_EVENT:" + order.getCustomerCode() + ":" + RcConstants.RiskEventRule.R019.code + ":" + RcDateUtils.getCurrentDay();
            long totalAmount = rcRedisService.redisIncr(key, order.getAmount(), Duration.standardDays(2));
            long rcAmount = Long.parseLong(amountRule.getRuleParam1()) * 100;
            log.info("商户[{}]易票联订单号[{}]金额[{}]监控[{}]", order.getCustomerCode(),
                    order.getTransactionNo(),
                    order.getAmount(),
                    RcConstants.RiskEventRule.R019.message.replace("[param1]", amountRule.getRuleParam1()));
            if (totalAmount >= rcAmount) {
                log.info("金额[{}]>=[{}]超限", totalAmount, rcAmount);
                isCloseBusiness = true;
                riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R019.code, order.getCustomerCode(), customerName, String.valueOf(totalAmount / 100.0));
            }
        }

        if (isCloseBusiness) {
            closeBusiness(order.getCustomerCode());
        }

    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        return Constants.PayState.SUCCESS.code.equals(order.getPayState());
    }
}

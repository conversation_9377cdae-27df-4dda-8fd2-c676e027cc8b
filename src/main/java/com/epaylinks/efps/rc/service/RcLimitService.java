package com.epaylinks.efps.rc.service;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.rc.domain.RcArchive;
import org.springframework.data.redis.core.RedisTemplate;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.domain.RcLimit;

/**
 * <AUTHOR>
 * @date 2018.09.28
 */
public interface RcLimitService {

	/**
	 * 新增风控指标
	 * @param rcLimit
	 * @return
	 */
	boolean save(RcLimit rcLimit);

	void saveToRedis(RcLimit rcLimit);

	void updateToRedis(RcLimit rcLimit);

	/**
	 * 查询某个指标是否已存在 判断标准 限制值，风控指标定义编码，业务类型，业务对象类型，业务对象ID一致
	 * @param rcLimit
	 * @return
	 */
	boolean queryExistence(RcLimit rcLimit);
	
	/**
	 * 根据业务类型以及业务对象ID来查询相应的指标（本应从redis中获取，宇翔补充）
	 * @param businessType
	 * @param businessTargetType
	 * @param businessTargetId
	 * @return
	 */
	 List<RcLimit> queryByBusinessTypeAndTaregetId(String businessType , String businessTargetType, String businessTargetId);
	
	/**
	 * 根据业务对象ID来查询相应的指标（宇翔补充）
	 * @param businessTargetId
	 * @return
	 */
	public List<RcLimit> queryByBusinessTaregetId(String businessTargetId);

	/**
	 * 根据商户查找额度限制列表
	 */
	Map<String,Long> queryCustomerAmountLimit(String code, String parentCode, String level);

	Map<String,Long> queryMinAmountLimit(String targetType,String code, String level, RcArchive rcArchive);

	/**
	 * 查询商户/客户号当前限额
	 * @param targetType
	 * @param code
	 * @param level
	 * @param rcArchive
	 * @return
	 */
	Map<String,Long> queryAmountLimit(String targetType,String code,String level,RcArchive rcArchive);

	void setRcArchive(RcArchive rcArchive);

	/**
	 * 更新风控指标
	 */
	boolean updateLimit(RcLimit rcLimit);
	
	/**
     * 更新风控限额
     */
    boolean updateAmountLimit(Long defineId ,String businessTagerId, String limitValue, Long userId, String userName );
    
    /**
     * 更新风控限额
     */
    boolean updateAmountLimit(Long defineId , String businessTagerType, String businessTagerId, String limitValue, Long userId, String userName );

	/**
	 * 根据风控指标定义ID和业务对象ID查找风控指标，从oracle(旧，支持005商户编号)
	 * @param defineId
	 * @param businessTagerId
	 * @return
	 */
	RcLimit queryLimit(Long defineId ,String businessTagerId);
	
	/**
	 *  根据风控指标定义ID和业务对象ID查找风控指标
	 * @param defineId
	 * @param businessTagerType
	 * @param businessTagerId
	 * @return
	 */
	RcLimit queryLimit(Long defineId, String businessTagerType, String businessTagerId);

	PageResult<Map<String,Object>> queryAllLimit(String customerCode, int pageNum, int pageSize);
	
	/**
	 * 查询redis限额
	 * @param customerCode
	 * @return
	 */
	Map<String, Object> queryRedisLimit(String targetId, String datetime);


	/**
	 * 根据商户查询限额如果不存在则查询默认限额
	 */
	RcLimit queryByTargerIdIfNullByLevel(Long defineId, String targetType, String targetId);

	void updateRcLevel(String archiveCode);

    Map<String, Long> queryCertAmountLimit(String code, String businessTargetType, String level);

	Map<String, Long> queryIndustryAmountLimit(String code, String businessTargetType);

    Map<String, Long> queryAmountLimit(String code, String businessTargetType, String level);

	/**
	 * 已设置过限额的商户查询新增的默认限额
	 * @param code
	 * @param businessTargetType
	 * @param level
	 * @return
	 */
	Map<String, Long> queryAddAmountLimitMap(String code, String businessTargetType, String level);

	Map<String, Long> queryAddIndustryAmountLimitMap(String code, String businessTargetType, String level);

	int deleteRcLimit(RcLimit rcLimit);
}

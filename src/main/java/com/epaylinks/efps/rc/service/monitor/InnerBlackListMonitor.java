package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.service.BwListService;
import com.epaylinks.efps.rc.service.RcRedisService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.util.CardUtils;
import com.google.common.collect.ImmutableList;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 14:06
 */
@Service
public class InnerBlackListMonitor implements TransactionMonitor {
    @Autowired
    BwListService bwListService;

    @Autowired
    RcRedisService rcRedisService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordService riskEventRecordService;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    private static final List<String> UNION_QR_BIZ_CODES = ImmutableList.of("UnionSweepDebit", "UnionSweep", "UnionQrcodeDebitCard",
            "UnionQrcode", "UnionJSDebit", "UnionJS");

    /**
     * R025
     *
     * @param order
     */
    @Override
    public void monitor(RcTxsOrder order) {
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(order.getCustomerCode(), order.getCustomerCode(), UserType.PPS_USER.code);
        String customerName = Optional.ofNullable(customerInfo.getName()).orElse("");

        String cardNo = order.getTargetIdMap().get(RcConstants.BusinessTagerType.BANK_CARD.code);

        RiskEventRule cardRule = riskEventRuleService.getRule(order.getCustomerCode(), RcConstants.RiskEventRule.R025);
        if (cardRule != null) {
            Map<String, String> cardNoCheck = new HashMap<>();
            cardNoCheck.put(RcConstants.BusinessTagerType.BANK_CARD.code, cardNo);
            Map<String, String> cardNoBlack = bwListService.getInnerBlackList(cardNoCheck);
            if (MapUtils.isNotEmpty(cardNoBlack)) {
                riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R025.code, order.getCustomerCode(), customerName,
                        String.format("卡号[%s]", CardUtils.getHiddenBankCardNo(cardNo)));
            }
        }
    }

    private Map<String, String> customerCodeCheckMap(RcTxsOrder order) {
        Map<String, String> map = new HashMap<>();
        map.put(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, order.getCustomerCode());
        return map;
    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        return Constants.PayState.SUCCESS.code.equals(order.getPayState()) &&
                Constants.rcBusinessType.GATEWAY_PAY.code.equals(order.getBusinessType()) &&
                UNION_QR_BIZ_CODES.contains(order.getBusinessCode()) &&
                StringUtils.isNotBlank(order.getCustomerCode()) &&
                StringUtils.isNotBlank(order.getTargetIdMap().get(RcConstants.BusinessTagerType.BANK_CARD.code)) &&
                MapUtils.isNotEmpty(
                        bwListService.getInnerBlackList(customerCodeCheckMap(order))
                );
    }
}

package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 
 * 提现单笔最高限额。出金分离为代付、提现，该类保留做提现单笔最高限额  20210729 fwy
 * <AUTHOR>
 *
 */
@Service("Single-Max-OUT-Amount")
public class SingleMaxOutAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

	@Autowired
	private RcCalculteBasic rcCalculteBasic;

	@Autowired
	private RcArchiveService rcArchiveService;
	
//	@Logable(businessTag = "Single-Max-OUT-Amount:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
	    
	    // 出金分离为代付、提现，非提现业务不做判断  20210729
        if (!rcCalculteBasic.isCashWithdrawal(rcCalculateRequest.getBusinessCode())) {
            return CommonOuterResponse.SUCCEE;
        }
        
	    String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (StringUtils.isBlank(amountStr)) {
			throw new AppException(RcConstants.RcIndex.AMOUNT.code);
		}
		Long amount = Long.parseLong(amountStr);
		if (amount > Long.parseLong(rcLimit.getLimitValue())) {
			// 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(), "/",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
			//实际发生值已经大于限定值
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}
	
	@Override
	public void reset(RcLimit rcLimit) {
		// TODO Auto-generated method stub
		
	}

//	@Logable(businessTag = "Single-Max-OUT-Amount:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
		// TODO Auto-generated method stub
		
	}

}

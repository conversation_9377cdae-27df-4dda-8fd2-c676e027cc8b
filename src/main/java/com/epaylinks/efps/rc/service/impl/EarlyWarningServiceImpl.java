package com.epaylinks.efps.rc.service.impl;

import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.rc.drools.DroolsService;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.EarlyWarningMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RiskEventRule;
import com.epaylinks.efps.rc.service.EarlyWarningService;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RiskEventRecordService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.util.RcDateUtils;

@Service
public class EarlyWarningServiceImpl implements EarlyWarningService {
    
    @Autowired 
    private EarlyWarningMapper earlyWarningMapper;
    @Autowired
    private RiskEventRuleService riskEventRuleService;
    @Autowired
    private RiskEventRecordService riskEventRecordService;
    @Autowired
    private RcArchiveService rcArchiveService;
    @Autowired
    private DroolsService droolsService;
    
    private MyRedisTemplate redisTemplate;
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;
    
    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(10); // 创建一个线程池
    
    private String getPosUnionPayKey(String businessId, String currentDay) {
        return  businessId + ":" + "POSUnionPay:" + currentDay;
    }
    
    private String getNonPosUnionPayKey(String businessId, String currentDay) {
        return  businessId + ":" + "NonPOSUnionPay:" + currentDay;
    }

    @Override
    public void checkLoginCountWarning(String businessTargetId, String businessType) {
        
        // R006("R006", "高频登陆, 商户在[param1]小时内登陆次数≥[param2]次"), // 关注 （风控校验登录时）
        if (!Constants.rcBusinessType.LOG_IN.code.equals(businessType)) {
            return;
        }
        
        fixedThreadPool.execute(new Runnable() {
           
            @Override
            public void run() {
                TxsPayResultMsg txsPayResultMsg = new TxsPayResultMsg();
                txsPayResultMsg.setBusinessTargetIds(new HashMap<>());
                txsPayResultMsg.getBusinessTargetIds().put(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                txsPayResultMsg.setBusinessType(businessType);
                txsPayResultMsg.setIndexs(new HashMap<>());
                droolsService.fireRuleIgnoreException(txsPayResultMsg);

                RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R006);
                if (rule == null || RcConstants.RiskEventRuleStatus.OFF.code.equals(rule.getRuleStatus())
                        || rule.getRuleParam1() == null || rule.getRuleParam2() == null) {
                    return;
                }
                
                Long limitCount = Long.parseLong(rule.getRuleParam2());
                int count = earlyWarningMapper.countLoginTimes(businessTargetId, Integer.parseInt(rule.getRuleParam1())); 
                if (count >= limitCount - 1) {// 登录成功前统计之前记录，要对应加上本次操作
                    RcArchive archive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                    String name = archive != null ? archive.getArchiveName() : ""; 
                    riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R006.code, businessTargetId, name, String.valueOf(count + 1));
                }
                
            }
        });        
        
    }
    
    @Override
    public void checkSingleAmountWarning(String businessTargetId, String businessType, String businessCode,
            Long amount) {
        /*
         * R001("R001", "交易监控, 商户网络支付单笔入金交易≥[param1]元"), // 预警  （风控校验时）
         * R002("R002", "交易监控, 商户银行卡收单笔入金交易≥[param1]元"), //  预警    （风控校验时）
         */
        
        if (!Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)) {
            return;
        }
        
        fixedThreadPool.execute(new Runnable() {
            
            @Override
            public void run() {
                RcConstants.RiskEventRule ruleCode = isPOSUnionPay(businessCode) ? RcConstants.RiskEventRule.R002 : RcConstants.RiskEventRule.R001;
                RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, ruleCode);
                if (rule == null || RcConstants.RiskEventRuleStatus.OFF.code.equals(rule.getRuleStatus())
                        || rule.getRuleParam1() == null) {
                    return;
                }
                
                Long limitAmount = Long.parseLong(rule.getRuleParam1()) * 100; 
                if (amount >= limitAmount) {
                    RcArchive archive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                    String name = archive != null ? archive.getArchiveName() : ""; 
                    riskEventRecordService.addEventRecord(ruleCode.code, businessTargetId, name, String.valueOf(amount / 100.0));
                }
            }
        });

    }

    @Override
    public void checkTxsOrderAmountWarning(String businessTargetId, String businessType, String businessCode,
            Long amount) {
        /*
        R003("R003", "交易监控, 商户网络支付（除收单外）日交易量≥[param1]元"), // 预警 （风控记录订单时）
        R004("R004", "交易监控, 商户银行卡收单日交易量≥[param1]元"), //  预警 （风控记录订单时）
        R005("R005", "交易监控, 商户代付交易日交易笔数≥[param1]次"), // 预警  （风控记录订单时）
        R007("R007", "高退款率, 商户当日退款交易与总交易占比≥[param1]"), // 关注 （风控记录订单时）
        R014("R014", "交易监控, 商户收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), ---所有成功状态的收单，除线下银行卡收单 // 预警 （风控记录订单时）
        R015("R015", "交易监控, POS终端收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), // 预警 （风控记录订单时）
         */
        if (!Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)
                && !Constants.rcBusinessType.REFUND.code.equals(businessType)
                && !Constants.rcBusinessType.WITHDRAW.code.equals(businessType)
                || amount == null) {
            return;
        }
        
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();

        fixedThreadPool.execute(new Runnable() {
            
            @Override
            public void run() {
            
                RcArchive archive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                String name = archive != null ? archive.getArchiveName() : "";
                
                // R003, 交易监控, 商户网络支付（除收单外）日交易量≥[param1]元, 预警 （风控记录订单时）
                if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType) &&
                        !isPOSUnionPay(businessCode)) {
                    String redisKey = getNonPosUnionPayKey(businessTargetId, RcDateUtils.getCurrentDay());
                    Long sumAmount = redisTemplate.opsForValue().increment(redisKey, amount);
                    redisTemplate.expire(redisKey, 4, TimeUnit.DAYS); // 设置有效期
                   
                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R003);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null) {
                        Long limitAmount = Long.parseLong(rule.getRuleParam1())  * 100;
//                        Long sumAmount = earlyWarningMapper.sumOnlineTradingAmount(businessTargetId); // 统计rc_txs_order已经包含当前交易
                        if (sumAmount >= limitAmount) {
                            riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R003.code, businessTargetId, name, String.valueOf(sumAmount / 100.0));
                        }
                    }
                }
                
                // R004, 交易监控, 商户银行卡收单日交易量≥[param1]元, 预警 （风控记录订单时）
                if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)
                        && isPOSUnionPay(businessCode)) {
                    String redisKey = getPosUnionPayKey(businessTargetId, RcDateUtils.getCurrentDay());
                    Long sumAmount = redisTemplate.opsForValue().increment(redisKey, amount);
                    redisTemplate.expire(redisKey, 4, TimeUnit.DAYS); // 设置有效期

                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R004);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null) {
                        Long limitAmount = Long.parseLong(rule.getRuleParam1())  * 100;
//                        Long sumAmount = earlyWarningMapper.sumPOSUnionAmount(businessTargetId); // 统计rc_txs_order已经包含当前交易
                        if (sumAmount >= limitAmount) {
                            riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R004.code, businessTargetId, name, String.valueOf(sumAmount / 100.0));
                        }
                    }
                }
                
                // R005, 交易监控, 商户代付交易日交易笔数≥[param1]次, 预警  （风控记录订单时）
                if (Constants.rcBusinessType.WITHDRAW.code.equals(businessType) && 
                        ("Withdraw".equals(businessCode) || "Withdraw-CreditCard".equals(businessCode))) {
                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R005);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null) {
                        Long limitCount = Long.parseLong(rule.getRuleParam1()) ;
                        int count = earlyWarningMapper.countWithDraw(businessTargetId); // 统计rc_txs_order已经包含当前交易
                        if ( count >= limitCount) {
                            riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R005.code, businessTargetId, name, String.valueOf(count));
                        }
                    }
                }
                
                // R007("R007", "高退款率, 商户当日退款交易与总交易占比≥[param1]"), // 关注 （风控记录订单时）
                if (Constants.rcBusinessType.REFUND.code.equals(businessType)) {
                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R007);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null) {
                        int payCount = earlyWarningMapper.countTxsByBusinessType(businessTargetId, Constants.rcBusinessType.GATEWAY_PAY.code);
                        if (payCount > 0) {
                            int refundCount = earlyWarningMapper.countTxsByBusinessType(businessTargetId, Constants.rcBusinessType.REFUND.code);
                            double rate = refundCount * 100.0 / payCount;
                            double limitRate = Double.parseDouble(rule.getRuleParam1().replace("%", "")); // 记录格式为xx%
                            if ( rate >= limitRate) {
                                riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R007.code, businessTargetId, name, rate + "%");
                            }
                        }
                    }
                }
                
                // R014("R014", "交易监控, 商户收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), ---所有成功状态的收单，除线下银行卡收单// 预警 （风控记录订单时）
                if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)
                        && !isPOSUnionPay(businessCode)) {
                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R014);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null && rule.getRuleParam2() != null) {
                        Double limitTimes = Double.parseDouble(rule.getRuleParam1()); // 限制倍数
                        Long limitAmount = Long.parseLong(rule.getRuleParam2()) * 100; // 限制金额
//                      Long sumAmount = earlyWarningMapper.sumOnlineTradingAmount(businessTargetId); // 统计rc_txs_order已经包含当前交易
//                      Long lastThreeDaySumAmount = earlyWarningMapper.sumLastThreeDayOnlineTradingAmount(businessTargetId); // 统计前3天交易金额
                        // redis交易额
                        Long sumAmount = redisTemplate.opsForValue().increment(getNonPosUnionPayKey(businessTargetId, RcDateUtils.getCurrentDay()), 0L);
                        Long lastDayAmount = redisTemplate.opsForValue().increment(getNonPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-1)), 0L);    // 昨天
                        Long lastSecondDayAmount = redisTemplate.opsForValue().increment(getNonPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-2)), 0L); // 前天 
                        Long lastThirdDayAmount = redisTemplate.opsForValue().increment(getNonPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-3)), 0L); // 大前天
                        Long lastThreeDaySumAmount = lastDayAmount + lastSecondDayAmount + lastThirdDayAmount;
                        boolean matchTimes = false;  // 比值是否符合倍数要求
                        if (lastThreeDaySumAmount > 0) {
                            Double times = sumAmount / (lastThreeDaySumAmount * 1.0d / 3 ); // 当天比前3天平均值
                            matchTimes = times >= limitTimes;
                        } else if (sumAmount > 0) {// 当前三天金额为0，今天金额>0时，表示无穷大倍数，符合倍数要求 
                            matchTimes = true;
                        }
                        if (sumAmount >= limitAmount && matchTimes) {
                            riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R014.code, businessTargetId, name, String.valueOf(sumAmount / 100.0));
                        }
                    }
                }
                
                // R015("R015", "交易监控, POS终端收单日交易量跟前3日日均交易量比值≥[param1]倍，且≥[param2]元"), // 预警 （风控记录订单时）
                if (Constants.rcBusinessType.GATEWAY_PAY.code.equals(businessType)
                        && isPOSUnionPay(businessCode)) {
                    RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, RcConstants.RiskEventRule.R015);
                    if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                            && rule.getRuleParam1() != null && rule.getRuleParam2() != null) {
                        
                        Double limitTimes = Double.parseDouble(rule.getRuleParam1()); // 限制倍数
                        Long limitAmount = Long.parseLong(rule.getRuleParam2()) * 100; // 限制金额
//                        Long sumAmount = earlyWarningMapper.sumPOSUnionAmount(businessTargetId); // 统计rc_txs_order已经包含当前交易
//                        Long lastThreeDaySumAmount = earlyWarningMapper.sumLastThreeDayPOSUnionAmount(businessTargetId); // 统计前3天交易金额
                        // redis交易额
                        Long sumAmount = redisTemplate.opsForValue().increment(getPosUnionPayKey(businessTargetId, RcDateUtils.getCurrentDay()), 0L);
                        Long lastDayAmount = redisTemplate.opsForValue().increment(getPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-1)), 0L);    // 昨天
                        Long lastSecondDayAmount = redisTemplate.opsForValue().increment(getPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-2)), 0L); // 前天 
                        Long lastThirdDayAmount = redisTemplate.opsForValue().increment(getPosUnionPayKey(businessTargetId, RcDateUtils.addDay(-3)), 0L); // 大前天
                        Long lastThreeDaySumAmount = lastDayAmount + lastSecondDayAmount + lastThirdDayAmount;
                        boolean matchTimes = false; // 比值是否符合倍数要求
                        if (lastThreeDaySumAmount > 0) {
                            Double times = sumAmount / (lastThreeDaySumAmount * 1.0d / 3 ); // 当天比前3天平均值
                            matchTimes = times >= limitTimes;
                        } else if (sumAmount > 0) { // 当前三天金额为0，今天金额>0时，表示无穷大倍数，符合倍数要求 
                            matchTimes = true;
                        }
                        if (sumAmount >= limitAmount && matchTimes) {
                            riskEventRecordService.addEventRecord(RcConstants.RiskEventRule.R015.code, businessTargetId, name, String.valueOf(sumAmount / 100.0));
                        }
                    }
                }
            }
        });
    }

    @Override
    public void checkCalculateLimitWarning(String businessTargetId, String rcLimitType) {

        // R008, 黑名单, 商户当日命中黑名单≥[param1]次, 关注  （风控命中黑名单时）
        // R009, 交易限额, 商户当日触发限额类风控≥[param1]次, 关注 （风控触发限额时）
        if (!RcConstants.RcLimitType.BLACK.code.equals(rcLimitType)
                && !RcConstants.RcLimitType.AMOUNT.code.equals(rcLimitType)) {
            return;
        }

        fixedThreadPool.execute(new Runnable() {
            
            @Override
            public void run() {
                TxsPayResultMsg txsPayResultMsg = new TxsPayResultMsg();
                txsPayResultMsg.setBusinessTargetIds(new HashMap<>());
                txsPayResultMsg.getBusinessTargetIds().put(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                txsPayResultMsg.setBusinessType("rcLimit");
                txsPayResultMsg.setIndexs(new HashMap<>());
                txsPayResultMsg.getIndexs().put("rcLimitType", rcLimitType);
                droolsService.fireRuleIgnoreException(txsPayResultMsg);

                RcConstants.RiskEventRule ruleCode  = RcConstants.RcLimitType.BLACK.code.equals(rcLimitType) ?
                        RcConstants.RiskEventRule.R008 : RcConstants.RiskEventRule.R009 ;
                RiskEventRule rule = riskEventRuleService.getRule(businessTargetId, ruleCode);
                if (rule != null && RcConstants.RiskEventRuleStatus.ON.code.equals(rule.getRuleStatus())
                        && rule.getRuleParam1() != null) {
                   
                    int count = 0; 
                    if ( RcConstants.RcLimitType.BLACK.code.equals(rcLimitType)) {
                        count = earlyWarningMapper.countBlackListLimit(businessTargetId);
                    } else {
                        count = earlyWarningMapper.countAmountLimit(businessTargetId);
                    }
                    Long limitAmount = Long.parseLong(rule.getRuleParam1()) ;
                    
                    if ( count >= limitAmount) {
                        RcArchive archive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, businessTargetId);
                        String name = archive != null ? archive.getArchiveName() : "";
                        riskEventRecordService.addEventRecord(ruleCode.code, businessTargetId, name, String.valueOf(count));
                    }
                }
            }
        });

    }

    /**
     * 是否银行卡收单业务
     * @param businessCode
     * @return
     */
    private boolean isPOSUnionPay(String businessCode) {
        
        if ("POSUnionCreditCardPay_ABROAD".equals(businessCode)
                || "POSUnionDebitCardPay_ABROAD".equals(businessCode)
                || "POSUnionNfcCreditCardPay".equals(businessCode)
                || "POSUnionQrcodeDebitCard".equals(businessCode)
                || "POSUnionQrcodeCreditCard".equals(businessCode)
                || "POSUnionDebitCardPay".equals(businessCode)
                || "POSUnionCreditCardPay".equals(businessCode)
                || "POSUnionNfcDebitCardPay".equals(businessCode)) {
            return true;
        } 
        return false;
    }
}

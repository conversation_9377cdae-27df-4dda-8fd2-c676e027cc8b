package com.epaylinks.efps.rc.service.export;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.datadownload.DataExportService;
import com.epaylinks.efps.common.datadownload.FileService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.export.IExportFileService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.StringBuilderWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ExportRcArchiveService implements IExportFileService {
    @Autowired
    private DataExportService dataExportService;

    @Autowired
    private FileService fileService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public void payQueryExport(Map map, String userType, String fileNameFinal, Long exportId, Integer exportPageSize) {
        try {
            getExportFile(map, userType, fileNameFinal, exportPageSize);
            String uniqueId = dataExportService.uploadFile(fileNameFinal, null);
            fileService.exportUpdate(exportId, uniqueId, (short) 1, "生成成功");
        } catch (AppException e) {
            e.printStackTrace();
            fileService.exportUpdate(exportId, "", (short) 2, e.getErrorMsg());
        } catch (Exception e) {
            e.printStackTrace();
            logger.printMessage("生成失败:" + e.getMessage());
            logger.printLog(e);
            fileService.exportUpdate(exportId, "", (short) 2, "生成失败");
        } finally {
            File file = new File(fileNameFinal);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    public void getExportFile(Map map, String userType, String fileNameFinal, Integer exportPageSize) throws Exception {
        List<String> listTitles = dataExportService.getTitlesFromFieldAnnotation(RiskInfoExportResponse.class, userType);

        CSVPrinter csvPrinter = dataExportService.downloadCsv(listTitles);
        dataExportService.createFile(csvPrinter, fileNameFinal, 1);
        IOUtils.closeQuietly(csvPrinter);
        int i = 0;
        while (true) {
            map.put("beginRowNo", i * exportPageSize + 1);
            map.put("endRowNo", ++i * exportPageSize);
            logger.printMessage("param:" + JSON.toJSONString(map));
            List<RiskInfoExportResponse> recordPage = rcArchiveService.exportRiskInfo(map,true).getRows();
            Map<String, Long> statisticsData = new HashMap<>();
            Appendable out = new StringBuilderWriter();
            CSVPrinter printer = CSVFormat.DEFAULT.print(out);
            dataExportService.printlnCsvPublic(printer, statisticsData, recordPage, userType);
            dataExportService.createFile(printer, fileNameFinal, 2);
            IOUtils.closeQuietly(printer);
            if (recordPage.size() == 0) {
                break;
            }
            logger.printMessage("风控信息导出：当前第" + i + "页，" + recordPage.size() + "行导出");
        }
    }
}

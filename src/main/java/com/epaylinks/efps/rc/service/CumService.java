package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.rc.domain.cum.CustomerInfo;
import com.epaylinks.efps.rc.domain.cum.CustomerObjects;
import com.epaylinks.efps.rc.domain.cum.CustomerSettleInfo;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("cum")
public interface CumService {


	@PostMapping("/Customer/customerSettleInfo")
	CustomerSettleInfo queryCustomerSettleInfo(@RequestParam("customerCode") String customerCode);

	@GetMapping("/Customer/CustomerInfo")
	CustomerInfo getCustomerInfo(@RequestParam("customerCode") String customerCodeParam,
	                             @RequestHeader(value = "x-customer-code", required = false) String customerCode
	);

	@GetMapping("/Customer/CustomerAllInfo")
	CustomerObjects viewCustomerAllInfo(@RequestHeader("x-customer-code") String customerCode);




}

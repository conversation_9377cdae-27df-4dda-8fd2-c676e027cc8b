package com.epaylinks.efps.rc.service;

import java.util.Map;

import com.epaylinks.efps.common.util.PageResult;
import com.epaylinks.efps.rc.controller.request.AlipayPushRiskRequest;
import com.epaylinks.efps.rc.vo.ChannelRiskRecordVo;

public interface ChannelRiskService {
    
    /**
     * 分页查询风险记录信息
     * @param map
     * @return
     */
    public PageResult<ChannelRiskRecordVo> pageQuery(Map map,Long userId);
    
    /**
     * 记录阿里推动风险记录
     * @param alipayPushRiskRequest
     */
    public void saveAlipayRisk(AlipayPushRiskRequest alipayPushRiskRequest);

}

package com.epaylinks.efps.rc.service.rccalculate.in;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

@Service("Card-Day-Max-IN-Count")
public class CardDayInTradeNumRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;


    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //目前只做快捷支付
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod()) ||
                rcCalculteBasic.isPosCardPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
            if(customerCode == null || bankCardNo == null ) {
                return CommonOuterResponse.SUCCEE;
            }

            String countStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.TRADE_NUM.code);
            if (StringUtils.isBlank(countStr)) {
                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
            }
            long count = Long.parseLong(countStr);
            long dayInCount = 0L;
            String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
            if(redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)) {
                dayInCount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo) + count;
            }else {
                redisTemplate.opsForHash().put(hashKey,bankCardNo,0L);
                redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
                dayInCount = count;
            }

            if (dayInCount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInCount - count+"",count+"",false,"RC交易受限", rcArchive.getArchiveType());
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;

    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String payState =  paramMap.get("payState");
        if(payState.equals("00")){
            String bankCardNo = paramMap.get("bankCardNo");
            String customerCode = paramMap.get("customerCode");
            String payMethod = paramMap.get("payMethod");
            if (StringUtils.isNotBlank(bankCardNo) &&  StringUtils.isNotBlank(customerCode) &&
                    (rcCalculteBasic.isQuickPayMethod(payMethod) || rcCalculteBasic.isPosCardPayMethod(payMethod))){
                long count = Long.parseLong(paramMap.get(RcConstants.RcIndex.TRADE_NUM.code));
                String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentDay());
                Long nowCount = (Long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
                nowCount += count ;
                redisTemplate.opsForHash().put(hashKey,bankCardNo,nowCount);
            }
        }
    }

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //每日重置日最大入金为0
        Date date = new Date();
        String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastKey);
        for(String bankCardNo: map.keySet()) {
            long dayInCount = (long) map.get(bankCardNo);
            if(dayInCount > 0){
                RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), rcLimit.getBusinessTagerId() + "_" + bankCardNo,
                        rcLimit.getDefineCode(), dayInCount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
                rcLimitDataMapper.insert(rcLimitData);
            }
            if(!redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)){// 设置今天的值
                redisTemplate.opsForHash().put(hashKey, bankCardNo, 0L);
            }
        }
        redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
    }
}

package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 15:37
 */
@Service
public class RcRedisService {
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    public Long redisIncr(String key, Long value, Duration expireDuration) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Long incr = redisTemplate.opsForValue().increment(key, value);
        if (expireDuration != null) {
            redisTemplate.expire(key, expireDuration.getMillis(), TimeUnit.MILLISECONDS); // 设置有效期
        }
        return incr;
    }

    public Object getCache(String key) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        return redisTemplate.opsForValue().get(key);
    }

    public Boolean setIfAbsent(String key,String value,long timeOut,TimeUnit timeUnit) {
        MyRedisTemplate redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Boolean putSuccess = redisTemplate.opsForValue().setIfAbsent(key,value);
        redisTemplate.expire(key, timeOut, timeUnit); // 设置有效期
        return putSuccess;
    }
}

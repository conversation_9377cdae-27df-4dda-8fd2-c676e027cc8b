package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;
import com.epaylinks.efps.common.business.pas.domain.User;
import com.epaylinks.efps.common.business.pas.service.PasService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

@Service
public class DataAuthService {
    @Autowired
    private PasService pasService;

    @Autowired
    private RedisDataTransService redisDataTransService;

    public void setMapParam(Map<String,Object> param, Long userId) {
        if (userId != null) {
            User user = pasService.getUserByUserId(userId);
            if (user == null) {
                throw new AppException(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
            }
            if (Constants.PasUserType.COMPANY.code.equals(user.getUserType())) {
                param.put("userCompanyId", user.getCompanyId());
            } else if (Constants.PasUserType.SALES.code.equals(user.getUserType())) {
                param.put("businessManId", user.getUid());
            } else {
                List<Long> companyIds = redisDataTransService.getCompanyIdList(userId);
                if (!companyIds.isEmpty()) {
                    param.put("companyIds", companyIds);
                }
            }
        }
    }
}

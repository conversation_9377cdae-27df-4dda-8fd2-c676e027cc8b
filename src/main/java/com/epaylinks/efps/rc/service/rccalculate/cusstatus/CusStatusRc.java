package com.epaylinks.efps.rc.service.rccalculate.cusstatus;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 商户状态计算指标
 * <AUTHOR>
 *
 */
@Service("CUS-STATUS")
public class CusStatusRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private RcArchiveMapper rcArchiveMapper;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;

//	@Logable(businessTag = "CUS-STATUS:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void reset(RcLimit rcLimit) {
		// TODO Auto-generated method stub
		
	}

	@Logable(businessTag = "CUS-STATUS:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

	    RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(rcLimit.getBusinessTagerId());
		if (RcConstants.RcCustomerStatus.NORMAL.code.equals(rcArchive.getCusStatus())) {
			return CommonOuterResponse.SUCCEE;
		}
		rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),"注销","/","/",false,"RC商户注销", rcArchive.getArchiveType());
		return "RC商户注销";
	}

}

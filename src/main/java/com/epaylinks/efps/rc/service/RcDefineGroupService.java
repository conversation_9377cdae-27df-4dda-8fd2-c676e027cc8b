package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.dao.RcDefineGroupMapper;
import com.epaylinks.efps.rc.domain.RcDefineGroup;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class RcDefineGroupService {

	@Autowired
	private RcDefineGroupMapper rcDefineGroupMapper;


	public PageResult<Map<String,Object>> pageSmallQuery(Long bigId,String identifier,int pageNum,int pageSize){

		PageResult<Map<String,Object>> result = new PageResult<>();

		//总记录数
		int total = rcDefineGroupMapper.totalPageQuery(bigId,identifier);

		result.setTotal(total);

		//当前页面
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;
		List<Map<String,Object>> list = rcDefineGroupMapper.pageQuery(bigId,identifier,startNum,endNum);
		//根据小类名称查询大类名称
		for(Map<String,Object> map : list){
			String bigName = rcDefineGroupMapper.queryName(map.get("smallName").toString());
			map.put("bigName",bigName);
		}

		result.setRows(list);
		return result;
	}


	public List<Map<String,Object>> groupQuery(){
		//先把父节点为0的分组查出
		List<Map<String,Object>> list = rcDefineGroupMapper.selectGroupByParentId(0L);

		//遍历把下面的所有子节点查询
		for (Map<String,Object> map : list){
			List<Map<String,Object>> list1 = rcDefineGroupMapper.selectGroupByParentId(Long.valueOf(map.get("id").toString()));
			map.put("list",list1);
		}

		return list;
	}


	public PageResult<RcDefineGroup> pageBigQuery(String identifier,int pageNum,int pageSize){
		PageResult<RcDefineGroup> result = new PageResult<>();

		//总记录数
		int total = rcDefineGroupMapper.totalBigPageQuery(identifier);
		result.setTotal(total);

		//当前页面
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;
		List<RcDefineGroup> list = rcDefineGroupMapper.pageBigQuery(identifier,startNum,endNum);

		result.setRows(list);

		return result;
	}




}

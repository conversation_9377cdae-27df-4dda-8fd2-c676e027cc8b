package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.controller.response.LimitSwitchStatusResponse;
import com.epaylinks.efps.rc.dao.LimitSwitchMapper;
import com.epaylinks.efps.rc.domain.LimitSwitch;
import com.epaylinks.efps.rc.service.LimitSwitchService;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class LimitSwitchServiceImpl implements LimitSwitchService {
    @Autowired
    private LimitSwitchMapper limitSwitchMapper;
    @Autowired
    private RcCalculteBasic rcCalculteBasic;
    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Override
    public LimitSwitchStatusResponse getLimitStatus(String customerNo) {
        LimitSwitchStatusResponse response = new LimitSwitchStatusResponse();
        response.setCustomerNo(customerNo);
        response.setDaliyStatus(RcConstants.LimitAmountStatus.CLOSE.code);
        response.setSingleStatus(RcConstants.LimitAmountStatus.CLOSE.code);

        LimitSwitch daliyLimit = limitSwitchMapper.selectByCustomerNoAndIndex(customerNo,"Daliy-Out-Limit");
        if (!StringUtils.isEmpty(daliyLimit) && daliyLimit.getLimitStatus().equals("1")) {
            response.setDaliyStatus(RcConstants.LimitAmountStatus.OPEN.code);
            response.setDaliyValue(daliyLimit.getLimitValue());
            response.setRemainValue(daliyLimit.getLimitValue());
            redisTemplate = myRedisTemplateService.getMyRedisTemplate();
            String key = rcCalculteBasic.getKey(customerNo , "Day-Max-OUT-Amount" , RcDateUtils.getCurrentDay());
            Object result = redisTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(result)) {
                Long valule = Long.parseLong(result + "");
                response.setRemainValue(daliyLimit.getLimitValue() - valule);
            }
        }
        LimitSwitch singleLimit = limitSwitchMapper.selectByCustomerNoAndIndex(customerNo,"Single-Out-Limit");
        if (!StringUtils.isEmpty(singleLimit) && singleLimit.getLimitStatus().equals("1")) {
            response.setSingleStatus(RcConstants.LimitAmountStatus.OPEN.code);
            response.setSingleValue(singleLimit.getLimitValue());
        }
        if (!response.getDaliyStatus().equals(response.getSingleStatus())) {
            response.setDaliyStatus(RcConstants.LimitAmountStatus.CLOSE.code);
            response.setSingleStatus(RcConstants.LimitAmountStatus.CLOSE.code);
        }
        return response;
    }

    @Override
    public LimitSwitch getLimitInfo(String customerNo,String index) {
        LimitSwitch limitSwitch = limitSwitchMapper.selectByCustomerNoAndIndex(customerNo,index);
        return limitSwitch;
    }

    @Override
    public Long selectPriKey() {
        return limitSwitchMapper.selectPriKey();
    }

    @Override
    public int insert(LimitSwitch limitSwitch) {
        return limitSwitchMapper.insert(limitSwitch);
    }

    @Override
    public int updateLimitSwitch(LimitSwitch limitSwitch) {
        return limitSwitchMapper.updateByPrimaryKey(limitSwitch);
    }
}

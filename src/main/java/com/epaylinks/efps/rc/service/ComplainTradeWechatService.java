package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.rc.vo.ComplainImportDataVo;

import java.util.Map;

public interface ComplainTradeWechatService {

    /**
     * 导入微信风险商户数据
     * @param importDataVo
     */
    Map<Integer, BatchDetail> importWechatRiskStoreData(ComplainImportDataVo importDataVo);

    /**
     * 导入微信投诉数据
     * @param importDataVo
     */
    Map<Integer, BatchDetail>  importWechatComplaindata(ComplainImportDataVo importDataVo);
}

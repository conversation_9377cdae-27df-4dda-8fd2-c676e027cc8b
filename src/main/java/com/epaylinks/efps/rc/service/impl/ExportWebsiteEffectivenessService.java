package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.datadownload.DataExportService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.GamblingPyramidRecord;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;
import com.epaylinks.efps.rc.service.CustService;
import com.epaylinks.efps.rc.service.FsService;
import com.epaylinks.efps.rc.service.GamblingPyramidService;
import com.epaylinks.efps.rc.util.RcCvsUtils;
import com.epaylinks.efps.rc.vo.ExportWebsiteEffectivenessVo;
import com.epaylinks.efps.rc.vo.UrlConnVo;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.StringBuilderWriter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import sun.misc.BASE64Encoder;
import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class ExportWebsiteEffectivenessService {

    @Autowired
    private RcArchiveMapper rcArchiveMapper;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private CustService custService;

    @Autowired
    private GamblingPyramidService gamblingPyramidService;

    @Autowired
    private FsService fsService;

    @Autowired
    private DataExportService dataExportService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${websiteExportEmail}")
    private String email;

    public void sendCheckList(List<String> customerNoList) throws Exception {
        List<ExportWebsiteEffectivenessVo> vos = rcArchiveMapper.exportWebByImport(customerNoList);
        if (vos != null && vos.size() > 0) {
            build(vos,"import");
        }
    }

    public void build(List<ExportWebsiteEffectivenessVo> voList,String source) throws Exception {
        String parentFilePath = "tmp/export/网站有效性检查报告_" + System.currentTimeMillis() + "/";
        File parentFile = new File(parentFilePath);
        if(!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String filePath = parentFilePath + System.currentTimeMillis() +  ".csv";
        // 检验网站有效性
        GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
        if ("import".equals(source)) {
            checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.MANUAL);
        } else {
            checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.REGULAR);
        }
        for (int i = 0; i < voList.size(); i++) {
            ExportWebsiteEffectivenessVo vo = voList.get(i);
            // 导入可能存在URL为空
            if (org.apache.commons.lang3.StringUtils.isBlank(vo.getSiteUrl())) {
                vo.setRemark("网站不存在");
            } else {
                CommonOuterResponse<GamblingPyramidRecord> response = gamblingPyramidService.commonRiskCheck(vo.getCustomerNo(),"", RcConstants.BusinessTagerType.WEB.code,vo.getSiteUrl(),null,0L,checkExtraParam);
                GamblingPyramidRecord gamblingPyramidRecord = response.getData();
                UrlConnVo urlConnVo = gamblingPyramidRecord.getUrlConnVo();
                vo.setCheckStatus(RcConstants.CheckStatus.getMessageByCode(gamblingPyramidRecord.getVerificationResults()));
                vo.setWebsiteApp(urlConnVo.getTitle());
                vo.setRiskTag(transRiskTag(gamblingPyramidRecord.getRiskTag()));
                String remark = gamblingPyramidRecord.getResponseCode() + " " + gamblingPyramidRecord.getResponseMsg();
                vo.setRemark(remark);
                vo.setHits(urlConnVo.getKeyword());
            }
            voList.set(i,vo);
        }
        // 写入CSV
        if(voList != null && !voList.isEmpty()) {
            logger.printMessage("result size:" + voList.size());
        }

        exportCsvNew(voList,filePath);
        File file = new File(filePath);
        String uploadToken = fsService.uploadToken("upload", "rc", "0", "上传导出附件");
        FileUploadResponse resp = fsService.uploadFile(getMultipartFile(file), uploadToken, "rc");
        logger.printMessage("resp返回:" + JSON.toJSONString(resp));
        // 发送邮件
        custService.emailForExportWebsiteValid(email,resp.getUniqueId());

        if (file.exists()) {
            file.delete();
        }
    }

    public void handBuild(Integer num) throws Exception {
        String parentFilePath = "tmp/export/网站有效性检查报告_" + System.currentTimeMillis() + "/";
        File parentFile = new File(parentFilePath);
        if(!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String filePath = parentFilePath + System.currentTimeMillis() +  ".csv";

        List<List<String>> result = new ArrayList<>();
        List<ExportWebsiteEffectivenessVo> voList = rcArchiveMapper.exportWebsiteEffectiveness(null);
        if (num != null) {
            voList = voList.subList(0,num);
        }
        // 检验网站有效性
        GamblingPyramidService.CheckExtraParam checkExtraParam = new GamblingPyramidService.CheckExtraParam();
        checkExtraParam.setSource(GamblingPyramidService.CheckExtraParam.Source.REGULAR);
        for (int i = 0; i < voList.size(); i++) {
            ExportWebsiteEffectivenessVo vo = voList.get(i);
            CommonOuterResponse<GamblingPyramidRecord> response = gamblingPyramidService.commonRiskCheck(vo.getCustomerNo(),"", RcConstants.BusinessTagerType.WEB.code,vo.getSiteUrl(),null,0L,checkExtraParam);
            GamblingPyramidRecord gamblingPyramidRecord = response.getData();
            UrlConnVo urlConnVo = gamblingPyramidRecord.getUrlConnVo();
            vo.setCheckStatus(RcConstants.CheckStatus.getMessageByCode(gamblingPyramidRecord.getVerificationResults()));
            vo.setWebsiteApp(urlConnVo.getTitle());
            vo.setRiskTag(transRiskTag(gamblingPyramidRecord.getRiskTag()));
            String remark = gamblingPyramidRecord.getResponseCode() + " " + gamblingPyramidRecord.getResponseMsg();
            vo.setRemark(remark);
            vo.setHits(urlConnVo.getKeyword());
            voList.set(i,vo);
        }
        // 写入CSV
//        for (ExportWebsiteEffectivenessVo exportWebsiteEffectivenessVo : voList) {
//            List<String> vo = new ArrayList<>();
//            for(Field field: ExportWebsiteEffectivenessVo.class.getDeclaredFields()) {
//                field.setAccessible(true);
//                if( field.getType().equals(String.class)) {
//                    vo.add(field.get(exportWebsiteEffectivenessVo) != null ? (String) field.get(exportWebsiteEffectivenessVo) : "");
//                }
//            }
//            result.add(vo);
//        }
        if(voList != null && !voList.isEmpty()) {
            logger.printMessage("result size:" + voList.size());
//            String oldContent = RcCvsUtils.readFile(file);
//            RcCvsUtils.doExport("序号,商户号,商户名称,所属平台商编号,所属平台商名称,所属代理商编号,所属代理商名称,角色,业务员,所属分公司,网址,网站或APP名称,网址检验状态,风险标签,核验结果备注", result, file, oldContent);
        }
        exportCsvNew(voList,filePath);
//        exportCsv(result,filePath);
        File file = new File(filePath);
        String uploadToken = fsService.uploadToken("upload", "rc", "0", "上传导出附件");
        FileUploadResponse resp = fsService.uploadFile(getMultipartFile(file), uploadToken, "rc");
        logger.printMessage("resp返回:" + JSON.toJSONString(resp));
        // 发送邮件
        custService.emailForExportWebsiteValid(email,resp.getUniqueId());

        if (file.exists()) {
            file.delete();
        }
    }

    /**
     * 写到CSV文件
     * @param result
     * @param filePath
     * @throws Exception
     */
    private void exportCsv(List<List<String>> result,String filePath) throws Exception {
        BufferedWriter writer = Files.newBufferedWriter(Paths.get(filePath), Charset.forName("GBK"));
//        BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.EXCEL);
        csvPrinter.printRecord("序号,商户号,商户名称,所属平台商编号,所属平台商名称,所属代理商编号,所属代理商名称,角色,业务员,所属分公司,网址,网站或APP名称,网址检验状态,风险标签,核验结果备注,命中的敏感词".split(","));
        for (int i = 0; i < result.size(); i++) {
            List<String> line = result.get(i);
            List<Object> print = new ArrayList<>();
            for (int j = 0; j < line.size(); j++) {
                String test = line.get(j);
                if (isLongNumber(test) && test.length() > 10) { // 长数字文本避免科学计数法
                    String formattedValue = test + "\t";
                    print.add(formattedValue);
                } else {
                    print.add(test);
                }
            }
            csvPrinter.printRecord(print);
        }
        csvPrinter.flush();
        csvPrinter.close();
        writer.close();
    }


    /**
     * 校验是否为纯数字文本
     * @param str
     * @return
     */
    private boolean isLongNumber(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }


    private void exportCsvNew(List<ExportWebsiteEffectivenessVo> result,String filePath) throws Exception {
        redisTemplate.opsForValue().set("webValidCheck", JSONObject.toJSONString(result),120,TimeUnit.HOURS);
        List<String> listTitles = dataExportService.getTitlesFromFieldAnnotation(ExportWebsiteEffectivenessVo.class, "1");
        CSVPrinter csvPrinter = dataExportService.downloadCsv(listTitles);

        dataExportService.printlnCsvPublic(csvPrinter,null,result,"1");
        dataExportService.createFile(csvPrinter,filePath,1);
        csvPrinter.flush();
        csvPrinter.close();
    }

    private String encodeBase64File(String path) throws Exception {
        File file = new File(path);
        FileInputStream inputFile = new FileInputStream(file);
        byte[] buffer = new byte[(int)file.length()];
        inputFile.read(buffer);
        inputFile.close();
        return new BASE64Encoder().encode(buffer);
    }

    private static void checkUrlValid(String url) {
        try {
            URL u = new URL(url);
            HttpURLConnection huc = (HttpURLConnection) u.openConnection();
            huc.setRequestMethod("GET");
            huc.setConnectTimeout(5000); // 设置连接超时，单位为毫秒
            huc.connect();
            int responseCode = huc.getResponseCode();
            System.out.println("response:" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                System.out.println("连接成功");
            } else {
                System.out.println("连接失败，响应码为：" + responseCode);
            }
            huc.disconnect();
        } catch (Exception e) {
            System.out.println("连接失败，异常信息为：" + e.getMessage());
        }
    }

    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = new FileInputStream(file);
             OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }

        return new CommonsMultipartFile(item);
    }

    public static String getTitleFromURL(String urlString) {
        try {
            URL url = new URL(urlString);
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(5000);
            conn.addRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 4.2.1; M040 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36");
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("<title>")) {
                    int startIndex = line.indexOf("<title>") + 7;
                    int endIndex = line.indexOf("</title>");
                    return line.substring(startIndex, endIndex);
                }
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        String urlString = "https://www.yii666.com/blog/135331.html";
//        String urlString = "https://dev-efps.epaylinks.cn/api/rc/gamblingPyramid/pageQuery";
        String title = getTitleFromURL(urlString);
        System.out.println("Title: " + title);
//        checkUrlValid(urlString);
    }

    private String transRiskTag(String riskTag){
        if(StringUtils.isEmpty(riskTag)){
            return "无";
        }
        String showData = riskTag.replace(RcConstants.RiskTag.VIOLATION.code,RcConstants.RiskTag.VIOLATION.comment);
        return showData;
    }

    public void exportByRedis() throws Exception {
        String parentFilePath = "tmp/export/网站有效性检查报告_" + System.currentTimeMillis() + "/";
        File parentFile = new File(parentFilePath);
        if(!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String filePath = parentFilePath + System.currentTimeMillis() +  ".csv";
        String redisValue =  redisTemplate.opsForValue().get("webValidCheck").toString();
        List voList = JSONObject.parseObject(redisValue,List.class);

        List<ExportWebsiteEffectivenessVo> result = new ArrayList<>();

        if(voList != null && !voList.isEmpty()) {
            logger.printMessage("result size:" + voList.size());
//            String oldContent = RcCvsUtils.readFile(file);
//            RcCvsUtils.doExport("序号,商户号,商户名称,所属平台商编号,所属平台商名称,所属代理商编号,所属代理商名称,角色,业务员,所属分公司,网址,网站或APP名称,网址检验状态,风险标签,核验结果备注", result, file, oldContent);
        } else {
            logger.printMessage("voList为空" + voList);
            return;
        }

        for (Object obj : voList) {
            Map map = (Map) obj;
            ExportWebsiteEffectivenessVo vo = new ExportWebsiteEffectivenessVo();
            vo.setRownum((String) map.get("rownum"));
            vo.setCustomerNo((String) map.get("customerNo"));
            vo.setCustomerName((String) map.get("customerName"));
            vo.setPlatCustomerNo((String) map.get("platCustomerNo"));
            vo.setPlatCustomerName((String) map.get("platCustomerName"));
            vo.setServiceCustomerNo((String) map.get("serviceCustomerNo"));
            vo.setServiceCustomerName((String) map.get("serviceCustomerName"));
            vo.setCategory((String) map.get("category"));
            vo.setBusinessMan((String) map.get("businessMan"));
            vo.setCompanyName((String) map.get("companyName"));
            vo.setSiteUrl((String) map.get("siteUrl"));
            vo.setWebsiteApp((String) map.get("websiteApp"));
            vo.setCheckStatus((String) map.get("checkStatus"));
            vo.setRiskTag((String) map.get("riskTag"));
            vo.setRemark((String) map.get("remark"));
            vo.setHits((String) map.get("hits"));
            result.add(vo);
        }

        exportCsvNew(result,filePath);
        File file = new File(filePath);
        String uploadToken = fsService.uploadToken("upload", "rc", "0", "上传导出附件");
        FileUploadResponse resp = fsService.uploadFile(getMultipartFile(file), uploadToken, "rc");
        logger.printMessage("resp返回:" + JSON.toJSONString(resp));
        // 发送邮件
        custService.emailForExportWebsiteValid(email,resp.getUniqueId());

        if (file.exists()) {
            file.delete();
        }
    }
}

package com.epaylinks.efps.rc.service.rccalculate.in;

import java.util.Date;
import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 单日最大入金限额
 * <AUTHOR>
 *
 */
@Service("Year-Max-IN-Amount")
public class YearMaxInAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;

	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;

	@Autowired
	private RcArchiveService rcArchiveService;
	@Autowired
	private YearMaxInAmountRc self;
	
	//单日最大入金指标
	private static final String Day_Max_In_Amount = "Day-Max-IN-Amount";
	
	@Override
	public void reset(RcLimit rcLimit) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		//每年重置年入金限额为0
		Date date = new Date();
		String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
		String key = rcCalculteBasic.getKey(tagerId , rcLimit.getDefineCode(), RcDateUtils.getCurrentYear());
		if (date.getMonth() == 0 && date.getDate() == 1) {
			//如果当天是一月一日
			self.historyDataHandler(rcLimit, date , RcLimitData.Status.FINISHED.code);
			redisTemplate.opsForValue().set(key, 0L);
		}else {
			long yearInAmount = self.historyDataHandler(rcLimit, date , RcLimitData.Status.PROCESSING.code);
			redisTemplate.opsForValue().set(key, yearInAmount);
		}
	}

//	@Logable(businessTag = "Year-Max-IN-Amount:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
		
	    redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
	                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
		String key = rcCalculteBasic.getKey(tagerId, rcLimit.getDefineCode(),RcDateUtils.getCurrentYear());
		String dayKey = rcCalculteBasic.getKey(tagerId , Day_Max_In_Amount, RcDateUtils.getCurrentDay());

		String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (StringUtils.isBlank(amountStr)) {
			throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
		}
		long amount = Long.parseLong(amountStr);
		long yearInAmount = redisTemplate.opsForValue().increment(key, 0L);
		long dayInAmount = redisTemplate.opsForValue().increment(dayKey, 0L);
		if (amount + yearInAmount + dayInAmount > Long.parseLong(rcLimit.getLimitValue())) {
			// 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInAmount + yearInAmount +"",amountStr,true,"RC交易受限", rcArchive.getArchiveType());
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}

//	@Logable(businessTag = "Year-Max-IN-Amount:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
		// TODO Auto-generated method stub
		
	}

	public long historyDataHandler(RcLimit rcLimit , Date date , String status) {

	    String tagerId = RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(rcLimit.getBusinessTagerType()) ?
                rcLimit.getBusinessTagerId() : (rcLimit.getBusinessTagerType() + "_" + rcLimit.getBusinessTagerId());
		String lastDayKey = rcCalculteBasic.getKey(tagerId, Day_Max_In_Amount, RcDateUtils.getLastDay());
		long dayInAmount = redisTemplate.opsForValue().increment(lastDayKey, 0L);
		Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);
		RcLimitData yearInAmountRcLimitData = rcLimitDataMapper.selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
		        tagerId, RcDateUtils.getYearString(selectDate), RcLimitData.Status.PROCESSING.code);
		long yearInAmount = 0;
		if (yearInAmountRcLimitData == null) {
			yearInAmountRcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), 
			        tagerId, rcLimit.getDefineCode(), dayInAmount + "", 
					date, date , status, RcDateUtils.getYearString(selectDate));
			rcLimitDataMapper.insert(yearInAmountRcLimitData);
			yearInAmount = dayInAmount;
		}else {
			yearInAmount = Long.parseLong(yearInAmountRcLimitData.getValue()) + dayInAmount;
			yearInAmountRcLimitData.setValue(yearInAmount + "");
			yearInAmountRcLimitData.setUpdatetime(date);
			yearInAmountRcLimitData.setStatus(status);
			rcLimitDataMapper.updateByPrimaryKey(yearInAmountRcLimitData);
		}
		return yearInAmount;
	}

}

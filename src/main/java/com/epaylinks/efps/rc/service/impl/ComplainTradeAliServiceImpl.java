package com.epaylinks.efps.rc.service.impl;

import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.dao.ComplainTradeMapper;
import com.epaylinks.efps.rc.domain.ComplainTrade;
import com.epaylinks.efps.rc.service.ComplainTradeAliService;
import com.epaylinks.efps.rc.vo.ComplainImportDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ComplainTradeAliServiceImpl implements ComplainTradeAliService {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private ComplainTradeMapper complainTradeMapper;

    @Override
    public Map<Integer, BatchDetail> importRiskGoData(ComplainImportDataVo importDataVo) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String batchNo = importDataVo.getBatchNo();
        int colCount = importDataVo.getTitleList().size(); // 以表头列数为数据列数校验
        if (Objects.isNull(importDataVo.getTitleList()) || Objects.isNull(importDataVo.getDataMap())) {
            throw new AppException(RcCode.PARAM_ERROR.code, "支付宝风险商户批量导入,解析数据为空");
        }
        logger.printMessage("准备进行支付宝风险商户，批量数据解析..");
        // 解析来自批量导入的数据
        for (Iterator iterator = importDataVo.getDataMap().keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            //将excel数据设置到对象中
            ComplainTrade complainTrade = new ComplainTrade();
            try{
                List<String> dataList = importDataVo.getDataMap().get(rowNo);
                if(dataList.get(0).contains("笔记录")){
                    break;
                }
                // 校验非空
                if (dataList.size() < colCount) {
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            "支付宝风险商户批量导入" + ":" + (rowNo + 1) + "行缺少数据");
                }

                if(dataList.size() > colCount){
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            "支付宝风险商户批量导入" + ":" + (rowNo + 1) + "行数据不规范");
                }

                //上游商户号
                if(!StringUtils.isBlank(dataList.get(4))){
                    List<String> remarkList = Arrays.asList(dataList.get(4).split("；"));
                    for (String tmp : remarkList){
                        if(tmp.contains("风险商户id")){
                            complainTrade.setChannelMchId(tmp.split(":")[1]);
                        }

                        if(tmp.contains("银行分配的商户ID")){
                            //排除QRA开头的银行分配的商户ID记录，此属前置平台，该行数据不导入
                            if(tmp.split(":")[1].startsWith("QRA")){
                                throw new AppException(RcCode.PARAM_ERROR.code,  "银行分配的商户ID为前置平台QRA开头不导入");
                            }
                        }
                    }
                }

                //批次号
                complainTrade.setBatchNo(batchNo);

                //上游机构
                complainTrade.setChannelType("支付宝");

                //处理时间
                complainTrade.setOrderNo(dataList.get(2));

                //风险描述
                if(StringUtils.isBlank(dataList.get(1))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "风险描述为空");
                }
                complainTrade.setRiskDesc(dataList.get(1));

                //风险识别时间
                if(StringUtils.isBlank(dataList.get(3))){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "风险识别时间为空");
                }
                try{
                    complainTrade.setRiskTime(DateUtils.parseDate(dataList.get(3).trim(), "yyyy-MM-dd HH:mm:ss"));
                }catch (Exception e){
                    throw new AppException(RcCode.PARAM_ERROR.code,
                            RcCode.PARAM_ERROR.message + ":风险识别时间格式错误.");
                }

                //投诉内容
                complainTrade.setComplainContent(dataList.get(6));

                complainTrade.setUniRemark("AliStore" + dataList.get(3).trim() + complainTrade.getChannelMchId());
                if(complainTradeMapper.hasComplainRemark(complainTrade.getUniRemark())){
                    throw new AppException(RcCode.PARAM_ERROR.code,  "上游记录已存在");
                }
                insertRecord(complainTrade);
                result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short)0, complainTrade.getChannelMchId(), "处理成功"));
            }catch (Exception e){
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, complainTrade.getChannelMchId(), (short) 1, complainTrade.getChannelMchId(), RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return result;

    }

    /**
     * 插入上游风险投诉记录
     * @param complainTrade
     */
    private void insertRecord(ComplainTrade complainTrade){
        List<ComplainTrade> list = complainTradeMapper.selectCustByChannel(complainTrade.getChannelMchId());
        if(list!=null && list.size()>0) {
            ComplainTrade custVo = list.get(0);
            complainTrade.setCustomerCode(custVo.getCustomerCode());
            ComplainTrade custInfo = complainTradeMapper.selectByCust(complainTrade.getCustomerCode());
            complainTrade.setCustName(custInfo.getCustName());
            complainTrade.setAgentCustomerCode(custInfo.getAgentCustomerCode());
            complainTrade.setAgentCustName(custInfo.getAgentCustName());
            complainTrade.setPlatCustomerCode(custInfo.getPlatCustomerCode());
            complainTrade.setPlatCustName(custInfo.getPlatCustName());
            complainTrade.setBusinessMan(custInfo.getBusinessMan());
        }
        Long id = complainTradeMapper.selectIdFromSeq();
        complainTrade.setCtId(id);
        complainTrade.setCreateTime(new Date());
        complainTradeMapper.insert(complainTrade);
    }


    /**
     * 封装导出明细
     * @param rowNo
     * @param rowName
     * @param status
     * @param relateId
     * @param remarks
     * @return
     */
    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }
}

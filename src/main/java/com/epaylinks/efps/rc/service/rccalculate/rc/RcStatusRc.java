package com.epaylinks.efps.rc.service.rccalculate.rc;

import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 风控状态计算指标
 * <AUTHOR>
 *
 */
@Service("RC-STATUS")
public class RcStatusRc implements RcCalculate , RcIndexReset , RcIndexAddValue{
	@Autowired
	private RcArchiveMapper rcArchiveMapper;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;

//	@Logable(businessTag = "RC-STATUS:calculateValue")
	@Override
	public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void reset(RcLimit rcLimit) {
		// TODO Auto-generated method stub
		
	}

//	@Logable(businessTag = "RC-STATUS:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {

	    RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(rcLimit.getBusinessTagerId());
		if (rcArchive.getRcStatus().equals(RcConstants.RcStatus.NORMAL.code)) {
			return CommonOuterResponse.SUCCEE;
		}
		rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),"冻结","/","/",false,"RC风控冻结", rcArchive.getArchiveType());
		return "RC风控冻结";
	}

}

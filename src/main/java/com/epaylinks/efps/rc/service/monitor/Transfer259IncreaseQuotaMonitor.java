package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.Transfer259LimitService;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.vo.EBankConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【PJ23】259 代付交易管控，累计不可代付额度
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/19 15:58
 */
@Service
public class Transfer259IncreaseQuotaMonitor implements TransactionMonitor {
    @Autowired
    private CommonLogger commonLogger;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private Transfer259LimitService limitService;

    @Autowired
    private RcArchiveService rcArchiveService;

    private boolean isRecharge(String customerCode) {
        try {
            EBankConfigVo vo = rcArchiveService.queryEBankConfig(customerCode);
            return vo != null && "2".equals(vo.getEBankSetting());
        } catch (Exception e) {
            commonLogger.printLog(e);
            return false;
        }
    }

    @Override
    public void monitor(RcTxsOrder order) {
        //网银充值，收单发一次通知，审核通过时也发一次通知。收单的不计算额度。
        if ("EnterpriseUnion".equals(order.getBusinessCode()) && isRecharge(order.getCustomerCode())) {
            return;
        }

        long cantTransferQuota = order.getAmount() * (100 - rcArchiveService.getWithdrawProportion(order.getCustomerCode())) / 100;

        rcCalculteBasic.redisIncr(limitService.getRedisKey(order.getCustomerCode()), cantTransferQuota);
    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        //非FZ的收单交易
        return Constants.PayState.SUCCESS.code.equals(order.getPayState()) &&
                Constants.rcBusinessType.GATEWAY_PAY.code.equals(order.getBusinessType()) &&
                order.getTransactionNo() != null &&
                !order.getTransactionNo().startsWith(TransactionType.FZ.no) &&
                order.getAmount() != null &&
                order.getCustomerCode() != null &&
                order.getTargetIdMap().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code) != null; //只处理商户，个人不处理
    }
}

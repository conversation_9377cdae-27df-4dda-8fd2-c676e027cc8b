package com.epaylinks.efps.rc.service.rccalculate.rc;

import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;


@Service("RC-BALANCE")
public class RcBalance implements RcCalculate , RcIndexReset , RcIndexAddValue{

//    @Logable(businessTag = "RC-BALANCE:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> indexs) {
        // TODO Auto-generated method stub

    }

    @Override
    public void reset(RcLimit rcLimit) {
        // TODO Auto-generated method stub

    }

//    @Logable(businessTag = "RC-BALANCE:calculate")
    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        // TODO Auto-generated method stub
        return CommonOuterResponse.SUCCEE;
    }


}

package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.controller.response.PaySettingResponse;
import com.epaylinks.efps.rc.controller.response.RiskInfoExportResponse;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.RcCalculateLogMapper;
import com.epaylinks.efps.rc.dao.RcLimitMapper;
import com.epaylinks.efps.rc.dao.TemporaryLimitMapper;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.fs.FileUploadResponse;
import com.epaylinks.efps.rc.kafka.KafkaProducer;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.util.CheckUtils;
import com.epaylinks.efps.rc.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class RcArchiveServiceImpl implements RcArchiveService {

	@Autowired
	private RcArchiveMapper rcArchiveMapper;
	@Autowired
	private RcLimitService rcLimitService;
	@Autowired
    private RcLimitMapper rcLimitMapper;
	@Autowired
    private AccService accService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private RcCalculateLogMapper rcCalculateLogMapper;
    @Autowired
    private CommonLogger commonLogger;
    @Autowired
    private RcAuditRecordService rcAuditRecordService;
    @Autowired
    private OtherService otherService;
    @Autowired
    private KafkaProducer kafkaProducer;
    @Autowired
    private LogService logService;
    @Autowired
    private RcOperateLogService rcOperateLogService;
    @Autowired
    private CustService custService;
    @Autowired
    private TemporaryLimitMapper temporaryLimitMapper;
    @Autowired
    private FsService fsService;
    @Autowired
    private RedisDataTransService redisDataTransService;
    
    private static final String RC_STATUS_CHANGE_KEY = "rcStatusChange"; // 风控系统更新账户状态、风控状态 kafka key
    private static final String RC_BIND_CARD_CHANGE_KEY = "rcBindCardChange"; // 风控系统更新报备标识kafka key
    private final SimpleDateFormat format = new SimpleDateFormat("HH:mm");

    
	@Override
	public RcArchive selectByCodeOrName(String code, String name) {

	    if (StringUtils.isBlank(code) && StringUtils.isBlank(name)) {
	        throw new AppException(RcCode.RC_ARG_ERROR.code, RcCode.RC_ARG_ERROR.message);
	    }
		RcArchive rcArchive = rcArchiveMapper.selectByCodeOrName(code,name);

		return rcArchive;
	}

    @Override
    public RcArchive selectByCodeOrName(String code, String name, Long userId) {
        if (StringUtils.isBlank(code) && StringUtils.isBlank(name)) {
            throw new AppException(RcCode.RC_ARG_ERROR.code, RcCode.RC_ARG_ERROR.message);
        }
        Map param = new HashMap();
        param.put("levelCode",code);
        param.put("name",name);

        setPermissionParam(param,userId);
        RcArchive rcArchive = rcArchiveMapper.selectByMap(param);

        return rcArchive;
    }

    @Override
    public RcArchive selectByTypeAndCode(String type, String code) {
        if (StringUtils.isBlank(code) && StringUtils.isBlank(type)) {
            throw new AppException(RcCode.RC_ARG_ERROR.code, RcCode.RC_ARG_ERROR.message);
        }
        RcArchive rcArchive = rcArchiveMapper.selectByTypeAndCode(type, code);

        return rcArchive;
    }

	@Override
	public boolean updateStatus(RcLimit rcLimit,RcArchive rcArchive) {
		//先更新ORACLE
		int i = rcArchiveMapper.updateByPrimaryKeySelective(rcArchive) ;
		if(i != 1){
			return false;
		}

		//再更新redis
		rcLimitService.updateLimit(rcLimit);

		return true;
	}
	
	@Override
    public boolean updateRcArchiveSelective(RcArchive rcArchive) {
        //先更新ORACLE
        int i = rcArchiveMapper.updateByPrimaryKeySelective(rcArchive) ;
        if(i != 1){
            return false;
        }
        return true;
    }
	
	@Override
    public boolean updateRcArchiveByPrimaryKey(RcArchive rcArchive) {
        //先更新ORACLE
        int i = rcArchiveMapper.updateByPrimaryKey(rcArchive) ;
        if(i != 1){
            return false;
        }
        return true;
    }

	@Override
	public String selectInfoId(String customerCode) {
		return rcArchiveMapper.selectInfoId(customerCode);
	}

	@Override
	public RcArchive selectById(Long rcArchiveId) {
		return rcArchiveMapper.selectByPrimaryKey(rcArchiveId);
	}

	@Override
	public PageResult<Map<String, String>> pageLimitQuery(String archiveCode, int pageNum, int pageSize) {
		PageResult<Map<String, String>> result = new PageResult<>();

		//总记录数
		int total = rcArchiveMapper.totalLimitQuery(archiveCode);
		result.setTotal(total);

		//当前页面数据
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;
		List<Map<String,String>> list = rcArchiveMapper.listLimitQuery(archiveCode,startNum,endNum);

		//数据处理，如果type为Long，则为金额
		for (Map<String,String> map : list){
			if("Long".equals(map.get("type"))){
				BigDecimal b = new BigDecimal(map.get("limitValue"));
				b = b.divide(new BigDecimal(100));
				map.put("limitValue",b.toString());
			}
		}

		result.setRows(list);

		return result;
	}

	@Override
	public void insert(RcArchive rcArchive) {
		//先插入数据库
		 rcArchiveMapper.insertSelective(rcArchive);
	}

	@Override
	public void updateCusStatus(String customerCode, String oldStatus, String newStatus) {
		rcArchiveMapper.updateCusStatus(customerCode,oldStatus,newStatus);
	}

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public CommonOuterResponse updateRcBalance(RcArchive rcArchive, Long amount, User user) {
	    rcArchiveMapper.updateByPrimaryKeySelective(rcArchive);
	    DefineCode rcBalanceDefine = DefineCode.RC_BALANCE;
        //查找商户当前风控冻结金额
        RcLimit rcLimit = rcLimitService.queryLimit(rcBalanceDefine.defineId,rcArchive.getArchiveCode());

        if(rcLimit == null){
            if (amount < 0){
                return CommonOuterResponse.fail(RcCode.RC_BALANCE_AMOUNT_EXCEPTION.code,RcCode.RC_BALANCE_AMOUNT_EXCEPTION.message);
            }
            rcLimit = new RcLimit();
            rcLimit.setUserId(user.getUid());
            rcLimit.setUserName(user.getName());
            rcLimit.setBusinessTagerId(rcArchive.getArchiveCode());
            rcLimit.setLimitValue(amount+"");
            rcLimit.setBusinnesType(rcBalanceDefine.businessType);
            rcLimit.setCreateTime(new Date());
            rcLimit.setDefineCode(rcBalanceDefine.defineCode);
            rcLimit.setLimitId(sequenceService.nextValue("rc"));
            rcLimit.setBusinessTagerType(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            rcLimit.setDefineId(rcBalanceDefine.defineId);
            rcLimit.setLimitType("Long");
            rcLimit.setUnit(rcBalanceDefine.unit);
            rcLimitService.save(rcLimit);
        }else {
            Long limitValue = Long.valueOf(rcLimit.getLimitValue());
            if (limitValue + amount < 0){
                return CommonOuterResponse.fail(RcCode.RC_BALANCE_AMOUNT_EXCEPTION.code,RcCode.RC_BALANCE_AMOUNT_EXCEPTION.message);
            }
            rcLimit.setLimitValue(limitValue+amount+"");
            rcLimitService.updateLimit(rcLimit);
        }

        //调用ACC，如果ACC更新失败则会抛出异常，做回滚处理
        try {
            CommonOuterResponse response = accService.updateRcBalance(rcArchive.getArchiveCode(),amount);
            if (!response.getReturnCode().equals(CommonOuterResponse.SUCCEE)){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }catch (AppException e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonOuterResponse.fail(e.getErrorCode(),e.getMessage());
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonOuterResponse.fail(RcCode.RC_FEIGIN_EXCEPTION.code,RcCode.RC_FEIGIN_EXCEPTION.message+"acc");
        }

        return CommonOuterResponse.success();
    }

	@Override
	public void updateRcLevel(RcArchive rcArchive, String level) {
		//修改风控状态
        rcArchive.setUpdateTime(new Date());
        rcArchive.setRcLevel(level);
        int i = rcArchiveMapper.updateByPrimaryKey(rcArchive);
        if (i == 1){
            //更新风控等级
            RcLimit rcLimit = rcLimitMapper.queryLimit(DefineCode.RC_LEVEL.defineId, rcArchive.getArchiveType(), rcArchive.getArchiveCode());
            if(rcLimit == null){
                //存量商户创建一个新的风控指标
                rcLimit = new RcLimit();
                rcLimit.setLimitId(sequenceService.nextValue("rc"));
                rcLimit.setCreateTime(new Date());
                rcLimit.setUserId(rcArchive.getUserId());
                rcLimit.setUserName(rcArchive.getUserName());
                rcLimit.setLimitType("String");
                rcLimit.setLimitValue(level);
                rcLimit.setBusinessTagerType(rcArchive.getArchiveType());
                rcLimit.setBusinessTagerId(rcArchive.getArchiveCode());
                rcLimit.setBusinnesType(DefineCode.RC_LEVEL.businessType);
                rcLimit.setDefineId(DefineCode.RC_LEVEL.defineId);
                rcLimit.setDefineCode(DefineCode.RC_LEVEL.defineCode);
                rcLimit.setUnit(DefineCode.RC_LEVEL.unit);
                rcLimitService.save(rcLimit);
            }else {// 更新
                rcLimit.setLimitValue(level);
                rcLimitService.updateLimit(rcLimit);
            }
            //先删除redis中的数据
            rcLimitService.updateRcLevel(rcArchive.getArchiveCode());
            //再删除原有风险等级指标
            rcLimitMapper.deleteLevelLimit(rcArchive.getArchiveCode());

        }
    }

    @Override
    public PageResult<RcArchivePageResponse> pageQuery(Map<String, Object> paramMap) {
        
        PageResult<RcArchivePageResponse> result = new PageResult<>();
	    //查总数
        int total = rcArchiveMapper.totalPageQuery(paramMap);
        if (total < 1) {
            result.setTotal(total);
            result.setRows(new ArrayList<RcArchivePageResponse>());
            return result;
        }

        //分页查询
        List<RcArchivePageResponse> data = rcArchiveMapper.pageQuery(paramMap);
        if (!data.isEmpty()) {
            data.forEach(archive ->{
               if (RcConstants.BusinessTagerType.TERM.code.equals(archive.getArchiveType())){ // 查询终端序列号  
                   archive.setMachineCode(rcArchiveMapper.queryTermMachineCode(archive.getArchiveCode()));
               } else if (RcConstants.BusinessTagerType.PERSON.code.equals(archive.getArchiveType())){ // 查询客户号  
                   archive.setClientNo(rcArchiveMapper.queryClientNo(archive.getArchiveCode()));
               }
            });
        }

        result.setTotal(total);
        result.setRows(data);
        return result;

    }

    @Override
    public PageResult<RcCalculateLog> queryCalculateLog(int startNum, int endNum, Map<String, String> map) {
        PageResult<RcCalculateLog> pageResult = new PageResult<>();
            int total = rcCalculateLogMapper.pageQueryCount(map.get("targetTypeCode"),map.get("businessTargetId"),map.get("transactionNo"),map.get("limitTypeCode"),map.get("startTime"),map.get("endTime"));
            pageResult.setTotal(total);
            List<RcCalculateLog> rows = null;
            if (total == 0) {
                pageResult.setRows(new ArrayList<>());
            }else {
                if ("1".equals(map.get("export"))) {// 导出，设置起止行
                    startNum = 1;
                    endNum = total;
                }
                rows = rcCalculateLogMapper.pageQuery(startNum,endNum,map.get("targetTypeCode"),map.get("businessTargetId"),map.get("transactionNo"),map.get("limitTypeCode"),map.get("startTime"),map.get("endTime"));

                for (RcCalculateLog rcCalculateLog : rows){
                    DefineCode defineCode = DefineCode.getDefineCode(rcCalculateLog.getRcLimitDefineCode());
                    RcConstants.RcLimitType rcLimitType = RcConstants.RcLimitType.getLimitType(rcCalculateLog.getRcLimitType());
                    RcConstants.RCTargetType targetType = RcConstants.RCTargetType.getTargetType(rcCalculateLog.getBusinessTargetType());
                    if (DefineCode.WITHDRAW_DAY_MAX_COUNT.defineCode.equals(rcCalculateLog.getRcLimitDefineCode())) { // 特殊处理
                        rcCalculateLog.setRcLimitDefineCode(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcCalculateLog.getBusinessTargetType()) 
                                ? "商户当日累计转账限次" : "证件当日累计转账限次");
                    } if (DefineCode.DAY_MAX_OUT_COUNT.defineCode.equals(defineCode.defineCode)) { // 特殊处理
                        rcCalculateLog.setRcLimitDefineCode(RcConstants.RCTargetType.CUSTOMER_CODE.code.equals(rcCalculateLog.getBusinessTargetType()) 
                                ? "商户当日累计提现限次" : "证件当日累计提现限次");
                    } else {
                        rcCalculateLog.setRcLimitDefineCode(defineCode.name);    
                    }
                    rcCalculateLog.setBusinessTargetType(targetType.message);
                    rcCalculateLog.setRcLimitType(rcLimitType.message);
                    try {
                        if (rcCalculateLog.getLimitUnit().equals("Long")){
                            long transactionValue  = Long.valueOf(rcCalculateLog.getTransactionValut());
                            rcCalculateLog.setTransactionValut(new BigDecimal(transactionValue).divide(new BigDecimal(100)) +"元");
                            long rcLimitValue  = Long.valueOf(rcCalculateLog.getRcLimitValue());
                            long totalValue = rcCalculateLog.getCumulativeValue().equals("/") ? -1 : Long.valueOf(rcCalculateLog.getCumulativeValue());
                            rcCalculateLog.setRcLimitValue(new BigDecimal(rcLimitValue).divide(new BigDecimal(100)) +"元");
                            if (totalValue != -1){
                                rcCalculateLog.setCumulativeValue(new BigDecimal(totalValue).divide(new BigDecimal(100)) +"元");
                            }
                        }

                        //这里因为有几个特殊的类型，需要转换
                        if (rcCalculateLog.getRcLimitType().equals(RcConstants.RcLimitType.BLACK.code)|| rcCalculateLog.getRcLimitDefineCode().equals(DefineCode.ACCOUNT_STATUS.defineCode)){
                            long transactionValue  = Long.valueOf(rcCalculateLog.getTransactionValut());
                            rcCalculateLog.setTransactionValut(new BigDecimal(transactionValue).divide(new BigDecimal(100)) +"元");
                        }
                    }catch (Exception e){
                        //如果是转换失败，就是什么事情都不做
                        commonLogger.printLog(e.getMessage());
                    }
                }
                pageResult.setRows(rows);
            }
        pageResult.setCode(CommonOuterResponse.SUCCEE);
        return pageResult;
    }
    

    @Override
    public PageResult<RcCalculateLog> exportCalculateLog(Map<String, String> map) {

        map.put("export", "1");
        return queryCalculateLog(1, 1, map);
    }

    @Override
    public void saveCertRcArchive(String certificateType, String certificateNo, Long userId) {
        
        RcArchive certRcArchive = rcArchiveMapper.selectByTypeAndCode(certificateType, certificateNo);
        if (certRcArchive != null){
            return;
        }
        certRcArchive = new RcArchive();
        certRcArchive.setArchiveId(sequenceService.nextValue("rc"));
        certRcArchive.setUserId(userId);
        certRcArchive.setUserName(null);
        certRcArchive.setAccountStatus("0");
        certRcArchive.setRcStatus("0");
        certRcArchive.setCusStatus("1");
        certRcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
        certRcArchive.setArchiveType(certificateType);
        certRcArchive.setArchiveCode(certificateNo);
        certRcArchive.setArchiveName(RcConstants.BusinessTagerType.getCommentByCode(certificateType));
        certRcArchive.setRcLevel("LOW_LEVEL");
        certRcArchive.setRegTime(new Date());
        certRcArchive.setCertificateNo(certificateNo);    
        //保存证件档案
        rcArchiveMapper.insert(certRcArchive);

    }

    @Override
    public void saveClientNoRcArchive(String clientNo, Integer type, String mcc, String industry, Long registeredCapital, Long userId) {
        RcArchive clientRcArchive = rcArchiveMapper.selectByTypeAndCode(RcConstants.BusinessTagerType.CLIENT_NO.code, clientNo);
        if (clientRcArchive != null){
            return;
        }
        clientRcArchive = new RcArchive();
        clientRcArchive.setArchiveId(sequenceService.nextValue("rc"));
        clientRcArchive.setUserId(userId);
        clientRcArchive.setUserName(null);
        clientRcArchive.setAccountStatus("0");
        clientRcArchive.setRcStatus("0");
        clientRcArchive.setCusStatus("1");
        clientRcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
        clientRcArchive.setArchiveType(RcConstants.BusinessTagerType.CLIENT_NO.code);
        clientRcArchive.setArchiveCode(clientNo);
        clientRcArchive.setArchiveName(RcConstants.BusinessTagerType.CLIENT_NO.message);
        clientRcArchive.setRcLevel("LOW_LEVEL");
        clientRcArchive.setRegTime(new Date());
        clientRcArchive.setType(type);
        clientRcArchive.setRegisteredCapital(registeredCapital);
        if (Constants.CustomerType.BUSINESSMAN.code == type || Constants.CustomerType.MICRO.code == type) {
            clientRcArchive.setMcc(mcc);
        } else if (Constants.CustomerType.ENTERPRISE.code == type || Constants.CustomerType.ABROAD.code == type
                || Constants.CustomerType.GOVERNMENT.code == type || Constants.CustomerType.OTHERS.code == type) {
            clientRcArchive.setIndustry(industry);
        }

        //保存证件档案
        rcArchiveMapper.insert(clientRcArchive);
    }

    @Override
    public void saveRcArchive(String archiveType, String archiveCode ,String archiveName, Long userId) {
        
        RcArchive rcArchive = rcArchiveMapper.selectByTypeAndCode(archiveType, archiveCode);
        if (rcArchive == null){ // 新增
            rcArchive = new RcArchive();
            rcArchive.setArchiveId(sequenceService.nextValue("rc"));
            rcArchive.setUserId(userId);
            rcArchive.setUserName(null);
            rcArchive.setAccountStatus("0");
            rcArchive.setRcStatus("0");
            rcArchive.setCusStatus("1");
            rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
            rcArchive.setArchiveType(archiveType);
            rcArchive.setArchiveCode(archiveCode);
            rcArchive.setArchiveName(archiveName);
            if (archiveName == null) {
                rcArchive.setArchiveName(RcConstants.BusinessTagerType.getCommentByCode(archiveType));
            }
            rcArchive.setRcLevel("LOW_LEVEL");
            rcArchive.setRegTime(new Date());
            //保存证件档案
            rcArchiveMapper.insert(rcArchive);
        } else {
            // 更新，只能更新名称
            rcArchive.setArchiveName(archiveName);
            rcArchive.setUpdateTime(new Date());
            rcArchive.setUserId(userId);
            rcArchiveMapper.updateByPrimaryKeySelective(rcArchive);
        }
       
    }


    @Override
    public RcAuditRecord saveChangeStatusAuditRecord(Long rcArchiveId, String statusName, String newStatus, String reason, Long userId) {
        
        RcArchive record = rcArchiveMapper.selectByPrimaryKey(rcArchiveId);
        if (record == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        AuditStatusVo oldVo = new AuditStatusVo();
        oldVo.setStatus(record.getAccountStatus());
        oldVo.setReason(record.getAccountStatusReason());
        oldVo.setStatusName(statusName);
        
        AuditStatusVo newVo = new AuditStatusVo();
        newVo.setStatusName(statusName);
        newVo.setStatus(newStatus);
        newVo.setReason(reason);
        
        // RcArchive不需要加审核状态字段记录
        
        // 保存待审核记录
        return rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.ACCOUNT_STATUS.code, 
                rcArchiveId, oldVo.toString(), newVo.toString(), RcConstants.AuditActionType.UPDATE.code, reason, userId,null);
    }
    
    /**
     *  审核后或不需审核时处理
     * @param rcArchiveId
     * @param statusName
     * @param status
     * @param reason
     * @return
     */
    @Override
    public CommonOuterResponse saveRcArchiveStatus(Long rcArchiveId, String statusName, String status, String reason, String remark, Long userId,Boolean batchFlag,List<JSONObject> jsonObjects) {

        RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(rcArchiveId);
        if (rcArchive == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }

        // 用于日志展示
        String jg = "";
        // 操作的内容
        String operateContent = "";
        // 用于记录原值
        String origValue = "";
        String origReason = "";
        // 根据UserId查找user
        User user = otherService.selectUserById(userId);
        if (user == null) {
            return CommonOuterResponse.fail(RcCode.USER_NOT_EXIXT.code, RcCode.USER_NOT_EXIXT.message);
        }

        // 根据statusName确定修改的是哪一个状态
        RcArchive.Status s = null;
        for (RcArchive.Status s1 : RcArchive.Status.values()) {
            if (statusName.equals(s1.statusName)) {
                s = s1;
                break;
            }
        }
        if (s == null) {
            return CommonOuterResponse.fail(RcCode.ARCHIVE_STATUS_EXCEPTION.code, RcCode.ARCHIVE_STATUS_EXCEPTION.message);
        }

        rcArchive.setUserId(userId);
        rcArchive.setUserName(user.getName());
        if (RcArchive.Status.RC.statusName.equals(statusName)) {
            operateContent = "风控状态";
            origValue = "0".equals(rcArchive.getRcStatus()) ? "正常" : "冻结";
            origReason = rcArchive.getRcStatusReason();
            // 修改风控状态
            rcArchive.setRcStatus(status);
            switch (status) {
            case "0":
                jg = "正常";
                break;
            case "1":
                jg = "冻结";
                break;
            }
            rcArchive.setRcStatusReason(reason);
        } else if (RcArchive.Status.ACCOUNT.statusName.equals(statusName)) {
            operateContent = "账户状态";
            origValue = "0".equals(rcArchive.getAccountStatus()) ? "正常"
                    : ("1".equals(rcArchive.getAccountStatus()) ? "冻结"
                            : ("2".equals(rcArchive.getAccountStatus()) ? "止付" : "禁止入金"));
            origReason = rcArchive.getAccountStatusReason();
            // 修改账户状态
            rcArchive.setAccountStatus(status);
            switch (status) {
            case "0":
                jg = "正常";
                break;
            case "1":
                jg = "冻结";
                break;
            case "2":
                jg = "止付";
                break;
            case "3":
                jg = "禁止入金";
                break;
            }
            rcArchive.setAccountStatusReason(reason);
            // 止付/禁止入金修改商户状态为正常
            if ((RcConstants.AccountStatus.BANDOUT.code.equals(status) || RcConstants.AccountStatus.BANDIN.code.equals(status))
                    && "2".equals(rcArchive.getCusStatus())) {
                rcArchive.setCusStatus("1");
            }
        } else if (RcArchive.Status.CUSTOMER.statusName.equals(statusName)) {// 运营门户操作商户注销，RC同步记录
            operateContent = s.alias;
            origValue = RcConstants.RcCustomerStatus.getMessageByCode(rcArchive.getCusStatus());

            // 修改商户状态
            rcArchive.setCusStatus(status);
            jg = RcConstants.RcCustomerStatus.getMessageByCode(status);
        }

        // 查找Limit
        DefineCode defineCode = null;
        for (DefineCode code : DefineCode.values()) {
            if (s.statusCode.equals(code.defineCode)) {
                defineCode = code;
                break;
            }
        }

        RcLimit rcLimit = rcLimitService.queryLimit(defineCode.defineId,rcArchive.getArchiveType(), rcArchive.getArchiveCode());
        if (rcLimit == null) {
            // 存量商户创建一个新的风控指标
            rcLimit = new RcLimit();
            rcLimit.setLimitId(sequenceService.nextValue("rc"));
            rcLimit.setCreateTime(new Date());
            rcLimit.setUserId(userId);
            rcLimit.setUserName(user.getName());
            rcLimit.setLimitType("String");
            rcLimit.setLimitValue(status);
            rcLimit.setBusinessTagerType("005");
            rcLimit.setBusinessTagerId(rcArchive.getArchiveCode());
            rcLimit.setBusinnesType(defineCode.businessType);
            rcLimit.setDefineId(defineCode.defineId);
            rcLimit.setDefineCode(defineCode.defineCode);
            rcLimitService.save(rcLimit);
        } else {
            // 新商户
            rcLimit.setUserId(userId);
            rcLimit.setUserName(user.getName());
            rcLimit.setLimitValue(status);
        }

        boolean flag = this.updateStatus(rcLimit, rcArchive);
        if (!flag) {
            return CommonOuterResponse.fail(RcCode.SYSTEM_EXCEPTION.code, RcCode.SYSTEM_EXCEPTION.message);
        }

        // region 插入操作日志
        RcOperateLog log = new RcOperateLog();
        Date modifyDate = new Date();
        // 在风险档案菜单里
        log.setPermId("80201");
        log.setCode(rcArchive.getArchiveCode());
        log.setName(rcArchive.getArchiveName());
        log.setType("2");
        log.setOperator(String.valueOf(userId));
        log.setOperateTime(modifyDate);
        log.setOperateContent(operateContent);
        log.setOrigValue(origValue);
        log.setNewValue(jg);
        rcOperateLogService.insert(log);

        if (origReason == null || !origReason.equals(reason)) {
            log = new RcOperateLog();
            // 在风险档案菜单里
            log.setPermId("80201");
            log.setCode(rcArchive.getArchiveCode());
            log.setName(rcArchive.getArchiveName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent(operateContent + "修改原因");
            log.setOrigValue(origReason);
            log.setNewValue(reason);
            rcOperateLogService.insert(log);
        }
        if (RcArchive.Status.CUSTOMER.statusName.equals(statusName)) {
            log = new RcOperateLog();
            log.setPermId("80201");
            log.setCode(rcArchive.getArchiveCode());
            log.setName(rcArchive.getArchiveName());
            log.setType("2");
            log.setOperator(String.valueOf(userId));
            log.setOperateTime(modifyDate);
            log.setOperateContent("注销备注");
            log.setOrigValue("");
            log.setNewValue(remark);
            rcOperateLogService.insert(log);
        }

        // endregion

        // 记录操作日志
        OpLogHandle.setOpContent("用户：" + user.getName() + "修改了商户：" + rcArchive.getArchiveCode() + "的" + s.alias + "改为:"
                + jg + ",原因是:" + reason);

        // kafka发送状态更新消息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("customerCode", rcArchive.getArchiveCode());
        jsonObject.put("statusName", statusName);
        jsonObject.put("status", status);
        jsonObject.put("reason", reason);
        jsonObject.put("userId", userId);

        // 批量操作等全部成功后再发送kafka
        if (batchFlag) {
            jsonObjects.add(jsonObject);
            return CommonOuterResponse.success("更新成功");
        }

        kafkaProducer.send("CUS_CustomerChange", RC_STATUS_CHANGE_KEY, jsonObject);

        return CommonOuterResponse.success("更新成功");
    }

    @Override
    public void auditChangeStatus(Long audId, Short auditResult, Long userId) {
        
        RcAuditRecord auditRecord = rcAuditRecordService.selectByPrimaryId(audId);
        if (auditRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        if (!RcConstants.AuditStatus.WAITING.code.equals(auditRecord.getAuditResult())) {
            throw new AppException(RcCode.AUD_EXCEPTION.code, RcCode.AUD_EXCEPTION.message);
        }
        
        if (RcConstants.AuditStatus.SUCCESS.code.equals(auditResult)) {// 审核成功
            
           if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())){
               AuditStatusVo newValueObj = JSONObject.parseObject(auditRecord.getNewValue(), AuditStatusVo.class);
               this.saveRcArchiveStatus(auditRecord.getTargetId(), newValueObj.getStatusName(), newValueObj.getStatus(), newValueObj.getReason(),null, auditRecord.getOperId(),false,null);
            }
            
        } else {// 驳回
            if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())){
                // 更新时，更审核记录状态即可，不需其他处理
            }
        }
        // 更新审核记录状态
        rcAuditRecordService.updateAuditRecordStatus(audId, auditResult, userId);
        
    }

    @Override
    @Transactional
    @Logable(businessTag = "RcArchiveServiceImpl.batchAuditRecord")
    public void batchAuditRecord(String audIds, Short auditResult,String remark, Long userId,String autoAudit) {
        CommonOuterResponse response = new CommonOuterResponse();
        if (StringUtils.isBlank(audIds)) {
            throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
        }
        CheckUtils.paramCheckLength(remark,200,"备注");

        String[] paramStrs = audIds.split(",");
        // 交易设置列表
        List<String> newValues = new ArrayList<>();
        // 出金报备设置发送kafka列表
        List<BindCardVo> bindCardVos = new ArrayList<>();
        // 风险等级同步
        Map<String,String> syncLevelMap = new HashMap<>();
        // 更新风控和账户状态需发送的kafka列表
        List<JSONObject> jsonObjects = new ArrayList<>();
        // 反诈审核后需自动审核拒绝的记录
        List<Long> refuseList = new ArrayList<>();
        // 遍历审核
        for (int i = 0; i < paramStrs.length; i++) {
            Long audId = Long.parseLong(paramStrs[i]);
            RcAuditRecord rcAuditRecord = rcAuditRecordService.selectByPrimaryId(audId);
            if (rcAuditRecord == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
            }
            // 判断状态
            if (!(rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0)) {
                throw new AppException(RcCode.AUD_EXCEPTION.code,RcCode.AUD_EXCEPTION.message);
            }
            RcArchive rcArchive = selectById(rcAuditRecord.getTargetId());
            String newValue = rcAuditRecord.getNewValue();
            // 审核类型、需判断是否有审核权限
            String type = rcAuditRecord.getTargetType();
            String auditFlag = null;
            if (auditResult.compareTo(RcConstants.AuditStatus.SUCCESS.code) == 0) {
                switch (type) {
                    case "RISK_LEVEL" : // 风险等级设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskLevelAudit");
                        RcLevelVo rcLevelVo = JSONObject.parseObject(newValue,RcLevelVo.class);
                        rcArchive.setUserId(rcLevelVo.getUserId());
                        rcArchive.setUserName(rcLevelVo.getUserName());
                        rcArchive.setRcLevel(rcLevelVo.getRcLevel());
                        rcArchive.setRcLevelReason(rcLevelVo.getRcLevelReason());
                        this.updateRcLevel(rcArchive,rcLevelVo.getRcLevel());
                        syncLevelMap.put(rcArchive.getArchiveCode(),rcLevelVo.getRcLevel());
                        break;
                    case "RISK_CONTROL_STATUS" : // 风控状态
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskControlAudit");
                        AuditStatusVo auditStatusVo = JSONObject.parseObject(newValue,AuditStatusVo.class);
                        this.saveRcArchiveStatus(rcArchive.getArchiveId(),auditStatusVo.getStatusName(),auditStatusVo.getStatus(),auditStatusVo.getReason(),null,auditStatusVo.getUserId(),true,jsonObjects);
                        break;
                    case "ACCOUNT_STATUS" : // 账户状态
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskAccountAudit");
                        // 反诈平台自动响应调用账户冻结使用userid=0不会被拦截，审核账户冻结未审核记录时需判断是否为最新一笔修改
                        RcAuditRecord latestRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.ACCOUNT_STATUS.code,rcAuditRecord.getTargetId());
                        if (!latestRecord.getId().equals(rcAuditRecord.getId()) && userId != 0L) { //
                            throw new AppException(RcCode.AUDIT_FAIL.code,"当前状态与流程申请时初始状态不一致，该申请将标记为作废处理，若需操作请重新发起申请");
                        }
                        AuditStatusVo newValueObj = JSONObject.parseObject(newValue, AuditStatusVo.class);
                        this.saveRcArchiveStatus(rcAuditRecord.getTargetId(), newValueObj.getStatusName(), newValueObj.getStatus(), newValueObj.getReason(), null,newValueObj.getUserId(),true,jsonObjects);
                        // 反诈-需自动审核拒绝的ID列表
                        if (userId == 0L) {
                            List<Long> waitList = rcAuditRecordService.queryListByTargetTypeAndId(RcConstants.AuditTargetType.ACCOUNT_STATUS.code,rcAuditRecord.getTargetId(),audId);
                            refuseList.addAll(waitList);
                        }
                        break;
                    case "RISK_CONTROL_FREEZING" : // 风控冻结金额
                        auditFlag = otherService.selectAuditAuthRC(userId,"controlFreezingAudit");
                        RcFreeingVo rcFreeingVo = JSON.parseObject(newValue,RcFreeingVo.class);
                        rcArchive.setRcBalanceReason(rcFreeingVo.getReason());
                        User user = otherService.selectUserById(userId);
                        if (rcArchive == null || user == null){
                            throw new AppException(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
                        }
                        this.updateRcBalance(rcArchive,rcFreeingVo.getAmount(),user);
                        break;
                    case "WITHDRAWAL_REPORT" : // 出金报备设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"withDrawalReportAudit");
                        BindCardVo bindCardVo = JSON.parseObject(newValue,BindCardVo.class);
                        rcArchive.setUserId(bindCardVo.getUserId());
                        rcArchive.setUserName(bindCardVo.getUserName());
                        rcArchive.setBindCard(bindCardVo.getBindCard());
                        rcArchive.setBindCardReason(bindCardVo.getReason());
                        rcArchive.setBindCardRatio(bindCardVo.getBindCardRatio());
                        boolean flag = this.updateRcArchiveSelective(rcArchive);
                        if (!flag){
                            throw new AppException(RcCode.SYSTEM_EXCEPTION.code,RcCode.SYSTEM_EXCEPTION.message);
                        }
                        bindCardVo.setCustomerNo(rcArchive.getArchiveCode());
                        bindCardVos.add(bindCardVo);
                        break;
                    case "AUTHENTICATION_FINANCING" : // 鉴权理财
                        auditFlag = otherService.selectAuditAuthRC(userId,"authFinancingAudit");
                        AuthFinVo authFinVo = JSONObject.parseObject(newValue,AuthFinVo.class);
                        rcArchive.setUserId(authFinVo.getUserId());
                        rcArchive.setUserName(authFinVo.getUserName());
                        rcArchive.setAuthFinacial(authFinVo.getAuthFinacial());
                        this.updateRcArchiveSelective(rcArchive);
                        break;
                    case "RECHARGE" : // 充值设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"rechargeAudit");
                        RechargeVo rechargeVo = JSON.parseObject(newValue,RechargeVo.class);
                        rcArchive.setUserId(rechargeVo.getUserId());
                        rcArchive.setUserName(rechargeVo.getUserName());
                        rcArchive.setCheckRechargeCard(rechargeVo.getCheckRechargeCard());
                        rcArchive.setCheckCardReason(rechargeVo.getReason());
                        rcArchive.setEBankSetting(rechargeVo.getEBankSetting());
                        rcArchive.setTradeStartTime(rechargeVo.getTradeStartTime());
                        rcArchive.setTradeEndTime(rechargeVo.getTradeEndTime());

                        boolean rechargeFlag = this.updateRcArchiveByPrimaryKey(rcArchive);
                        if (!rechargeFlag){
                            throw new AppException(RcCode.SYSTEM_EXCEPTION.code, "充值设置更新失败");
                        }
                        break;
                    case "TRANSACTION" : // 交易设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"transcationAudit");
                        newValues.add(newValue);
                        break;
                    case "PAYMENT_SETTING": // 代付设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"paymentSettingAudit");
                        ProportionVo proportionVo = JSON.parseObject(newValue,ProportionVo.class);
//                        rcArchive.setProportion(proportionVo.getProportion());
                        rcArchive.setMinWithdrawRatio(proportionVo.getMinWithdrawRatio());
                        rcArchive.setWithdrawStartTime(proportionVo.getWithdrawStartTime());
                        rcArchive.setWithdrawEndTime(proportionVo.getWithdrawEndTime());
                        rcArchive.setWithdrawReason(proportionVo.getWithdrawReason());
                        rcArchive.setWithdrawUniqueId(proportionVo.getUniqueId());
                        this.updateRcArchiveByPrimaryKey(rcArchive);
                        break;
                    case "ORDER_TRANS_PAY_SETTING": // 订单转账支付设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"orderTransPaySettingAudit");
                        PaySettingVo vo = JSON.parseObject(newValue,PaySettingVo.class);
                        Map<String,Object> param = new HashMap<>();
                        param.put("archiveId",rcArchive.getArchiveId());
                        param.put("paySetting",vo.getPaySetting());
                        param.put("paySettingReason",vo.getPaySettingReason());
                        param.put("paySettingUniqueId",vo.getPaySettingUniqueId());
                        rcArchiveMapper.updatePaySetting(param);
                        break;
                }
            } else if (auditResult.compareTo(RcConstants.AuditStatus.FAIL.code) == 0) {
                switch (type) {
                    case "RISK_LEVEL" : // 风险等级设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskLevelAudit");
                        syncLevelMap.put(rcArchive.getArchiveCode(),rcArchive.getRcLevel());
                        break;
                    case "RISK_CONTROL_STATUS" : // 风控状态
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskControlAudit");
                        break;
                    case "ACCOUNT_STATUS" : // 账户状态
                        auditFlag = otherService.selectAuditAuthRC(userId,"riskAccountAudit");
                        break;
                    case "RISK_CONTROL_FREEZING" : // 风控冻结金额
                        auditFlag = otherService.selectAuditAuthRC(userId,"controlFreezingAudit");
                        break;
                    case "WITHDRAWAL_REPORT" : // 出金报备设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"withDrawalReportAudit");
                        break;
                    case "AUTHENTICATION_FINANCING" : // 鉴权理财
                        auditFlag = otherService.selectAuditAuthRC(userId,"authFinancingAudit");
                        break;
                    case "RECHARGE" : // 充值设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"rechargeAudit");
                        break;
                    case "TRANSACTION" : // 交易设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"transcationAudit");
                        break;
                    case "PAYMENT_SETTING" : // 代付设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"paymentSettingAudit");
                        break;
                    case "ORDER_TRANS_PAY_SETTING": // 订单转账支付设置
                        auditFlag = otherService.selectAuditAuthRC(userId,"orderTransPaySettingAudit");
                        break;
                }
            } else {
                throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
            }
            // 判断是否有审核权限
            if (userId != 0 && StringUtils.isEmpty(auditFlag)) {
                throw new AppException(RcCode.NO_AUDIT_PERMISSION.code,RcCode.NO_AUDIT_PERMISSION.message);
            }
            // 更新记录表状态
            rcAuditRecord.setAuditResult(auditResult);
            rcAuditRecord.setAuditOperId(userId);
            rcAuditRecord.setRemarks(remark);
            rcAuditRecord.setAuditTime(new Date());
            rcAuditRecordService.updateAuditRecord(rcAuditRecord);
            try {
                // 添加操作日志
                RcOperateLog log = new RcOperateLog();
                Date modifyDate = new Date();
                log.setPermId("80801");
                if (type.equals("TRANSACTION")) { // 交易设置不是存储在rc_archive
                    String customerNo = (String) (JSONObject.parseObject(newValue,Map.class)).get("customerNo");
                    Customer customer = otherService.queryCustomerByCustomerNo(customerNo);
                    if (customer == null) {
                        throw new AppException(RcCode.USER_NOT_EXIXT.code,RcCode.USER_NOT_EXIXT.message);
                    }
                    log.setCode(customer.getCustomerNo());
                    log.setName(customer.getName());
                } else {
                    log.setCode(rcArchive.getArchiveCode());
                    log.setName(rcArchive.getArchiveName());
                }
                log.setType("2");
                log.setOperator(String.valueOf(userId));
                log.setOperateTime(modifyDate);
                log.setOperateContent("审核风控档案信息");
                log.setOrigValue("待审核");
                log.setNewValue(auditResult.compareTo(RcConstants.AuditStatus.SUCCESS.code) == 0 ? "审核通过" : "审核不通过");
                rcOperateLogService.insert(log);
                // 更新待办
                if (autoAudit.equals("1")) {
                    response = custService.updateRCItem("RISK_CONTROL_AUDIT","record-" + rcAuditRecord.getId());
                }
            } catch (Exception e) {
                logService.printLog(e);
                throw new AppException(response.getReturnCode(),response.getReturnMsg());
            }
        }
        // 风险等级同步
        if (!syncLevelMap.isEmpty() && syncLevelMap.size() > 0) {
            commonLogger.printMessage("风险等级同步：" + JSON.toJSONString(syncLevelMap));
            try {
                Iterator<Map.Entry<String, String>> iterator = syncLevelMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry entry = iterator.next();
                    custService.syncCustomerRcLevel((String) entry.getKey(), RcConstants.RcToCustLevel.getMessageByCode((String) entry.getValue()));
                }
            } catch (Exception e) {
                commonLogger.printMessage("风险等级同步错误：" + e.getMessage());
                throw new AppException(RcCode.RC_FEIGIN_EXCEPTION.code,RcCode.RC_FEIGIN_EXCEPTION.message);
            }
        }
        // 交易设置调用cust更新
        try {
            if (newValues != null && newValues.size() > 0) {
                response = custService.saveLocConfigList(newValues,userId);
            }
        } catch (Exception e) {
            throw new AppException(RcCode.RC_FEIGIN_EXCEPTION.code,RcCode.RC_FEIGIN_EXCEPTION.message);
        }
        // 出金报备发送kafka消息
        if (bindCardVos != null && bindCardVos.size() > 0) {
            bindCardVos.forEach(bindCardVo -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("customerCode", bindCardVo.getCustomerNo());
                jsonObject.put("bindCard", bindCardVo.getBindCard());
                jsonObject.put("reason", bindCardVo.getReason());
                jsonObject.put("userId", bindCardVo.getUserId());
                kafkaProducer.send("CUS_CustomerChange", RC_BIND_CARD_CHANGE_KEY, jsonObject);
            });
        }
        // 风控/账户状态发送kafka消息
        if (!jsonObjects.isEmpty()) {
            jsonObjects.forEach(jsonObject -> {
                commonLogger.printMessage("rc-jsonObjects:" + JSON.toJSONString(jsonObject));
                kafkaProducer.send("CUS_CustomerChange", RC_STATUS_CHANGE_KEY, jsonObject);
            });
        }
        // 反诈调用后将之前待审核记录自动审核拒绝
        if (!refuseList.isEmpty()) {
            commonLogger.printMessage("refuseList:" + refuseList);
            ExecutorService fixedThreadPool = Executors.newFixedThreadPool(1);

            fixedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    refuseList.forEach(refuseId -> {
                        try {
                            Thread.sleep(2000); // 风控日志-风控操作日志 根据时间分组，避免多个审核记录在页面显示合并
                            batchAuditRecord(String.valueOf(refuseId),new Short("2"),"反诈商户自动冻结",0L,"1");
                        } catch (Exception e) {
                            commonLogger.printMessage(refuseId + "反诈商户自动冻结错误：" + e.getMessage());
                            commonLogger.printLog(e);
                        }
                    });
                }
            });
        }
    }

    @Override
    public List<RcArchive> selectByCertificateNo(String certificateNo) {

        return rcArchiveMapper.selectByCertificateNo(certificateNo);
    }

    @Override
    public EBankConfigVo queryEBankConfig(String customerCode) {
        
        //根据Id查找风控档案
        RcArchive rcArchive = selectByTypeAndCode(RcConstants.RCTargetType.CUSTOMER_CODE.code, customerCode);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }

        // 取上级，向上获取
        if (RcConstants.EBankSetting.PARENT.code.equals(rcArchive.getEBankSetting())) {
            String parentCustomerCode = otherService.queryUpperCustomerCode(customerCode);
            if (parentCustomerCode != null) {
                return queryEBankConfig(parentCustomerCode);
            }
        }
        
        // 迭代完了上级，顶级依然是取上级，则返回设置有误
        if (RcConstants.EBankSetting.PARENT.code.equals(rcArchive.getEBankSetting())) {
            throw new AppException(RcCode.EBANK_SETTING_ERROR.code, RcCode.EBANK_SETTING_ERROR.message);
        }
        String paySetting = StringUtils.isBlank(rcArchive.getPaySetting()) ? "0" : rcArchive.getPaySetting();
        return new EBankConfigVo(rcArchive.getEBankSetting(), rcArchive.getCheckRechargeCard(),paySetting, rcArchive.getTradeStartTime(), rcArchive.getTradeEndTime());
    }

    @Override
    @Transactional
    public RcAuditRecord saveAuditRecordForBatch(RcArchive rcArchive, String statusName, String status, String reason, Long userId,Long defaultUserId,String flag){
        RcAuditRecord rcAuditRecord = new RcAuditRecord();
        AuditStatusVo newValue = new AuditStatusVo();
        newValue.setUserId(userId);
        newValue.setStatusName(statusName);
        newValue.setStatus(status);
        newValue.setReason(reason);
        if (RcArchive.Status.ACCOUNT.statusName.equals(statusName)) {
            AuditStatusVo oldValue = new AuditStatusVo();
            oldValue.setStatusName(statusName);
            oldValue.setUserId(rcArchive.getUserId());
            oldValue.setStatus(rcArchive.getAccountStatus());
            oldValue.setReason(rcArchive.getAccountStatusReason());
            rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.ACCOUNT_STATUS.code,rcArchive.getArchiveId(), JSON.toJSONString(oldValue)
                    ,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,null);
        } else if (RcArchive.Status.RC.statusName.equals(statusName)) {
            AuditStatusVo oldValue = new AuditStatusVo();
            oldValue.setStatusName(statusName);
            oldValue.setUserId(rcArchive.getUserId());
            oldValue.setStatus(rcArchive.getRcStatus());
            oldValue.setReason(rcArchive.getRcStatusReason());
            rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code,rcArchive.getArchiveId(), JSON.toJSONString(oldValue)
                    ,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,null);
        }
        // 自动审核调用审核方法
        if ("0".equals(flag)) {
            ((RcArchiveServiceImpl) AopContext.currentProxy()).batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultUserId,"0");
        }
        return rcAuditRecord;
    }

    @Override
    @Transactional
    public RcAuditRecord saveAuditRecord(RcArchive rcArchive, String statusName, String status, String reason, Long userId,Long defaultUserId,String uniqued) {
        // 查询是否自动审核 0：自动审核；1：待审核
        String flag = otherService.queryParamValueByTypeAndName("RC_FLAG", "AUDIT", false);
        CommonOuterResponse response = new CommonOuterResponse();
        RcAuditRecord rcAuditRecord = new RcAuditRecord();
        AuditStatusVo newValue = new AuditStatusVo();
        newValue.setUserId(userId);
        newValue.setStatusName(statusName);
        newValue.setStatus(status);
        newValue.setReason(reason);
        if (RcArchive.Status.ACCOUNT.statusName.equals(statusName)) {
            AuditStatusVo oldValue = new AuditStatusVo();
            oldValue.setStatusName(statusName);
            oldValue.setUserId(rcArchive.getUserId());
            oldValue.setStatus(rcArchive.getAccountStatus());
            oldValue.setReason(rcArchive.getAccountStatusReason());
            rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.ACCOUNT_STATUS.code,rcArchive.getArchiveId(), JSON.toJSONString(oldValue)
                    ,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqued);
            try {
                if ("1".equals(flag)) {
                    response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
                            rcArchive.getArchiveCode()  + "," + RcConstants.AuditTargetType.ACCOUNT_STATUS.code,"record-" + rcAuditRecord.getId(),userId);
                }
            } catch (Exception e) {
                throw new AppException(response.getReturnCode(),response.getReturnMsg());
            }
        } else if (RcArchive.Status.RC.statusName.equals(statusName)) {
            AuditStatusVo oldValue = new AuditStatusVo();
            oldValue.setStatusName(statusName);
            oldValue.setUserId(rcArchive.getUserId());
            oldValue.setStatus(rcArchive.getRcStatus());
            oldValue.setReason(rcArchive.getRcStatusReason());
            rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code,rcArchive.getArchiveId(), JSON.toJSONString(oldValue)
                    ,JSON.toJSONString(newValue),RcConstants.AuditActionType.UPDATE.code,reason,userId,uniqued);
            try {
                if ("1".equals(flag)) {
                    response = custService.checkRCItem("80801",rcArchive.getArchiveCode() + "_风控档案修改待审核", "RISK_CONTROL_AUDIT",
                            rcArchive.getArchiveCode()  + "," + RcConstants.AuditTargetType.RISK_CONTROL_STATUS.code,"record-" + rcAuditRecord.getId(),userId);
                }
            } catch (Exception e) {
                throw new AppException(response.getReturnCode(),response.getReturnMsg());
            }
        }
        // 自动审核调用审核方法
        if ("0".equals(flag)) {
            batchAuditRecord(rcAuditRecord.getId() + "",new Short("1"),"",defaultUserId,"0");
        }
        return rcAuditRecord;
    }

    @Override
    public PageResult<RiskInfoExportResponse> exportRiskInfo(Map<String, Object> paramMap, boolean download) throws Exception {
        List<RiskInfoExportResponse> responseList = rcArchiveMapper.queryRiskInfo(paramMap);
        if (responseList != null && responseList.size() > 0) {
            logService.printLog("风控导出笔数：" + responseList.size());
            Integer begin = (Integer) paramMap.get("beginRowNo");
            for (int i = 0; i < responseList.size(); i++) {
                RiskInfoExportResponse response = responseList.get(i);
                response.setRownum(begin++);
                String riskNewValue = response.getRiskNewValue();
                if (!org.springframework.util.StringUtils.isEmpty(riskNewValue)) {
                    Map riskMap = JSONObject.parseObject(riskNewValue,Map.class);
                    response.setLastFrozenReason(riskMap.get("reason") != null ? String.valueOf(riskMap.get("reason")) : "");
                }
                String accountNewValue = response.getAccountNewValue();
                if (!org.springframework.util.StringUtils.isEmpty(accountNewValue)) {
                    Map accountMap = JSONObject.parseObject(accountNewValue,Map.class);
                    response.setLastAbnormalReason(accountMap.get("reason") != null ? String.valueOf(accountMap.get("reason")) : "");
                }
                // 入金临时限额
                if (RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(response.getTemporaryStatus())) {
                    String temporaryItem = "";
                    Map<String,Object> limitMap = new HashMap<>();
                    limitMap.put("type",RcConstants.RCTargetType.CUSTOMER_CODE.code);
                    limitMap.put("code",response.getCustomerNo());
                    limitMap.put("enable",RcConstants.TemporaryEnableStatus.ENABLE.code);
                    for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
                        limitMap.put("limitType",value.code);
                        TemporaryLimit temporaryLimit = temporaryLimitMapper.queryByCodeAndType(limitMap);
                        if (temporaryLimit == null || temporaryLimit.getLimitValue() == null) {
                            continue;
                        }
                        temporaryItem = temporaryItem + value.shortName + "：" + temporaryLimit.getLimitValue() + "；";
                    }
                    response.setTemporaryLimitItem(temporaryItem);
                }
                responseList.set(i,response);
            }
        }
        PageResult pageResult = new PageResult<>();
        pageResult.setRows(responseList);
        return pageResult;
    }

    /**
     * 设置临时入金参数校验
     * @param temporaryLimitVo
     */
    private void checkTemporaryDepositLimit(TemporaryLimitVo temporaryLimitVo) {
        if (RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())) {
            if (temporaryLimitVo.getAlipaySingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getWechatSingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getUnionCodeSingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getUnionOnlineSingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getQuickPaySingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getBankPaySingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getUnionAppSingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)
                    && temporaryLimitVo.getAllSingleMaxLimitState().equals(RcConstants.TemporaryEnableStatus.DISABLE.code)) {
                throw new AppException(RcCode.RC_ARG_ERROR.code,"选择了“限制临时入金”，则必须至少选中其中一个项目才能操作");
            }
        }
    }

    @Override
    @Transactional
    public CommonOuterResponse temporaryDepositLimit(TemporaryLimitVo temporaryLimitVo, Long userId) throws Exception {
        checkTemporaryDepositLimit(temporaryLimitVo);

        if (temporaryLimitVo.getArchiveId() == null) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：风控档案ID不能为空");
        }

        Date modifyDate = new Date();
        RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(temporaryLimitVo.getArchiveId());
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code, RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        RcAuditRecord record = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.TEMPORARY_DEPOSIT_LIMIT.code,rcArchive.getArchiveId());
        if (record != null && record.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0) {
            throw new AppException(RcCode.UNAPPROVED_RECORDS_EXIST.code,RcCode.UNAPPROVED_RECORDS_EXIST.message);
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("type",RcConstants.RCTargetType.CUSTOMER_CODE.code);
        paramMap.put("code",rcArchive.getArchiveCode());
        // 获取旧值
        TemporaryLimitVo oldParam = new TemporaryLimitVo();
        oldParam.setArchiveId(temporaryLimitVo.getArchiveId());
        oldParam.setTemporaryStatus(rcArchive.getTemporaryStatus());
        oldParam.setTemporaryStatusReason(rcArchive.getTemporaryStatusReason());
        List<TemporaryLimit> oldList = temporaryLimitMapper.queryListByCode(paramMap);
        transToTemporaryLimit(oldParam,oldList);
        // 保存审核记录
        RcAuditRecord rcAuditRecord = new RcAuditRecord();
        rcAuditRecord = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.TEMPORARY_DEPOSIT_LIMIT.code,rcArchive.getArchiveId(), JSON.toJSONString(oldParam)
                ,JSON.toJSONString(temporaryLimitVo),RcConstants.AuditActionType.UPDATE.code,temporaryLimitVo.getTemporaryStatusReason(),userId,null);

        // 保存修改记录
        if (!temporaryLimitVo.getTemporaryStatus().equals(rcArchive.getTemporaryStatus())) {
            addRcLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",modifyDate,"临时入金限制状态",userId,RcConstants.TemporaryStatus.getCommentByCode(rcArchive.getTemporaryStatus()),RcConstants.TemporaryStatus.getCommentByCode(temporaryLimitVo.getTemporaryStatus()));
        }
        if (!temporaryLimitVo.getTemporaryStatusReason().equals(rcArchive.getTemporaryStatusReason())) {
            addRcLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",modifyDate,"临时入金限制变更原因",userId,oldParam.getTemporaryStatusReason(),temporaryLimitVo.getTemporaryStatusReason());
        }
        for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
            // 限制值
            Field field = TemporaryLimitVo.class.getDeclaredField(value.code);
            field.setAccessible(true);
            Long oldValue = field.get(oldParam) == null ? null : (Long) field.get(oldParam);
            Long newValue = field.get(temporaryLimitVo) == null ? null : (Long) field.get(temporaryLimitVo);
            if (!Objects.equals(oldValue,newValue)) {
                addRcLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",modifyDate,RcConstants.TemporaryLimit.getCommentByCode(value.code),userId,
                        oldValue == null ? "" : String.valueOf(oldValue),newValue == null ? "" : String.valueOf(newValue));
            }
            // 状态
            Field fieldState = TemporaryLimitVo.class.getDeclaredField(value.codeState);
            fieldState.setAccessible(true);
            String oldState = fieldState.get(oldParam) == null ? "2" : (String) fieldState.get(oldParam); // 默认禁用
            String newState = fieldState.get(temporaryLimitVo) == null ? "2" : (String) fieldState.get(temporaryLimitVo);
            if (!oldState.equals(newState)) {
                addRcLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",modifyDate,RcConstants.TemporaryLimit.getCommentByCode(value.code) + "状态",userId,
                            "2".equals(oldState) ? "禁用" : "启用","2".equals(newState) ? "禁用" : "启用");
            }
        }
        // 自动审核
        auditTemporary(rcAuditRecord.getId(),new Short("1"),"自动审核",0L);
        return CommonOuterResponse.success();
    }

    @Override
    @Transactional
    public CommonOuterResponse preTemporaryDepositLimit(TemporaryLimitVo temporaryLimitVo, Long userId) throws Exception {
        checkTemporaryDepositLimit(temporaryLimitVo);
        String customerNo = temporaryLimitVo.getCustomerNo();
        if (StringUtils.isBlank(customerNo)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：商户编号");
        }
        RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerNo);
        // 加T代表初审时设置的临时数据，如T005；商户未首次复审通过操作的是临时数据;已首次复审通过的，操作正式数据 -- by wangshaobin 20231228
        if (rcArchive == null) {
            Date modifyDate = new Date();
            otherService.updateDraftTemporary(temporaryLimitVo.getTemporaryStatus(),temporaryLimitVo.getTemporaryStatusReason(),customerNo);

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("type","T" + RcConstants.RCTargetType.CUSTOMER_CODE.code);
            paramMap.put("code",customerNo);
            for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
                // 限制值
                paramMap.put("limitType",value.code);
                Field field = TemporaryLimitVo.class.getDeclaredField(value.code);
                field.setAccessible(true);
                Long newValue = field.get(temporaryLimitVo) == null ? null : (Long) field.get(temporaryLimitVo);
                // 状态
                Field fieldState = TemporaryLimitVo.class.getDeclaredField(value.codeState);
                fieldState.setAccessible(true);
                String enable = fieldState.get(temporaryLimitVo) == null ? RcConstants.TemporaryEnableStatus.DISABLE.code : (String) fieldState.get(temporaryLimitVo);
                TemporaryLimit temporaryLimit = temporaryLimitMapper.queryByCodeAndType(paramMap);
                if (temporaryLimit == null) {
                    temporaryLimit = createTemporaryLimit("T" + RcConstants.RCTargetType.CUSTOMER_CODE.code, customerNo, value.code,newValue,enable);
                    temporaryLimitMapper.insert(temporaryLimit);
                } else {
                    temporaryLimit.setLimitValue(newValue);
                    temporaryLimit.setEnable(enable);
                    temporaryLimit.setUpdateTime(modifyDate);
                    temporaryLimitMapper.updateByPrimaryKey(temporaryLimit);
                }
            }
        } else {
            temporaryLimitVo.setArchiveId(rcArchive.getArchiveId());
            ((RcArchiveServiceImpl) AopContext.currentProxy()).temporaryDepositLimit(temporaryLimitVo,userId);
        }
        // 操作日志
        try {
            String commont = RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus()) ? "限制临时入金" : "正常";
            CommonOuterResponse response = custService.saveSingleOperation(customerNo,commont,"风控临时入金限制",userId);
            if (!response.isSuccess()) {
                throw new AppException(response.getReturnCode(),response.getReturnMsg());
            }
        } catch (Exception e) {
            throw new AppException(RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.code,RcCode.CALL_SUBSYSTEM_EXCEPTIOIN.message + "：cust");
        }
        return CommonOuterResponse.success();
    }

    @Override
    public void auditTemporary(Long audId, Short auditResult, String remark, Long userId) throws Exception {
        RcAuditRecord rcAuditRecord = rcAuditRecordService.selectByPrimaryId(audId);
        if (rcAuditRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        // 判断状态
        if (!(rcAuditRecord.getAuditResult().compareTo(RcConstants.AuditStatus.WAITING.code) == 0)) {
            throw new AppException(RcCode.AUD_EXCEPTION.code,RcCode.AUD_EXCEPTION.message);
        }
        Date modifyDate = new Date();
        RcArchive rcArchive = selectById(rcAuditRecord.getTargetId());
        String newParam = rcAuditRecord.getNewValue();
        TemporaryLimitVo temporaryLimitVo = JSONObject.parseObject(newParam,TemporaryLimitVo.class);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("type",RcConstants.RCTargetType.CUSTOMER_CODE.code);
        paramMap.put("code",rcArchive.getArchiveCode());
        for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
            // 限制值
            paramMap.put("limitType",value.code);
            Field field = TemporaryLimitVo.class.getDeclaredField(value.code);
            field.setAccessible(true);
            Long newValue = field.get(temporaryLimitVo) == null ? null : (Long) field.get(temporaryLimitVo);
            // 状态
            Field fieldState = TemporaryLimitVo.class.getDeclaredField(value.codeState);
            fieldState.setAccessible(true);
            String enable = fieldState.get(temporaryLimitVo) == null ? RcConstants.TemporaryEnableStatus.DISABLE.code : (String) fieldState.get(temporaryLimitVo);
            TemporaryLimit temporaryLimit = temporaryLimitMapper.queryByCodeAndType(paramMap);
            if (temporaryLimit == null) {
                temporaryLimit = createTemporaryLimit(RcConstants.RCTargetType.CUSTOMER_CODE.code, rcArchive.getArchiveCode(), value.code,newValue,enable);
                temporaryLimitMapper.insert(temporaryLimit);
            } else {
                temporaryLimit.setLimitValue(newValue);
                temporaryLimit.setEnable(enable);
                temporaryLimit.setUpdateTime(modifyDate);
                temporaryLimitMapper.updateByPrimaryKey(temporaryLimit);
            }
        }
        // 更新记录表状态
        rcArchive.setTemporaryStatus(temporaryLimitVo.getTemporaryStatus());
        rcArchive.setTemporaryStatusReason(temporaryLimitVo.getTemporaryStatusReason());
        rcArchive.setUpdateTime(modifyDate);
        rcArchiveMapper.updateByPrimaryKey(rcArchive);

        rcAuditRecord.setAuditResult(auditResult);
        rcAuditRecord.setAuditOperId(userId);
        rcAuditRecord.setRemarks(remark);
        rcAuditRecord.setAuditTime(modifyDate);
        rcAuditRecordService.updateAuditRecord(rcAuditRecord);
        // 保存自动审核记录
        addRcLog("80201",rcArchive.getArchiveCode(),rcArchive.getArchiveName(),"2",modifyDate, "审核临时入金限制",userId, "待审核","审核通过");
    }

    @Override
    public TemporaryLimitVo getTemporaryDepositLimit(Long archiveId) throws Exception {
        if (archiveId == null) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：档案ID");
        }
        TemporaryLimitVo temporaryLimitVo = new TemporaryLimitVo();
        RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(archiveId);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        temporaryLimitVo.setArchiveId(archiveId);
        temporaryLimitVo.setTemporaryStatus(rcArchive.getTemporaryStatus() != null ? rcArchive.getTemporaryStatus() : RcConstants.TemporaryStatus.NORMAL.code);
        temporaryLimitVo.setTemporaryStatusReason(rcArchive.getTemporaryStatusReason());
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("type",RcConstants.RCTargetType.CUSTOMER_CODE.code);
        paramMap.put("code",rcArchive.getArchiveCode());
        List<TemporaryLimit> temporaryLimits = temporaryLimitMapper.queryListByCode(paramMap);
        if (temporaryLimits != null && temporaryLimits.size() > 0) {
            for (TemporaryLimit temporaryLimit : temporaryLimits) {
                Field field = TemporaryLimitVo.class.getDeclaredField(temporaryLimit.getLimitType());
                field.setAccessible(true);
                field.set(temporaryLimitVo,temporaryLimit.getLimitValue() == null ? 10000 : temporaryLimit.getLimitValue());

                Field fieldState = TemporaryLimitVo.class.getDeclaredField(temporaryLimit.getLimitType() + "State");
                fieldState.setAccessible(true);
                fieldState.set(temporaryLimitVo,temporaryLimit.getEnable());
            }
        } else {
            for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
                Field field = TemporaryLimitVo.class.getDeclaredField(value.code);
                field.setAccessible(true);
                field.set(temporaryLimitVo,10000L);
            }
        }
        return temporaryLimitVo;
    }

    @Override
    public TemporaryLimitVo preGetTemporaryDepositLimit(String customerNo,String source) throws Exception {
        if (StringUtils.isBlank(customerNo)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,RcCode.INDEX_NOT_ENOUGH.message + "：商户编号");
        }
        TemporaryLimitVo temporaryLimitVo = new TemporaryLimitVo();
        RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerNo);
        if (rcArchive == null || "sync".equals(source)) {
            Customer customer = otherService.queryCustomerDraftByCustomerNo(customerNo);
            if (customer == null) {
                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message + "：商户");
            }
            temporaryLimitVo.setCustomerNo(customerNo);
            temporaryLimitVo.setTemporaryStatus(customer.getTemporaryStatus());
            temporaryLimitVo.setTemporaryStatusReason(customer.getTemporaryStatusReason());
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("type","T" + RcConstants.RCTargetType.CUSTOMER_CODE.code);
            paramMap.put("code",customerNo);
            List<TemporaryLimit> temporaryLimits = temporaryLimitMapper.queryListByCode(paramMap);
            if (temporaryLimits != null && temporaryLimits.size() > 0) {
                for (TemporaryLimit temporaryLimit : temporaryLimits) {
                    Field field = TemporaryLimitVo.class.getDeclaredField(temporaryLimit.getLimitType());
                    field.setAccessible(true);
                    field.set(temporaryLimitVo,temporaryLimit.getLimitValue() == null ? 10000 : temporaryLimit.getLimitValue());

                    Field fieldState = TemporaryLimitVo.class.getDeclaredField(temporaryLimit.getLimitType() + "State");
                    fieldState.setAccessible(true);
                    fieldState.set(temporaryLimitVo,temporaryLimit.getEnable());
                }
            } else {
                for (RcConstants.TemporaryLimit value : RcConstants.TemporaryLimit.values()) {
                    Field field = TemporaryLimitVo.class.getDeclaredField(value.code);
                    field.setAccessible(true);
                    field.set(temporaryLimitVo,10000L);
                }
            }
        } else {
            temporaryLimitVo = getTemporaryDepositLimit(rcArchive.getArchiveId());
        }
        temporaryLimitVo.setCustomerNo(customerNo);
        return temporaryLimitVo;
    }

    @Override
    @Logable(businessTag = "RcArchiveServiceImpl.getTemporaryLimit")
    public Long getTemporaryLimit(String customerNo,String limitType){
        RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerNo);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        if (!RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(rcArchive.getTemporaryStatus())) {
            return null;
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("type",RcConstants.RCTargetType.CUSTOMER_CODE.code);
        paramMap.put("code",customerNo);
        paramMap.put("limitType",limitType);
        paramMap.put("enable",RcConstants.TemporaryEnableStatus.ENABLE.code);
        TemporaryLimit temporaryLimit = temporaryLimitMapper.queryByCodeAndType(paramMap);
        if (temporaryLimit == null) {
            return null;
        }
        return temporaryLimit.getLimitValue();
    }

    @Override
    public CommonOuterResponse releaseTemporary(String customerNo, Long userId) throws Exception {
        // 获取临时入金限额
        TemporaryLimitVo temporaryLimitVo = preGetTemporaryDepositLimit(customerNo,"1");
        if (!RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())) {
            throw new AppException(RcCode.STATE_ERROR.code,RcCode.STATE_ERROR.message);
        }
        temporaryLimitVo.setTemporaryStatus(RcConstants.TemporaryStatus.NORMAL.code);
        temporaryLimitVo.setTemporaryStatusReason("解除风控临时入金限制");
        // 设置临时入金限制
        preTemporaryDepositLimit(temporaryLimitVo,userId);
        return CommonOuterResponse.success();
    }

    @Override
    public int getWithdrawProportion(String customerCode) {
        RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerCode);
        if (rcArchive == null || rcArchive.getMinWithdrawRatio() == null) {
//            String proportion = otherService.queryWithdrawParam("PROPORTION");
            String minWithdrawRatio = otherService.queryWithdrawParam("MIN_WITHDRAW_RATIO");
            if (StringUtils.isNotBlank(minWithdrawRatio)) {
                return Integer.parseInt(minWithdrawRatio);
            } else {
                return 0;
            }
        }
        return rcArchive.getMinWithdrawRatio();
    }

    @Override
    public WithdrawTime getWithdrawTime(String customerCode) {
        RcArchive rcArchive = rcArchiveMapper.selectByArchiveCode(customerCode);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        String startTime = StringUtils.isNotBlank(rcArchive.getWithdrawStartTime()) ? rcArchive.getWithdrawStartTime() : otherService.queryWithdrawParam("WITHDRAW_START_TIME");
        String endTime = StringUtils.isNotBlank(rcArchive.getWithdrawEndTime()) ? rcArchive.getWithdrawEndTime() : otherService.queryWithdrawParam("WITHDRAW_END_TIME");
        Boolean canWithdraw = true;
        LocalTime now = LocalTime.now();
        LocalTime nowTime = LocalTime.of(now.getHour(),now.getMinute());
        if (StringUtils.isNotBlank(startTime)) {
            String[] time = startTime.split(":");
            LocalTime compareTime = LocalTime.of(Integer.parseInt(time[0]),Integer.parseInt(time[1]));
            if (nowTime.compareTo(compareTime) == -1) { // 当前时间小于开始时间
                canWithdraw = false;
            }
        }
        if (StringUtils.isNotBlank(endTime)) {
            String[] time = endTime.split(":");
            LocalTime compareTime = LocalTime.of(Integer.parseInt(time[0]),Integer.parseInt(time[1]));
            if (nowTime.compareTo(compareTime) == 1) { // 当前时间大于结束时间
                canWithdraw = false;
            }
        }
        WithdrawTime withdrawTime = new WithdrawTime();
        withdrawTime.setStartTime(startTime);
        withdrawTime.setEndTime(endTime);
        withdrawTime.setCanWithdraw(canWithdraw);
        return withdrawTime;
    }

    private Integer getByteLength(String param) {
        try {
            if (null == param) {
                return 0;
            }
            return param.getBytes("GBK").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 999;
    }

    @Override
    public void paySetting(Long archiveId, String paySetting, String reason, String uniqueId, Long userId) {
        RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(archiveId);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        if (getByteLength(reason) > 300) {
            throw new AppException(RcCode.DATA_TOO_LONG.code,RcCode.DATA_TOO_LONG.message);
        }
        Map<String,Object> param = new HashMap<>();
        param.put("archiveId",archiveId);
        param.put("paySetting",paySetting);
        param.put("paySettingReason",reason);
        param.put("paySettingUniqueId",uniqueId);
        rcArchiveMapper.updatePaySetting(param);
    }

    @Override
    public PaySettingResponse getPaySetting(Long archiveId) {
        RcArchive rcArchive = rcArchiveMapper.selectByPrimaryKey(archiveId);
        if (rcArchive == null) {
            throw new AppException(RcCode.RC_ARCHIVE_NOT_EXISTS.code,RcCode.RC_ARCHIVE_NOT_EXISTS.message);
        }
        PaySettingResponse paySettingResponse = new PaySettingResponse();
        paySettingResponse.setPaySetting(rcArchive.getPaySetting());
        paySettingResponse.setReason(rcArchive.getPaySettingReason());
        paySettingResponse.setUniqueId(rcArchive.getPaySettingUniqueId());
        if (StringUtils.isNotBlank(rcArchive.getPaySettingUniqueId())) {
            Map<String,String> map = fsService.filePath(rcArchive.getPaySettingUniqueId(), 30, 100, "download");
            if(!FileUploadResponse.SUCCESS.equals(map.get("resultCode"))){
                throw new AppException(RcCode.FILE_DOWNLOAD_FAIL.code,RcCode.FILE_DOWNLOAD_FAIL.message);
            }
            paySettingResponse.setFileName(map.get("fileName"));
            paySettingResponse.setUrl(map.get("filePath"));
        }
        return paySettingResponse;
    }

    private TemporaryLimit createTemporaryLimit(String type,String code,String limitType,Long limitValue,String enable) {
        TemporaryLimit temporaryLimit = new TemporaryLimit();
        Date modifyDate = new Date();
        temporaryLimit.setId(temporaryLimitMapper.querySeq());
        temporaryLimit.setType(type);
        temporaryLimit.setCode(code);
        temporaryLimit.setLimitType(limitType);
        temporaryLimit.setLimitValue(limitValue);
        temporaryLimit.setUnit("1");
        temporaryLimit.setEnable(enable);
        temporaryLimit.setCreateTime(modifyDate);
        temporaryLimit.setUpdateTime(modifyDate);
        return temporaryLimit;
    }

    private void addRcLog(String permId,String code,String name,String type,Date modifyDate,String operateContent,Long userId,String origValue,String newValue) {
        RcOperateLog log = new RcOperateLog();
        log.setPermId(permId);
        log.setCode(code);
        log.setName(name);
        log.setType(type);
        log.setOperator(String.valueOf(userId));
        log.setOperateTime(modifyDate);
        log.setOperateContent(operateContent);
        log.setOrigValue(origValue);
        log.setNewValue(newValue);
        rcOperateLogService.insert(log);
    }

    private void transToTemporaryLimit(TemporaryLimitVo temporaryLimitVo,List<TemporaryLimit> temporaryLimits) throws Exception {
        if (temporaryLimits.size() == 0) {
            return;
        }
        for (TemporaryLimit value : temporaryLimits) {
            Field field = TemporaryLimitVo.class.getDeclaredField(value.getLimitType());
            field.setAccessible(true);
            field.set(temporaryLimitVo,value.getLimitValue());

            Field fieldState = TemporaryLimitVo.class.getDeclaredField(value.getLimitType() + "State");
            fieldState.setAccessible(true);
            fieldState.set(temporaryLimitVo,value.getEnable());
        }
    }

    public static void main(String[] args) throws ParseException {
        String startTime = "15:27";
        String endTime = "15:30";
        Boolean canWithdraw = true;
        LocalTime now = LocalTime.now();
        LocalTime nowTime = LocalTime.of(now.getHour(),now.getMinute());
        if (StringUtils.isNotBlank(startTime)) {
            String[] time = startTime.split(":");
            LocalTime compareTime = LocalTime.of(Integer.parseInt(time[0]),Integer.parseInt(time[1]));
            System.out.println(nowTime.compareTo(compareTime));
            if (nowTime.compareTo(compareTime) == -1) { // 当前时间小于开始时间
                canWithdraw = false;
            }
        }
        if (StringUtils.isNotBlank(endTime)) {
            String[] time = endTime.split(":");
            LocalTime compareTime = LocalTime.of(Integer.parseInt(time[0]),Integer.parseInt(time[1]));
            System.out.println(nowTime.compareTo(compareTime));
            if (nowTime.compareTo(compareTime) == 1) { // 当前时间大于结束时间
                canWithdraw = false;
            }
        }
        System.out.println("打印：" + canWithdraw);
    }

    private void setPermissionParam(Map param,Long userId) {
        User operUser = otherService.selectUserById(userId);
        if (operUser != null) {
            if (Constants.PasUserType.COMPANY.code.equals(operUser.getUserType())){ // 分公司管理员能查询分公司下所有商户
                param.put("userCompanyId", operUser.getCompanyId());
            } else if (Constants.PasUserType.SALES.code.equals(operUser.getUserType())){ // 分公司管理员能查询业务员的商户
                param.put("businessManId", userId);
            } else {
                List<Long> companyIds = redisDataTransService.getCompanyIdList(userId);
                if (!companyIds.isEmpty()) {
                    param.put("companyIds", companyIds);
                }
            }
        }
    }
}

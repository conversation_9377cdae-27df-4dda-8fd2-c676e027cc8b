package com.epaylinks.efps.rc.service;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.vo.StatusAuditRecordPageVo;

/**
 * 审核记录接口
 * <AUTHOR>
 * @date 2020-11-11
 *
 */
public interface RcAuditRecordService {

    /**
     * 查询审核记录
     * @param id
     * @return
     */
    public RcAuditRecord selectByPrimaryId(Long id);
    
    /**
     * 查询审核记录
     * @param targetType
     * @param targetId
     * @return
     */
    public RcAuditRecord queryAuditRecord(String targetType, Long targetId);

    List<Long> queryListByTargetTypeAndId(String targetType, Long targetId,Long id);

    public RcAuditRecord queryHistoryAuditRecord(String targetType, Long targetId,String auditTime,String field);
    
    /**
     * 保存审核记录
     * @param targetType
     * @param targetId
     * @param oldValue
     * @param newValue
     * @param actionType
     * @param userId
     * @return 
     */
    public RcAuditRecord saveAuditRecord(String targetType, Long targetId, String oldValue, String newValue, String actionType, Long userId,String uniqued);
    
    /**
     * 保存审核记录
     * @param targetType
     * @param targetId
     * @param oldValue
     * @param newValue
     * @param actionType
     * @param reason
     * @param userId
     * @return 
     */
    public RcAuditRecord saveAuditRecord(String targetType, Long targetId, String oldValue, String newValue, String actionType, String reason, Long userId,String uniqued);
    
    /**
     * 审核，修改状态（最新记录）
     * @param targetType
     * @param targetId
     * @param auditResult
     * @param userId
     */
    public void updateAuditRecordStatus(String targetType, Long targetId, Short auditResult, Long userId);

    /**
     * 审核，修改状态（最新记录,带审核意见）
     * @param targetType
     * @param targetId
     * @param auditResult
     * @param userId
     * @param remarks
     */
    void updateAuditRecordStatus(String targetType, Long targetId, Short auditResult, Long userId, String remarks);

    
    /**
     * 按记录id修改审核状态
     * @param auditId
     * @param auditResult
     * @param userId
     */
    void updateAuditRecordStatus(Long auditId, Short auditResult, Long userId);

    int updateAuditRecord(RcAuditRecord auditRecord);

    int updateByPrimaryKeySelective(RcAuditRecord auditRecord);
    /**
     * 分页查询审核记录
     * @param paramMap
     * @return
     */
    PageResult<StatusAuditRecordPageVo> pageQuery(Map<String, Object> paramMap,Long userId);

    List<RcAuditRecord> listByParam(Map<String, Object> paramMap);

    RcAuditRecord queryByTargetTypeAndId(String type,Long id);
    
}

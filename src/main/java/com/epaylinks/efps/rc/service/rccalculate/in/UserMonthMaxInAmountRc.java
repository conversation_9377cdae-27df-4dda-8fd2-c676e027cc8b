package com.epaylinks.efps.rc.service.rccalculate.in;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Service("User-Month-Max-IN-Amount")
public class UserMonthMaxInAmountRc implements RcCalculate, RcIndexReset, RcIndexAddValue {

    @Autowired
    private MyRedisTemplateService myRedisTemplateService;

    private MyRedisTemplate redisTemplate ;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcLimitDataMapper rcLimitDataMapper;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private UserMonthMaxInAmountRc self;

    private static final String User_Day_Max_In_Amount = DefineCode.USER_DAY_IN_AMOUNT.defineCode;


    @Override
    public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        //目前只做快捷支付
        if (rcCalculteBasic.isQuickPayMethod(rcCalculateRequest.getPayMethod())){
            String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
            String identityCard =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.IDENTITY_CARD.code);
            if(customerCode == null || identityCard == null ) {
                return CommonOuterResponse.SUCCEE;
            }
            //金额
            String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
            long amount = Long.parseLong(amountStr);
            long dayInAmount = 0L;
            long monthInAmount = 0L;
            String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() , RcDateUtils.getCurrentMonth());
            String dayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , User_Day_Max_In_Amount, RcDateUtils.getCurrentDay());
            if(redisTemplate.opsForHash().hasKey(hashKey, identityCard)) {
                monthInAmount =redisTemplate.opsForHash().get(hashKey, identityCard) == null ? 0L : (Long) redisTemplate.opsForHash().get(hashKey, identityCard);
            }

            if(redisTemplate.opsForHash().hasKey(dayHashKey, identityCard)) {
                dayInAmount = redisTemplate.opsForHash().get(hashKey, identityCard) == null ? 0L :(Long) redisTemplate.opsForHash().get(dayHashKey, identityCard);
                dayInAmount += amount;
            }

            if (dayInAmount + monthInAmount > Long.parseLong(rcLimit.getLimitValue())) {
                // 记录触发日记
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
                rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayInAmount + monthInAmount - amount +"",amount +"",true,"RC交易受限", rcArchive.getArchiveType());
                return "RC交易受限";
            }
        }
        return CommonOuterResponse.SUCCEE;
    }

    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> paramMap) {

    }

    @Override
    public void reset(RcLimit rcLimit) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        Date date = new Date();
        String lastDayHahsKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , User_Day_Max_In_Amount,RcDateUtils.getLastDay());
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , rcLimit.getDefineCode(), RcDateUtils.getCurrentMonth());
        Map<String, Object> map = redisTemplate.opsForHash().entries(lastDayHahsKey);// 根据昨日历史银行卡号迭代，redis会当天的月记录
        for (String identityCard : map.keySet()) {
            if (date.getDate() == 1) {
                // 如果当前时间是每月的一号
                self.historyDataHandler(rcLimit, identityCard, date, RcLimitData.Status.FINISHED.code);
                redisTemplate.opsForHash().put(hashKey, identityCard, 0L);// 初始化今天的值
            } else {
                // 如果不是每月的一号
                long monthInAmount = self.historyDataHandler(rcLimit, identityCard, date, RcLimitData.Status.PROCESSING.code);
                redisTemplate.opsForHash().put(hashKey, identityCard, monthInAmount);// 月度数据更新
            }
        }
    }


    public Long historyDataHandler(RcLimit rcLimit , String bankCardNo, Date date , String status) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String lastDayHashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() , User_Day_Max_In_Amount, RcDateUtils.getLastDay());
        String businessTargetId =  rcLimit.getBusinessTagerId() + "_" + bankCardNo;

        long dayInAmount = 0;
        if(redisTemplate.opsForHash().hasKey(lastDayHashKey, bankCardNo)) {
            dayInAmount = (long) redisTemplate.opsForHash().get(lastDayHashKey, bankCardNo);
        }

        Date selectDate = new Date(date.getYear(), date.getMonth(), date.getDate() - 1);// 昨日时间
        RcLimitData monthInAmountRcLimitData = rcLimitDataMapper
                .selectByDefindCodeAndBusinessTargetIdAndDatetimeAndStatus(rcLimit.getDefineCode(),
                        businessTargetId, RcDateUtils.getMonthString(selectDate) , RcLimitData.Status.PROCESSING.code);
        long monthInAmount = 0;
        if (monthInAmountRcLimitData == null) {
            RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"),
                    businessTargetId, rcLimit.getDefineCode(), dayInAmount + "", date, date ,
                    status, RcDateUtils.getMonthString(selectDate));
            rcLimitDataMapper.insert(rcLimitData);
            monthInAmount = dayInAmount;
        }else {
            monthInAmount = Long.parseLong(monthInAmountRcLimitData.getValue()) + dayInAmount;
            monthInAmountRcLimitData.setValue(monthInAmount + "");
            monthInAmountRcLimitData.setUpdatetime(date);
            monthInAmountRcLimitData.setStatus(status);
            rcLimitDataMapper.updateByPrimaryKey(monthInAmountRcLimitData);
        }
        return monthInAmount;
    }
}

package com.epaylinks.efps.rc.service;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.rc.domain.*;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.dao.RcArchiveMapper;
import com.epaylinks.efps.rc.dao.RcAudLimitMapper;
import com.epaylinks.efps.rc.dao.RcLimitAudMapper;


/**
 * <AUTHOR>
 */
@Service
public class AmountLimitAudService {

	@Autowired
	private RcLimitAudMapper rcLimitAudMapper;

	@Autowired
	private RcAudLimitMapper rcAudLimitMapper;

	@Autowired
	private RcArchiveMapper rcArchiveMapper;
	
    @Autowired
    private OtherMapper otherMapper;

	@Autowired
	private SequenceService sequenceService;


	public PageResult<RcLimitAud> queryAudListPage(String customerCode,String status,Integer pageNum,Integer pageSize,String startTime,String endTime){
		PageResult<RcLimitAud> result = new PageResult<>();

		//查询总数量
		int total = rcLimitAudMapper.pageTotal(customerCode,status,startTime,endTime);

		//查询当前页面
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;
		List<RcLimitAud> list = rcLimitAudMapper.queryByPage(customerCode,status,startTime,endTime,startNum,endNum);

		result.setTotal(total);
		result.setRows(list);

		return result;
	}

	 /**
     * 根据Id查询审核记录
     * @param audId
     * @return
     */
    public RcLimitAud selectLimitAudById(Long audId){
       
        return rcLimitAudMapper.selectByPrimaryKey(audId);
    }


	/**
	 * 根据Id查询审核详情
	 * @param audId
	 * @return
	 */
	public RcLimitAud selectAudByKey(Long audId){
		RcLimitAud rcLimitAud = rcLimitAudMapper.selectByPrimaryKey(audId);

		if(rcLimitAud == null){
			return rcLimitAud;
		}

		//查询风控等级
		RcArchive rcArchive = rcArchiveMapper.selectByTypeAndCode(rcLimitAud.getTargetType(), rcLimitAud.getAudCode());
		rcLimitAud.setRcLevel(rcArchive.getRcLevel());

		//查询默认限额
		List<Map<String,String>> defaultList = rcAudLimitMapper.selectList(rcLimitAud.getAudId(),"DEFAULT_VALUE");
		AmountLimit defaultLimit = trans(defaultList);
		rcLimitAud.setDefaultLimits(defaultLimit);

		//查询申请限额
		List<Map<String,String>> newList = rcAudLimitMapper.selectList(rcLimitAud.getAudId(),"LIMIT_VALUE");
		AmountLimit newLimit = trans(newList);
		rcLimitAud.setNewLimits(newLimit);

		//查询原本限额
		List<Map<String,String>> oldMap = rcAudLimitMapper.selectList(rcLimitAud.getAudId(),"OLD_VALUE");
		if(oldMap.isEmpty()){
			//取默认值
			oldMap.addAll(defaultList);
		}
		AmountLimit oldLimit = trans(oldMap);
		rcLimitAud.setOldLimits(oldLimit);

		return rcLimitAud;
	}


	public int updateByPrimaryKey(RcLimitAud rcLimitAud){
		return rcLimitAudMapper.updateByPrimaryKeySelective(rcLimitAud);
	}

	/**
	 * 获取最新审核记录
	 * @param customerCode
	 * @param targetType
	 * @return
	 */
	public RcLimitAud queryLastRecord(String customerCode, String targetType){
		return rcLimitAudMapper.queryLastRecord(customerCode, targetType);
	}
	
	/**
	 * 获取最新审核意见
	 * @param customerCode
	 * @param targetType
	 * @param audStatus
	 * @return
	 */
    public String queryLastOpinion(String customerCode, String targetType, String audStatus){
        
        RcLimitAud limitAud = rcLimitAudMapper.queryLastRecord(customerCode, targetType);
        if (limitAud == null || !limitAud.getAudStatus().equals(audStatus)) { // 存量不存在审核记录，或者最后的审核状态不一致，则返回空
            return null;
        }
        return limitAud.getLastOpinion();
    }

	public int insertAudLimit(RcAudLimit limit){
		return rcAudLimitMapper.insert(limit);
	}

	public int insertLimitAud(RcLimitAud aud){
		return rcLimitAudMapper.insertSelective(aud);
	}

	/**
	 * 插入审核记录（待审核）
	 * @param rcArchive
	 * @param userId
	 * @return
	 */
	public RcLimitAud insertLimitAudRecord(RcArchive rcArchive, Long userId) {
	    
        RcLimitAud audRecord = new RcLimitAud();
        audRecord.setAudId(sequenceService.nextValue("rc"));
        audRecord.setAudCode(rcArchive.getArchiveCode());
        audRecord.setTargetType(rcArchive.getArchiveType());
        audRecord.setAudName(rcArchive.getArchiveName());
        audRecord.setUserId(userId);
        audRecord.setCreateTime(new Date());
        audRecord.setAudStatus(RcConstants.AudStatus.WAITING.code);
        
        User user = otherMapper.selectUserById(userId);
        if (user != null){
            audRecord.setUserName(user.getName());
        }
        
        rcLimitAudMapper.insert(audRecord);
        
        return audRecord;
	}
	
	
	public AmountLimit trans(List<Map<String,String>> list){
		Map<String,Long> defaultMap = new HashedMap();

		for (Map<String,String> map : list){
			defaultMap.put(map.get("DEFINE_CODE"),Long.valueOf(map.get("LIMIT_VALUE")));
		}
		AmountLimit defaultLimit = new AmountLimit();
        defaultLimit.setInAmountSingle(defaultMap.get(DefineCode.SINGLT_IN.defineCode));
        defaultLimit.setInAmountDay(defaultMap.get(DefineCode.DAY_IN.defineCode));
        defaultLimit.setInAmountMonth(defaultMap.get(DefineCode.MONTH_IN.defineCode));
        defaultLimit.setInAmountYear(defaultMap.get(DefineCode.YEAR_IN.defineCode));
        defaultLimit.setOutAmountSingle(defaultMap.get(DefineCode.SINGLT_OUT.defineCode));
        defaultLimit.setOutAmountDay(defaultMap.get(DefineCode.DAY_OUT.defineCode));
        defaultLimit.setOutAmountMonth(defaultMap.get(DefineCode.MONTH_OUT.defineCode));
        defaultLimit.setOutAmountYear(defaultMap.get(DefineCode.YEAR_OUT.defineCode));
       defaultLimit.setWithdrawAmountDay(defaultMap.get(DefineCode.DAY_WITHDRAW.defineCode));
        defaultLimit.setCardOutAmountDay(defaultMap.get(DefineCode.CARD_DAY_OUT_AMOUNT.defineCode));
        defaultLimit.setCardOutAmountMonth(defaultMap.get(DefineCode.CARD_MONTH_OUT_AMOUNT.defineCode));
        defaultLimit.setCardOutCountDay(defaultMap.get(DefineCode.CARD_DAY_OUT_COUNT.defineCode));
        defaultLimit.setCardOutCountMonth(defaultMap.get(DefineCode.CARD_MONTH_OUT_COUNT.defineCode));

		return defaultLimit;
	}
	

	/**
	 * 根据审核记录ID与code查询限额申请值
	 * @param audId
	 * @param defineCode
	 * @return
	 */
	public RcAudLimit queryAudLimit(Long audId, String defineCode){
	    
		return rcAudLimitMapper.queryAudLimit(audId, defineCode);
	}


	/**
	 * 提交限额修改审核
	 * @param amountLimitObject
	 * @param userId
	 */
    public void submitLimitAud(RcArchive rcArchive, AmountLimitObject amountLimitObject, Long userId) {
        
        String targetType = amountLimitObject.getType();
        
        // 保存审核记录
        RcLimitAud limitAudRecord = insertLimitAudRecord(rcArchive, userId);
        
        if (RcConstants.BusinessTagerType.CUSTOMER_CODE.code.equals(targetType)) {
            MainAmountLimit amountLimit = amountLimitObject.getMainAmount();
            // 20221205 新增商户门户出金设置
            OutAmountLimit outAmountLimit = amountLimitObject.getOutAmountLimit();
            InstAmountLimit instAmountLimit = amountLimitObject.getInstAmountLimit() == null ? new InstAmountLimit() : amountLimitObject.getInstAmountLimit();
            WithdrawAmountLimit withdrawAmount = amountLimitObject.getWithdrawAmount();
            CardAmountLimit cardAmount = amountLimitObject.getCardAmount();
            UserAmountLimit userAmount = amountLimitObject.getUserAmount();
            MerchantAmountLimit merchantAmountLimit = amountLimitObject.getMerchantAmountLimit();
            PlatCustomerAmountLimit platCustomerAmountLimit = amountLimitObject.getPlatCustomerAmountLimit();
        
            // 保存商户维度审核申请值
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), amountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(),outAmountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(),instAmountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), withdrawAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), cardAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), userAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), merchantAmountLimit);
            if (platCustomerAmountLimit != null) {
                saveAudLimitValueFromObj(limitAudRecord.getAudId(), platCustomerAmountLimit);
            }

        } else if (RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(targetType)
                || RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(targetType)) {
            CertificateAmountLimit certificateAmountLimit = amountLimitObject.getCertificateAmountLimit();
            InstAmountLimit instAmountLimit = amountLimitObject.getInstAmountLimit() == null ? new InstAmountLimit() : amountLimitObject.getInstAmountLimit();
            // 保存证件维度审核申请值
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), certificateAmountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(),instAmountLimit);
        
        } else if (RcConstants.BusinessTagerType.TERM.code.equals(targetType)) {
            TermAmountLimit termAmountLimit = amountLimitObject.getTermAmountLimit();
            
            // 保存终端维度审核申请值
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), termAmountLimit);
            
        } else if (RcConstants.BusinessTagerType.PERSON.code.equals(targetType)) {
            PersonAmountLimit personAmountLimit = amountLimitObject.getPersonAmountLimit();
            
            // 保存个人审核申请值
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), personAmountLimit);
        } else if (RcConstants.BusinessTagerType.CLIENT_NO.code.equals(targetType)) {
            MainAmountLimit amountLimit = amountLimitObject.getMainAmount();
            OutAmountLimit outAmountLimit = amountLimitObject.getOutAmountLimit();
            WithdrawAmountLimit withdrawAmount = amountLimitObject.getWithdrawAmount();
            CardAmountLimit cardAmount = amountLimitObject.getCardAmount();
            UserAmountLimit userAmount = amountLimitObject.getUserAmount();
            MerchantAmountLimit merchantAmountLimit = amountLimitObject.getMerchantAmountLimit();

            saveAudLimitValueFromObj(limitAudRecord.getAudId(), amountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(),outAmountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), withdrawAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), cardAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), userAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), merchantAmountLimit);
        } else if (RcConstants.BusinessTagerType.INDUSTRY.code.equals(targetType)) {
            MainAmountLimit amountLimit = amountLimitObject.getMainAmount();
            WithdrawAmountLimit withdrawAmount = amountLimitObject.getWithdrawAmount();
            CardAmountLimit cardAmount = amountLimitObject.getCardAmount();
            UserAmountLimit userAmount = amountLimitObject.getUserAmount();
            MerchantAmountLimit merchantAmountLimit = amountLimitObject.getMerchantAmountLimit();

            saveAudLimitValueFromObj(limitAudRecord.getAudId(), amountLimit);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), withdrawAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), cardAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), userAmount);
            saveAudLimitValueFromObj(limitAudRecord.getAudId(), merchantAmountLimit);
        }
        
        // 风控档案状态更改为审核状态
        rcArchive.setAudStatus(RcConstants.AudStatus.WAITING.code);
        rcArchive.setUpdateTime(new Date());
        rcArchiveMapper.updateByPrimaryKey(rcArchive);
        
    }
    
    /**
     * 保存审核申请值
     * @param audId
     * @param defineCode
     * @param value
     */
    private void savetAudLimitValue(Long audId, String defineCode, String value) {
        
        RcAudLimit record = queryAudLimit(audId, defineCode);
        if (record != null) {// 更新值
            record.setLimitValue(value);
            rcAudLimitMapper.updateByPrimaryKey(record);
        
        } else { // 新增值
            record =  new RcAudLimit();
            record.setAudId(audId);
            record.setDefineCode(defineCode);
            record.setLimitValue(value);
            record.setLimitId(sequenceService.nextValue("rc"));
            
            rcAudLimitMapper.insert(record);
        }
            
    }

    private String getFieldName(Class<?> clazz, String fieldName) {
        return clazz.getSimpleName() + ":" + fieldName;
    }
    
    public void saveAudLimitValueFromObj(Long audId, Object obj) {
        if(obj != null) {
            Class<?> claz = obj.getClass();
            Field[] fields = claz.getDeclaredFields();
            for(Field field:fields) {
                try {
                    field.setAccessible(true);
              /*      ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                    boolean readOnly = property.readOnly();
                    if (readOnly) { // hidden 不保存
                        continue;
                    }*/
                    Object value = field.get(obj);
                    if(value != null) {
                        savetAudLimitValue(audId, getFieldName(claz, field.getName()), String.valueOf(value));
                    }       
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    public <T> T queryAudLimitValueToObj(Long audId, Class<T> claz) {
        T obj;
        try {
            obj = claz.newInstance();
        } catch (InstantiationException | IllegalAccessException e1) {
            return null;
        }
        Field[] fields = claz.getDeclaredFields();
        for(Field field:fields) {
            try {
                field.setAccessible(true);
                RcAudLimit limit = queryAudLimit(audId, getFieldName(claz, field.getName()));
                // 存在历史数据未拼接类名，审核时查询不到对应值
                if (limit == null) {
                    limit = queryAudLimit(audId,field.getName());
                }
                if (limit != null) {
                    field.set(obj, Long.parseLong(limit.getLimitValue()));    
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return obj;
        
    }

}

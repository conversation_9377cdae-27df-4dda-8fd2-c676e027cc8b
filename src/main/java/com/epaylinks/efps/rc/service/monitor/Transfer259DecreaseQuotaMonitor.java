package com.epaylinks.efps.rc.service.monitor;

import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.OtherMapper;
import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.service.Transfer259LimitService;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【PJ23】259 代付交易管控，扣减不可代付额度
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/19 15:58
 */
@Service
public class Transfer259DecreaseQuotaMonitor implements TransactionMonitor {
    @Autowired
    private OtherMapper otherMapper;

    @Autowired
    private RcCalculteBasic rcCalculteBasic;

    @Autowired
    private Transfer259LimitService limitService;

    @Override
    public void monitor(RcTxsOrder order) {
        //账户分账不处理
        if (rcCalculteBasic.isZHFZ(order.getIndexMap().get("payMethod"))) {
            return;
        }

        //上游代付不处理
        if (Constants.rcBusinessType.WITHDRAW.code.equals(order.getBusinessType()) && otherMapper.isChannelTransfer(order.getTransactionNo()) > 0) {
            return;
        }

        //减额度
        rcCalculteBasic.redisIncr(limitService.getRedisKey(order.getCustomerCode()), -order.getAmount());
    }

    @Override
    public boolean shouldMonitor(RcTxsOrder order) {
        return Constants.PayState.SUCCESS.code.equals(order.getPayState()) &&
                (
                        (
                                Constants.rcBusinessType.INSIDE_PAY_OUT.code.equals(order.getBusinessType()) ||
                                        Constants.rcBusinessType.WITHDRAW.code.equals(order.getBusinessType()) ||
                                        Constants.rcBusinessType.REFUND.code.equals(order.getBusinessType())
                        ) ||
                                "TKDJ".equals(order.getBusinessCode())
                ) &&
                !order.getTransactionNo().startsWith(TransactionType.FZJY.no) &&
                order.getAmount() != null &&
                order.getCustomerCode() != null &&
                order.getTargetIdMap().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code) != null; //只处理商户，个人不处理
    }
}

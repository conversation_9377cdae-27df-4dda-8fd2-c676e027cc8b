package com.epaylinks.efps.rc.service;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.domain.cust.CustomerTotalConfig;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = "cust")
public interface CustService {


    @RequestMapping(value = "/business/changStatus", method = RequestMethod.POST)
    CommonOuterResponse<Object> changStatus(
            @RequestParam(value = "customerCode", required = true) String customerCode,    //"客户编码"
            @RequestParam(value = "businessCode", required = true) String businessCode,    //"业务编码"
            @RequestParam(value = "settCycle", required = false) String settCycle,    //"结算周期"
            @RequestParam(value = "status", required = true) String status     //"更新状态：0：停用，1：启用"
    );

    /**
     * 审核之后更新配置
     * @param newValues
     * @param userId
     * @return
     */
    @PostMapping("/totalControl/saveLocConfigList")
    CommonOuterResponse saveLocConfigList(@RequestBody List<String> newValues,
                                                 @RequestHeader(value = "x-userid") Long userId);

    /**
     * 添加待办
     * @param permId
     * @param detail
     * @param itemType
     * @param frontParam
     * @param uniqueTag
     * @param operUserId
     * @return
     */
    @PostMapping("/itemRecord/checkRCItem")
    CommonOuterResponse<String> checkRCItem(
            @RequestParam(value = "permId", required = true) String permId,
            @RequestParam(value = "detail", required = true) String detail,
            @RequestParam(value = "itemType", required = true) String itemType,
            @RequestParam(value = "frontParam", required = true) String frontParam,
            @RequestParam(value = "uniqueTag", required = true) String uniqueTag,
            @RequestParam(value = "operUserId", required = false) Long operUserId);

    /**
     * 更新待办
     * @param itemType
     * @param uniqueTag
     * @return
     */
    @PostMapping("/itemRecord/updateRCItem")
    CommonOuterResponse<String> updateRCItem(
            @RequestParam(value = "itemType", required = true) String itemType,
            @RequestParam(value = "uniqueTag", required = true) String uniqueTag);

    @GetMapping("/customer/queryCustomerByCustomerNo")
    Customer queryCustomerByCustomerNo(@RequestParam(value = "customerNo") String customerNo);

    @PostMapping("/emailSend/emailForExportWebsiteValid")
    void emailForExportWebsiteValid(@RequestParam("email") String email,@RequestParam("uniqueId") String uniqueId);

    /**
     * 查询商户总控设置（支持平台商、代理商）
     *
     * @param customerCode
     * @return
     */
    @RequestMapping(value = "/customer/queryTotalConfig", method = RequestMethod.GET)
    CustomerTotalConfig queryTotalConfig(@RequestParam("customerCode") String customerCode);

    @RequestMapping(value = "/merchant/saveSingleOperation", method = RequestMethod.POST)
    CommonOuterResponse saveSingleOperation(@RequestParam(value = "customerNo") String customerNo,
                                            @RequestParam(value = "commont",required = false) String commont,
                                            @RequestParam(value = "title") String title,
                                            @RequestHeader(value = "x-userid") Long userId);

    @RequestMapping(value = "/customer/syncCustomerRcLevel",method = RequestMethod.POST)
    CommonOuterResponse syncCustomerRcLevel(@RequestParam(value = "customerNo") String customerNo,@RequestParam("limitOutTrade") String limitOutTrade);
}
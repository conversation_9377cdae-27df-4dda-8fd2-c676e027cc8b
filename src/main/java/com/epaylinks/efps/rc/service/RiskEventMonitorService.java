package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.rc.domain.RcTxsOrder;
import com.epaylinks.efps.rc.drools.DroolsService;
import com.epaylinks.efps.rc.service.monitor.TransactionMonitor;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 17:50
 */
@Service
public class RiskEventMonitorService {
    private static final Logger log = LoggerFactory.getLogger(RiskEventMonitorService.class);

    @Autowired
    private List<TransactionMonitor> transactionMonitors;

    @Autowired
    private DroolsService droolsService;

    private static final ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5); // 创建一个线程池

    /**
     * 使用规则引擎
     * @param txsPayResultMsg 交易信息
     */
    public void asyncDroolsMonitor(TxsPayResultMsg txsPayResultMsg) {
        fixedThreadPool.execute(() -> {
            try {
                droolsService.fireRule(txsPayResultMsg);
            } catch (Exception e) {
                log.error(txsPayResultMsg.getTransactionNo() + "监控异常", e);
            }
        });
    }

    public void asyncMonitor(RcTxsOrder order) {
        fixedThreadPool.execute(() -> {
            for (TransactionMonitor transactionMonitor : transactionMonitors) {
                try {
                    if (transactionMonitor.shouldMonitor(order)) {
                        transactionMonitor.monitor(order);
                    }
                } catch (Exception e) {
                    log.error(order.getTransactionNo() + "监控异常", e);
                }
            }
        });
    }
}

package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.common.LogService;
import com.epaylinks.efps.rc.dao.BwListMapper;
import com.epaylinks.efps.rc.domain.BwList;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcAuditRecord;
import com.epaylinks.efps.rc.domain.RcOperateLog;
import com.epaylinks.efps.rc.service.*;
import com.epaylinks.efps.rc.util.CardUtils;
import com.epaylinks.efps.rc.util.DesensitizeUtils;
import com.epaylinks.efps.rc.vo.AuditBwListVo;
import com.epaylinks.efps.rc.vo.AuditBwQueryResponse;
import com.epaylinks.efps.rc.vo.BwListVo;


import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
@Service
public class BwListServiceImpl implements BwListService {

	@Autowired
	private BwListMapper bwListMapper;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private RcOperateLogService rcOperateLogService;

    @Autowired
    private OtherService otherService;

    @Autowired
    private RcAuditRecordService rcAuditRecordService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private BwListService self;

    @Autowired
    private LogService logService;

    @Autowired
    private CustService custService;

    @Autowired
    private BlackListService blackListService;

    /*
    private static Map<String, String> RISK_TAG_MAP = ImmutableMap.<String, String>builder()
            .put("gambling","涉赌")
            .put("pyramid","涉传销")
            .put("fraud","涉诈")
            .put("none","无风险标签")
            .put("cashout","疑似套现")
            .put("violation","疑似违规")
            .build();
            */
    private static Map<String, String> RISK_TAG_MAP = RcConstants.RiskTag.getRiskTagMap();

    /**
     * 为黑白名单中的对象类型：001身份证；002手机号；003社会统一信用代码；004银行卡添加hash
     * @param bwList
     */
    private void hashBwList(BwList bwList){
        if(Objects.isNull(bwList)){
            return;
        }
        if(RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(bwList.getBusinessTagerType()) ||
           RcConstants.BusinessTagerType.CELL_PHONE_NUMBER.code.equals(bwList.getBusinessTagerType()) ||
           RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(bwList.getBusinessTagerType()) ||
           RcConstants.BusinessTagerType.BANK_CARD.code.equals(bwList.getBusinessTagerType())){
            bwList.setTagerHash(CardUtils.getHash(bwList.getBusinessTagerId()));
        }
    }


	@Logable(businessTag = "bwList.saveBwList")
	@Override
	public void saveBwList(BwList bwList) throws Exception {

        bwList.setStatus(RcConstants.AuditStatus.SUCCESS.code);
        bwList.setUseStatus(RcConstants.UseStatus.ENABLE.code);
        hashBwList(bwList);
        if (bwList.getBwId() == null) {// 新增
            bwList.setBwId(sequenceService.nextValue("rc"));
            //先插入oracle中
            bwListMapper.insertSelective(bwList);

        } else {// 审核更新
            bwListMapper.updateByPrimaryKey(bwList);
        }

		//再插入redis中
		String key = getKey(bwList.getBwType());

		//存值
		redisTemplate.opsForHash().put(key,bwList.getBusinessTagerId(),bwList);

		// region 插入操作日志
		RcOperateLog log = new RcOperateLog();
		// 在名单管理菜单里
		log.setPermId("80101");
		log.setCode(getTargetIdName(bwList));
		log.setName(bwList.getBusinessTagerTypeName());
		log.setType("1");
		log.setOperateContent("新增");
		log.setOperator(String.valueOf(bwList.getUserId()));
		log.setOperateTime(new Date());
		rcOperateLogService.insert(log);
		// endregion

	}

	@Override
	@Logable(businessTag = "queryValueInBw")
	public Map<String,Boolean> queryValueInBw(String bwType, List<String> list) {
		Map<String,Boolean> map = new HashedMap();
		//从redis中获取数据
		String key = getKey(bwType);
		for(String value : list){
			map.put(value,false);
            Object object = redisTemplate.opsForHash().get(key, value);
            if (object != null){
                map.put(value,true);
            }
        }
		return map;
	}

	String getKey(String type){
		String key = "";
		if (BwList.BwType.BLACK.code.equals(type)){
			key = BwList.BwType.BLACK.message;
		}else if(BwList.BwType.WHITE.code.equals(type)){
			key = BwList.BwType.WHITE.message;
		}else if(BwList.BwType.GREY.code.equals(type)){
            key = BwList.BwType.GREY.message;
        }
		return key;
	}

	@Override
	public boolean queryExistence(BwList bwList) {
		return bwListMapper.queryExistence(bwList) != 0?true:false;
	}

	@Logable(businessTag = "delete")
	@Override
	public void deleteByPrimaryKey(Long bwId) {
	    //先查出来
        BwList bwList = bwListMapper.selectByPrimaryKey(bwId);

		//先删除oracle里面的数据
		int i = bwListMapper.deleteByPrimaryKey(bwId);

		//判断删除是否成功
		if(i == 1){
			//再删除redis里面的数据
			String key = getKey(bwList.getBwType());
			redisTemplate.opsForHash().delete(key,bwList.getBusinessTagerId());

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			// 在名单管理菜单里
			log.setPermId("80101");
			log.setCode(getTargetIdName(bwList));
			log.setName(bwList.getBusinessTagerTypeName());
			log.setType("3");
			log.setOperateContent("删除");
			log.setOperator(String.valueOf(bwList.getUserId()));
			log.setOperateTime(new Date());
			rcOperateLogService.insert(log);
			// endregion

		}
	}

	@Override
	public void updateByPrimaryKeySelective(BwList bwList) throws Exception {
		//先从数据库中获取修改前的黑白名单状态
		BwList origBwList = bwListMapper.selectByPrimaryKey(bwList.getBwId());
        bwList.setUseStatus(RcConstants.UseStatus.ENABLE.code); // 改使用状态
        bwList.setStatus(RcConstants.BwListStatus.NORMAL.code); // 审核状态

		//先更新oracle数据库
        hashBwList(bwList);
		int i = bwListMapper.updateByPrimaryKeySelective(bwList);

		//判断oracle是否更新成功
		if(i == 1){

		    // 先删除旧名单缓存
            redisTemplate.opsForHash().delete(getKey(origBwList.getBwType()), origBwList.getBusinessTagerId());
            // 写入新名单缓存
			redisTemplate.opsForHash().put(getKey(bwList.getBwType()), bwList.getBusinessTagerId(), bwList);

			// region 插入操作日志
			RcOperateLog log = new RcOperateLog();
			// 在名单管理菜单里
			log.setPermId("80101");
			log.setCode(getTargetIdName(origBwList));
			log.setName(origBwList.getBusinessTagerTypeName());
			log.setType("2");
			log.setOperator(String.valueOf(bwList.getUserId()));
			log.setOperateTime(new Date());

			if (bwList.getBusinessTagerType() != null && !bwList.getBusinessTagerType().equals(origBwList.getBusinessTagerType())) {
				log.setId(-1);
				log.setOperateContent("类型名称");
				log.setOrigValue(origBwList.getBusinessTagerTypeName());
				log.setNewValue(bwList.getBusinessTagerTypeName());
				rcOperateLogService.insert(log);
			}
			if (bwList.getBwType() != null && !bwList.getBwType().equals(origBwList.getBwType())) {
				log.setId(-1);
				// 0黑名单 1白名单
				log.setOperateContent("黑白名单");
				log.setOrigValue("0".equals(origBwList.getBwType()) ? "黑名单" : "白名单");
				log.setNewValue("0".equals(bwList.getBwType()) ? "黑名单" : "白名单");
                // 新增灰名单
                if ("2".equals(origBwList.getBwType())) {
                    log.setOrigValue("灰名单");
                }
                if ("2".equals(bwList.getBwType())) {
                    log.setNewValue("灰名单");
                }
				rcOperateLogService.insert(log);
			}
			if (bwList.getBusinessTagerId() != null && !bwList.getBusinessTagerId().equals(origBwList.getBusinessTagerId())) {
				log.setId(-1);
				log.setOperateContent("类型值");
				log.setOrigValue(getTargetIdName(origBwList));
				log.setNewValue(bwList.getBusinessTagerId());
				rcOperateLogService.insert(log);
			}
			if (bwList.getRemark() != null && !bwList.getRemark().equals(origBwList.getRemark())) {
				log.setId(-1);
				log.setOperateContent("备注");
				log.setOrigValue(origBwList.getRemark());
				log.setNewValue(bwList.getRemark());
				rcOperateLogService.insert(log);
			}
            if (!Objects.equals(origBwList.getRiskTag(),bwList.getRiskTag())) {
                log.setId(-1);
                log.setOperateContent("风控标签");
                log.setOrigValue(getRiskTag(origBwList.getRiskTag()));
                log.setNewValue(getRiskTag(bwList.getRiskTag()));
                rcOperateLogService.insert(log);
            }
			// endregion

		}
	}

	@Override
	public PageResult<BwListVo> pageQuery(int pageNum, int pageSize, Map<String, Object> map,Boolean download) throws Exception {

	    PageResult<BwListVo> result = new PageResult<>();
        List<String> targetTypeList = new ArrayList<>();
        targetTypeList.add("BW_LIST");

		//查询总记录数
		// int total = bwListMapper.queryTotal(map.get("bwType"),map.get("startTime"),map.get("endTime"),map.get("businessTagerType"),map.get("businessTagerId"));
	    int total = bwListMapper.queryTotal(map);
		result.setTotal(total);

		//当前页面记录
		int endNum = pageSize * pageNum;
		int startNum = endNum - pageSize + 1;

		map.put("startNum", startNum);
		map.put("endNum", endNum);
        if (download) {
            map.put("endNum", total);
        }
		// List<BwList> list = bwListMapper.queryByPage(startNum,endNum,map.get("bwType"),map.get("startTime"),map.get("endTime"),map.get("businessTagerType"),map.get("businessTagerId"));
		List<BwList> list = bwListMapper.queryByPage(map);
		List<BwListVo> resultList = new ArrayList<BwListVo>();
		if (list != null && !list.isEmpty()) {
		    list.forEach(bwList -> {
		        BwListVo vo = new BwListVo();
		        BeanUtils.copyProperties(bwList, vo);
	            vo.setBusinessTagerName(getTargetIdName(bwList));
                if (bwList.getStatus() != null && bwList.getStatus().compareTo(RcConstants.AuditStatus.WAITING.code) != 0) {
                    RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord("BW_LIST",bwList.getBwId());
                    if (rcAuditRecord != null) {
                        vo.setComments(rcAuditRecord.getRemarks());
                    }
                }
                map.put("targetTypeList",targetTypeList);
                map.put("targetId",bwList.getBwId());
                List<RcAuditRecord> records = rcAuditRecordService.listByParam(map);
                if (records != null && records.size() > 0) {
                    RcAuditRecord record = records.get(0);
                    vo.setOldValue(JSONObject.parseObject(record.getOldValue(),Map.class));
                    vo.setNewValue(JSONObject.parseObject(record.getNewValue(),Map.class));
                }
                if (download) {
                    vo.setRiskTag(translateRiskTag(vo.getRiskTag()).stream().collect(Collectors.joining(",")));
                }
		        resultList.add(vo);
		    });
		}
		result.setRows(resultList);
		return result;
	}

    @Override
    public List<BwListVo> exportBwList(Map<String, Object> map) {
        List<String> targetTypeList = new ArrayList<>();
        targetTypeList.add("BW_LIST");
        List<BwList> list = bwListMapper.queryByPage(map);
        List<BwListVo> resultList = new ArrayList<BwListVo>();
        if (list != null && !list.isEmpty()) {
            list.forEach(bwList -> {
                BwListVo vo = new BwListVo();
                BeanUtils.copyProperties(bwList, vo);
                vo.setBusinessTagerName(getTargetIdName(bwList));
                if (bwList.getStatus() != null && bwList.getStatus().compareTo(RcConstants.AuditStatus.WAITING.code) != 0) {
                    RcAuditRecord rcAuditRecord = rcAuditRecordService.queryAuditRecord("BW_LIST",bwList.getBwId());
                    if (rcAuditRecord != null) {
                        vo.setComments(rcAuditRecord.getRemarks());
                    }
                }
                map.put("targetTypeList",targetTypeList);
                map.put("targetId",bwList.getBwId());
                List<RcAuditRecord> records = rcAuditRecordService.listByParam(map);
                if (records != null && records.size() > 0) {
                    RcAuditRecord record = records.get(0);
                    vo.setOldValue(JSONObject.parseObject(record.getOldValue(),Map.class));
                    vo.setNewValue(JSONObject.parseObject(record.getNewValue(),Map.class));
                }
                vo.setRiskTag(translateRiskTag(vo.getRiskTag()).stream().collect(Collectors.joining(",")));
                vo.setBusinessTagerTypeName(RcConstants.BusinessTagerType.getCommentByCode(vo.getBusinessTagerType()));
                resultList.add(vo);
            });
        }
        return resultList;
    }

    @Override
	public boolean isBlackList(String businessTagerType, String businessTagerId) {
	    List<BwList> list = bwListMapper.getByTagerTypeAndTagerId(businessTagerType, businessTagerId);
		// 0黑名单 1白名单
	    for(BwList bwList: list) {
	        if ("0".equals(bwList.getBwType()) && !RcConstants.UseStatus.DISABLE.code.equals(bwList.getUseStatus())) {
	            return true;
	        }
	    }
		return false;
	}

	private String getTargetIdName(BwList bwList) {

	    if (RcConstants.BusinessTagerType.BUSINESS_ADDRESS.code.equals(bwList.getBusinessTagerType())
                && bwList.getBusinessTagerId() != null &&  bwList.getBusinessTagerId().contains("-")) {
            // 翻译省市
            String addr[] = bwList.getBusinessTagerId().split("-");
            return (otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[0], true)
                    + "-"
                    + otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[1], true));
        } else {
            return bwList.getBusinessTagerId();
        }

	}

    private String getRiskTag(String tag) {
        return String.join(",", translateRiskTag(tag));
    }

    @Override
    public int blackOrWhite(String businessTagerType, String businessTagerId) {

        List<BwList> list = bwListMapper.getByTagerTypeAndTagerId(businessTagerType, businessTagerId);
        boolean isBlack = false;
        boolean isWhite = false;
        for(BwList bwList: list) {
            if ( RcConstants.UseStatus.DISABLE.code.equals(bwList.getUseStatus())) {
                continue; // 未使用状态不作处理
            }
            if ("0".equals(bwList.getBwType())) {
                isBlack = true;
            } else if ("1".equals(bwList.getBwType())) { // 一旦判断为白名单，则直接跳出（白名单优先）
                isWhite = true;
                break;
            }
        }

        if (isWhite) {// 属于白名单 
            return 1;
        }

        if (isBlack) {// 属于黑单
            return -1;
        }

        return 0;
    }


    @Override
    public AuditBwQueryResponse queryAuditBwList(Long bwId) {

        BwList bwRecord = bwListMapper.selectByPrimaryKey(bwId);
        if (bwRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.BW_LIST.code, bwId);
        if (auditRecord == null) {
            throw new AppException(RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.AUD_RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        // 旧数据
        AuditBwListVo oldValueObj = JSONObject.parseObject(auditRecord.getOldValue(), AuditBwListVo.class);
        // 新数据        
        AuditBwListVo newValueObj = JSONObject.parseObject(auditRecord.getNewValue(), AuditBwListVo.class);

        AuditBwQueryResponse bwObj = new AuditBwQueryResponse();
        bwObj.setActionType(auditRecord.getActionType());
        bwObj.setNewBwObject(newValueObj);
        bwObj.setOldBwObject(oldValueObj);
        bwObj.setBwId(bwId);

        return bwObj;
    }


    /**
     * 创建黑白名单待审核记录
     * @param bwList
     * @throws Exception
     */
    @Logable(businessTag = "bwList.saveAuditBwList")
    @Override
    @Transactional
    public RcAuditRecord saveCreateBwAuditRecord(BwList bwList, Long userId) throws Exception {
        CommonOuterResponse response = new CommonOuterResponse();

        bwList.setUserId(userId);
//        bwList.setBwId(sequenceService.nextValue("rc"));
        bwList.setStatus(RcConstants.BwListStatus.WAITING.code);
        bwList.setUseStatus(RcConstants.UseStatus.DISABLE.code); // 新增审核为不可用状态

        // 先插入oracle中
        hashBwList(bwList);
        bwListMapper.insertSelective(bwList);

        AuditBwListVo vo = new AuditBwListVo();
        BeanUtils.copyProperties(bwList, vo);
        // 保存待审核记录
        RcAuditRecord record =  rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.BW_LIST.code,
                bwList.getBwId(), null, vo.toString(), RcConstants.AuditActionType.CREATE.code, userId,null);
        // 添加待办
        try {
            String targetId = bwList.getBusinessTagerId();
            String type = bwList.getBusinessTagerType();
            if ("001".equals(type) || "003".equals(type)) {
                targetId = DesensitizeUtils.getHiddenCreditCardNo(targetId);
            } else if ("002".equals(type)) {
                targetId = DesensitizeUtils.getHiddenMobilePhone(targetId);
            } else if ("004".equals(type)) {
                targetId = DesensitizeUtils.getHiddenBankCardNo(targetId);
            }
            if ("007".equals(bwList.getBusinessTagerType())) {
                response = custService.checkRCItem("80101",getTargetIdName(bwList) + "_名单管理待审核", "LIST_MANAGEMENT",
                        bwList.getBwType() + "," + bwList.getBusinessTagerType() + "," + bwList.getBusinessTagerId(),"record-" + record.getId(),userId);
            } else {
                response = custService.checkRCItem("80101",targetId + "_名单管理待审核", "LIST_MANAGEMENT",
                        bwList.getBwType() + "," + bwList.getBusinessTagerType() + "," + bwList.getBusinessTagerId(),"record-" + record.getId(),userId);
            }
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }
        if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }

        return record;
    }


    @Override
    public void saveDelBwAuditRecord(Long bwId, Long userId) {

        BwList bwRecord = bwListMapper.selectByPrimaryKey(bwId);
        if (bwRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        bwRecord.setUserId(userId);
        bwRecord.setStatus(RcConstants.BwListStatus.WAITING.code);
//        bwList.setUseStatus(RcConstants.UseStatus.DISABLE.code); // 不需更改使用状态
        // 先插入oracle中
        bwListMapper.updateByPrimaryKeySelective(bwRecord);

        AuditBwListVo vo = new AuditBwListVo();
        BeanUtils.copyProperties(bwRecord, vo);
        // 保存待审核记录
        rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.BW_LIST.code,
                bwRecord.getBwId(), vo.toString(), null, RcConstants.AuditActionType.DELETE.code, userId,null);

    }

    @Override
    @Transactional
    public void saveUpdateBwAuditRecord(BwList bwList, Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();

        BwList bwRecord = bwListMapper.selectByPrimaryKey(bwList.getBwId());
        if (bwRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        AuditBwListVo oldVo = new AuditBwListVo();
        BeanUtils.copyProperties(bwRecord, oldVo);

        AuditBwListVo newVo = new AuditBwListVo();
        BeanUtils.copyProperties(bwList, newVo);

        bwRecord.setUserId(userId);
        bwRecord.setStatus(RcConstants.BwListStatus.WAITING.code);
//      bwList.setUseStatus(RcConstants.UseStatus.DISABLE.code); // 不需更改使用状态

        // 更新
        //bwListMapper.updateByPrimaryKeySelective(bwRecord); // 更新原值状态为待审核
        bwListMapper.updateByPrimaryKey(bwRecord);

        // 保存待审核记录
        RcAuditRecord record = rcAuditRecordService.saveAuditRecord(RcConstants.AuditTargetType.BW_LIST.code,
                bwList.getBwId(), oldVo.toString(), newVo.toString(), RcConstants.AuditActionType.UPDATE.code, userId,null);

        // 添加待办
        try {
            String targetId = bwList.getBusinessTagerId();
            String type = bwList.getBusinessTagerType();
            if ("001".equals(type) || "003".equals(type)) {
                targetId = DesensitizeUtils.getHiddenCreditCardNo(targetId);
            } else if ("002".equals(type)) {
                targetId = DesensitizeUtils.getHiddenMobilePhone(targetId);
            } else if ("004".equals(type)) {
                targetId = DesensitizeUtils.getHiddenBankCardNo(targetId);
            }
            if ("007".equals(bwList.getBusinessTagerType())) {
                response = custService.checkRCItem("80101",getTargetIdName(bwRecord) + "_名单管理待审核", "LIST_MANAGEMENT",
                        bwRecord.getBwType() + "," + bwRecord.getBusinessTagerType() + "," + bwRecord.getBusinessTagerId(),"record-" + record.getId(),userId);
            } else {
                response = custService.checkRCItem("80101",targetId + "_名单管理待审核", "LIST_MANAGEMENT",
                        bwRecord.getBwType() + "," + bwRecord.getBusinessTagerType() + "," + bwRecord.getBusinessTagerId(),"record-" + record.getId(),userId);
            }
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }
        if (!response.getReturnCode().equals(CommonOuterResponse.success().getReturnCode())) {
            throw new AppException(response.getReturnCode(),response.getReturnMsg());
        }
    }

    @Transactional
    @Logable(businessTag = "bwList.auditBwList")
    @Override
    public void auditBwList(Long bwId, Short auditResult, Long userId,String comments) throws Exception {

        BwList bwRecord = bwListMapper.selectByPrimaryKey(bwId);
        if (bwRecord == null) {
            throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code, RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
        }
        RcAuditRecord auditRecord = rcAuditRecordService.queryAuditRecord(RcConstants.AuditTargetType.BW_LIST.code, bwId);

        if (RcConstants.AuditStatus.SUCCESS.code.equals(auditResult)) {// 审核成功

            if (RcConstants.AuditActionType.CREATE.code.equals(auditRecord.getActionType())){
                // 新增审核通过，改黑白名单记录状态，写缓存，加日志
                bwRecord.setStatus(auditResult);
                self.saveBwList(bwRecord);

                // 身份证、营业执照增加黑名单处理，增加对商户冻结 2021.2.1
                // self.frozenByBwListCertNo(bwRecord); // 不冻结，2021.2.5，就是这快

            } else if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())){
                AuditBwListVo newValueObj = JSONObject.parseObject(auditRecord.getNewValue(), AuditBwListVo.class);
                BeanUtils.copyProperties(newValueObj, bwRecord);
                // 更新审核通过，改黑白名单记录状态，写缓存，加日志
                self.updateByPrimaryKeySelective(bwRecord);

                // 身份证、营业执照增加黑名单处理，增加对商户冻结 2021.2.1
                // self.frozenByBwListCertNo(bwRecord); // 不冻结，2021.2.5，就是这快

            } else if  (RcConstants.AuditActionType.DELETE.code.equals(auditRecord.getActionType())){
               // 删除某条名单信息时，如果审核通过，则删掉该条名单信息，再次查询则无法查到（该逻辑保持与现在的删除一致）
               self.deleteByPrimaryKey(bwId);
            }

        } else {// 驳回

            if (RcConstants.AuditActionType.CREATE.code.equals(auditRecord.getActionType())){
               // 2）新增某条名单信息时，如果驳回，则删掉该条名单信息（无缓存），再次查询则无法查到
                bwListMapper.deleteByPrimaryKey(bwId);

            } else if (RcConstants.AuditActionType.UPDATE.code.equals(auditRecord.getActionType())
                    || RcConstants.AuditActionType.DELETE.code.equals(auditRecord.getActionType())){
                // 更新或删除被驳回时，更新记录状态即可，不需其他处理
                bwRecord.setStatus(RcConstants.BwListStatus.NORMAL.code);
                bwListMapper.updateByPrimaryKey(bwRecord);
            }
        }
        // 更新审核记录状态
        rcAuditRecordService.updateAuditRecordStatus(RcConstants.AuditTargetType.BW_LIST.code, bwId, auditResult, userId,comments);
        // 更新待办
        custService.updateRCItem("LIST_MANAGEMENT","record-" + auditRecord.getId());
    }

    @Override
    @Transactional
    public void batchAuditBwList(String bwIds, Short auditResult, Long userId,String comments) throws Exception {
        if (StringUtils.isEmpty(bwIds)) {
            throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message + "：不能为空");
        }
        String[] strs = bwIds.split(",");
        for (int i = 0; i < strs.length; i++) {
            Long bwId;
            try {
                bwId = Long.parseLong(strs[i]);
            } catch (Exception e) {
                throw new AppException(RcCode.PARAM_ERROR.code,RcCode.PARAM_ERROR.message);
            }
            auditBwList(bwId,auditResult,userId,comments);
        }
    }


    @Override
    public void frozenByBwListCertNo(BwList bwRecord){

        if (!"0".equals(bwRecord.getBwType())
                || !RcConstants.UseStatus.ENABLE.code.equals(bwRecord.getUseStatus())
                || (!RcConstants.BusinessTagerType.IDENTITY_CARD.code.equals(bwRecord.getBusinessTagerType())
                        && !RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(bwRecord.getBusinessTagerType()))
        ) { // 非黑名单，非营业执照，非身份证，返回
            return ;
        }

//      List<RcArchive> list = rcArchiveService.selectByCertificateNo(bwRecord.getBusinessTagerId());
        List<String> list = null;
        if (RcConstants.BusinessTagerType.BUSINESS_LICENSE.code.equals(bwRecord.getBusinessTagerType())) {
            list = otherService.selectCustomerByBusinessLicenseNo(bwRecord.getBusinessTagerId());
        } else {
            list = otherService.selectCustomerByCertificateNo(bwRecord.getBusinessTagerId());
        }

        if (list == null || list.isEmpty()){
            return;
        }

        list.forEach(customerNo -> {
            // 冻结商户
            try {
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerNo);
                if (rcArchive != null) {
                    rcArchiveService.saveRcArchiveStatus(rcArchive.getArchiveId(), RcArchive.Status.RC.statusName, RcConstants.RcStatus.FROZEN.code, "证件号黑名单",null, 0L,false,null);
                }
            }catch(Exception e) {
                logService.printLog(e);
            }
        });

    }

    @Override
    public List<BwList> getByTagerTypeAndTagerId(String businessTargetType, String businessTargetId) {
        return bwListMapper.getByTagerTypeAndTagerId(businessTargetType,businessTargetId);
    }

    @Override
    public List<BwList> getByTagerType(String businessTargetType) {
        return bwListMapper.getByTagerType(businessTargetType);
    }

    @Override
    public void hashOldInfo() {
        int pageNum = 1;
        int pageSize = 50;
        List<BwList> oldList;
        do{
            //分页查询需要添加哈希的黑白名单信息
            Map map = new HashMap();
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            oldList = bwListMapper.selectNoHashByPage(map);
            if(Objects.nonNull(oldList) && oldList.size()>0){
                for(BwList oldInfo : oldList){
                    if(Objects.nonNull(oldInfo)){
                        try {
                            //System.out.println("存量数据添加哈希："+oldInfo.getBwId());
                            hashBwList(oldInfo);
                            bwListMapper.updateHashInfoBySelective(oldInfo);
                        }catch(Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }while (Objects.nonNull(oldList) && oldList.size()>0);
    }

    @Override
    public List<String> getBwRiskTags(String businessTargetType, String businessTargetId) {
        List<BwList> bwLists = bwListMapper.getByTagerTypeAndTagerId(businessTargetType,businessTargetId);
        if (bwLists == null || bwLists.isEmpty()) {
            return Collections.emptyList();
        }

        return translateRiskTag(bwLists.get(0).getRiskTag());
    }

    @Override
    public List<String> translateRiskTag(String riskTag) {
        if (!StringUtils.isEmpty(riskTag)) {
            return Arrays.stream(riskTag.split(","))
                    .map(tag -> Optional.ofNullable(RISK_TAG_MAP.get(tag)).orElse(tag))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public void processStockData() {

    }

    public List<String> getBwRiskTagCode(String businessTargetType, String businessTargetId) {
        List<String> tagList = new ArrayList<>();
        List<BwList> list = bwListMapper.getByTagerTypeAndTagerId(businessTargetType, businessTargetId);
        if (list.isEmpty()) {
            tagList.add("norisk");
        } else {
            BwList bwList = list.get(0);
            //黑名单
            if ("0".equals(bwList.getBwType()) && !RcConstants.UseStatus.DISABLE.code.equals(bwList.getUseStatus())) {
                if (StringUtils.isEmpty(bwList.getRiskTag()) || "none".equals(bwList.getRiskTag())) {
                    tagList.add("otherrisk");
                } else {
                    tagList.addAll(Arrays.asList(bwList.getRiskTag().split(",")));
                }
            } else {
                tagList.add("norisk");
            }
        }
        return tagList;
    }

    @Override
    public Map<String, String> getInnerBlackList(Map<String, String> typeValue) {
        typeValue.entrySet().removeIf(entry -> blackListService.queryByTypeAndValue(entry.getKey(),entry.getValue()) == null);
        return typeValue;
    }
}


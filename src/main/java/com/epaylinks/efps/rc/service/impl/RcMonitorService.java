package com.epaylinks.efps.rc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcCloseBusinessMapper;
import com.epaylinks.efps.rc.dao.RcCloseTermMapper;
import com.epaylinks.efps.rc.dao.RiskEventRecordMapper;
import com.epaylinks.efps.rc.domain.*;
import com.epaylinks.efps.rc.kafka.KafkaProducer;
import com.epaylinks.efps.rc.service.CustService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RiskEventRuleService;
import com.epaylinks.efps.rc.vo.TxsPayResultMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/25 15:21
 */
@Service
public class RcMonitorService {
    private static final Logger log = LoggerFactory.getLogger(RcMonitorService.class);

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private RcCloseTermMapper rcCloseTermMapper;

    @Autowired
    private CustService custService;

    @Autowired
    private RcCloseBusinessMapper rcCloseBusinessMapper;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    @Autowired
    private RiskEventRuleService riskEventRuleService;

    @Autowired
    private RiskEventRecordMapper riskEventRecordMapper;

    @Autowired
    private OtherService otherService;

    public enum AccountStatus {
        NORMAL("0"),
        FREEZE("1"),
        BAN_PAY_OUT("2"),
        BAN_PAY_IN("3");

        public final String code;

        AccountStatus(String code) {
            this.code = code;
        }
    }

    public boolean changeAccountStatus(String customerCode, AccountStatus accountStatus, String reason) {
        RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);
        if (accountStatus != AccountStatus.NORMAL && !AccountStatus.NORMAL.code.equals(rcArchive.getAccountStatus())) {
            log.info("账户非正常状态，不允许修改");
            return false;
        }
        if (accountStatus.code.equals(rcArchive.getAccountStatus())) {
            log.info("账户状态不需要修改");
            return false;
        }
        RcAuditRecord rcAuditRecord = rcArchiveService.saveAuditRecord(rcArchive,
                "accountStatus", accountStatus.code, reason, 0L, 0L, null);
        rcArchiveService.batchAuditRecord(String.valueOf(rcAuditRecord.getId()), (short) 1, "通过", 0L, "1");
        return true;
    }

    public void changeAccountStatusAndRecoverAfterDays(String customerCode, AccountStatus accountStatus, String reason, int days) {
        boolean b = changeAccountStatus(customerCode, accountStatus, reason);
        if (b) {
            Timex now = Timex.now();
            log.info("修改商户[{}]账户状态[{}],[{}]天后自动恢复", customerCode, accountStatus, days);
            RcCloseTerm rcCloseTerm = new RcCloseTerm();
            rcCloseTerm.setCustomerCode(customerCode);
            rcCloseTerm.setTermCode(accountStatus.name());
            rcCloseTerm.setCloseTime(now.toDate());
            rcCloseTerm.setCreateTime(now.toDate());
            rcCloseTerm.setUpdateTime(now.toDate());
            rcCloseTerm.setReopenTime(now.plus(java.time.Duration.ofDays(days)).start().toDate());
            rcCloseTerm.setCloseType(1);
            rcCloseTermMapper.insert(rcCloseTerm);
        }
    }

    public void closeTerm(String termCode) {
        log.info("发kafka停用终端[{}]", termCode);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("terminalCode", termCode);
        kafkaProducer.send("CUS_CustomerChange", "rcHandUpTerminal", jsonObject);
    }

    public void openTerm(String termCode) {
        log.info("发kafka启用终端[{}]", termCode);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("terminalCode", termCode);
        kafkaProducer.send("CUS_CustomerChange", "rcEnableTerminal", jsonObject);
    }

    @Logable(businessTag = "closeTermAndReopenAfterDays")
    public void closeTermAndReopenAfterDays(String customerCode, String termCode, int days) {
        Timex now = Timex.now();
        log.info("停用终端[{}],[{}]天后自动启用", termCode, days);
        RcCloseTerm rcCloseTerm = new RcCloseTerm();
        rcCloseTerm.setCustomerCode(customerCode);
        rcCloseTerm.setTermCode(termCode);
        rcCloseTerm.setCloseTime(now.toDate());
        rcCloseTerm.setCreateTime(now.toDate());
        rcCloseTerm.setUpdateTime(now.toDate());
        rcCloseTerm.setReopenTime(now.plus(java.time.Duration.ofDays(days)).start().toDate());
        rcCloseTerm.setCloseType(0);
        try {
            rcCloseTermMapper.insert(rcCloseTerm);
            closeTerm(termCode);
        } catch (Exception e) {
            log.error("关闭终端异常", e);
        }
    }

    public void autoReopenTerm(Date reopenTime) {
        List<RcCloseTerm> rcCloseTerms = rcCloseTermMapper.selectByReopenTime(reopenTime);
        for (RcCloseTerm rcCloseTerm : rcCloseTerms) {
            if (rcCloseTerm.getCloseType() == 0) {
                log.info("商户[{}]重启终端[{}]", rcCloseTerm.getCustomerCode(), rcCloseTerm.getTermCode());
                try {
                    openTerm(rcCloseTerm.getTermCode());
                } catch (Exception e) {
                    log.error("重启终端异常", e);
                }
            } else if (rcCloseTerm.getCloseType() == 1) {
                log.info("商户[{}]取消禁止入金", rcCloseTerm.getCustomerCode());
                try {
                    changeAccountStatus(rcCloseTerm.getCustomerCode(), AccountStatus.NORMAL, "自动取消禁止入金");
                } catch (Exception e) {
                    log.error("取消禁止入金异常", e);
                }
            }
        }
    }

    public void closeBusiness(String customerCode, String businessCode) {
        try {
            CommonOuterResponse<?> resp = custService.changStatus(customerCode, businessCode, null, "0");
            if (!resp.isSuccess()) {
                log.info("关闭业务失败[{}][{}]", resp.getReturnCode(), resp.getReturnMsg());
            }
        } catch (Exception e) {
            log.error("关闭业务异常", e);
        }
    }

    public void closeBusinessAndReopenAfterDays(String customerCode, String businessCode, int days) {
        List<String> businessCodeList = cumCacheService.getCustomerBusinessInsts(customerCode,
                        CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code)
                .stream()
                .map(CustomerBusinessInstance::getBusinessCode)
                .collect(Collectors.toList());

        if (!businessCodeList.contains(businessCode)) {
            log.info("商户未开通此业务[{}]", businessCode);
            return;
        }

        Timex now = Timex.now();
        log.info("商户[{}]关闭业务[{}]", customerCode, businessCode);
        RcCloseBusiness rcCloseBusiness = new RcCloseBusiness();
        rcCloseBusiness.setCustomerCode(customerCode);
        rcCloseBusiness.setBusinessCode(businessCode);
        rcCloseBusiness.setCloseTime(now.toDate());
        rcCloseBusiness.setCreateTime(now.toDate());
        rcCloseBusiness.setUpdateTime(now.toDate());
        rcCloseBusiness.setReopenTime(now.plus(java.time.Duration.ofDays(days)).start().toDate());

        rcCloseBusinessMapper.insert(rcCloseBusiness);

        closeBusiness(customerCode, businessCode);
    }

    public void addEventRecord(String ruleCode, String triggerCustomerCode, String triggerValue) {
        RiskEventRule rule = riskEventRuleService.getRule(ruleCode);
        if (rule == null) {
            return;
        }
        RiskEventRecord data = new RiskEventRecord();
        Long dataId = riskEventRecordMapper.selectIdFromSeq();
        data.setEventId(dataId);
        data.setRuleCode(rule.getRuleCode());
        data.setTriggerTime(new Date());
        data.setRuleType(otherService.queryParamValueByTypeAndName("RISK_EVENT_RULE_TYPE", rule.getRuleType(), true));
        data.setRuleDesc(rule.getRuleDesc());
        data.setTriggerCustCode(triggerCustomerCode);
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(triggerCustomerCode, triggerCustomerCode, UserType.PPS_USER.code);
        data.setTriggerCustName(customerInfo.getName());
        data.setTriggerValue(triggerValue);
        data.setTriggerAction(rule.getTriggerAction());
        data.setParamType(rule.getParamType());
        riskEventRecordMapper.addEventRecord(data);
    }

    public void addEventRecord(String ruleCode, TxsPayResultMsg msg) {
        String triggerCustomerCode = Optional.ofNullable(msg.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code)).orElse("");
        String triggerValue = null;
        if (msg.getIndexs() != null) {
            triggerValue = Optional.ofNullable(msg.getIndexs().get("_TriggerValue" + ruleCode)).orElse(msg.getTransactionNo());
        }
        if (triggerValue == null) {
            triggerValue = triggerCustomerCode;
        }
        addEventRecord(ruleCode, triggerCustomerCode, triggerValue);
    }
}

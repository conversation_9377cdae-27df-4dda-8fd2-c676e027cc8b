package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.rc.controller.request.VerifyCheckBusinessRequest;
import com.epaylinks.efps.rc.controller.request.VerifyConfigRequest;
import com.epaylinks.efps.rc.controller.response.VerifyConfigResponse;
import com.epaylinks.efps.rc.domain.VerifyConfig;

import java.util.List;
import java.util.Map;

public interface VerifyConfigService {
    void check(VerifyConfigRequest request,Long userId);

    CommonOuterResponse add(VerifyConfigRequest request,Long userId);

    CommonOuterResponse edit(VerifyConfigRequest request,Long userId);

    CommonOuterResponse delete(String ids,Long userId);

    CommonOuterResponse audit(String ids,String auditResult,String opinion,Long userId);

    PageResult pageQuery(Map<String,Object> paramMap,Long userId,Boolean download);

    List<VerifyConfigResponse> exportPage(Map<String,Object> paramMap);

    CommonOuterResponse checkBusiness(VerifyCheckBusinessRequest request,Long userId);

    String queryVerifyChannel(VerifyConfig verifyConfig,String customerNo);

    VerifyConfig queryByCustomerNoValid(String customerNo);

    CommonOuterResponse getFirstVerifyChannel();
}

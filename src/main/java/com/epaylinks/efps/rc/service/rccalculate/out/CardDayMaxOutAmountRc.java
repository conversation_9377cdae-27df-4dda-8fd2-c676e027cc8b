package com.epaylinks.efps.rc.service.rccalculate.out;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.myredis.MyRedisTemplate;
import com.epaylinks.efps.common.myredis.MyRedisTemplateService;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.dao.RcLimitDataMapper;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.RcLimitData;
import com.epaylinks.efps.rc.service.RcCalculate;
import com.epaylinks.efps.rc.service.RcIndexAddValue;
import com.epaylinks.efps.rc.service.RcIndexReset;
import com.epaylinks.efps.rc.service.rccalculate.RcCalculteBasic;
import com.epaylinks.efps.rc.util.RcDateUtils;
import com.epaylinks.efps.rc.vo.RcCalculateRequest;

/**
 * 同卡代付单日最大出金限额
 * <AUTHOR>
 * @since 2018-12-10
 *
 */
@Service("Card-Day-Max-OUT-Amount")
public class CardDayMaxOutAmountRc implements RcCalculate , RcIndexReset , RcIndexAddValue{

	@Autowired
	private MyRedisTemplateService myRedisTemplateService;

	private MyRedisTemplate redisTemplate ;

	@Autowired
	private RcArchiveService rcArchiveService;

	@Autowired
	private RcLimitDataMapper rcLimitDataMapper;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private RcCalculteBasic rcCalculteBasic;
	
	@Override
	public void reset(RcLimit rcLimit) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
		//每日重置日最大出金为0
		Date date = new Date();
		String lastKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getLastDay());
		String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
		Map<String, Object> map = redisTemplate.opsForHash().entries(lastKey);
		for(String bankCardNo: map.keySet()) {
		      long dayOutAmount = (long) map.get(bankCardNo);
		      // businessTargetId 为 ${targetId}_${bankCardNo}
		      if(dayOutAmount > 0){
			      RcLimitData rcLimitData = new RcLimitData(sequenceService.nextValue("RcLimitData"), rcLimit.getBusinessTagerId() + "_" + bankCardNo,  
			                rcLimit.getDefineCode(), dayOutAmount + "", date, date , RcLimitData.Status.FINISHED.code, RcDateUtils.getLastDay());
			      rcLimitDataMapper.insert(rcLimitData);
		      }
		      if(!redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)){// 设置今天的值
		    	  redisTemplate.opsForHash().put(hashKey, bankCardNo, 0L);
		      }
		}
  	   redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值

	}

//	@Logable(businessTag = "Card-Day-Max-OUT-Amount:calculate")
	@Override
	public String calculate(RcLimit rcLimit, RcCalculateRequest rcCalculateRequest) {
		redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        if(!rcCalculteBasic.isWithdrawals(rcCalculateRequest.getBusinessCode())){// 非代付业务不做判断
            return CommonOuterResponse.SUCCEE;
        }
        String customerCode =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.CUSTOMER_CODE.code);
        String bankCardNo =  rcCalculateRequest.getBusinessTargetIds().get(RcConstants.BusinessTagerType.BANK_CARD.code);
        if(customerCode == null || bankCardNo == null ) {
            return CommonOuterResponse.SUCCEE;
        }
	    String amountStr = rcCalculateRequest.getIndexs().get(RcConstants.RcIndex.AMOUNT.code);
		if (StringUtils.isBlank(amountStr)) {
			throw new AppException(RcCode.INDEX_NOT_ENOUGH.code);
		}
		long amount = Long.parseLong(amountStr);
		long dayOutAmount = 0L;
        String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
		if(redisTemplate.opsForHash().hasKey(hashKey, bankCardNo)) {
			redisTemplate.opsForHash().put(hashKey,bankCardNo,(long) redisTemplate.opsForHash().get(hashKey, bankCardNo) + amount);
		}else {
			redisTemplate.opsForHash().put(hashKey,bankCardNo,amount);
			redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
		}
		dayOutAmount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
		if (dayOutAmount > Long.parseLong(rcLimit.getLimitValue())) {
			redisTemplate.opsForHash().put(hashKey,bankCardNo,((long) redisTemplate.opsForHash().get(hashKey, bankCardNo) - amount));
			// 记录触发日记
            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(rcLimit.getBusinessTagerType(), rcLimit.getBusinessTagerId());
			rcCalculteBasic.insertCalculateLog(rcCalculateRequest.getTransactionNo(),rcArchive.getArchiveCode(),rcArchive.getArchiveName(), rcLimit.getDefineCode(),rcLimit.getLimitValue(),dayOutAmount - amount+"",amount+"",true,"RC交易受限", rcArchive.getArchiveType());
			//实际发生值已经大于限定值
			return "RC交易受限";
		}
		return CommonOuterResponse.SUCCEE;
	}

//    @Logable(businessTag = "Card-Day-Max-OUT-Amount:calculateValue")
    @Override
    public void calculateValue(RcLimit rcLimit, Map<String, String> map) {
        redisTemplate = myRedisTemplateService.getMyRedisTemplate();
        String payState = map.get("payState");

        if(payState.equals("01")){
			String businessCode = map.get("businessCode");
			if (!rcCalculteBasic.isWithdrawals(businessCode)) {// 非代付业务不做判断
				return;
			}
			String customerCode = map.get("customerCode");
			String bankCardNo = map.get("bankCardNo");
			if(customerCode == null || bankCardNo == null) {// 提现业务不限制
				return;
			}
			long amount = Long.parseLong(map.get(RcConstants.RcIndex.AMOUNT.code));
			long currentAmount = 0L;
			String hashKey = rcCalculteBasic.getKey(rcLimit.getBusinessTagerId() ,rcLimit.getDefineCode() ,RcDateUtils.getCurrentDay());
			currentAmount = (long) redisTemplate.opsForHash().get(hashKey, bankCardNo);
			redisTemplate.opsForHash().put(hashKey, bankCardNo, -amount + currentAmount);// 增加值
			redisTemplate.expire(hashKey, 32, TimeUnit.DAYS);// 设置超时值
		}
    }

}

package com.epaylinks.efps.rc.service.rccalculate;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.service.BwListService;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.vo.RcCalculateRequestWrapper;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/26 17:43
 */
@Service
public class ApplyMerchantRc {
    @Autowired
    RcCalculteBasic rcCalculteBasic;

    @Autowired
    BwListService bwListService;

    @Autowired
    private OtherService otherService;

    private static final List<RcConstants.BusinessTagerType> RISK_CHECK_TARGET_TYPE = ImmutableList.of(
            RcConstants.BusinessTagerType.BUSINESS_LICENSE,
            RcConstants.BusinessTagerType.IDENTITY_CARD
    );

    private void riskCheck(RcCalculateRequestWrapper request) {
        for (RcConstants.BusinessTagerType businessTagerType : RISK_CHECK_TARGET_TYPE) {
            String target = request.getTarget(businessTagerType);
            if (target != null) {
                rcCalculteBasic.riskCheck(businessTagerType, target);
            }
        }
    }

    private String translateAddr(String code) {

        if (code != null && code.contains("-")) {
            // 翻译省市
            String[] addr = code.split("-");
            return (otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[0], true)
                    + "-"
                    + otherService.queryParamValueByTypeAndName("EPSP_REGION_CODE", addr[1], true));
        } else {
            return code;
        }

    }

    private CommonOuterResponse<?> createBlackResponse(RcCalculateRequestWrapper request, RcConstants.BusinessTagerType businessTagerType) {
        String riskTagsDesc = null;
        List<String> bwRiskTags = bwListService.getBwRiskTags(businessTagerType.code, request.getTarget(businessTagerType));
        if (!bwRiskTags.isEmpty()) {
            riskTagsDesc = "(" + Joiner.on(",").join(bwRiskTags) + ")";
        }

        String message = "RC" + businessTagerType.message + "存在风险";
        if (riskTagsDesc != null) {
            message += riskTagsDesc;
        }
        rcCalculteBasic.insertCalculateLog(request.getTransactionNo(),
                businessTagerType == RcConstants.BusinessTagerType.BUSINESS_ADDRESS ? translateAddr(request.getTarget(businessTagerType)) : request.getTarget(businessTagerType),
                businessTagerType.message,
                businessTagerType.blackType == null ? null : businessTagerType.blackType.defineCode,
                "是", "/", request.getTarget(businessTagerType), false, message, businessTagerType.code);

        CommonOuterResponse<?> commonOuterResponse = new CommonOuterResponse<>();
        commonOuterResponse.setReturnCode(RcCode.RC_CACLCUELATE_ERROR.code);
        commonOuterResponse.setReturnMsg(message);
        return commonOuterResponse;
    }

    public Optional<CommonOuterResponse<?>> getApplyMerchantCalcResponse(RcCalculateRequestWrapper request) {
        if (!request.getBusinessType().isPresent()) {
            return Optional.empty();
        }

        if (request.getBusinessType().get() != Constants.rcBusinessType.APPLY_MERCHANT) {
            return Optional.empty();
        }

        riskCheck(request);

        int checkBwValue1 = 0;
        int checkBwValue2 = 0;
        int checkBwValue3 = 0;
        int checkBwValue4 = 0;
        int checkBwValue5 = 0;
        int checkBwValue6 = 0;
        int checkBwValueCustomerName = 0;

        // 判断身份证是否黑名单
        if (request.getTarget(RcConstants.BusinessTagerType.IDENTITY_CARD) != null) {
            // 如果身份证类型值处于白名单，但该身份证前4位类型值处于黑名单，优先判断白名单，该身份证号允许进件
            checkBwValue1 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.IDENTITY_CARD.code,
                    request.getTarget(RcConstants.BusinessTagerType.IDENTITY_CARD));
            if (checkBwValue1 < 0) {
                return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.IDENTITY_CARD));
            }
            if (checkBwValue1 == 0) {
                checkBwValue2 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.IDENTITY_CARD_PREFIX.code,
                        request.getTarget(RcConstants.BusinessTagerType.IDENTITY_CARD).substring(0, 4));
                if (checkBwValue2 < 0) {
                    return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.IDENTITY_CARD_PREFIX));
                }
            }
        }

        // 判断营业执照是否黑名单
        if (request.getTarget(RcConstants.BusinessTagerType.BUSINESS_LICENSE) != null) {
            checkBwValue3 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.BUSINESS_LICENSE.code,
                    request.getTarget(RcConstants.BusinessTagerType.BUSINESS_LICENSE));
            if (checkBwValue3 < 0) {
                return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.BUSINESS_LICENSE));
            }
        }

        // 判断银行卡是否黑名单
        if (request.getTarget(RcConstants.BusinessTagerType.BANK_CARD) != null) {
            checkBwValue4 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.BANK_CARD.code,
                    request.getTarget(RcConstants.BusinessTagerType.BANK_CARD));
            if (checkBwValue4 < 0) {
                return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.BANK_CARD));
            }
        }

        // 判断手机号码是否黑名单
        if (request.getTarget(RcConstants.BusinessTagerType.CELL_PHONE_NUMBER) != null) {
            checkBwValue5 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.CELL_PHONE_NUMBER.code,
                    request.getTarget(RcConstants.BusinessTagerType.CELL_PHONE_NUMBER));
            if (checkBwValue5 < 0) {
                return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.CELL_PHONE_NUMBER));
            }
        }

        // 判断商户名称是否黑名单
        if (request.getTarget(RcConstants.BusinessTagerType.CUSTOMER_NAME) != null) {
            checkBwValueCustomerName = bwListService.blackOrWhite(RcConstants.BusinessTagerType.CUSTOMER_NAME.code,
                    request.getTarget(RcConstants.BusinessTagerType.CUSTOMER_NAME));
            if (checkBwValueCustomerName < 0) {
                return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.CUSTOMER_NAME));
            }
        }

        if (request.getTarget(RcConstants.BusinessTagerType.BUSINESS_ADDRESS) != null) {
            boolean noCheckBusinessAddr = checkBwValue1 > 0 || checkBwValue2 > 0 || checkBwValue3 > 0 || checkBwValue4 > 0 || checkBwValue5 > 0 || checkBwValueCustomerName > 0;
            if (!noCheckBusinessAddr) {
                checkBwValue6 = bwListService.blackOrWhite(RcConstants.BusinessTagerType.BUSINESS_ADDRESS.code,
                        request.getTarget(RcConstants.BusinessTagerType.BUSINESS_ADDRESS));
                if (checkBwValue6 < 0) {
                    return Optional.of(createBlackResponse(request, RcConstants.BusinessTagerType.BUSINESS_ADDRESS));
                }
            }
        }

        return Optional.of(CommonOuterResponse.success());
    }

}

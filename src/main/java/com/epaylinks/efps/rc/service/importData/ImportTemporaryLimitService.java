package com.epaylinks.efps.rc.service.importData;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.rc.command.RcCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.vo.TemporaryLimitVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("importTemporaryLimitService")
public class ImportTemporaryLimitService implements BatchService {
    @Autowired
    private CommonLogger logger;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        Long userId = Long.parseLong(extraData.get("userId") + "");
        Map<Integer, BatchDetail> resultMap = new HashMap<>();
        for(Integer rowNo : dataMap.keySet()) {
            try {
                List<String> cellList = dataMap.get(rowNo);
                TemporaryLimitVo temporaryLimitVo = new TemporaryLimitVo();
                for (int i = 0; i < cellList.size(); i++) {
                    String data = cellList.get(i);
                    switch (i) {
                        case 0: // 序号
                            break;
                        case 1: // 商户编号*
                            checkBlank(data,"商户编号/入金限制/变更原因字段必填");
                            RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code,data);
                            if (rcArchive == null) {
                                throw new AppException(RcCode.RECORD_NOT_EXISTS_EXCEPTION.code,RcCode.RECORD_NOT_EXISTS_EXCEPTION.message);
                            }
                            temporaryLimitVo =  rcArchiveService.getTemporaryDepositLimit(rcArchive.getArchiveId());
                            break;
                        case 2: // 入金限制*
                            checkBlank(data,"商户编号/入金限制/变更原因字段必填");
                            if ("限制".equals(data)) {
                                temporaryLimitVo.setTemporaryStatus(RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code);
                            } else if ("未限制".equals(data)) {
                                temporaryLimitVo.setTemporaryStatus(RcConstants.TemporaryStatus.NORMAL.code);
                            } else {
                                throw new AppException(RcCode.RC_ARG_ERROR.code,RcCode.RC_ARG_ERROR.message);
                            }
                            break;
                        case 3: // 变更原因*
                            checkBlank(data,"商户编号/入金限制/变更原因字段必填");
                            if (getByteLength(data) > 300) {
                                throw new AppException(RcCode.DATA_TOO_LONG.code,RcCode.DATA_TOO_LONG.message);
                            }
                            temporaryLimitVo.setTemporaryStatusReason(data);
                            break;
                        case 4: // 业务
                            if (RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())
                                    && StringUtils.isBlank(data)) {
                                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,"“入金限制”为“限制”，业务/最高限额（元）/是否启用 必填");
                            }
                            if (StringUtils.isBlank(data)) {
                                break;
                            }
                            temporaryLimitVo.setProperties(getByComment(data + "最高限额"));
                            break;
                        case 5: // 最高限额（元）
                            if (RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())
                                    && StringUtils.isBlank(data)) {
                                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,"“入金限制”为“限制”，业务/最高限额（元）/是否启用 必填");
                            }
                            if (StringUtils.isBlank(data)) {
                                break;
                            }
                            Double limit = Double.parseDouble(data);
                            Field field = TemporaryLimitVo.class.getDeclaredField(temporaryLimitVo.getProperties());
                            field.setAccessible(true);
                            Long value = Math.round(limit * 100);
                            field.set(temporaryLimitVo,value);
                            break;
                        case 6: // 是否启用
                            if (RcConstants.TemporaryStatus.TEMPORARY_LIMIT.code.equals(temporaryLimitVo.getTemporaryStatus())
                                    && StringUtils.isBlank(data)) {
                                throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,"“入金限制”为“限制”，业务/最高限额（元）/是否启用 必填");
                            }
                            if (StringUtils.isBlank(data)) {
                                break;
                            }
                            String state = "";
                            if ("启用".equals(data)) {
                                state = "1";
                            } else if ("禁用".equals(data)) {
                                state = "2";
                            } else {
                                throw new AppException(RcCode.RC_ARG_ERROR.code,RcCode.RC_ARG_ERROR.message);
                            }
                            Field fieldState = TemporaryLimitVo.class.getDeclaredField(temporaryLimitVo.getProperties() + "State");
                            fieldState.setAccessible(true);
                            fieldState.set(temporaryLimitVo,state);
                            break;
                    }
                }
                logger.printMessage("导入临时入金限制记录：" + JSON.toJSONString(temporaryLimitVo));
                CommonOuterResponse outerResponse = rcArchiveService.temporaryDepositLimit(temporaryLimitVo,userId);
                if (outerResponse.isSuccess()) {
                    resultMap.put(rowNo,buildDetail(rowNo,null, RcConstants.SuccessFail.SUCCESS.code,null,"导入成功"));
                } else {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, outerResponse.getReturnMsg()));
                }
            } catch (Exception e) {
                e.printStackTrace();
                logger.printMessage("导入临时入金限制记录错误：" + e.getMessage());
                logger.printLog(e);
                if (e instanceof AppException) {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                } else {
                    resultMap.put(rowNo, buildDetail(rowNo, null, RcConstants.SuccessFail.FAIL.code, null, RcCode.SYSTEM_EXCEPTION.message));
                }
            }
        }
        return resultMap;
    }

    private void checkBlank(String data,String message) {
        if (StringUtils.isBlank(data)) {
            throw new AppException(RcCode.INDEX_NOT_ENOUGH.code,message + "不为空");
        }
    }

    private BatchDetail buildDetail( Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    private Integer getByteLength(String param) {
        try {
            if (null == param) {
                return 0;
            }
            return param.getBytes("GBk").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 999;
    }

    static String getByComment(String name) {
        for (RcConstants.TemporaryLimit temporaryLimit : RcConstants.TemporaryLimit.values()) {
            if (temporaryLimit.comment.equals(name)) {
                return temporaryLimit.code;
            }
        }
        throw new AppException(RcCode.RC_ARG_ERROR.code,RcCode.RC_ARG_ERROR.message);
    }
}

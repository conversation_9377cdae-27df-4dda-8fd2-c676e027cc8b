package com.epaylinks.efps.rc.service;

import com.epaylinks.efps.rc.controller.response.LimitSwitchStatusResponse;
import com.epaylinks.efps.rc.domain.LimitSwitch;

public interface LimitSwitchService {
    /**
     * 查询商户出金单日限额/单笔限额
     * @param customerNo
     * @return
     */
    LimitSwitchStatusResponse getLimitStatus(String customerNo);

    /**
     * 查询商户指标
     * @param customerNo
     * @param index
     * @return
     */
    LimitSwitch getLimitInfo(String customerNo,String index);

    /**
     * 获取下一个主键
     * @return
     */
    Long selectPriKey();

    /**
     * 新增记录
     * @return
     */
    int insert(LimitSwitch limitSwitch);

    /**
     * 更新记录
     * @param limitSwitch
     * @return
     */
    int updateLimitSwitch(LimitSwitch limitSwitch);
}

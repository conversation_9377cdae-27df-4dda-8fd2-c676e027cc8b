package com.epaylinks.efps.rc.kafka;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.rc.command.DefineCode;
import com.epaylinks.efps.rc.command.RcConstants;
import com.epaylinks.efps.rc.controller.AmountLimitController;
import com.epaylinks.efps.rc.domain.RcArchive;
import com.epaylinks.efps.rc.domain.RcLimit;
import com.epaylinks.efps.rc.domain.User;
import com.epaylinks.efps.rc.domain.cust.Customer;
import com.epaylinks.efps.rc.service.OtherService;
import com.epaylinks.efps.rc.service.RcArchiveService;
import com.epaylinks.efps.rc.service.RcLimitService;
import com.epaylinks.efps.rc.vo.TemporaryLimitVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableKafka
@Component
@Transactional
public class CusCustomerConsumer {
    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private OtherService ortherService;

    @Autowired
    private RcArchiveService rcArchiveService;

    @Autowired
    private RcLimitService rcLimitService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private AmountLimitController amountLimitController;

    @Autowired
    private OtherService otherService;

    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5); // 创建一个线程池

    @Logable(businessTag = "CUS_CustomerChange")
    @KafkaListener(topics = {"CUS_CustomerChange"}, containerFactory = "customContainerFactory")
    public void consumeMsg(ConsumerRecord<?, ?> record) {
        logger.printLog("CUS_CustomerChange消费");
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = (String) kafkaMessage.get();
            String key = (String) record.key();
            this.consumeMsg(key, message);
        }
    }


    @Logable(businessTag = "logException")
    public void logException(Exception e) {
        // TODO Auto-generated method stub

    }

    private boolean isPerson(String customerType) {
        return "1".equals(customerType) || //1类个人
                "2".equals(customerType) || //2类个人
                "3".equals(customerType); //3类个人
    }

    private RcConstants.BusinessTagerType getCustomerBusinessTagerTypeByCustomerType(String customerType) {
        return isPerson(customerType) ? RcConstants.BusinessTagerType.PERSON : RcConstants.BusinessTagerType.CUSTOMER_CODE;
    }

    private String getRcLevel(RcArchive parentRcArchive, String customerType) {
        if ("1".equals(customerType)) {
            return RcConstants.RcLevel.LOW_LEVEL.code;
        }

        if ("2".equals(customerType)) {
            return RcConstants.RcLevel.MIDDLE_LEVEL.code;
        }

        if ("3".equals(customerType)) {
            return RcConstants.RcLevel.HIGHT_LEVEL.code;
        }

        return parentRcArchive != null ? parentRcArchive.getRcLevel() : RcConstants.RcLevel.LOW_LEVEL.code;
    }

    private RcConstants.BusinessTagerType getCertBusinessTagerTypeByCustomerType(String customerType) {
        if ("1".equals(customerType) || //1类个人
                "2".equals(customerType) || //2类个人
                "3".equals(customerType) || //3类个人
                Constants.CustomerType.MICRO.code.toString().equals(customerType)) {//小微
            return RcConstants.BusinessTagerType.IDENTITY_CARD;
        } else {
            return RcConstants.BusinessTagerType.BUSINESS_LICENSE;
        }
    }

    public void consumeMsg(String key, String message) {
        logger.printLog("CUS_CustomerChange消费:" + key + "," + message);
        Object obj = JSONObject.parse(message);
        JSONObject jsonObj = JSON.parseObject(obj.toString());
        try {
            logger.printLog(jsonObj.toJSONString());
            //新增商户，建立风控档案
            if ("customerAdd".equals(key)) {
                String customerCode = jsonObj.getString("customerCode");
                String customerName = jsonObj.getString("customerName");
                String customerType = jsonObj.getString("customerType");
                String customerCategory = jsonObj.getString("customerCategory"); // 商户角色
                String parentCustomerCode = jsonObj.getString("parentCustomerCode");
                Date regDate = jsonObj.getDate("regDate");
                String limitOutTrade = jsonObj.getString("limitOutTrade");
                Long limitOperator = jsonObj.getLong("limitOperator");
                String clientNo = jsonObj.getString("clientNo");
                Integer type = Integer.parseInt(customerType);
                Long registeredCapital = jsonObj.getLong("registeredCapital");

                String mcc = jsonObj.getString("mcc");
                String industry = otherService.queryIndustryByCode(customerCode);


                RcConstants.BusinessTagerType certBusinessTagerType = getCertBusinessTagerTypeByCustomerType(customerType);
                RcConstants.BusinessTagerType customerBusinessTagerType = getCustomerBusinessTagerTypeByCustomerType(customerType);

                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(customerBusinessTagerType.code,
                        customerCode);
                //防止重复消费
                if (rcArchive != null) {
                    return;
                }

                String certificateType = certBusinessTagerType.code;

                String certificateNo = (certBusinessTagerType == RcConstants.BusinessTagerType.IDENTITY_CARD ?
                        jsonObj.getString("lawyerCertNo") : jsonObj.getString("businessLicenseNo"));


                RcArchive parentRcArchive = null;
                if (parentCustomerCode != null) { // 父级商户风控档案（取风控等级）
                    parentRcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, parentCustomerCode);
                }

                Long userId = 0L;
                User user = ortherService.selectUserById(userId);

                rcArchive = new RcArchive();
                Long archiveId = sequenceService.nextValue("rc");
                rcArchive.setArchiveId(archiveId);
                rcArchive.setUserId(userId);
                rcArchive.setUserName(user.getName());
                //设置状态
                rcArchive.setAccountStatus("0");
                rcArchive.setRcStatus("0");
                rcArchive.setArchiveType(customerBusinessTagerType.code);
                rcArchive.setCusStatus("1");
                // 同步商户初审时设置的风险等级
                if (StringUtils.isNotBlank(limitOutTrade)) {
                    if ("1".equals(limitOutTrade)) {
                        rcArchive.setRcLevel(RcConstants.RcLevel.LOW_LEVEL.code);
                    } else if ("2".equals(limitOutTrade)) {
                        rcArchive.setRcLevel(RcConstants.RcLevel.MIDDLE_LEVEL.code);
                    } else if ("3".equals(limitOutTrade)) {
                        rcArchive.setRcLevel(RcConstants.RcLevel.HIGHT_LEVEL.code);
                    }
                }
                if (StringUtils.isBlank(rcArchive.getRcLevel())) {
                    rcArchive.setRcLevel(getRcLevel(parentRcArchive, customerType));
                }
                rcArchive.setBindCard("1");     // 默认需要报备
                rcArchive.setBindCardReason("初始设置");
                rcArchive.setAudStatus(RcConstants.AudStatus.SUCCESS.code);
                rcArchive.setArchiveCode(customerCode);
                rcArchive.setArchiveName(customerName);
                rcArchive.setRegTime(regDate);
                rcArchive.setCertificateNo(certificateNo);
                rcArchive.setParentCode(parentCustomerCode);
                rcArchive.setType(type);
                rcArchive.setMcc(mcc);
                rcArchive.setIndustry(industry);

                // 网银业务设置  ********
                if (Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerCategory)
                        || Constants.customerCategory.EFPS_CUSTOMER_SERVICE.code.equals(customerCategory)) { // 平台商代理商收单
                    rcArchive.setEBankSetting(RcConstants.EBankSetting.ACCEPT_ORDER.code);
                } else {
                    rcArchive.setEBankSetting(RcConstants.EBankSetting.PARENT.code); // 其他取上级
                }

                //保存
                rcArchiveService.insert(rcArchive);

                //风控状态指标
                RcLimit rcStatus = new RcLimit();
                rcStatus.setLimitId(sequenceService.nextValue("rc"));
                rcStatus.setLimitValue(rcArchive.getRcStatus());
                rcStatus.setUserId(rcArchive.getUserId());
                rcStatus.setUserName(rcArchive.getUserName());
                rcStatus.setBusinnesType(DefineCode.RC_STATUS.businessType);
                rcStatus.setDefineCode(DefineCode.RC_STATUS.defineCode);
                rcStatus.setDefineId(DefineCode.RC_STATUS.defineId);
                rcStatus.setLimitType("String");
                rcStatus.setBusinessTagerType(customerBusinessTagerType.code);
                rcStatus.setBusinessTagerId(rcArchive.getArchiveCode());
                rcStatus.setCreateTime(new Date());
                rcLimitService.save(rcStatus);

                //账户状态指标
                RcLimit accountStatus = new RcLimit();
                accountStatus.setLimitId(sequenceService.nextValue("rc"));
                accountStatus.setLimitValue(rcArchive.getAccountStatus());
                accountStatus.setUserId(rcArchive.getUserId());
                accountStatus.setUserName(rcArchive.getUserName());
                accountStatus.setBusinnesType(DefineCode.ACCOUNT_STATUS.businessType);
                accountStatus.setDefineCode(DefineCode.ACCOUNT_STATUS.defineCode);
                accountStatus.setDefineId(DefineCode.ACCOUNT_STATUS.defineId);
                accountStatus.setLimitType("String");
                accountStatus.setBusinessTagerType(customerBusinessTagerType.code);
                accountStatus.setBusinessTagerId(rcArchive.getArchiveCode());
                accountStatus.setCreateTime(new Date());
                rcLimitService.save(accountStatus);

                //风险等级
                RcLimit rcLevel = new RcLimit();
                rcLevel.setLimitId(sequenceService.nextValue("rc"));
                rcLevel.setLimitValue(rcArchive.getRcLevel());
                rcLevel.setUserId(rcArchive.getUserId());
                rcLevel.setUserName(rcArchive.getUserName());
                rcLevel.setBusinnesType(DefineCode.RC_LEVEL.businessType);
                rcLevel.setDefineCode(DefineCode.RC_LEVEL.defineCode);
                rcLevel.setDefineId(DefineCode.RC_LEVEL.defineId);
                rcLevel.setLimitType("String");
                rcLevel.setBusinessTagerType(customerBusinessTagerType.code);
                rcLevel.setBusinessTagerId(rcArchive.getArchiveCode());
                rcLevel.setCreateTime(new Date());
                rcLimitService.save(rcLevel);
/*
                if (customerBusinessTagerType == RcConstants.BusinessTagerType.PERSON) {
                    //非同名银行账户转账限制
                    RcLimit rcLimit = rcLimitService.queryLimit(DefineCode.ACCOUNT_NAME_LIMIT.defineId, customerBusinessTagerType.code,
                            rcArchive.getArchiveCode());
                    RcLimit rcAmountName = new RcLimit();
                    rcAmountName.setLimitId(sequenceService.nextValue("rc"));
                    rcAmountName.setLimitValue(rcLimit == null ? "1" : rcLimit.getLimitValue());
                    rcAmountName.setUserId(rcArchive.getUserId());
                    rcAmountName.setUserName(rcArchive.getUserName());
                    rcAmountName.setBusinnesType(DefineCode.ACCOUNT_NAME_LIMIT.businessType);
                    rcAmountName.setDefineCode(DefineCode.ACCOUNT_NAME_LIMIT.defineCode);
                    rcAmountName.setDefineId(DefineCode.ACCOUNT_NAME_LIMIT.defineId);
                    rcAmountName.setLimitType("String");
                    rcAmountName.setBusinessTagerType(customerBusinessTagerType.code);
                    rcAmountName.setBusinessTagerId(rcArchive.getArchiveCode());
                    rcAmountName.setCreateTime(new Date());
                    rcLimitService.saveToRedis(rcAmountName); //不插表，只保存到redis
                }
*/
                // 证件类型档案（小微记录身份证，企业个体工商记录营业执照）
                rcArchiveService.saveCertRcArchive(certificateType, certificateNo, userId);

                // 客户号档案
                rcArchiveService.saveClientNoRcArchive(clientNo,type,mcc,industry,registeredCapital,userId);

                // 设置100限额 100限额入口取消，limitOutTrade用于风险等级设置
                /*
                if (limitOutTrade != null && "1".equals(limitOutTrade)) {
                    fixedThreadPool.execute(new Runnable() {
                        @Override
                        public void run() {
                            amountLimitController.updateDaliyWithdrawal(customerCode,limitOperator,"add");
                        }
                    });
                }
                 */
                // 同步风控临时入金限制
                Customer customer = otherService.queryCustomerDraftByCustomerNo(customerCode);
                if (customer != null && StringUtils.isNotBlank(customer.getTemporaryStatus())) {
                    fixedThreadPool.execute(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                logger.printMessage("同步风控临时入金限制：" + customerCode);
                                TemporaryLimitVo temporaryLimitVo = rcArchiveService.preGetTemporaryDepositLimit(customerCode,"sync");
                                temporaryLimitVo.setArchiveId(archiveId);
                                Thread.sleep(5000);
                                rcArchiveService.temporaryDepositLimit(temporaryLimitVo,userId);
                            } catch (Exception e) {
                                e.printStackTrace();
                                logger.printMessage("同步风控临时入金限制错误：" + e.getMessage());
                                logger.printLog(e);
                            }
                        }
                    });
                }

            } else if ("customerStatusChange".equals(key)) {
                //修改商户状态
                rcArchiveService.updateCusStatus(jsonObj.getString("customerCode"), jsonObj.getString("oldStatus"), jsonObj.getString("newStatus"));

            } else if ("customerUpdate".equals(key)) { // 商户信息修改
                RcArchive rcArchive = rcArchiveService.selectByTypeAndCode(RcConstants.BusinessTagerType.CUSTOMER_CODE.code, jsonObj.getString("customerCode"));
                if (rcArchive != null) {
                    // 判断是否修改商户名称
                    boolean isUpdate = false;
                    String customerType = jsonObj.getString("customerType");
                    String customerName = jsonObj.getString("customerName");
                    String parentCustomerCode = jsonObj.getString("parentCustomerCode");
                    String certificateNo = Constants.CustomerType.MICRO.code.toString().equals(customerType) ?
                            jsonObj.getString("lawyerCertNo") : jsonObj.getString("businessLicenseNo");
                    String certificateType = Constants.CustomerType.MICRO.code.toString().equals(customerType) ?
                            RcConstants.BusinessTagerType.IDENTITY_CARD.code : RcConstants.BusinessTagerType.BUSINESS_LICENSE.code;
                    String limitOutTrade = jsonObj.getString("limitOutTrade");
                    Long limitOperator = jsonObj.getLong("limitOperator");
                    String customerCode = jsonObj.getString("customerCode");
                    if (!rcArchive.getArchiveName().equals(customerName)) {
                        rcArchive.setArchiveName(customerName);
                        isUpdate = true;
                    }
                    if (parentCustomerCode != null && !parentCustomerCode.equals(rcArchive.getParentCode())) {
                        rcArchive.setParentCode(parentCustomerCode);
                        isUpdate = true;
                    }
                    if (rcArchive.getCertificateNo() != null && !rcArchive.getCertificateNo().equals(certificateNo)) {
                        rcArchive.setCertificateNo(certificateNo);
                        isUpdate = true;
                        // 证件类型档案（小微记录身份证，企业个体工商记录营业执照）
                        rcArchiveService.saveCertRcArchive(certificateType, certificateNo, 0L);
                    }
                    if (isUpdate) {
                        rcArchiveService.updateRcArchiveSelective(rcArchive);
                    }
                    // 设置100限额
                    /*
                    if (limitOutTrade != null && "1".equals(limitOutTrade)) {
                        fixedThreadPool.execute(new Runnable() {
                            @Override
                            public void run() {
                                amountLimitController.updateDaliyWithdrawal(customerCode,limitOperator,"update");
                            }
                        });
                    }
                     */
                }
            }
        } catch (Exception e) {
            logger.printMessage("创建风控的档案失败:" + e.getLocalizedMessage());
            logger.printLog(e);
            logger.printLog("商户：" + jsonObj.getString("customerCode") + "创建风控的档案失败，原因是：" + e.getMessage());
        }
    }


}

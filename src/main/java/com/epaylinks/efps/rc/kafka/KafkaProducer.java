package com.epaylinks.efps.rc.kafka;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.log.Logable;

/**
* <AUTHOR>
* @version kafka生产者发送消息类
* @since 2019-12-03
* @return  
*/
@Component
public class KafkaProducer {

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
	 
	@Logable(businessTag="KafkaProducer.send")
	public <T> T send(final String topic, String key, T data) {
		kafkaTemplate.send(topic, key, JSONObject.toJSONString(data)); 
		return data;
	}
}
